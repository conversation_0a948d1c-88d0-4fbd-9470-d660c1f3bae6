# Environment variables template
# Copy this file to .env and fill in your values

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/zeus_db
DB_USER=username
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432
DB_NAME=zeus_db

# JWT
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AWS
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Environment
ENVIRONMENT=dev

# Graph Database (Neo4j)
GRAPH_DATABASE_URL=bolt://localhost:7687
GRAPH_DATABASE_USER=neo4j
GRAPH_DATABASE_PASSWORD=password

# Redis
REDIS_URL=redis://localhost:6379

# External APIs
GROQ_API_KEY=your-groq-api-key
