from app.database import SessionLocal
from app.models import models
from datetime import datetime
import logging

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

def add_llm_red_flags():
    db = SessionLocal()
    try:
        # Example data to insert into the LLMredFlagsStatus table

        db.query(models.LLMredFlagsStatus).delete()
        db.commit()
        rules_data = [
            {"rule_name": "txn_cnt_lt", "rule_description": "The merchant has processed an unusually low total transaction volume of transactions, which could indicate a new or inactive business"},
            {"rule_name": "txn_cnt_7d", "rule_description": "In the past week, the merchant has only processed transactions, showing potential business slowdown or operational issues"},
            {"rule_name": "txn_amt_avg_lt", "rule_description": "The average transaction amount is notably low, which may indicate structuring or suspicious transaction patterns"},
            {"rule_name": "txn_amt_avg_7d", "rule_description": "Recent 7-day average transaction amount shows significant deviation from historical patterns"},
            {"rule_name": "international_txn_cnt_pct", "rule_description": "A high percentage of international transactions could indicate cross-border money laundering activities"},
            {"rule_name": "avg_cx_pii_score_lt", "rule_description": "Low average customer PII verification score suggests potential identity verification issues"},
            {"rule_name": "avg_cx_pii_score_7d", "rule_description": "Recent 7-day customer PII score indicates deteriorating customer verification standards"},
            {"rule_name": "hrs_since_last_transaction", "rule_description": "Unusual gap in hours since last transaction may indicate business disruption or account takeover"},
            {"rule_name": "late_night_txn_amt_avg_lt", "rule_description": "Suspicious average transaction amount during off-hours (1-4 AM) requires investigation"},
            {"rule_name": "late_night_txn_amt_avg_7d", "rule_description": "Recent week's late-night (1-4 AM) average transaction amount shows concerning patterns"},
            {"rule_name": "ip_density_lt_score_lt", "rule_description": "Low IP address diversity score suggests potential automated fraud or single-source transactions"},
            {"rule_name": "device_id_density_lt_score_lt", "rule_description": "Device ID concentration indicates possible device fingerprinting evasion"},
            {"rule_name": "card_num_density_lt_score_lt", "rule_description": "High card number concentration score may indicate card testing or fraud"},
            {"rule_name": "cx_density_lt_score_lt", "rule_description": "Customer concentration score suggests potential synthetic identity fraud"},
            {"rule_name": "chargeback_txn_cnt_pct_lt_score_lt", "rule_description": "Elevated chargeback rate indicates customer disputes and potential fraud"},
            {"rule_name": "international_txn_cnt_pct_lt_score_lt", "rule_description": "High international transaction ratio requires enhanced due diligence"},
            {"rule_name": "failed_txn_cnt_pct_lt_score_lt", "rule_description": "Failed transaction rate suggests possible card testing or fraud attempts"},
            {"rule_name": "cancelled_txn_cnt_pct_lt_score_lt", "rule_description": "High cancellation rate may indicate buyer's remorse or fraudulent activity"},
            {"rule_name": "name_mismatch_txn_cnt_pct_lt_score_lt", "rule_description": "Name mismatch rate suggests potential identity theft or fraud"},
            {"rule_name": "risky_cx_txn_cnt_pct_lt_score_lt", "rule_description": "High-risk customer transaction rate requires immediate attention"},
            {"rule_name": "round_txn_cnt_pct_lt_score_lt", "rule_description": "Suspicious round amount transactions may indicate structured payments"},
            {"rule_name": "late_night_txn_cnt_pct_lt_score_lt", "rule_description": "Unusual late-night transaction pattern between 1-4 AM requires review"},
            {"rule_name": "ip_density_7d_score_7d", "rule_description": "Recent 7-day IP concentration shows concerning access patterns"},
            {"rule_name": "device_id_density_7d_score_7d", "rule_description": "Weekly device ID pattern with concentration suggests coordinated fraud"},
            {"rule_name": "card_num_density_7d_score_7d", "rule_description": "7-day card number concentration indicates possible card testing"},
            {"rule_name": "cx_density_7d_score_7d", "rule_description": "Weekly customer concentration may indicate coordinated fraud ring"},
            {"rule_name": "chargeback_txn_cnt_pct_7d_score_7d", "rule_description": "Recent 7-day chargeback spike requires immediate investigation"},
            {"rule_name": "international_txn_cnt_pct_7d_score_7d", "rule_description": "Weekly international transaction surge suggests potential money laundering"},
            {"rule_name": "failed_txn_cnt_pct_7d_score_7d", "rule_description": "7-day failed transaction rate indicates possible fraud attempts"},
            {"rule_name": "cancelled_txn_cnt_pct_7d_score_7d", "rule_description": "Weekly cancellation rate suggests customer dissatisfaction or fraud"},
            {"rule_name": "name_mismatch_txn_cnt_pct_7d_score_7d", "rule_description": "Recent name mismatch rate indicates identity verification issues"},
            {"rule_name": "risky_cx_txn_cnt_pct_7d_score_7d", "rule_description": "Weekly high-risk customer activity requires enhanced monitoring"},
            {"rule_name": "round_txn_cnt_pct_7d_score_7d", "rule_description": "7-day round amount transaction pattern suggests potential structuring"},
            {"rule_name": "late_night_txn_cnt_pct_7d_score_7d", "rule_description": "Weekly late-night transaction pattern shows suspicious timing"},
            {"rule_name": "cx_complaint_txn_pct_7d_score_7d", "rule_description": "Customer complaint rate in past week indicates serious service issues"},
        ]

        # Insert data into the database
        for i, rule in enumerate(rules_data, 1):
            # Check if rule already exists
            existing_rule = db.query(models.LLMredFlagsStatus).filter(
                models.LLMredFlagsStatus.rule_name == rule["rule_name"]
            ).first()
            
            if not existing_rule:
                new_rule = models.LLMredFlagsStatus(
                    rule_number=i,
                    rule_name=rule["rule_name"],
                    rule_description=rule["rule_description"],
                    status=True,
                    created_at=datetime.now()
                )
                db.add(new_rule)
                logger.info(f"Added rule: {rule['rule_name']}")
            else:
                logger.info(f"Rule already exists: {rule['rule_name']}")

        db.commit()
        logger.info("Successfully added all LLM red flags")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to add LLM red flags: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    add_llm_red_flags()