absl-py==2.1.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.10
aiosignal==1.3.2
aiosqlite==0.20.0
annotated-types==0.7.0
anyio==4.7.0
asttokens==3.0.0
async-timeout==4.0.3
asyncpg==0.30.0
attrs==24.3.0
beautifulsoup4==4.12.3
cachetools==5.5.0
certifi==2024.12.14
charset-normalizer==3.4.1
colorama==0.4.6
comm==0.2.2
contourpy==1.3.1
cycler==0.12.1
dataclasses-json==0.6.7
datasets==2.19.0
debugpy==1.8.11
decorator==5.1.1
dill==0.3.8
distro==1.9.0
docker==7.1.0
exceptiongroup==1.2.2
executing==2.1.0
filelock==3.16.1
fonttools==4.55.3
frozenlist==1.5.0
fsspec==2024.3.1
gensim==4.3.3
google-auth==2.37.0
greenlet==3.1.1
groq==0.13.1
h11==0.14.0
h5py==3.12.1
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.26.5
idna==3.10
ipykernel==6.29.5
ipython==8.31.0
jedi==0.19.2
Jinja2==3.1.5
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jupyter_client==8.6.3
jupyter_core==5.7.2
keras==3.7.0
kiwisolver==1.4.8
langchain==0.3.13
langchain-community==0.3.13
langchain-core==0.3.28
langchain-groq==0.2.2
langchain-text-splitters==0.3.4
langgraph==0.2.60
langgraph-checkpoint==2.0.9
langgraph-checkpoint-postgres==2.0.9
langgraph-sdk==0.1.48
langsmith==0.2.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.23.2
matplotlib==3.9.3
matplotlib-inline==0.1.7
mdurl==0.1.2
ml_dtypes==0.5.0
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
multiprocess==0.70.16
mypy-extensions==1.0.0
namex==0.0.8
nest-asyncio==1.6.0
networkx==3.4.2
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
optree==0.13.1
orjson==3.10.13
packaging==24.2
pandas==1.5.3
parso==0.8.4
pexpect==4.9.0
pillow==11.0.0
platformdirs==4.3.6
prompt_toolkit==3.0.48
propcache==0.2.1
psutil==6.1.1
psycopg==3.2.3
psycopg-binary==3.2.3
psycopg-pool==3.2.4
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==18.1.0
pyarrow-hotfix==0.6
pyasn1==0.6.1
pyasn1_modules==0.4.1
pydantic==2.10.4
pydantic-settings==2.7.1
pydantic_core==2.27.2
Pygments==2.18.0
pyparsing==3.2.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==13.9.4
rsa==4.9
safetensors==0.4.5
scikit-learn==1.6.0
scipy==1.13.1
seaborn==0.13.2
sentence-transformers==3.3.1
six==1.17.0
smart-open==7.1.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.36
stack-data==0.6.3
sympy==1.13.1
tabulate==0.9.0
tavily-python==0.5.0
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.8.0
tokenizers==0.19.1
torch==2.5.1
torchvision==0.20.1
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.44.2
triton==3.1.0
typing-inspect==0.9.0
typing_extensions==4.12.2
urllib3==2.3.0
wcwidth==0.2.13
wrapt==1.17.0
xxhash==3.5.0
yarl==1.18.3
