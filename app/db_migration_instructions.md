# Database Migration Instructions

## Add `has_visualization` column to `active_chatids` table

Run the following SQL to add the new column:

```sql
ALTER TABLE public.active_chatids ADD COLUMN has_visualization BOOLEAN DEFAULT FALSE;
```

This adds the column with a default value of `false`, so that:
1. All existing chats will have `has_visualization = false`
2. New chats created without specifying this value will have `has_visualization = false`
3. Only chats explicitly created for visualizations will have `has_visualization = true`

## Testing the Migration

To verify the migration was successful:

```sql
SELECT * FROM public.active_chatids LIMIT 5;
```

You should see the new `has_visualization` column with `false` values for existing chats. 