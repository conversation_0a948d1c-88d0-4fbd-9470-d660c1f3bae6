# Main FastAPI application - Enhanced Modular Architecture
from fastapi import FastAPI, HTTPException, Depends
from fastapi.responses import JSONResponse
from fastapi import status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import logging
from pathlib import Path
import os
import boto3
import json
from botocore.exceptions import ClientError

# Import new modular routers
from src.auth.router import router as auth_router
from src.merchants.router import router as merchants_router
from src.metrics.router import router as metrics_router
from src.rules.router import router as rules_router
from src.red_flags.router import router as red_flags_router
from src.investigations.router import router as investigations_router
from src.reports.router import router as reports_router
from src.admin.router import router as admin_router
from src.dashboard.router import router as dashboard_router
from src.case_management.router import router as case_management_router
from src.jobs.router import router as jobs_router
from src.customers.router import router as customers_router
from src.credit.router import router as credit_router
from src.backtesting.router import router as backtesting_router

# Import legacy routers for backward compatibility
try:
    from .routers import (
        chat_router, chat_router2, digital_footprint_router,
        credit_dashboard_router, monitoring_router, email_communication_router,
        metricsGPTRouter, strategy_backtesting_router, merchant_post_router,
        report_gen_router, job_sch_router, rule_repo_router, red_flag_router,
        merchant_red_flags, customer_red_flags, creditInsolvancy_router
    )
    from .routers.metricApis import router as legacy_metric_router
    from .routers.metricGenerationApis import router as legacy_metric_generation_router
    from .api.endpoints import financial_metrics, transaction_metrics
    LEGACY_ROUTERS_AVAILABLE = True
except ImportError:
    LEGACY_ROUTERS_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Legacy routers not available - some endpoints may not be accessible")

# Import configuration
try:
    from src.config import PROJECT_NAME, PROJECT_VERSION, API_V1_STR, ENVIRONMENT
except ImportError:
    # Fallback configuration
    PROJECT_NAME = "Zeus API"
    PROJECT_VERSION = "1.0.0"
    API_V1_STR = "/api/v1"
    ENVIRONMENT = os.getenv("ENVIRONMENT", "dev")

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def get_secret():
    if os.getenv('ENVIRONMENT') == 'dev':
        secret_name = "zeus/dev"
        region_name = "us-east-1"
        session = boto3.session.Session()
        client = session.client(service_name='secretsmanager', region_name=region_name)
        try:
            response = client.get_secret_value(SecretId=secret_name)
            secret = json.loads(response['SecretString'])
            for key, value in secret.items():
                os.environ[key] = value
        except ClientError as e:
            logger.error(f"Error fetching secrets: {e}")
            raise

# Get secrets before app initialization
get_secret()

app = FastAPI(
    title=PROJECT_NAME,
    description="Backend API for Zeus Payment Processing System - Enhanced Modular Architecture",
    version=PROJECT_VERSION,
    openapi_tags=[
        {"name": "Authentication", "description": "Operations with authentication"},
        {"name": "Merchants", "description": "Merchant management operations"},
        {"name": "Customers", "description": "Customer management operations"},
        {"name": "Credit", "description": "Credit assessment and scoring"},
        {"name": "Backtesting", "description": "Strategy analysis and backtesting"},
        {"name": "Rules", "description": "Rule management and validation"},
        {"name": "Red Flags", "description": "Risk flag management"},
        {"name": "Investigations", "description": "Investigation and case management"},
        {"name": "Reports", "description": "Report generation and analytics"},
        {"name": "Admin", "description": "System administration"},
        {"name": "Dashboard", "description": "Dashboard and visualization"},
        {"name": "Jobs", "description": "Job scheduling and management"},
        {"name": "Metrics", "description": "Metrics calculation and analysis"}
    ],
    swagger_ui_init_oauth={
        "usePkceWithAuthorizationCodeGrant": True,
        "useBasicAuthenticationWithAccessCodeGrant": True
    }
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include new modular routers (primary endpoints)
app.include_router(auth_router, prefix=f"{API_V1_STR}/auth", tags=["Authentication"])
app.include_router(merchants_router, prefix=f"{API_V1_STR}/merchants", tags=["Merchants"])
app.include_router(customers_router, prefix=f"{API_V1_STR}/customers", tags=["Customers"])
app.include_router(credit_router, prefix=f"{API_V1_STR}/credit", tags=["Credit"])
app.include_router(metrics_router, prefix=f"{API_V1_STR}/metrics", tags=["Metrics"])
app.include_router(rules_router, prefix=f"{API_V1_STR}/rules", tags=["Rules"])
app.include_router(red_flags_router, prefix=f"{API_V1_STR}/red-flags", tags=["Red Flags"])
app.include_router(investigations_router, prefix=f"{API_V1_STR}/investigations", tags=["Investigations"])
app.include_router(reports_router, prefix=f"{API_V1_STR}/reports", tags=["Reports"])
app.include_router(admin_router, prefix=f"{API_V1_STR}/admin", tags=["Admin"])
app.include_router(dashboard_router, prefix=f"{API_V1_STR}/dashboard", tags=["Dashboard"])
app.include_router(case_management_router, prefix=f"{API_V1_STR}/case-management", tags=["Case Management"])
app.include_router(jobs_router, prefix=f"{API_V1_STR}/jobs", tags=["Jobs"])
app.include_router(backtesting_router, prefix=f"{API_V1_STR}/backtesting", tags=["Backtesting"])

# Include legacy routers (for backward compatibility)
if LEGACY_ROUTERS_AVAILABLE:
    # Chat functionality
    app.include_router(chat_router, prefix=f"{API_V1_STR}/chat", tags=["Chat"])
    app.include_router(chat_router2, prefix=f"{API_V1_STR}/chat2", tags=["NewChat"])

    # Digital footprint and credit dashboard
    app.include_router(digital_footprint_router, prefix=f"{API_V1_STR}/digital-footprint", tags=["Digital Footprint"])
    app.include_router(credit_dashboard_router, prefix=f"{API_V1_STR}/credit-dashboard", tags=["Credit Dashboard"])

    # Monitoring and communication
    app.include_router(monitoring_router, prefix=f"{API_V1_STR}/monitoring", tags=["Monitoring"])
    app.include_router(email_communication_router, prefix=f"{API_V1_STR}/email-communication", tags=["Email Communication"])

    # Legacy metrics APIs
    app.include_router(legacy_metric_router, prefix=f"{API_V1_STR}/legacy-metrics", tags=["Legacy Metrics"])
    app.include_router(legacy_metric_generation_router, prefix=f"{API_V1_STR}/legacy-metrics-generation", tags=["Legacy Metrics Generation"])
    app.include_router(financial_metrics.router, prefix=f"{API_V1_STR}/financial-metrics", tags=["Financial Metrics"])
    app.include_router(transaction_metrics.router, prefix=f"{API_V1_STR}/transaction-metrics", tags=["Transaction Metrics"])

    # Additional legacy routers
    app.include_router(merchant_post_router, prefix=f"{API_V1_STR}/merchant", tags=["Merchant Operations"])
    app.include_router(report_gen_router, prefix=f"{API_V1_STR}/report-gen", tags=["Report Generation"])
    app.include_router(job_sch_router, prefix=f"{API_V1_STR}/job-sch", tags=["Job Scheduling"])
    app.include_router(rule_repo_router, prefix=f"{API_V1_STR}/rule-repo", tags=["Rule Repository"])
    app.include_router(red_flag_router, prefix=f"{API_V1_STR}/red-flag", tags=["Red Flag"])
    app.include_router(merchant_red_flags.router, prefix=f"{API_V1_STR}/merchant-red-flags", tags=["Merchant Red Flags"])
    app.include_router(customer_red_flags, prefix=f"{API_V1_STR}/customer-red-flags", tags=["Customer Red Flags"])
    app.include_router(creditInsolvancy_router, prefix=f"{API_V1_STR}/creditInsolvancy", tags=["Credit Insolvency"])

    # AI and advanced features
    app.include_router(metricsGPTRouter.router, prefix="/api/metrics-gpt", tags=["Metrics GPT"])
    # Note: Strategy backtesting is now handled by the dedicated backtesting module
else:
    logger.warning("Legacy routers not available - some functionality may be limited")
# Configure templates and static files
templates_dir = Path(__file__).resolve().parent / "templates"
static_dir = Path(__file__).resolve().parent / "static"
templates_dir.mkdir(exist_ok=True)
static_dir.mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

@app.get("/")
async def root():
    return {
        "message": "Welcome to Zeus Payment Processing API - Enhanced Modular Architecture",
        "version": PROJECT_VERSION,
        "environment": ENVIRONMENT,
        "features": {
            "modular_architecture": True,
            "enhanced_endpoints": True,
            "backward_compatibility": True,
            "total_modules": 14,
            "legacy_support": LEGACY_ROUTERS_AVAILABLE
        }
    }

# Legacy authentication endpoints (for backward compatibility)
from fastapi.security import HTTPBasic, HTTPBasicCredentials, OAuth2PasswordBearer, OAuth2PasswordRequestForm
from typing import Annotated
from sqlalchemy.orm import Session

try:
    from .database import get_db
    from .utils.auth import authenticate_user, get_password_hash
    from .utils.jwt import create_access_token

    security = HTTPBasic()

    @app.get("/users/me")
    def read_current_user(credentials: Annotated[HTTPBasicCredentials, Depends(security)]):
        return {"username": credentials.username, "password": credentials.password}

    @app.post("/token")
    async def login_for_access_token(form_data: Annotated[OAuth2PasswordRequestForm, Depends()], db: Session = Depends(get_db)):
        user = authenticate_user(db, form_data.username, form_data.password)
        if not user:
            raise HTTPException(
                status_code=401,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"}
            )

        access_token = create_access_token(data={"sub": user.email})
        return {"access_token": access_token, "token_type": "bearer"}

except ImportError:
    logger.warning("Legacy authentication utilities not available")

@app.middleware("http")
async def log_requests(request, call_next):
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        logger.error(f"Request failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "detail": "Internal server error",
                "message": str(e),
                "path": str(request.url.path) if hasattr(request, 'url') else "unknown"
            }
        )
