from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.responses import JSONResponse
from fastapi import status
from fastapi.middleware.cors import CORSMiddleware
from .routers import merchant_router,merchant_post_router, case_management_router, report_gen_router, email_communication_router, job_sch_router, auth_router, rule_repo_router, monitoring_router, red_flag_router, rule_repo_router, chat_router, admin_router, rules, merchant_red_flags, metrics, red_flags, metricsGPTRouter, chat_router2, customer_red_flags, dashboard_router
from .routers.metricApis import router as metric_router
from .routers.metricGenerationApis import router as metric_generation_router
from .api.endpoints import financial_metrics, transaction_metrics
from .routers import merchant_router,merchant_post_router, case_management_router, report_gen_router, email_communication_router, job_sch_router, auth_router, rule_repo_router, monitoring_router, red_flag_router, rule_repo_router, chat_router, admin_router, creditInsolvancy_router
from .database import engine
from .models import models
from .middleware.error_handler import error_handler
from .utils.seed_merchant import seed_merchant_data
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import logging
from pathlib import Path
import pyperclip
import asyncio
import subprocess
from contextlib import asynccontextmanager
from .routers.merchantRelatedApis import router as merchant_router
from .routers.digitalFootprintapis import router as digital_footprint_router
from .routers.creditDashboardapis import router as credit_dashboard_router
from .routers.customerAPIs import router as customer_router
from fastapi.security import HTTPBasic, HTTPBasicCredentials, OAuth2PasswordBearer, OAuth2PasswordRequestForm
from typing import Annotated
from sqlalchemy.orm import Session
from .database import get_db
from .utils.auth import authenticate_user, get_password_hash
from .utils.jwt import create_access_token
# from .utils.auth import get_current_superuser
import os
import boto3
import json
from botocore.exceptions import ClientError
from .routers.strategy_backtesting import router as strategy_backtesting_router

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def get_secret():
    if os.getenv('ENVIRONMENT') == 'dev':
        secret_name = "zeus/dev"
        region_name = "us-east-1"
        session = boto3.session.Session()
        client = session.client(service_name='secretsmanager', region_name=region_name)
        try:
            response = client.get_secret_value(SecretId=secret_name)
            secret = json.loads(response['SecretString'])
            for key, value in secret.items():
                os.environ[key] = value
        except ClientError as e:
            logger.error(f"Error fetching secrets: {e}")
            raise

# Get secrets before app initialization
get_secret()

app = FastAPI(
    title="Zeus API",
    description="Backend API for Zeus Payment Processing System",
    version="1.0.0",
    openapi_tags=[{"name": "Authentication", "description": "Operations with authentication"}],
    swagger_ui_init_oauth={
        "usePkceWithAuthorizationCodeGrant": True,
        "useBasicAuthenticationWithAccessCodeGrant": True
    }
)

security = HTTPBasic()

# Add error handling middleware
app.middleware("http")(error_handler)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(chat_router2, prefix="/api/v1/chat2", tags=["NewChat"])
app.include_router(dashboard_router, prefix="/api/v1/dashboard", tags=["Dashboard"])
app.include_router(creditInsolvancy_router, prefix="/api/v1/creditInsolvancy", tags=["Credit Insolvency"])
app.include_router(credit_dashboard_router, prefix="/api/v1/credit-dashboard", tags=["Credit Dashboard"])
app.include_router(digital_footprint_router, prefix="/api/v1/digital-footprint", tags=["Vineet"])
app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(merchant_router, prefix="/api/v1/merchants", tags=["Merchant Management"]) 
app.include_router(customer_router, prefix="/api/v1/customers", tags=["Customer Management"])
app.include_router(chat_router, prefix="/api/v1/chat", tags=["Chat"])
app.include_router(metric_router, prefix="/api/v1/metrics", tags=["Metrics"])
app.include_router(metric_generation_router, prefix="/api/v1/metrics", tags=["Metrics"])
app.include_router(metrics.router, prefix="/api/v1/metrics", tags=["Metrics"])
app.include_router(financial_metrics.router, prefix="/api/v1", tags=["Financial Metrics"])
app.include_router(transaction_metrics.router, prefix="/api/v1", tags=["Transaction Metrics"])
# app.include_router(risk_router, prefix="/api/v1/risk", tags=["risk"])
app.include_router(monitoring_router, prefix="/api/v1/monitoring", tags=["Monitoring"])
app.include_router(case_management_router, prefix="/api/v1/case-management", tags=["Case Management"])
app.include_router(rule_repo_router, prefix="/api/v1/rule-repo", tags=["Rule Repository"])
app.include_router(red_flag_router, prefix="/api/v1/red-flag", tags=["Red Flag"])
app.include_router(rule_repo_router, prefix="/api/v1/rule-repo", tags=["Rule Repository"])
# app.include_router(graph_creation_router, prefix="/api/v1/graph-creation", tags=["Graph Creation"])
app.include_router(report_gen_router, prefix="/api/v1/report-gen", tags=["Report Generation"])
app.include_router(email_communication_router, prefix="/api/v1/email-communication", tags=["Email Communication"])
app.include_router(job_sch_router, prefix="/api/v1/job-sch", tags=["Job Scheduling"])
app.include_router(merchant_post_router, prefix="/api/v1/merchant", tags=["Merchant Operations"])
app.include_router(rules.router, prefix="/api/v1/rules", tags=["Rules"])
app.include_router(
    admin_router.router,
    prefix="/api/v1/admin",
    tags=["Admin"]
)
app.include_router(merchant_red_flags.router, prefix="/api/v1/merchant-red-flags", tags=["Merchant Red Flags"])
app.include_router(red_flags.router, prefix="/api/v1/red-flags", tags=["Red Flags"])
app.include_router(admin_router.router, prefix="/api/v1/admin", tags=["Admin"])
app.include_router(creditInsolvancy_router, prefix="/api/v1/creditInsolvancy", tags=["Credit Insolvency"])
app.include_router(metricsGPTRouter.router, prefix="/api", tags=["Metrics GPT"])
app.include_router(customer_red_flags, prefix="/api/v1/customer-red-flags", tags=["Customer Red Flags"])
app.include_router(strategy_backtesting_router, prefix="/api/v1/strategy/backtesting", tags=["Strategy Backtesting"])
# Configure templates and static files
templates_dir = Path(__file__).resolve().parent / "templates"
static_dir = Path(__file__).resolve().parent / "static"
templates_dir.mkdir(exist_ok=True)
static_dir.mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

@app.get("/")
async def root():
    return {"message": "Welcome to Zeus Payment Processing API"}

@app.get("/users/me")
def read_current_user(credentials: Annotated[HTTPBasicCredentials, Depends(security)]):
    return {"username": credentials.username, "password": credentials.password}

@app.post("/token")
async def login_for_access_token(form_data: Annotated[OAuth2PasswordRequestForm, Depends()], db: Session = Depends(get_db)):
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=401, 
            detail="Incorrect email or password", 
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    access_token = create_access_token(data={"sub": user.email})
    return {"access_token": access_token, "token_type": "bearer"}

@app.middleware("http")
async def log_requests(request, call_next):
    # logger.info(f"Request path: {request.url.path}")
    try:
        response = await call_next(request)
        # logger.info(f"Response status: {response.status_code}")
        return response
    except Exception as e:
        logger.error(f"Request failed: {str(e)}")
        # Return an error response instead of re-raising the exception
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "detail": "Internal server error",
                "message": str(e)
            }
        )
