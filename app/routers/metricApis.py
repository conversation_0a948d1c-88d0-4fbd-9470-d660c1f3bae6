from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Union
from ..database import get_db, engine
from ..models.models import MetricStore, transactions, customer, network_overview
from sqlalchemy import select, inspect, text
import pandas as pd
from datetime import datetime

router = APIRouter()

@router.get("/metrics")
async def get_metrics(
    status: Optional[str] = Query(None, description="Filter metrics by status"),
    db: Session = Depends(get_db)
):
    """
    Get metrics based on status filter.
    Returns metrics in format: [{"table_name": "metric_table", "metric_code": "metric_code", ...}]
    """
    try:
        # Base query
        query = select(MetricStore)
        
        # Add status filter if provided
        if status:
            query = query.where(MetricStore.status == status)
            
        # Execute query
        result = db.execute(query).scalars().all()
        
        # Format response
        metrics = []
        for metric in result:
            metric_dict = {
                "table_name": metric.metric_table,
                "metric_code": metric.metric_code,
                "name": metric.name,
                "description": metric.description,
                "status": metric.status,
                "type": metric.type,
                "priority": metric.priority,
                "is_active": metric.is_active,
                "schedule": metric.schedule,
                "created_at": metric.created_at,
                "updated_at": metric.updated_at
            }
            metrics.append(metric_dict)
            
        return {"metrics": metrics}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/condition-fields")
async def get_condition_fields(db: Session = Depends(get_db)):
    """
    Get available fields that can be used in conditions.
    Returns a single array containing all available fields in format table_name.field_name
    """
    try:
        condition_fields = []
        
        # Get all active metrics
        metrics_query = select(MetricStore).where(
            MetricStore.is_active == True,
            MetricStore.metric_table.isnot(None),
            MetricStore.metric_code.isnot(None)
        )
        metrics = db.execute(metrics_query).scalars().all()
        
        # Add metric values
        for metric in metrics:
            if metric.metric_table and metric.metric_code:
                condition_fields.append(f"{metric.metric_table}.{metric.metric_code}")
        
        # Add transaction fields
        inspector = inspect(transactions)
        for column in inspector.columns:
            if column.name not in ['id', 'created_at', 'updated_at']:
                condition_fields.append(f"{transactions.__tablename__}.{column.name}")
        
        # Add customer fields
        inspector = inspect(customer)
        for column in inspector.columns:
            if column.name not in ['id', 'created_at', 'updated_at', 'merchant_id']:
                condition_fields.append(f"{customer.__tablename__}.{column.name}")
        
        # Add network overview fields
        inspector = inspect(network_overview)
        for column in inspector.columns:
            if column.name not in ['id', 'created_at', 'updated_at', 'merchant_id']:
                condition_fields.append(f"{network_overview.__tablename__}.{column.name}")
        
        return {"fields": condition_fields}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))