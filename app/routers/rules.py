from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID
from jose import JWTError, jwt
from datetime import datetime
from pydantic import BaseModel

from ..database import get_db
from ..models.models import rules_store, User
from ..schemas.rule_schema import RuleSchema, RuleUpdateSchema, RuleResponse
from ..utils.auth import authenticate_user
from ..utils.jwt import create_access_token, SECRET_KEY, ALGORITHM
from fastapi.security import OAuth2PasswordBearer

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    user = db.query(User).filter(User.email == email).first()
    if user is None:
        raise credentials_exception
    return user.email

router = APIRouter(
    prefix="/rules",
    tags=["rules"],
    dependencies=[Depends(get_current_user)]
)

@router.post("/", response_model=RuleResponse, status_code=status.HTTP_201_CREATED)
def create_rule(rule: RuleSchema, db: Session = Depends(get_db), current_user: str = Depends(get_current_user)):
    # Check if rule code already exists
    existing_rule = db.query(rules_store).filter(rules_store.code == rule.code).first()
    if existing_rule:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Rule code already exists"
        )
    
    # Create new rule
    db_rule = rules_store(
        code=rule.code,
        name=rule.name,
        description=rule.description,
        type=rule.type,
        severity=rule.severity,
        fraud_type=rule.fraud_type,
        rule=rule.rule.dict(),
        created_by=current_user,
        updated_by=current_user
    )
    
    db.add(db_rule)
    db.commit()
    db.refresh(db_rule)
    return db_rule

@router.put("/{rule_id}", response_model=RuleResponse)
def update_rule(
    rule_id: UUID,
    rule_update: RuleUpdateSchema,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    # Get existing rule
    db_rule = db.query(rules_store).filter(rules_store.id == rule_id).first()
    if not db_rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Rule not found"
        )
    
    # Update rule fields
    update_data = rule_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_rule, field, value)
    
    # Update version and updated_by
    db_rule.version += 1
    db_rule.updated_by = current_user
    
    db.commit()
    db.refresh(db_rule)
    return db_rule

@router.delete("/{rule_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_rule(
    rule_id: UUID,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    # Get existing rule
    db_rule = db.query(rules_store).filter(rules_store.id == rule_id).first()
    if not db_rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Rule not found"
        )
    
    # Soft delete by setting is_deleted to True and recording deletion time
    db_rule.is_deleted = True
    db_rule.deleted_at = datetime.now()
    db_rule.updated_by = current_user
    
    db.commit()
    return None

@router.get("/{rule_id}", response_model=RuleResponse)
def get_rule(
    rule_id: UUID,
    include_deleted: bool = False,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    query = db.query(rules_store).filter(rules_store.id == rule_id)
    if not include_deleted:
        query = query.filter(rules_store.is_deleted == False)
    
    db_rule = query.first()
    if not db_rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Rule not found"
        )
    return db_rule

class RulesResponse(BaseModel):
    data: List[RuleResponse]

@router.get("/", response_model=RulesResponse)
def get_rules(
    skip: int = 0,
    limit: int = 100,
    include_deleted: bool = False,
    db: Session = Depends(get_db),
    current_user: str = Depends(get_current_user)
):
    query = db.query(rules_store)
    if not include_deleted:
        query = query.filter(rules_store.is_deleted == False)
    
    rules = query.offset(skip).limit(limit).all()
    
    transformed_rules = [
        RuleResponse(
            id=str(rule.id),
            code=rule.code,
            name=rule.name,
            description=rule.description,
            status=rule.status,
            type=rule.type,
            severity=rule.severity,
            fraud_type=rule.fraud_type,
            rule=rule.rule,
            version=rule.version,
            is_active=rule.is_active,
            created_at=rule.created_at,
            updated_at=rule.updated_at,
            created_by=rule.created_by,
            updated_by=rule.updated_by
        ) for rule in rules
    ]
    
    return {"data": transformed_rules}