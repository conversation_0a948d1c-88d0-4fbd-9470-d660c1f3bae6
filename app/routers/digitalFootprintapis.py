import logging

from fastapi import APIRouter
from pydantic import BaseModel
from fastapi.responses import JSONResponse
from typing import List, Dict

from ..utils.scrapers import (
    WebsiteAnalyzerV2,
    ContentAnalyzer,
    extract_reviews,
    extract_reviews_and_ratings,
    extract_product_and_services,
    extract_product_categories,
    extract_risk_and_news_analysis,
    extract_policies,
    full_scrapper_temp,

)

class DigitalFootprintRequest(BaseModel):
    name: str
    website: str

class DigitalFootprintRequestV2(BaseModel):
    name: str
    website: str
    product_categories: List[Dict[str, str]]

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/merchant_digital_footprint")
async def merchant_digital_footprint(request: DigitalFootprintRequest):
    """
    Get the digital footprint of a merchant.
    """
    try:
        # Initialize the website analyzer
        analyzer = WebsiteAnalyzerV2()
        # Perform the analysis
        results_url = analyzer.analyze_website(request.website)

        # Initialize the content analyzer
        content_analyzer = ContentAnalyzer()
        # Analyze the content of the website
        results_content = content_analyzer.analyze_website_content(request.website)

        results = {
            "url_analysis": results_url.model_dump(),
            "content_analysis": results_content.model_dump(),
        }
        # Return the result
        return JSONResponse({
            "success": True,
            "message": "Digital footprint analysis completed successfully.",
            "data": results
        })
    except Exception as e:
        logging.error(f"Error in merchant_digital_footprint: {e}")  
        return JSONResponse({
            "success": False,
            "message": str(e),
            "data": None
        })

@router.post("/extract_reviews")
async def extract_reviews_endpoint(request: DigitalFootprintRequest):
    """
    Extract reviews from a merchant's website.
    """
    try:
        # Extract reviews from the website
        reviews = extract_reviews(request.website, request.name)
        # Return the result
        return JSONResponse({
            "success": True,
            "message": "Reviews extracted successfully.",
            "data": reviews.model_dump()
        })
    except Exception as e:
        logging.error(f"Error in extract_reviews: {e}")  
        return JSONResponse({
            "success": False,
            "message": str(e),
            "data": None
        })
    
@router.post("/extract_reviews_and_ratings")
async def extract_reviews_and_ratings_endpoint(request: DigitalFootprintRequest):
    """
    Extract reviews and ratings from a merchant's website.
    """
    try:
        # Extract reviews and ratings from the website
        reviews_and_ratings = extract_reviews_and_ratings(request.website, request.name)
        # Return the result
        return JSONResponse({
            "success": True,
            "message": "Reviews and ratings extracted successfully.",
            "data": reviews_and_ratings.model_dump()
        })
    except Exception as e:
        logging.error(f"Error in extract_reviews_and_ratings: {e}")  
        return JSONResponse({
            "success": False,
            "message": str(e),
            "data": None
        })

@router.post("/extract_products_and_services_categories")
async def extract_products_and_services_categories_endpoint(request: DigitalFootprintRequest):
    """
    Extract products and services categories from a merchant's website.
    """
    try:
        # Extract products and services categories from the website
        products_and_services_categories = extract_product_categories(request.website, request.name)
        # Return the result
        return JSONResponse({
            "success": True,
            "message": "Products and services categories extracted successfully.",
            "data": products_and_services_categories.model_dump()
        })
    except Exception as e:
        logging.error(f"Error in extract_products_and_services_categories: {e}")  
        return JSONResponse({
            "success": False,
            "message": str(e),
            "data": None
        })

@router.post("/extract_product_and_services")
async def extract_product_and_services_endpoint(request: DigitalFootprintRequestV2):
    """
    Extract products and services from a merchant's website.
    """
    try:
        # Extract products and services from the website
        products_and_services = extract_product_and_services(request.website, request.name, request.product_categories)
        # Return the result
        return JSONResponse({
            "success": True,
            "message": "Products and services extracted successfully.",
            "data": products_and_services.model_dump()
        })
    except Exception as e:
        logging.error(f"Error in extract_product_and_services: {e}")  
        return JSONResponse({
            "success": False,
            "message": str(e),
            "data": None
        })
    
@router.post("/extract_risk_and_news_analysis")
async def extract_risk_and_news_analysis_endpoint(request: DigitalFootprintRequest):
    """
    Extract risk and news analysis from a merchant's website.
    """
    try:
        # Extract risk and news analysis from the website
        risk_and_news_analysis = extract_risk_and_news_analysis(request.website, request.name)
        # Return the result
        return JSONResponse({
            "success": True,
            "message": "Risk and news analysis extracted successfully.",
            "data": risk_and_news_analysis.model_dump()
        })
    except Exception as e:
        logging.error(f"Error in extract_risk_and_news_analysis: {e}")  
        return JSONResponse({
            "success": False,
            "message": str(e),
            "data": None
        })

@router.post("/extract_policies")
async def extract_policies_endpoint(request: DigitalFootprintRequest):
    """
    Extract policies from a merchant's website.
    """
    try:
        # Extract policies from the website
        policies = extract_policies(request.website)
        # Return the result
        return JSONResponse({
            "success": True,
            "message": "Policies extracted successfully.",
            "data": policies.model_dump()
        })
    except Exception as e:
        logging.error(f"Error in extract_policies: {e}")  
        return JSONResponse({
            "success": False,
            "message": str(e),
            "data": None
        })
    
@router.post("/full_scrapper")
async def full_scrapper_endpoint(request: DigitalFootprintRequest):
    """
    Perform a full scrapper analysis on a merchant's website.
    """
    try:
        # Perform a full scrapper analysis on the website
        full_scrapper_results = full_scrapper_temp(request.website, request.name)
        # Return the result
        return JSONResponse({
            "success": True,
            "message": "Full scrapper analysis completed successfully.",
            "data": full_scrapper_results.model_dump()
        })
    except Exception as e:
        logging.error(f"Error in full_scrapper: {e}")  
        return JSONResponse({
            "success": False,
            "message": str(e),
            "data": None
        })