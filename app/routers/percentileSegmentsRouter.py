from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Path, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID, uuid4
from datetime import datetime, date
from ..database import get_db
from ..models import models
from ..schemas import request_models
from ..schemas.response_models import TransactionResponse, TransactionListResponse, PaymentChannelListResponse, PayoutListResponse, CommunicationListResponse, TimelineEventResponse, TimelineEventListResponse, InvestigationResponse, InvestigationListResponse, InvestigationNoteListResponse, KeyMetricsResponse
from collections import defaultdict
from .ares.model.llm_transaction_analysis import run_this_please
from .ares.feature_generation.incremental_feature_gen import main
import pandas as pd
import numpy as np
import os
import json
from .ares.investigation_gpt.gpt_with_context import use_it
from .ares.model.summarizzer import summer_is_mine
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from neo4j import GraphDatabase, exceptions
import time
from transformers import AutoTokenizer, AutoModel
import torch
from sklearn.metrics.pairwise import cosine_similarity
from groq import Groq
import asyncio
from .apiProtection import get_current_user

router = APIRouter(
    prefix='',
    tags=['Monitoring']
)
@router.get("/overall")
async def get_overall_monitoring_data(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    monitoring_data = db.query(models.monitoring).order_by(models.monitoring.created_at.desc()).all()
    data = sorted([
        {
            "segment": {
                "number": m.segment_number,
                "highest_risk_score": m.segment_highest_risk,
                "merchant_count": m.segment_number_of_merchants
            },
        } for m in monitoring_data
    ], key=lambda x: x["segment"]["number"])
    return {"data": data}

@router.get("/{business_category}")
async def get_monitoring_data(
    business_category: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    
    
    category_data = db.query(models.monitoring_category).filter(
        models.monitoring_category.category == business_category
    ).order_by(models.monitoring_category.created_at.desc()).all()


    data = sorted([
        {
            "segment": {
                "number": m.segment_number,
                "highest_risk_score": m.segment_highest_risk,
                "merchant_count": m.segment_number_of_merchants
            },
        } for m in category_data
    ], key=lambda x: x["segment"]["number"])
    return {"data": data}


