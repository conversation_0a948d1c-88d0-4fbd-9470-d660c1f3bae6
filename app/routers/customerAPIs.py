from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from ..database import get_db
from ..models import models
from .apiProtection import get_current_user

router = APIRouter()

@router.get("/customerIDs")
async def get_customer_ids(db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    customers = db.query(models.customer).all()
    return [{"id": str(customer.id), "name": customer.name} for customer in customers] 