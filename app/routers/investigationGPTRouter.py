from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Path, status, Request
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from uuid import UUID, uuid4
from datetime import datetime
from typing import List, Dict, Any
from ..database import get_db
from ..models import models
from ..schemas.request_models import (
    InvestigationListCreate,
    CaseEventListCreate,
    CaseEventMetaDataListCreate,
    NoteCaseEvent, 
    CaseRelation,
    InvestigatorListCreate,
    InvestigationUpdate,
    Information
)
from decimal import Decimal
from urllib.parse import unquote
import pandas as pd
import requests
import logging
from .apiProtection import get_current_user
from app.routers.ares.investigation_gpt.agentic import chat, edit_and_respond
import json
import asyncio
from fastapi.responses import StreamingResponse
import os


from groq import Groq


router = APIRouter(
    prefix="",
    tags=["Chat"]
)


# Helper function to stream data
async def stream_response():
    async def generate():
        yield "Starting process...\n"
        await asyncio.sleep(1)
        yield "Processing stage 1 complete...\n"
        await asyncio.sleep(1)
        yield "Processing stage 2 complete...\n"
        await asyncio.sleep(1)
        yield "Processing complete!\n"
    
    return StreamingResponse(generate(), media_type="text/plain")

@router.post("/stream-api", include_in_schema=False)
async def post_stream_api(request: Request):
    # StreamingResponse allows sending data in chunks
    return StreamingResponse(stream_response(), media_type="text/plain")


@router.get("/active-chatids")
async def active_chatids(db: Session = Depends(get_db)):
    # Get all active chat IDs from the database, sorted by created_at in descending order
    active_chats = db.query(models.active_chatids).order_by(models.active_chatids.created_at.desc()).all()
    return {"active_chats": [{"chat_id": str(chat.chat_id), "has_visualization": chat.has_visualization} for chat in active_chats]}

@router.get("/new-chat")
async def new_chat(db: Session = Depends(get_db)):
    chat_id = uuid4()
    
    # Create new active chat record
    new_chat = models.active_chatids(
        chat_id=chat_id,
        has_visualization=False,
        created_at=datetime.now()
    )
    db.add(new_chat)
    db.commit()
    
    return {"chat_id": chat_id}

@router.get("/new-visualization-chat")
async def new_visualization_chat(db: Session = Depends(get_db)):
    chat_id = uuid4()
    
    # Create new active chat record with has_visualization=True
    new_chat = models.active_chatids(
        chat_id=chat_id,
        has_visualization=True,
        created_at=datetime.now()
    )
    db.add(new_chat)
    db.commit()
    
    return {"chat_id": chat_id}
    
@router.get("/chat/edit-prompt/{chat_id}/{message_id}")
async def edit_prompt(chat_id: str, message: str, message_id: str, db: Session = Depends(get_db)):
    # check if the chat_id is in the active_chatids table
    active_chat = db.query(models.active_chatids).filter(models.active_chatids.chat_id == chat_id).first()
    if not active_chat:
        raise HTTPException(status_code=404, detail="Chat not found")
    #check if the message_id is in the chat_history table
    message_history = db.query(models.ChatHistory).filter(models.ChatHistory.message_id == message_id).first()
    if not message_history:
        raise HTTPException(status_code=404, detail="Message not found")
    
    async def generate():
        try:
            # First, update the user message in chat history
            user_message = db.query(models.ChatHistory).filter(
                models.ChatHistory.chat_id == chat_id,
                models.ChatHistory.message_id == message_id,
                models.ChatHistory.writer == "user"
            ).first()
            
            if not user_message:
                yield f"data: {json.dumps({'error': 'Message not found'})}\n\n"
                return

            # Get the timestamp of the edited message
            edit_timestamp = user_message.created_at
            
            # Delete all messages after this timestamp
            db.query(models.ChatHistory).filter(
                models.ChatHistory.chat_id == chat_id,
                models.ChatHistory.created_at > edit_timestamp
            ).delete()
            
            # Update the message content
            user_message.message = message
            db.commit()

            # Generate new response
            async for chunk in edit_and_respond(chat_id, message, message_id):
                if isinstance(chunk, dict):
                    # If this is the final response from assistant, store it
                    if chunk.get("is_final") and chunk.get("type") == "assistant":
                        assistant_message = models.ChatHistory(
                            chat_id=chat_id,
                            message_id=message_id,
                            writer="assistant",
                            message=chunk.get("content", ""),
                            created_at=datetime.now()
                        )
                        db.add(assistant_message)
                        db.commit()
                    
                    yield f"data: {json.dumps(chunk)}\n\n"
                else:
                    yield f"data: {json.dumps({'content': str(chunk)})}\n\n"
                await asyncio.sleep(0.1)

        except Exception as e:
            print(f"Error in edit_prompt: {str(e)}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
        finally:
            yield "data: [DONE]\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream", 
            "Access-Control-Allow-Origin": "*",
            "X-Accel-Buffering": "no"
        }
    )

@router.get("/chat-history/{chat_id}")
async def get_chat_history(chat_id: str, db: Session = Depends(get_db)):
    history = db.query(models.ChatHistory).filter(models.ChatHistory.chat_id == chat_id).order_by(models.ChatHistory.created_at.desc()).all()
    return {"history": history}

@router.get("/chat/{chat_id}/has-visualization")
async def get_chat_visualization_status(chat_id: str, db: Session = Depends(get_db)):
    chat = db.query(models.active_chatids).filter(models.active_chatids.chat_id == chat_id).first()
    if not chat:
        return {"has_visualization": False}
    return {"has_visualization": chat.has_visualization}

@router.post("/chat/{chat_id}")
async def chat_endpoint(
    chat_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    db = next(get_db())
    try:
        # Get merchants data
        merchants = db.query(models.Merchant).all()
        if not merchants:
            merchants_data = []
        else:
            merchants_data = [{
                'mer_id': m.id,
                'mer_onboarding_timestamp': m.onboarding_date,
                'mer_incorporation_date': m.incorporation_date,
                'mer_first_txn_date': m.first_txn_date,
                'mer_lst_txn_date': m.last_txn_date,
                'mer_industry': m.industry,
                'mer_business_industry': m.industry,
                'mer_industry_mca': m.mca_description,
                'mer_fraud_flag': bool(m.fraud_flag) if m.fraud_flag is not None else False,
                'mer_avg_txn_size': float(m.avg_txn_size) if m.avg_txn_size else 0.0,
                'mer_total_txn': int(m.total_txn) if m.total_txn else 0,
                'mer_total_txn_fy': int(m.total_txn_fy) if m.total_txn_fy else 0,
                'mer_gst_risk_flag': m.gst_risk_flag,
                'mer_mca_fillings_risk_flag': m.mca_fillings_risk_flag,
                'mer_directors_risk_flag': m.directors_risk_flag,
                'mer_num_employees': m.num_employees,
                'mer_epfo_reg_status': m.epfo_reg_status,
                'mer_is_sanctioned': m.is_sanctioned,
                'mer_is_online_business': m.is_online_business,
                'mer_online_presence_flag': m.online_presence_flag,
                'mer_tax_irregularity_flag': m.tax_irregularity_flag,
                'mer_is_pan_compatible': m.is_pan_compatible,
                'mer_is_address_compatible': m.is_address_compatible,
                'mer_prior_fraud_investigation_flag': m.prior_fraud_investigation_flag,
                'mer_is_MCA_submission_taken': m.is_MCA_submission_taken,
                'mer_udyam_cert_flag': m.udyam_cert_flag
            } for m in merchants]

        # Get transactions data
        transactions = db.query(models.transactions).all()
        if not transactions:
            transactions_data = []
        else:
            transactions_data = [{
                'txn_id': t.id,
                'mer_id': t.merchant_id,
                'business_type': t.merchant_type or '',
                'mer_business_industry': t.merchant_type or '',
                'txn_timestamp': t.timestamp.isoformat() if t.timestamp else None,
                'txn_amount': float(t.amount) if t.amount else 0.0,
                'is_fraud_transaction': bool(t.is_fraud_transaction) if t.is_fraud_transaction is not None else False,
                'cx_id': str(t.cx_id) if t.cx_id else '',
                'cx_ip': t.cx_ip or '',
                'cx_device_id': t.cx_device_id or '',
                'cx_card_number': t.cx_card_number or '',
                'cx_pii_linkage_score': int(t.cx_pii_linkage_score) if t.cx_pii_linkage_score else 0,
                'is_cardholder_name_match': bool(t.is_cardholder_name_match) if t.is_cardholder_name_match is not None else True,
                'is_chargeback': bool(t.is_chargeback) if t.is_chargeback is not None else False,
                'is_cx_international': bool(t.is_cx_international) if t.is_cx_international is not None else False,
                'txn_status': bool(t.status == 'completed') if t.status else False,
                'is_cx_risky': bool(t.is_cx_risky) if t.is_cx_risky is not None else False,
                'invoice_amount': float(t.invoice_amount) if t.invoice_amount else 0.0,
                'is_cancelled': bool(t.is_cancelled) if t.is_cancelled is not None else False,
                'txn_currency': t.txn_currency or 'INR',
                'has_cx_complaint': bool(t.has_cx_complaint) if t.has_cx_complaint is not None else False,
            } for t in transactions]

        # Create DataFrames with explicit empty dataframes if no data
        if not merchants_data:
            merchants_df = pd.DataFrame(columns=[
                'mer_id', 'mer_onboarding_timestamp', 'mer_incorporation_date',
                'mer_first_txn_date', 'mer_lst_txn_date', 'mer_industry',
                'mer_business_industry', 'mer_industry_mca', 'mer_fraud_flag',
                'mer_avg_txn_size'
            ])
        else:
            merchants_df = pd.DataFrame(merchants_data)

        if not transactions_data:
            transactions_df = pd.DataFrame(columns=[
                'txn_id', 'mer_id', 'business_type', 'mer_business_industry',
                'txn_timestamp', 'txn_amount'
            ])
        else:
            transactions_df = pd.DataFrame(transactions_data)

        # Create directory and save files
        os.makedirs('.ares/data', exist_ok=True)
        
        # Save with error checking
        try:
            transactions_df.to_csv('.ares/data/transactions.csv', index=False)
            merchants_df.to_csv('.ares/data/merchants.csv', index=False)
            
            # Verify the data was saved correctly
            saved_transactions_df = pd.read_csv('.ares/data/transactions.csv')
            saved_merchants_df = pd.read_csv('.ares/data/merchants.csv')
            
            if saved_transactions_df.empty and saved_merchants_df.empty:
                print("Warning: Both saved DataFrames are empty")
            
        except Exception as e:
            print(f"Error saving or reading CSV files: {str(e)}")
            # Continue execution even if file operations fail

        # check if the chat_id is in the active_chatids table
        active_chat = db.query(models.active_chatids).filter(models.active_chatids.chat_id == chat_id).first()
        if not active_chat:
            raise HTTPException(status_code=404, detail="Chat not found")
        
        try:
            body = await request.json()
            prompt = body.get("prompt")
            message_id = uuid4()
            if not prompt:
                raise HTTPException(status_code=400, detail="Prompt is required")
                
            # Store user prompt in ChatHistory
            user_message = models.ChatHistory(
                chat_id=chat_id,
                message_id=message_id,
                writer="user",
                message=prompt,
            )
            db.add(user_message)
            db.commit()
                
            async def generate_response():
                try:
                    yield f"data: {json.dumps({'message_id': str(message_id)})}\n\n"
                    
                    # Collect the complete response
                    complete_response = []
                    async for chunk in chat(chat_id, prompt, message_id):
                        if isinstance(chunk, dict):
                            # Store the entire chunk instead of just content
                            complete_response.append(chunk)
                            yield f"data: {json.dumps(chunk)}\n\n"
                        else:
                            # For non-dict responses, wrap in a standard format
                            formatted_chunk = {
                                "type": "text",
                                "content": str(chunk),
                                "timestamp": datetime.now().isoformat()
                            }
                            complete_response.append(formatted_chunk)
                            yield f"data: {json.dumps({'content': str(chunk)})}\n\n"
                        await asyncio.sleep(0.1)

                    # Store assistant response in ChatHistory
                    # Convert the list of chunks to JSON string
                    full_response = json.dumps(complete_response, ensure_ascii=False)
                    
                    assistant_message = models.ChatHistory(
                        chat_id=chat_id,
                        message_id=message_id,
                        writer="assistant",
                        message=full_response,  # Store the entire response as JSON string
                    )
                    db.add(assistant_message)
                    db.commit()
                    
                except Exception as e:
                    print(f"Error in generate_response: {str(e)}")
                    yield f"data: {json.dumps({'error': str(e)})}\n\n"
                finally:
                    yield "data: [DONE]\n\n"

            return StreamingResponse(
                generate_response(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream",
                    "Access-Control-Allow-Origin": "*",
                    "X-Accel-Buffering": "no"
                }
            )
        except Exception as e:
            print(f"Error in chat_endpoint: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        print(f"Error in chat_endpoint outer try: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/web-search-chat/{chat_id}")
async def web_search_chat_endpoint(
    chat_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    
    # check if the chat_id is in the active_chatids table
    active_chat = db.query(models.active_chatids).filter(models.active_chatids.chat_id == chat_id).first()
    if not active_chat:
        raise HTTPException(status_code=404, detail="Chat not found")
    
    try:
        body = await request.json()
        prompt = body.get("prompt")
        prompt = f"Search on web for: {prompt}"
        message_id = uuid4()
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")
            
        # Store user prompt in ChatHistory
        user_message = models.ChatHistory(
            chat_id=chat_id,
            message_id=message_id,
            writer="user",
            message=prompt,
        )
        db.add(user_message)
        db.commit()
            
        async def generate_response():
            try:
                # First yield the message_id
                yield f"data: {json.dumps({'message_id': str(message_id)})}\n\n"
                
                # Collect the complete response
                complete_response = []
                async for chunk in chat(chat_id, prompt, message_id):
                    if isinstance(chunk, dict):
                        # Store the entire chunk instead of just content
                        complete_response.append(chunk)
                        yield f"data: {json.dumps(chunk)}\n\n"
                    else:
                        # For non-dict responses, wrap in a standard format
                        formatted_chunk = {
                            "type": "text",
                            "content": str(chunk),
                            "timestamp": datetime.now().isoformat()
                        }
                        complete_response.append(formatted_chunk)
                        yield f"data: {json.dumps({'content': str(chunk)})}\n\n"
                    await asyncio.sleep(0.1)

                # Store assistant response in ChatHistory
                # Convert the list of chunks to JSON string
                full_response = json.dumps(complete_response, ensure_ascii=False)
                
                assistant_message = models.ChatHistory(
                    chat_id=chat_id,
                    message_id=message_id,
                    writer="assistant",
                    message=full_response,  # Store the entire response as JSON string
                )
                db.add(assistant_message)
                db.commit()
                
            except Exception as e:
                print(f"Error in generate_response: {str(e)}")
                yield f"data: {json.dumps({'error': str(e)})}\n\n"
            finally:
                yield "data: [DONE]\n\n"

        return StreamingResponse(
            generate_response(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "X-Accel-Buffering": "no"
            }
        )
    except Exception as e:
        print(f"Error in chat_endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    

    
@router.post("/investigation-gpt-promopt-suggestions")
async def get_investigation_gpt_promopt_suggestions(data: Information, db: Session = Depends(get_db)):
    client = Groq(
        api_key=os.environ.get("GROQ_API_KEY"),
    )
    
    print(data)
    # Create a system prompt that explains what we want
    system_prompt = """You are a helpful assistant that generates short, focused investigation prompts.
    Based on the given context, generate 8 different prompts that are 3-4 words each.
    Each prompt should be investigation-focused and help gather more information.
    Return only the list of prompts, one per line, without any additional text or numbering."""

    # Combine the context from Information with our request
    user_prompt = f"""Context: {data.data}

    Generate 8 short investigation prompts (3-4 words each) related to this context."""
    
    chat_completion = client.chat.completions.create(
        messages=[
            {
                "role": "system",
                "content": system_prompt
            },
            {
                "role": "user",
                "content": user_prompt,
            }
        ],
        model="llama-3.3-70b-versatile",
        temperature=0.7,  # Add some creativity but not too random
        max_tokens=200    # Limit response length
    )
    
    # Get the response text
    response_text = chat_completion.choices[0].message.content
    
    # Process the response into a list of prompts
    prompts = [
        prompt.strip()
        for prompt in response_text.split('\n')
        if prompt.strip()  # Remove empty lines
    ]
    
    # Ensure we have at least 7 prompts
    if len(prompts) < 7:
        raise HTTPException(
            status_code=500,
            detail="Failed to generate enough prompts"
        )
    
    # Return the list of prompts
    return {"prompts": prompts}