from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Request, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import uuid
from fastapi.responses import JSONResponse
from datetime import datetime

from app.database import get_db
from app.routers.ares.metrics_gpt.metrics_gpt import use_metrics_gpt

router = APIRouter(
    prefix="/api/metrics-gpt",
    tags=["MetricsGPT"]
)

class MetricsQuestion(BaseModel):
    user_prompt: str
    chat_id: Optional[str] = None

class MetricsResponse(BaseModel):
    id: str
    chat_id: str
    response: str
    timestamp: str

async def _process_metrics_query(user_prompt: str, chat_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Internal function to process metrics GPT queries
    """
    chat_id = chat_id or str(uuid.uuid4())
    message_id = str(uuid.uuid4())
    
    # Process the query
    response = use_metrics_gpt(chat_id, user_prompt)
    
    # Format the response
    return {
        "id": message_id,
        "chat_id": chat_id,
        "response": response,
        "timestamp": datetime.now().isoformat(),
        "type": "report"
    }

@router.post("/ask", response_model=MetricsResponse)
async def ask_metrics_gpt(data: MetricsQuestion, db: Session = Depends(get_db)):
    """
    Ask a question to the metrics GPT
    """
    try:
        response_data = await _process_metrics_query(data.user_prompt, data.chat_id)
        return response_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status", response_model=Dict[str, str])
async def get_status():
    """
    Get the status of the MetricsGPT service
    """
    return {"status": "active", "version": "1.0.0"} 