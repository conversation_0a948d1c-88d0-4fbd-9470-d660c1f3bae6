from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from ..database import get_db
from ..schemas.rule_schemas import RuleListCreate,LLM_rules, RuleCreate, MetricListCreate, LLM_metric_list_create, RuleUpdate, MetricListCreate_numeric, MetricListCreate_string, MetricListCreate_boolean,metricupdate_numeric,metricupdate_string,metricupdate_boolean
from ..models import models
from typing import List
from sqlalchemy.exc import SQLAlchemyError
from uuid import UUID
from datetime import datetime
from .apiProtection import get_current_user

router = APIRouter(
    prefix="",
    tags=["Rule Repository"]
)
@router.get("/rules")
def get_rules(current_user: models.User = Depends(get_current_user)):
    db = next(get_db())
    # Fetch all metrics and store them in a dictionary for quick access
    metrics_dict_numeric = {metric.metric_name: metric for metric in db.query(models.Metrics_numeric).all()}
    metrics_dict_string = {metric.metric_name: metric for metric in db.query(models.Metrics_string).all()}
    metrics_dict_boolean = {metric.metric_name: metric for metric in db.query(models.Metrics_boolean).all()}
    print("\nMetrics Dictionary:")

    rules = db.query(models.Rules).all()
    data = []
    metric_equations = [{
        "operator": "AND",
        "conditions": [
            {
                "operator": "AND",
                "conditions": [
                    {"metric_name": "ip_density", "operation": metrics_dict_numeric.get("ip_density").metric_operation, "value": metrics_dict_numeric.get("ip_density").metric_value, "type": "numeric"},
                    {"metric_name": "cx_city", "operation": metrics_dict_string.get("cx_city").metric_operation, "value": metrics_dict_string.get("cx_city").metric_value, "type": "string"}
                ]
            },
            {
                "operator": "AND", 
                "conditions": [
                    {
                        "operator": "OR",
                        "conditions": [
                            {"metric_name": "pct_failed_txn_amt", "operation": metrics_dict_numeric.get("pct_failed_txn_amt").metric_operation, "value": metrics_dict_numeric.get("pct_failed_txn_amt").metric_value, "type": "numeric"},
                            {"metric_name": "is_forged_doc", "operation": metrics_dict_boolean.get("is_forged_doc").metric_operation, "value": metrics_dict_boolean.get("is_forged_doc").metric_value, "type": "boolean"}
                        ]
                    },
                    {
                        "operator": "AND",
                        "conditions": [
                            {"metric_name": "successful_txn_pct", "operation": metrics_dict_numeric.get("successful_txn_pct").metric_operation, "value": metrics_dict_numeric.get("successful_txn_pct").metric_value, "type": "numeric" },
                            {"metric_name": "mer_risk_score", "operation": metrics_dict_numeric.get("mer_risk_score").metric_operation, "value": metrics_dict_numeric.get("mer_risk_score").metric_value, "type": "numeric"}
                        ]
                    }
                ]
            }
        ]
    }]
    
    for i, rule in enumerate(rules):
        data.append({
            "rule_code": rule.rule_code,
            "rule_name": rule.rule_name,
            "rule_description": rule.rule_description,
            "rule_status": rule.rule_status,
            "rule_type": rule.rule_type,
            "fraud_type": rule.fraud_type,
            "rule_severity": rule.rule_severity,
            "metric_equation": metric_equations[i] if i < len(metric_equations) else {}
        })

    return {"data": data}

@router.get("/metrics")
def get_metrics(db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    metrics_numeric = db.query(models.Metrics_numeric).all()
    metrics_string = db.query(models.Metrics_string).all()
    metrics_boolean = db.query(models.Metrics_boolean).all()
    
    # Use a set to track unique metric names
    unique_metrics = {}
    
    # Add metrics while checking for duplicates
    for metric in metrics_numeric:
        unique_metrics[metric.metric_name] = {
            "metric_name": metric.metric_name,
            "metric_description": metric.metric_description,
        }
        
    for metric in metrics_string:
        unique_metrics[metric.metric_name] = {
            "metric_name": metric.metric_name,
            "metric_description": metric.metric_description,
        }
        
    for metric in metrics_boolean:
        unique_metrics[metric.metric_name] = {
            "metric_name": metric.metric_name,
            "metric_description": metric.metric_description,
        }

    # Convert dictionary values to list
    data = list(unique_metrics.values())
    return {"data": data}

@router.get("/LLM_metrics")
def get_LLM_metrics(db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    LLM_metrics = db.query(models.LLM_metrics).all()
    data = []
    for LLM_metric in LLM_metrics:
        data.append({
            "metric_name": LLM_metric.metric_name,
            "metric_description": LLM_metric.metric_description,
            "severity": LLM_metric.severity,
            "active_status": LLM_metric.active_status,
            "last_updated": LLM_metric.last_updated,
            "last_updated_by": LLM_metric.last_updated_by
        })
    return {"data": data}

@router.post("/{investigator_email}/{rule_code}/update_rule")
def update_rule(investigator_email: str, rule_code: str, rule: RuleUpdate, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    existing_rule = db.query(models.Rules).filter(models.Rules.rule_code == rule_code).first()
    if not existing_rule:
        raise HTTPException(status_code=404, detail="Rule not found")
    existing_rule.rule_severity = rule.severity
    existing_rule.rule_status = rule.active_status
    existing_rule.last_updated = datetime.now()
    existing_rule.last_updated_by = investigator_email
    db.commit()
    return {"message": "Rule updated successfully"}





@router.post("/{investigator_email}/{metric_name}/{Rule_code}/update_a_rule_metric_numeric")
def update_metric(investigator_email: str,Rule_code : str, metric_name: str, metric1: metricupdate_numeric, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    metric_numeric = db.query(models.Metrics_numeric).filter(models.Metrics_numeric.metric_name == metric_name, models.Metrics_numeric.rule_code == Rule_code).first()
    if not metric_numeric:
        raise HTTPException(status_code=404, detail="Metric not found")
    metric_numeric.metric_value = metric1.value
    metric_numeric.metric_operation = metric1.operation
    metric_numeric.last_updated = datetime.now()
    metric_numeric.last_updated_by = investigator_email
    db.commit()
    return {"message": "Metric updated successfully"}

@router.post("/{investigator_email}/{metric_name}/{Rule_code}/update_a_rule_metric_string")
def update_metric_string(investigator_email: str,Rule_code : str, metric_name: str, metric1: metricupdate_string, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    metric_string = db.query(models.Metrics_string).filter(models.Metrics_string.metric_name == metric_name, models.Metrics_string.rule_code == Rule_code).first()
    if not metric_string:
        raise HTTPException(status_code=404, detail="Metric not found")
    metric_string.metric_value = metric1.value
    metric_string.metric_operation = metric1.operation
    metric_string.last_updated = datetime.now()
    metric_string.last_updated_by = investigator_email
    db.commit()
    return {"message": "Metric updated successfully"}

@router.post("/{investigator_email}/{metric_name}/{Rule_code}/update_a_rule_metric_boolean")
def update_metric_boolean(investigator_email: str,Rule_code : str, metric_name: str, metric1: metricupdate_boolean, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    metric_boolean = db.query(models.Metrics_boolean).filter(models.Metrics_boolean.metric_name == metric_name, models.Metrics_boolean.rule_code == Rule_code).first()
    if not metric_boolean:
        raise HTTPException(status_code=404, detail="Metric not found")
    metric_boolean.metric_value = metric1.value
    metric_boolean.metric_operation = metric1.operation
    metric_boolean.last_updated = datetime.now()
    metric_boolean.last_updated_by = investigator_email
    db.commit()
    return {"message": "Metric updated successfully"}

@router.post("/{investigator_email}/{metric_name}/update_LLM_metric")
def update_LLM_metric(investigator_email: str, metric_name: str, LLM_metric: LLM_rules, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    rule = db.query(models.LLM_metrics).filter(models.LLM_metrics.metric_name == metric_name).first()
    if not rule:
        raise HTTPException(status_code=404, detail="Rule not found")
    rule.active_status = LLM_metric.active_status
    rule.severity = LLM_metric.severity
    rule.last_updated = datetime.now()
    rule.last_updated_by = investigator_email
    db.commit()
    return {"message": "Rule updated successfully"}


@router.post('/rules', include_in_schema=False)
def add_rules(rules: RuleListCreate, db: Session = Depends(get_db)):
    db.query(models.Rules).delete()
    for rule in rules.rules:
        db.add(models.Rules(rule_code=rule.rule_code, rule_name=rule.rule_name, rule_description=rule.rule_description, rule_status=rule.rule_status, rule_type=rule.rule_type,fraud_type=rule.fraud_type, rule_severity=rule.rule_severity, last_updated=datetime.now(), last_updated_by="ADMIN"))
    db.commit()
    return {"message": "Rules added successfully"}

@router.post("/metrics_numeric", include_in_schema=False)
def add_metrics(metrics: MetricListCreate_numeric, db: Session = Depends(get_db)):
    db.query(models.Metrics_numeric).delete()
    db.query(models.merchant_metric_value_numeric).delete()
    db.query(models.merchant_mertric_results).delete()
    merchants= db.query(models.Merchant).all()
    for merchant in merchants:
        for metric in metrics.metrics:
            db.add(models.merchant_mertric_results(merchant_id=merchant.id, metric_type=metric.metric_name))
            db.add(models.merchant_metric_value_numeric(merchant_id=merchant.id, metric_type=metric.metric_name))
    for metric in metrics.metrics:
        if metric.metric_type == "numeric":
            db.add(models.Metrics_numeric(metric_name=metric.metric_name, metric_description=metric.metric_description, metric_status=metric.metric_status, metric_value=metric.metric_value, metric_operation=metric.metric_operation,rule_code=metric.rule_code, last_updated=datetime.now(), last_updated_by="ADMIN"))
        elif metric.metric_type == "string":
            db.add(models.Metrics_string(metric_name=metric.metric_name, metric_description=metric.metric_description, metric_status=metric.metric_status, metric_value=metric.metric_value, metric_operation=metric.metric_operation,rule_code=metric.rule_code, last_updated=datetime.now(), last_updated_by="ADMIN"))
        elif metric.metric_type == "boolean":
            db.add(models.Metrics_boolean(metric_name=metric.metric_name, metric_description=metric.metric_description, metric_status=metric.metric_status, metric_value=metric.metric_value, metric_operation=metric.metric_operation,rule_code=metric.rule_code, last_updated=datetime.now(), last_updated_by="ADMIN"))
    db.commit() 
    return {"message": "Metrics added successfully"}


@router.post("/metrics_string", include_in_schema=False)
def add_metrics(metrics: MetricListCreate_string, db: Session = Depends(get_db)):
    db.query(models.Metrics_string).delete()
    db.query(models.merchant_metric_value_string).delete()
    db.query(models.merchant_mertric_results).delete()
    merchants= db.query(models.Merchant).all()
    for merchant in merchants:
        for metric in metrics.metrics:
            db.add(models.merchant_mertric_results(merchant_id=merchant.id, metric_type=metric.metric_name))
            db.add(models.merchant_metric_value_string(merchant_id=merchant.id, metric_type=metric.metric_name))
    for metric in metrics.metrics:
        if metric.metric_type == "numeric":
            db.add(models.Metrics_numeric(metric_name=metric.metric_name, metric_description=metric.metric_description, metric_status=metric.metric_status, metric_value=metric.metric_value, metric_operation=metric.metric_operation,rule_code=metric.rule_code, last_updated=datetime.now(), last_updated_by="ADMIN"))
        elif metric.metric_type == "string":
            db.add(models.Metrics_string(metric_name=metric.metric_name, metric_description=metric.metric_description, metric_status=metric.metric_status, metric_value=metric.metric_value, metric_operation=metric.metric_operation,rule_code=metric.rule_code, last_updated=datetime.now(), last_updated_by="ADMIN"))
        elif metric.metric_type == "boolean":
            db.add(models.Metrics_boolean(metric_name=metric.metric_name, metric_description=metric.metric_description, metric_status=metric.metric_status, metric_value=metric.metric_value, metric_operation=metric.metric_operation,rule_code=metric.rule_code, last_updated=datetime.now(), last_updated_by="ADMIN"))
    db.commit() 
    return {"message": "Metrics added successfully"}

@router.post("/metrics_boolean", include_in_schema=False)
def add_metrics(metrics: MetricListCreate_boolean, db: Session = Depends(get_db)):
    db.query(models.Metrics_boolean).delete()
    db.query(models.merchant_metric_value_boolean).delete()
    db.query(models.merchant_mertric_results).delete()
    merchants= db.query(models.Merchant).all()
    for merchant in merchants:
        for metric in metrics.metrics:
            db.add(models.merchant_mertric_results(merchant_id=merchant.id, metric_type=metric.metric_name))
            db.add(models.merchant_metric_value_boolean(merchant_id=merchant.id, metric_type=metric.metric_name))
    for metric in metrics.metrics:
        if metric.metric_type == "numeric":
            db.add(models.Metrics_numeric(metric_name=metric.metric_name, metric_description=metric.metric_description, metric_status=metric.metric_status, metric_value=metric.metric_value, metric_operation=metric.metric_operation,rule_code=metric.rule_code, last_updated=datetime.now(), last_updated_by="ADMIN"))
        elif metric.metric_type == "string":
            db.add(models.Metrics_string(metric_name=metric.metric_name, metric_description=metric.metric_description, metric_status=metric.metric_status, metric_value=metric.metric_value, metric_operation=metric.metric_operation,rule_code=metric.rule_code, last_updated=datetime.now(), last_updated_by="ADMIN"))
        elif metric.metric_type == "boolean":
            db.add(models.Metrics_boolean(metric_name=metric.metric_name, metric_description=metric.metric_description, metric_status=metric.metric_status, metric_value=metric.metric_value, metric_operation=metric.metric_operation,rule_code=metric.rule_code, last_updated=datetime.now(), last_updated_by="ADMIN"))
    db.commit() 
    return {"message": "Metrics added successfully"}

@router.post("/LLM_metrics", include_in_schema=False)
def add_LLM_metrics(LLM_metrics: LLM_metric_list_create, db: Session = Depends(get_db)):
    db.query(models.LLM_metrics).delete()
    for LLM_metric in LLM_metrics.metrics:
        db.add(models.LLM_metrics(metric_name=LLM_metric.metric_name, metric_description=LLM_metric.metric_description, severity=LLM_metric.severity, active_status=LLM_metric.active_status, last_updated=datetime.now(), last_updated_by="ADMIN"))
    db.commit()
    return {"message": "LLM Metrics added successfully"}
