from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import MessagesState
from langchain_openai import OpenAIEmbeddings
from langchain_core.vectorstores import InMemoryVectorStore
from langchain_core.documents import Document

import json
import sys
import uuid
from pathlib import Path
from decimal import Decimal
from datetime import date, datetime
from pydantic import BaseModel, Field
from typing import List
import numpy as np
from openai import OpenAI


from langgraph.graph import StateGraph, END
from langchain.chat_models import init_chat_model

# Add the project root to Python path
project_root = str(Path(__file__).resolve().parent.parent.parent.parent.parent)
sys.path.append(project_root)

# Import the actual functions
from app.api.endpoints.transaction_metrics import get_transaction_metrics as get_txn_metrics
from app.api.endpoints.financial_metrics import get_financial_metrics as get_fin_metrics
from app.routers.ares.modus_agent.get_db import get_db_async
from app.database import get_db

# Import the other direct functions
from app.routers.creditDashboardInternal import (
    get_about_company_internal,
    get_about_industry_internal,
    get_company_metrics_internal,
    get_risk_metrics_internal
)
from app.routers.merchant_red_flags import get_merchant_red_flags_internal

llm = init_chat_model("openai:gpt-4.1")
client = OpenAI()

ALL_RED_FLAGS_TYPES = [
    "insolvency_external_audit",
    "insolvency_external_executive",
    "insolvency_external_disclosures",
    "insolvency_external_financial",
    "insolvency_external_legal",
    "insolvency_external_operational",
    "insolvency_external_annualReport",
    "insolvency_external_industry",
    "insolvency_external_brand",
    "insolvency_financial_financialStatements",
    "insolvency_financial_financialMetrics",
    "transaction_monitoring",
    "insolvency_overview_pdAnalysis",
    "insolvency_overview_company",
    "insolvency_overview_industry",
    "insolvency_overview_transactionMetrics"
]

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)
    
class State(MessagesState):
    merchant_id: str = Field(..., description="The ID of the merchant")
    unique_metrics: dict = Field(
        default_factory=dict,
        description="A dictionary to store unique metrics extracted from the state"
    )
    current_report: dict = Field(
        default="",
        description="The current report being generated or modified"
    )
    to_add: dict = Field(
        default_factory=dict,
        description="A dictionary to store metrics and details to be added to the report"
    )
    report: dict = Field(
        default_factory=dict,
        description="The final report after adding requested metrics and details"
    )

async def get_transaction_metrics(merchant_id: str) -> str:
        try:
            print(f"Getting transaction metrics for merchant {merchant_id}")
            async_session_gen = get_db_async()
            db = await async_session_gen.__anext__() # Get the actual session from the generator
            metrics = await get_txn_metrics(merchant_id, db)
            if isinstance(metrics, str):
                try:
                    metrics = json.loads(metrics)
                except:
                    return metrics
            return json.dumps(metrics, ensure_ascii=False, cls=CustomJSONEncoder)
        except Exception as e:
            error_msg = f"Error getting transaction metrics: {str(e)}"
            print(error_msg)
            return error_msg
        finally:
            if async_session_gen:
                try:
                    await async_session_gen.aclose()
                except Exception as e:
                    print(f"Error closing session generator in get_transaction_metrics: {str(e)}")

async def get_about_the_company(merchant_id: str) -> str:
        try:
            print(f"Getting company information for merchant {merchant_id}")
            async_session_gen = get_db_async()
            db = await async_session_gen.__anext__() # Get the actual session from the generator
            data = await get_about_company_internal(merchant_id=merchant_id, db=db)
            return json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
        except Exception as e:
            error_msg = f"Error getting company information: {str(e)}"
            print(error_msg)
            return error_msg
        finally:
            if async_session_gen:
                try:
                    await async_session_gen.aclose()
                except Exception as e:
                    print(f"Error closing session generator in get_about_the_company: {str(e)}")

async def get_financial_metrics(merchant_id: str):
        try:
            print(f"Getting financial metrics for merchant {merchant_id}")
            async_session_gen = get_db_async()
            db = await async_session_gen.__anext__() # Get the actual session from the generator
            metrics = await get_fin_metrics(merchant_id, db)
            if isinstance(metrics, str):
                try:
                    return json.loads(metrics)
                except:
                    return metrics
            return metrics
        except Exception as e:
            error_msg = f"Error getting financial metrics: {str(e)}"
            print(error_msg)
            return error_msg
        finally:
            if async_session_gen:
                try:
                    await async_session_gen.aclose()
                except Exception as e:
                    print(f"Error closing session generator in get_financial_metrics: {str(e)}")

async def get_about_industry_and_risk(merchant_id: str) -> str:
    try:
        print(f"Getting industry and risk information for merchant {merchant_id}")
        async_session_gen = get_db_async()
        db = await async_session_gen.__anext__() # Get the actual session from the generator
        data = await get_about_industry_internal(merchant_id=merchant_id, db=db)
        return json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
    except Exception as e:
        error_msg = f"Error getting industry and risk information: {str(e)}"
        print(error_msg)
        return error_msg
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()
            except Exception as e:
                print(f"Error closing session generator in get_about_industry_and_risk: {str(e)}")

async def get_company_metrics(merchant_id: str) -> str:
    try:
        print(f"Getting company metrics for merchant {merchant_id}")
        async_session_gen = get_db_async()
        db = await async_session_gen.__anext__() # Get the actual session from the generator
        data = await get_company_metrics_internal(merchant_id=merchant_id, db=db)
        return json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
    except Exception as e:
        error_msg = f"Error getting company metrics: {str(e)}"
        print(error_msg)
        return error_msg
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()
            except Exception as e:
                print(f"Error closing session generator in get_company_metrics: {str(e)}")

async def get_risk_metrics(merchant_id: str) -> str:
    try:
        print(f"Getting risk metrics for merchant {merchant_id}")
        async_session_gen = get_db_async()
        db = await async_session_gen.__anext__() # Get the actual session from the generator
        data = await get_risk_metrics_internal(merchant_id=merchant_id, db=db)
        return json.dumps(data, ensure_ascii=False, cls=CustomJSONEncoder)
    except Exception as e:
        error_msg = f"Error getting risk metrics: {str(e)}"
        print(error_msg)
        return error_msg
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()
            except Exception as e:
                print(f"Error closing session generator in get_risk_metrics: {str(e)}")
                
async def unique_metric_details_name_extractor(state: State) -> str:
    """
    Extracts the unique metric name from the state.
    """
    if "unique_metrics" in state and state["unique_metrics"]:
        # metrics are already extracted, we can just return
        return {"unique_metrics": state["unique_metrics"]}
    
    metrics = {}
    # Use LLM to identify requested metrics
    metrics_map = {
        "financial": get_financial_metrics,
        "company_metrics": get_company_metrics,
        "transaction": get_transaction_metrics,
        "company_details": get_about_the_company,
        "industry": get_about_industry_and_risk,
        "risk_metrics": get_risk_metrics
    }
    merchant_id = state["merchant_id"]
    for key, func in metrics_map.items():
        try:
            result = await func(merchant_id)
            if isinstance(result, str):
                try:
                    result = json.loads(result)
                    if isinstance(result, dict) and "data" in result:
                        result = result["data"]
                except json.JSONDecodeError:
                    print(f"Error decoding JSON for {key}: {result}")
                    continue
            
            if result is not None and isinstance(result, dict):
                metrics[key] = list(result.keys())
            elif result is not None and isinstance(result, list):
                for d in result:
                    if key not in metrics:
                        metrics[key] = [d['label']]
                    else:
                        metrics[key].append(d['label'])
        except Exception as e:
            print(f"Error getting {key} for merchant {merchant_id}: {str(e)}")
            continue
    return {"unique_metrics": metrics}

async def main_query_extractor(state: State):
    """
    Extracts and identifies which metrics of each kind are requested to be added to the report.
    """

    unique_metrics = state["unique_metrics"]

    system_prompt = f"""
    You are a highly skilled assistant specializing in analyzing user queries to extract specific information requests. You will be provided with:

    1. The full conversation history. (the latest message is the user's query)
    2. The current state of the report.
    3. All available unique metrics and details for the merchant.
    4. All available red flags types for the merchant.

    ### Your Objective:
    Identify **which metrics, details, notes, or red flags** the user is asking to be added to the report that is already not present in the report.

    ### Available Data:
    The following are the types of metrics and details available for the merchant, along with their specific options:
    {json.dumps(unique_metrics, indent=2, ensure_ascii=False)}

    Red flags types:
    - insolvency_external_audit :  (for audit related flags)
    - insolvency_external_executive : (for executive management related flags)
    - insolvency_external_disclosures : (for disclosure related flags)
    - insolvency_external_financial : (for financial statement related flags)
    - insolvency_external_legal : (for legal issues related flags)
    - insolvency_external_operational : (for operational issues related flags)
    - insolvency_external_annualReport : (for annual report related flags)
    - insolvency_external_industry : (for industry specific flags)
    - insolvency_external_brand : (for brand/reputation related flags)
    - insolvency_financial_financialStatements : (for financial statement analysis flags)
    - insolvency_financial_financialMetrics : (for financial metrics related flags)
    - transaction_monitoring : (for transaction related flags)
    - insolvency_overview_pdAnalysis : (for probability of default analysis flags)
    - insolvency_overview_company : (for company overview related flags)
    - insolvency_overview_industry : (for industry overview flags)
    - insolvency_overview_transactionMetrics : (for transaction metrics flags)

    Current report state:
    {json.dumps(state.get("current_report",{}), indent=2, ensure_ascii=False, cls=CustomJSONEncoder)}

    ### Instructions:

    - Carefully analyze the user's query to determine which specific **metrics or details** are being requested.
    - The user may refer to **multiple items** at once — identify and list all such items.
    - Check the current report state to check which items has already been added to the report. Only return items that are not already present in the report.
    - For each type (e.g., financial, transaction, company_details), return one of the following:
    - A list of requested metrics or details (if specifically mentioned).
    - `"all"` (if the user makes a general request like "add all financial metrics" or add financial metrics or add company details).
    - An empty list (if no relevant request was made or if requested items aren't available).

    ### Additional Behaviors:

    - If the user asks to **add a note or comment**, include it in the `"notes"` list in the output JSON.
    - If the user mentions any **red flags**, try to extract the type of red flags the user is referring to and include them in the red flags list in the output JSON. The red flags list will contain a dictionary as follows:
    ```json
    {{
        "type": "red_flag_type",
        "keywords": ["keyword1 or phrases", "keyword2 or phrases", ...] or ["all"] or []
    }}
    - If the user asks to **add all red flags**, return `"all"` for the red flags list.

    ### Very Important:
    - Vague requests like "add financial metrics" or "include company details" should result in returning `"all"` for that type.
    - Only include names of metrics or details that are explicitly available in the provided list.
    - If the user references unavailable items, treat that type as an empty list.
    - only make one dictionary for each type of red flag.

    ### Output Format:
    Return your results in the following JSON structure. Do not remove or rename any keys:
    ```json
    {{
        "financial": ["metric1", "metric2", ...] or ["all"] or [],
        "company_metrics": ["metric1", "metric2", ...] or ["all"] or [],
        "transaction": ["metric1", "metric2", ...] or ["all"] or [],
        "company_details": ["detail1", "detail2", ...] or ["all"] or [],
        "industry": ["detail1", "detail2", ...] or ["all"] or [],
        "risk_metrics": ["metric1", "metric2", ...] or ["all"] or [],
        "notes": ["note1", "note2", ...] or [],
        "red_flags": ["all"] or [] or [
            {{
                "type": "red_flag_type1",
                "keywords": ["keyword1 or phrases", "keyword2 or phrases", ...] or ["all"] or [],
            }},
            {{
                "type": "red_flag_type2",
                "keywords": ["keyword1 or phrases", "keyword2 or phrases", ...] or ["all"] or [],
            }},
            ... // more red flags as needed
        ]
    }}
    ```
    - add more to any list corresponding to any key as needed.
    """
    response = await llm.ainvoke(
        [
            SystemMessage(content=system_prompt),
            *state["messages"],
        ]
    )
    result = response.content
    if "```" in result:
        result = result.split("```json")[-1].split("```")[0].strip()
    try:
        result = json.loads(result)
        return {"to_add": result}
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        print(f"Response content: {result}")
        return {}

async def find_red_flags_to_add(merchant_id: str, red_flags: List[dict]):
    """
    This function finds each type of red flags to add to the report.
    """
    try:
        async_session_gen = get_db_async()
        db = await async_session_gen.__anext__() # Get the actual session from the generator
        red_flags_to_add = {}
        if not red_flags:
            return red_flags_to_add
        
        if red_flags == ["all"]:
            red_flags = [
                {
                    "type": flag_type,
                    "keywords": ["all"]
                }
                for flag_type in ALL_RED_FLAGS_TYPES
            ]

        # get red flags from the database
        all_red_flags = {}
        for flag in red_flags:
            flag_type = flag.get("type")
            if flag_type not in all_red_flags:
                all_red_flags[flag_type] = await get_merchant_red_flags_internal(
                    merchant_id=merchant_id, db=db, rule_type=flag_type
                )

        vector_store = InMemoryVectorStore(embedding=OpenAIEmbeddings(model="text-embedding-3-small"))
        for flag_type, flags in all_red_flags.items():
            if not flags:
                continue
            documents = [Document(page_content=flag["description"], metadata={"flag": flag, "flag_type": flag_type}) for flag in flags]
            vector_store.add_documents(documents)

        for red_flag in red_flags:
            flag_type = red_flag.get("type")
            keywords = red_flag.get("keywords", [])
            red_flags_to_add[flag_type] = []
            if keywords == ["all"]:
                # if keywords is "all", we will add all red flags of this type
                if flag_type in all_red_flags:
                    red_flags_to_add[flag_type] = all_red_flags[flag_type]
            else:
                # if keywords is a list, we will do vector search to find the red flags that matches the keywords the most
                if keywords:
                    for keyword in keywords:
                        q_docs = vector_store.similarity_search(
                            keyword.strip(),
                            k=2,
                            filter={"flag_type": flag_type},
                        )
                        if q_docs:
                            for doc in q_docs:
                                flag = doc.metadata.get("flag")
                                red_flags_to_add[flag_type].append(flag)
                            
        return red_flags_to_add
    except Exception as e:
        error_msg = f"Error finding red flags: {str(e)}"
        print(error_msg)
        return {}
    finally:
        if async_session_gen:
            try:
                await async_session_gen.aclose()
            except Exception as e:
                print(f"Error closing session generator in find_red_flags_to_add: {str(e)}")

async def add_data_to_report(state: State):
    """
    Adds the requested metrics and details to the current report.
    """
    to_add = state.get("to_add", {})
    current_report = state.get("current_report", {})
    unique_metrics = state.get("unique_metrics", {})

    metrics = state.get("report", {})
    for key, value in to_add.items():
        if key == "red_flags":
            # value -> list of dictionaries with type and keywords
            red_flags_to_add = await find_red_flags_to_add(state["merchant_id"], value)
            if "red_flags" not in metrics:
                metrics["red_flags"] = red_flags_to_add
            else:
                # Merge red_flags_to_add into existing metrics["red_flags"]
                for flag_type, flags in red_flags_to_add.items():
                    if flag_type not in metrics["red_flags"]:
                        metrics["red_flags"][flag_type] = flags
                    else:
                        # Check for duplicates before adding
                        existing_ids = {flag.get("id") for flag in metrics["red_flags"][flag_type] if flag.get("id")}
                        for flag in flags:
                            if flag.get("id") not in existing_ids:
                                metrics["red_flags"][flag_type].append(flag)
        elif key == "notes":
            if "notes" not in metrics:
                metrics["notes"] = value if isinstance(value, list) else [value]
            else:
                # Add new notes to existing ones
                new_notes = value if isinstance(value, list) else [value]
                metrics["notes"].extend(new_notes)
        elif key in unique_metrics:
            metrics_map = {
                "financial": get_financial_metrics,
                "company_metrics": get_company_metrics,
                "transaction": get_transaction_metrics,
                "company_details": get_about_the_company,
                "industry": get_about_industry_and_risk,
                "risk_metrics": get_risk_metrics
            }
            for name, func in metrics_map.items():
                if key == name:
                    deta = await func(state["merchant_id"])
                    if isinstance(deta, str):
                        try:
                            deta = json.loads(deta)
                        except json.JSONDecodeError:
                            print(f"Error decoding JSON for {name}: {deta}")
                            continue
                    if isinstance(deta, dict) and "data" in deta:
                        deta = deta["data"]
                    
                    if name not in metrics:
                        # Initialize the metrics for this category
                        if isinstance(deta, list):
                            if value == ["all"]:
                                metrics[name] = deta
                            else:
                                metrics[name] = [d for d in deta if d['label'] in value]
                        else:
                            if value == ["all"]:
                                metrics[name] = deta
                            else:
                                metrics[name] = {k: v for k, v in deta.items() if k in value}
                    else:
                        # Merge new metrics with existing ones
                        if isinstance(deta, list):
                            if value == ["all"]:
                                # Get all items not already in metrics[name]
                                existing_labels = {d.get('label') for d in metrics[name] if isinstance(d, dict) and 'label' in d}
                                for d in deta:
                                    if d.get('label') not in existing_labels:
                                        metrics[name].append(d)
                            else:
                                # Get specific items not already in metrics[name]
                                existing_labels = {d.get('label') for d in metrics[name] if isinstance(d, dict) and 'label' in d}
                                for d in deta:
                                    if d.get('label') in value and d.get('label') not in existing_labels:
                                        metrics[name].append(d)
                        elif isinstance(deta, dict) and isinstance(metrics[name], dict):
                            if value == ["all"]:
                                # Merge all new keys that don't exist
                                for k, v in deta.items():
                                    if k not in metrics[name]:
                                        metrics[name][k] = v
                            else:
                                # Merge specific keys that don't exist
                                for k in value:
                                    if k in deta and k not in metrics[name]:
                                        metrics[name][k] = deta[k]
                    break
    
    for key, value in metrics.items():
        if key == "red_flags":
            existing_red_flag = current_report.get("red_flags", {})
            for flag_type, flags in value.items():
                if flag_type not in existing_red_flag:
                    existing_red_flag[flag_type] = [flag["description"] for flag in flags]
                else:
                    # Check for duplicates before adding to current_report
                    existing_descriptions = set(existing_red_flag[flag_type])
                    for flag in flags:
                        if flag["description"] not in existing_descriptions:
                            existing_red_flag[flag_type].append(flag["description"])
                            existing_descriptions.add(flag["description"])
            current_report["red_flags"] = existing_red_flag
        else:
            if key not in current_report:
                current_report[key] = []
            if isinstance(value, list):
                # Check for duplicates before adding to current_report
                existing_items = set(current_report[key])
                for item in value:
                    if isinstance(item, dict):
                        item_label = item.get("label")
                        if item_label and item_label not in existing_items:
                            current_report[key].append(item_label)
                            existing_items.add(item_label)
                    else:
                        if item not in existing_items:
                            current_report[key].append(item)
                            existing_items.add(item)
            elif isinstance(value, dict):
                # Check for duplicates before adding keys to current_report
                existing_items = set(current_report[key])
                for k in value.keys():
                    if k not in existing_items:
                        current_report[key].append(k)
                        existing_items.add(k)
            else:
                print(f"Warning: Unsupported value type for {key}: {type(value)}")

    return {"current_report": current_report, "report": metrics, "messages": [AIMessage(content="Data added to the report successfully.")]}


# Define the state graph
graph = StateGraph(State)

graph.add_node(
    "unique_metric_details_name_extractor",
    unique_metric_details_name_extractor,
)
graph.add_node(
    "main_query_extractor",
    main_query_extractor,
)
graph.add_node(
    "add_data_to_report",
    add_data_to_report,
)

graph.set_entry_point("unique_metric_details_name_extractor")
graph.add_edge("unique_metric_details_name_extractor", "main_query_extractor")
graph.add_edge("main_query_extractor", "add_data_to_report")
graph.add_edge("add_data_to_report", END)
