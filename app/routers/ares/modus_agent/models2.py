from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey, JSON, Date, Numeric, Text, UUID, DECIMAL, ARRAY, Enum, BigInteger, TIMESTAMP
from sqlalchemy.ext.declarative import declarative_base

import uuid
from datetime import datetime

Base = declarative_base()
class probe_industry_segments(Base):
    __tablename__ = 'probe_industry_segments'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment="Unique identifier for the industry segment entry.")
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False, comment="Foreign key to the probe_merchant table, identifying the merchant.")
    industry = Column(Text, comment="The broad industry classification of the merchant.")
    segments = Column(JSONB, comment="A JSONB field storing a detailed breakdown of industry segments relevant to the merchant.")
    created_at = Column(TIMESTAMP, default=datetime.now, comment="Timestamp when the record was created.")
    updated_at = Column(TIMESTAMP, default=datetime.now, onupdate=datetime.now, comment="Timestamp when the record was last updated.")

class merchant_metrics(Base):
    __tablename__ = 'merchant_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="Unique identifier for the merchant metric entry.")
    merchant_id = Column(UUID(as_uuid=True), nullable=False, comment="Unique identifier of the merchant associated with the metric.")
    metric_type = Column(String(255), comment="The type or name of the metric (e.g., 'Revenue', 'CustomerAcquisitionCost').")
    metric_value = Column(JSONB, comment="The value of the metric, stored as a JSONB object to accommodate various data structures.")
    year = Column(Integer, comment="The calendar year to which the metric applies.")
    financials_date = Column(Date, comment="The specific date of the financial data, if applicable (e.g., end of quarter).")
    created_at = Column(DateTime, default=datetime.now, comment="Timestamp when the metric record was created.")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="Timestamp when the metric record was last updated.")

class ExternalInsights(Base):
    __tablename__ = 'external_insights'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="Unique identifier for the external insight entry.")
    merchant_id = Column(UUID(as_uuid=True), index=True, nullable=False, comment="Unique identifier of the merchant to whom the insight pertains.")
    insight_type = Column(String(50), index=True, comment="The category or type of the external insight (e.g., 'MarketTrend', 'NewsArticle').")
    insight_value = Column(JSONB, nullable=True, comment="The actual insight content, stored as a JSONB object to support varied data formats.")
    created_at = Column(DateTime, default=datetime.now, comment="Timestamp when the external insight record was created.")