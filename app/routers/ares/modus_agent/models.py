from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey, JSON, Date, Numeric, Text, UUID, DECIMAL, ARRAY, Enum, BigInteger, TIMESTAMP
from sqlalchemy.ext.declarative import declarative_base
import uuid
from datetime import datetime

Base = declarative_base()
class probe_merchant(Base):
    __tablename__ = 'probe_merchant'
    __table_args__ = {'schema': 'public'}

    merchant_id = Column(UUID(as_uuid=True), primary_key=True, comment='Unique identifier for the merchant.')
    cin = Column(Text, comment='Corporate Identification Number of the merchant.')
    legal_name = Column(Text, comment='The full legal name of the merchant company.')
    efiling_status = Column(Text, comment='Current e-filing status of the merchant with regulatory bodies.')
    incorporation_date = Column(Date, comment='Date of incorporation of the merchant company.')
    paid_up_capital = Column(Numeric, comment='The total amount of money paid by shareholders for shares they have subscribed to.')
    sum_of_charges = Column(Numeric, comment='The total sum of all registered charges against the merchant.')
    authorized_capital = Column(Numeric, comment='The maximum amount of share capital that a company is authorized by its memorandum of association to issue to shareholders.')
    active_compliance = Column(Text, comment='Status of active compliance with regulatory requirements.')
    registered_address = Column(JSONB, comment='The official registered address of the merchant company, stored as JSON.')
    business_address = Column(JSONB, comment='The primary business address of the merchant, stored as JSON.')
    pan = Column(Text, comment='Permanent Account Number of the merchant company.')
    website = Column(Text, comment='Official website URL of the merchant.')
    classification = Column(Text, comment='Classification of the merchant based on industry or business type.')
    status = Column(Text, comment='Current operational status of the merchant (e.g., Active, Dormant).')
    last_agm_date = Column(Date, comment='Date of the last Annual General Meeting.')
    last_filing_date = Column(Date, comment='Date of the last regulatory filing.')
    email = Column(Text, comment='Primary email address of the merchant.')
    description = Column(Text, comment='A brief description of the merchant\'s business activities.')
    contact_email = Column(JSONB, comment='Additional contact email addresses, stored as JSON.')
    contact_phone = Column(JSONB, comment='Contact phone numbers, stored as JSON.')
    # LEI fields merged from probe_lei
    lei_number = Column(Text, comment='Legal Entity Identifier number.')
    lei_status = Column(Text, comment='Status of the LEI (e.g., Issued, Lapsed).')
    lei_registration_date = Column(Date, comment='Date when the LEI was registered.')
    lei_last_updated_date = Column(Date, comment='Date when the LEI information was last updated.')
    lei_next_renewal_date = Column(Date, comment='Date when the LEI is due for next renewal.')
    name_history = Column(JSONB, comment='Historical records of changes to the merchant\'s name, stored as JSON.')
    last_updated_date = Column(Date, comment='The last date any information in this record was updated.')

class probe_authorized_signatories(Base):
    __tablename__ = 'probe_authorized_signatories'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='Primary key for the authorized signatory record.')
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), comment='Foreign key linking to the merchant.')
    pan = Column(Text, comment='Permanent Account Number of the authorized signatory.')
    din = Column(Text, comment='Director Identification Number of the authorized signatory.')
    name = Column(Text, comment='Full name of the authorized signatory.')
    designation = Column(Text, comment='Designation of the authorized signatory (e.g., Director, CEO).')
    din_status = Column(Text, comment='Status of the DIN (e.g., Active, Deactivated).')
    gender = Column(Text, comment='Gender of the authorized signatory.')
    date_of_birth = Column(Date, comment='Date of birth of the authorized signatory.')
    age = Column(Integer, comment='Age of the authorized signatory.')
    date_of_appointment = Column(Date, comment='Date when the signatory was first appointed.')
    date_of_appointment_for_current_designation = Column(Date, comment='Date when the signatory was appointed to their current designation.')
    date_of_cessation = Column(Date, comment='Date when the signatory ceased their role, if applicable.')
    nationality = Column(Text, comment='Nationality of the authorized signatory.')
    address = Column(JSONB, comment='Address of the authorized signatory, stored as JSON.')
    association_history = Column(JSONB, comment='History of associations with other entities or roles, stored as JSON.')

class probe_financials(Base):
    __tablename__ = 'probe_financials'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='Primary key for the financial record.')
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), comment='Foreign key linking to the merchant.')
    year = Column(Date, comment='The financial year to which the financial data pertains (typically year-end date).')
    nature = Column(Text, comment='Nature of the financial statement (e.g., Standalone, consolidated).')
    stated_on = Column(Date, comment='Date on which the financial statement was stated or approved.')
    filing_type = Column(Text, comment='Type of filing (e.g., Audited, Provisional).')
    filing_standard = Column(Text, comment='Accounting standard followed for filing (e.g., Ind AS, IFRS).')

    # Balance Sheet: Assets
    tangible_assets = Column(Numeric, comment='Value of tangible assets (e.g., property, plant, equipment).')
    producing_properties = Column(Numeric, comment='Value of properties currently in production.')
    intangible_assets = Column(Numeric, comment='Value of intangible assets (e.g., patents, trademarks).')
    preproducing_properties = Column(Numeric, comment='Value of properties under development or not yet producing.')
    tangible_assets_capital_work_in_progress = Column(Numeric, comment='Value of tangible assets under construction or development.')
    intangible_assets_under_development = Column(Numeric, comment='Value of intangible assets under development.')
    noncurrent_investments = Column(Numeric, comment='Long-term investments held by the company.')
    deferred_tax_assets_net = Column(Numeric, comment='Net deferred tax assets.')
    foreign_curr_monetary_item_trans_diff_asset_account = Column(Numeric, comment='Exchange differences on foreign currency monetary items recognized in asset accounts.')
    long_term_loans_and_advances = Column(Numeric, comment='Loans and advances expected to be recovered after more than one year.')
    other_noncurrent_assets = Column(Numeric, comment='Other assets not expected to be converted into cash within one year.')
    current_investments = Column(Numeric, comment='Investments expected to be converted into cash within one year.')
    inventories = Column(Numeric, comment='Value of raw materials, work-in-progress, and finished goods.')
    trade_receivables = Column(Numeric, comment='Amounts due from customers for goods or services sold on credit.')
    cash_and_bank_balances = Column(Numeric, comment='Total cash and balances held with banks.')
    short_term_loans_and_advances = Column(Numeric, comment='Loans and advances expected to be recovered within one year.')
    other_current_assets = Column(Numeric, comment='Other assets expected to be converted into cash within one year.')
    given_assets_total = Column(Numeric, comment='Total given assets as per the balance sheet.')

    # Balance Sheet: Liabilities
    share_capital = Column(Numeric, comment='The total value of shares issued by the company.')
    reserves_and_surplus = Column(Numeric, comment='Accumulated profits and reserves of the company.')
    money_received_against_share_warrants = Column(Numeric, comment='Funds received for share warrants yet to be converted into shares.')
    share_application_money_pending_allotment = Column(Numeric, comment='Money received from share applications where allotment is pending.')
    deferred_government_grants = Column(Numeric, comment='Government grants recognized over time.')
    minority_interest = Column(Numeric, comment='The portion of a subsidiary’s equity not owned by the parent company.')
    long_term_borrowings = Column(Numeric, comment='Borrowings with a repayment period of more than one year.')
    deferred_tax_liabilities_net = Column(Numeric, comment='Net deferred tax liabilities.')
    foreign_curr_monetary_item_trans_diff_liability_account = Column(Numeric, comment='Exchange differences on foreign currency monetary items recognized in liability accounts.')
    other_long_term_liabilities = Column(Numeric, comment='Other liabilities due after more than one year.')
    long_term_provisions = Column(Numeric, comment='Provisions for obligations due after more than one year.')
    short_term_borrowings = Column(Numeric, comment='Borrowings with a repayment period of one year or less.')
    trade_payables = Column(Numeric, comment='Amounts owed to suppliers for goods or services purchased on credit.')
    other_current_liabilities = Column(Numeric, comment='Other liabilities due within one year.')
    short_term_provisions = Column(Numeric, comment='Provisions for obligations due within one year.')
    given_liabilities_total = Column(Numeric, comment='Total given liabilities as per the balance sheet.')

    # Subtotals
    total_equity = Column(Numeric, comment='Sum of share capital, reserves and surplus, and minority interest.')
    total_current_liabilities = Column(Numeric, comment='Sum of all short-term borrowings, trade payables, other current liabilities, and short-term provisions.')
    total_non_current_liabilities = Column(Numeric, comment='Sum of all long-term borrowings, deferred tax liabilities, other long-term liabilities, and long-term provisions.')
    net_fixed_assets = Column(Numeric, comment='Gross fixed assets less accumulated depreciation.')
    total_current_assets = Column(Numeric, comment='Sum of all current investments, inventories, trade receivables, cash and bank balances, short-term loans and advances, and other current assets.')
    capital_wip = Column(Numeric, comment='Capital work in progress.')
    total_debt = Column(Numeric, comment='Sum of all short-term and long-term borrowings.')

    # Notes
    gross_fixed_assets = Column(Numeric, comment='Original cost of fixed assets before depreciation.')
    trade_receivable_exceeding_six_months = Column(Numeric, comment='Trade receivables that are overdue for more than six months.')

    # Profit & Loss - Line Items
    net_revenue = Column(Numeric, comment='Total revenue from operations after deducting returns, allowances, and discounts.')
    total_cost_of_materials_consumed = Column(Numeric, comment='Total cost of raw materials and components used in production.')
    total_purchases_of_stock_in_trade = Column(Numeric, comment='Total cost of goods purchased for resale.')
    total_changes_in_inventories_or_finished_goods = Column(Numeric, comment='Change in the value of opening and closing inventories of finished goods, work-in-progress, and stock-in-trade.')
    total_employee_benefit_expense = Column(Numeric, comment='Total expenses related to employee benefits (e.g., salaries, wages, provident fund contributions).')
    total_other_expenses = Column(Numeric, comment='All other expenses not categorized elsewhere (e.g., rent, utilities, marketing).')
    operating_profit = Column(Numeric, comment='Profit generated from core business operations before interest and tax.')
    other_income = Column(Numeric, comment='Income generated from non-operating activities (e.g., interest income, dividend income).')
    depreciation = Column(Numeric, comment='Expense recognized for the wear and tear of tangible assets.')
    profit_before_interest_and_tax = Column(Numeric, comment='Profit before deducting interest expenses and income tax.')
    interest = Column(Numeric, comment='Expense incurred on borrowed funds.')
    profit_before_tax_and_exceptional_items_before_tax = Column(Numeric, comment='Profit before deducting income tax and exceptional items.')
    exceptional_items_before_tax = Column(Numeric, comment='Significant and unusual income or expenses that are not part of ordinary activities, before tax.')
    profit_before_tax = Column(Numeric, comment='Profit remaining after deducting all expenses except income tax.')
    income_tax = Column(Numeric, comment='Total income tax expense for the period.')
    profit_for_period_from_continuing_operations = Column(Numeric, comment='Profit earned from ongoing business activities after tax.')
    profit_from_discontinuing_operation_after_tax = Column(Numeric, comment='Profit or loss from operations that have been discontinued, after tax.')
    minority_interest_and_profit_from_associates_and_joint_ventures = Column(Numeric, comment='Share of profit attributable to minority interests and profit from investments in associates and joint ventures.')
    profit_after_tax = Column(Numeric, comment='Net profit available to shareholders after all expenses and taxes.')

    # P&L Subtotals
    total_operating_cost = Column(Numeric, comment='Sum of all costs directly related to the production and sale of goods or services.')

    # Revenue Breakup
    revenue_from_operations = Column(Numeric, comment='Revenue generated from the core business activities.')
    revenue_from_interest = Column(Numeric, comment='Revenue earned from interest on deposits or loans.')
    revenue_from_other_financial_services = Column(Numeric, comment='Revenue from financial services other than interest.')
    revenue_from_sale_of_products = Column(Numeric, comment='Revenue generated from the sale of manufactured goods.')
    revenue_from_sale_of_services = Column(Numeric, comment='Revenue generated from providing services.')
    other_operating_revenues = Column(Numeric, comment='Other revenues generated from operating activities but not explicitly categorized.')
    excise_duty = Column(Numeric, comment='Excise duty levied on goods manufactured.')
    service_tax_collected = Column(Numeric, comment='Service tax collected from customers.')
    other_duties_taxes_collected = Column(Numeric, comment='Other duties and taxes collected.')
    sale_of_goods_manufactured_domestic = Column(Numeric, comment='Revenue from sale of manufactured goods within the domestic market.')
    sale_of_goods_traded_domestic = Column(Numeric, comment='Revenue from sale of traded goods within the domestic market.')
    sale_or_supply_of_services_domestic = Column(Numeric, comment='Revenue from sale or supply of services within the domestic market.')
    sale_or_supply_of_services_export = Column(Numeric, comment='Revenue from sale or supply of services for export.')
    sale_of_goods_manufactured_export = Column(Numeric, comment='Revenue from sale of manufactured goods for export.')
    sale_of_goods_traded_export = Column(Numeric, comment='Revenue from sale of traded goods for export.')

    # Depreciation Breakup
    depreciation_amortisation = Column(Numeric, comment='Combined expense for depreciation of tangible assets and amortization of intangible assets.')
    depletion = Column(Numeric, comment='Expense for the consumption of natural resources.')
    depreciation_and_amortization = Column(Numeric, comment='Total depreciation and amortization expense.')

    # Cash Flow
    profit_before_tax_cf = Column(Numeric, comment='Profit before tax as reported in the cash flow statement.')
    adjustment_for_finance_cost_and_depreciation = Column(Numeric, comment='Adjustments made for non-cash items like finance cost and depreciation when calculating cash flow from operations.')
    adjustment_for_current_and_non_current_assets = Column(Numeric, comment='Adjustments for changes in current and non-current assets impacting cash flow.')
    adjustment_for_current_and_non_current_liabilities = Column(Numeric, comment='Adjustments for changes in current and non-current liabilities impacting cash flow.')
    other_adjustments_in_operating_activities = Column(Numeric, comment='Other non-cash adjustments in operating activities.')
    cash_flows_from_used_in_operating_activities = Column(Numeric, comment='Net cash generated from or used in operating activities.')
    cash_outflow_from_purchase_of_assets = Column(Numeric, comment='Cash spent on purchasing fixed assets or investments.')
    cash_inflow_from_sale_of_assets = Column(Numeric, comment='Cash received from selling fixed assets or investments.')
    income_from_assets = Column(Numeric, comment='Income generated from assets (e.g., interest, dividends).')
    other_adjustments_in_investing_activities = Column(Numeric, comment='Other adjustments related to investing activities.')
    cash_flows_from_used_in_investing_activities = Column(Numeric, comment='Net cash generated from or used in investing activities.')
    cash_outflow_from_repayment_of_capital_and_borrowings = Column(Numeric, comment='Cash paid for repaying capital and borrowings.')
    cash_inflow_from_raisng_capital_and_borrowings = Column(Numeric, comment='Cash received from issuing new shares or taking on new borrowings.')
    interest_and_dividends_paid = Column(Numeric, comment='Cash paid for interest on borrowings and dividends to shareholders.')
    other_adjustments_in_financing_activities = Column(Numeric, comment='Other adjustments related to financing activities.')
    cash_flows_from_used_in_financing_activities = Column(Numeric, comment='Net cash generated from or used in financing activities.')
    incr_decr_in_cash_cash_equv_before_effect_of_excg_rate_changes = Column(Numeric, comment='Increase or decrease in cash and cash equivalents before the effect of exchange rate changes.')
    adjustments_to_cash_and_cash_equivalents = Column(Numeric, comment='Adjustments to reconcile net increase/decrease to actual cash and cash equivalents.')
    incr_decr_in_cash_cash_equv = Column(Numeric, comment='Net increase or decrease in cash and cash equivalents.')
    cash_flow_statement_at_end_of_period = Column(Numeric, comment='Cash and cash equivalents at the end of the period as per the cash flow statement.')

    # PnL Key Schedule
    managerial_remuneration = Column(Numeric, comment='Remuneration paid to managerial personnel.')
    payment_to_auditors = Column(Numeric, comment='Fees paid to external auditors.')
    insurance_expenses = Column(Numeric, comment='Expenses incurred on insurance premiums.')
    power_and_fuel = Column(Numeric, comment='Expenses for power and fuel consumption.')

    # Auditor
    auditor_name = Column(Text, comment='Name of the auditor.')
    auditor_firm_name = Column(Text, comment='Name of the auditing firm.')
    pan = Column(Text, comment='Permanent Account Number of the auditor or auditing firm.')
    membership_number = Column(Text, comment='Membership number of the auditor.')
    firm_registration_number = Column(Text, comment='Registration number of the auditing firm.')
    auditor_address = Column(Text, comment='Address of the auditor or auditing firm.')

    # Auditor Comments
    report_has_adverse_remarks = Column(Text, comment='Indicates if the auditor report contains adverse remarks (Yes/No).')
    auditor_comments = Column(JSONB, comment='Detailed comments and observations from the auditor, stored as JSON.')
    auditor_additional = Column(JSONB, comment='Additional information provided by the auditor, stored as JSON.')

class probe_epfo_establishments(Base):
    __tablename__ = 'probe_epfo_establishments'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='Primary key for the EPFO establishment record.')
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False, comment='Foreign key linking to the merchant.')
    establishment_id = Column(Text, comment='Unique identifier for the EPFO establishment.')
    address = Column(Text, comment='Address of the EPFO establishment.')
    city = Column(Text, comment='City where the EPFO establishment is located.')
    latest_date_of_credit = Column(Text, comment='Latest date on which EPF contributions were credited.')
    date_of_setup = Column(Text, comment='Date when the establishment was set up with EPFO.')
    establishment_name = Column(Text, comment='Name of the establishment as registered with EPFO.')
    exemption_status_edli = Column(Text, comment='Exemption status for Employee Deposit Linked Insurance (EDLI).')
    exemption_status_pension = Column(Text, comment='Exemption status for Employees Pension Scheme (EPS).')
    exemption_status_pf = Column(Text, comment='Exemption status for Employees Provident Fund (PF).')
    no_of_employees = Column(Integer, comment='Number of employees registered under this establishment.')
    principal_business_activities = Column(Text, comment='Main business activities of the establishment.')
    amount = Column(Numeric, comment='The amount of latest contribution or related financial figure.')
    latest_wage_month = Column(Text, comment='The latest month for which wages were considered for EPF contribution.')
    working_status = Column(Text, comment='Current working status of the establishment (e.g., Active).')
    filing_details = Column(JSONB, comment='Details of EPFO filings, stored as JSON.')
    created_at = Column(TIMESTAMP, default=datetime.now(), comment='Timestamp when the record was created.')
    updated_at = Column(TIMESTAMP, default=datetime.now(), comment='Timestamp when the record was last updated.')

class probe_open_charges(Base):
    __tablename__ = 'probe_open_charges'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='Primary key for the open charges record.')
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False, comment='Foreign key linking to the merchant.')
    charge_id = Column(Integer, comment='Unique identifier for the specific charge.')
    date = Column(Date, comment='Date when the charge was created or last modified.')
    holder_name = Column(Text, comment='Name of the charge holder (e.g., bank, financial institution).')
    amount = Column(Numeric, comment='The monetary amount of the charge.')
    type = Column(Text, comment='Type of charge (e.g., Mortgage, Hypothecation).')
    created_at = Column(TIMESTAMP, default=datetime.now(), comment='Timestamp when the record was created.')
    updated_at = Column(TIMESTAMP, default=datetime.now(), comment='Timestamp when the record was last updated.')