from jinja2 import Template
import json
from pathlib import Path
# from weasyprint import H<PERSON><PERSON>

def generate_html_report(data: dict, output_html_path: str) -> str:
    """Generates an HTML report from the given JSON data, optimized for PDF rendering."""

    company_details = data.get("company_details", {}) or {}
    industry = data.get("industry", {}) or {}
    financial = data.get("financial", []) or []
    transaction = data.get("transaction", []) or []
    risk_metrics = data.get("risk_metrics", {}) or {}
    red_flags = data.get("red_flags", {}) or {}
    notes = data.get("notes", []) or []

    template_str = """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Modus Fraud Detection Report</title>
        <style>
            @page {
                size: A4;
                margin: 2cm;
            }

            body {
                font-family: 'Arial', sans-serif;
                font-size: 12px;
                margin: 0;
                color: #000;
            }

            h1 {
                color: #2739C1;
                font-size: 20px;
                margin-bottom: 0;
            }

            h2 {
                font-size: 16px;
                margin-top: 30px;
                border-bottom: 1px solid #2739C1;
                padding-bottom: 4px;
            }

            h3 {
                font-size: 14px;
                margin-top: 20px;
                margin-bottom: 5px;
                color: #444;
            }

            p {
                margin: 5px 0 10px;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
            }

            th, td {
                border: 1px solid #ccc;
                padding: 5px;
                text-align: left;
                font-size: 11px;
            }

            th {
                background-color: #f5f5f5;
                font-weight: bold;
            }

            ul {
                margin-top: 0;
                padding-left: 20px;
            }

            .note {
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <h1>Modus Fraud Detection Report</h1>

        {% if company_details.company_name %}
        <p><strong>Company:</strong> {{ company_details.company_name }}</p>
        {% endif %}

        {% if company_details.about_the_company %}
        <h2>Company Overview</h2>
        <p>{{ company_details.about_the_company }}</p>
        {% endif %}

        {% if industry %}
        <h2>Industry Overview</h2>
        {% if industry.is_industry_risky %}
        <p><strong>Industry Risk:</strong> {{ industry.is_industry_risky }}</p>
        {% endif %}
        {% if industry.about_the_industry %}
        <p>{{ industry.about_the_industry }}</p>
        {% endif %}
        {% if industry.justification %}
        <p><em>{{ industry.justification }}</em></p>
        {% endif %}
        {% endif %}

        {% if financial %}
        <h2>Financial Metrics</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            {% for item in financial %}
            <tr><td>{{ item.label }}</td><td>{{ item.value }}</td></tr>
            {% endfor %}
        </table>
        {% endif %}

        {% if transaction %}
        <h2>Transaction Metrics</h2>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
            {% for item in transaction %}
            <tr><td>{{ item.label }}</td><td>{{ item.value }}</td></tr>
            {% endfor %}
        </table>
        {% endif %}

        {% if risk_metrics %}
        <h2>Risk Metrics</h2>
        <table>
            {% for key, value in risk_metrics.items() %}
            <tr><td>{{ key.replace("_", " ").title() }}</td><td>{{ value }}</td></tr>
            {% endfor %}
        </table>
        {% endif %}

        {% if red_flags %}
        <h2>Red Flags</h2>
        {% for section, flags in red_flags.items() if flags %}
            <h3>{{ section.replace("_", " ").title() }}</h3>
            <table>
                <tr><th>Description</th><th>Severity</th><th>Created At</th></tr>
                {% for flag in flags %}
                <tr>
                    <td>{{ flag.description }}</td>
                    <td>{{ flag.severity }}</td>
                    <td>{{ flag.created_at }}</td>
                </tr>
                {% endfor %}
            </table>
        {% endfor %}
        {% endif %}

        <div class="note">
            <h2>Notes</h2>
            {% if notes %}
            <ul>
                {% for note in notes %}
                <li>{{ note }}</li>
                {% endfor %}
            </ul>
            {% else %}
            <p>No additional notes.</p>
            {% endif %}
        </div>
    </body>
    </html>
    """

    template = Template(template_str)
    html_content = template.render(
        company_details=company_details,
        industry=industry,
        financial=financial,
        transaction=transaction,
        risk_metrics=risk_metrics,
        red_flags=red_flags,
        notes=notes
    )

    Path(output_html_path).write_text(html_content, encoding="utf-8")
    return output_html_path


# def convert_html_to_pdf(input_html_path: str, output_pdf_path: str) -> None:
#     """Converts an HTML file into a PDF."""
#     HTML(input_html_path).write_pdf(output_pdf_path)
