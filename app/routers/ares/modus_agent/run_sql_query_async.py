import asyncpg

async def run_sql_query_async(db_uri: str, query: str, params: tuple = None):
    """
    Connects to a PostgreSQL database asynchronously using a DB URI,
    executes a given SQL query, and returns the results.

    Args:
        db_uri (str): The database connection URI (e.g., "postgresql://user:password@host:port/database").
        query (str): The SQL query string to execute.
        params (tuple, optional): A tuple of parameters to pass to the query. Defaults to None.

    Returns:
        list: A list of asyncpg.Record objects representing the query results.
              Returns an empty list if no results are returned (e.g., for INSERT/UPDATE/DELETE).
    """
    conn = None  # Initialize conn to None
    try:
        # Establish an asynchronous connection to the database
        conn = await asyncpg.connect(db_uri)
        print(f"Connected to PostgreSQL database using URI: {db_uri}")

        if params:
            # Execute the query with parameters
            results = await conn.fetch(query, *params)
        else:
            # Execute the query without parameters
            results = await conn.fetch(query)

        # convert results to a json-serializable format
        results = [dict(record) for record in results]
        print(f"Query executed successfully: '{query}'")
        return {
            "status": "success",
            "message": "Query executed successfully.",
            "results": results
        }

    except asyncpg.exceptions.PostgresError as e:
        print(f"PostgreSQL error: {e}")
        return {
            "status": "error",
            "message": str(e),
            "results": []
        }
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return {
            "status": "error",
            "message": str(e),
            "results": []
        }
    finally:
        if conn:
            # Close the connection
            await conn.close()
            print("Database connection closed.")