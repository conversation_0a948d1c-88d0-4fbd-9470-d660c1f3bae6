import json
from langchain.chat_models import init_chat_model
from typing import List
from langchain_core.messages import SystemMessage
from langchain_core.tools import tool
from langgraph.prebuilt import ToolNode, tools_condition
from pydantic import Field
from langgraph.graph import StateGraph, START
from langgraph.graph import MessagesState
from .test_async import search_keywords
from app.utils.async_sql_executor import run_sql_query_async_legacy

llm = init_chat_model("openai:gpt-4.1")

class StateDataAnalyst(MessagesState):
    """
    Represents the state of the data analyst agent.
    Inherits from MessagesState to manage conversation history.
    """
    iterations: int = Field(
        default=0,
        description="Number of iterations for refinement."
    )



@tool("schema_and_values_extractor", parse_docstring=True, description="Searches for semantically similar tables and columns in the database using keywords, and then extracts unique values for the identified columns.")
async def extract_schema_and_unique_values(
    keywords: List[str]
) -> str:
    """
    Searches for semantically similar tables and columns using keywords,
    and then extracts unique values for the identified columns (up to 10 unique values).

    Args:
        keywords (List[str]): The keywords extracted from the user query to find relevant tables and columns.
    """
    DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"
    params_for_unique_values = (10,) # Max unique values to consider for "categorical"

    # 1. Search for semantically similar tables and columns
    discovered_columns_info = {} # To store {table_name: [column_metadata_dicts]}

    for keyword in keywords:
        results = await search_keywords(keyword) # Assumes search_keywords is defined
        if results and results.get('documents') and results['documents'][0]:
            for doc, meta in zip(results['documents'][0], results['metadatas'][0]):
                table_name = meta.get('table_name')
                if not table_name:
                    continue

                column_info = {
                    'column_name': meta.get('column_name', 'N/A'),
                    'data_type': meta.get('data_type', 'N/A'),
                    'is_nullable': meta.get('is_nullable', "True"),
                    'comment': meta.get('comment', 'N/A')
                }

                if table_name not in discovered_columns_info:
                    discovered_columns_info[table_name] = []
                # Avoid duplicate column entries if multiple keywords point to the same column
                if not any(c['column_name'] == column_info['column_name'] for c in discovered_columns_info[table_name]):
                    discovered_columns_info[table_name].append(column_info)

    if not discovered_columns_info:
        return "No relevant tables or columns found for the given keywords."

    # 2. Extract unique values for the discovered columns
    final_results_with_values = {}
    for table_name, columns_metadata in discovered_columns_info.items():
        final_results_with_values[table_name] = []
        for col_meta in columns_metadata:
            column_name = col_meta['column_name']
            sql_query = f"SELECT DISTINCT \"{column_name}\" FROM \"{table_name.split('.')[-1]}\" LIMIT {params_for_unique_values[0] + 1};" # Fetch one more to check if > limit

            try:
                # Ensure run_sql_query_async_legacy is defined and accessible
                result_data = await run_sql_query_async_legacy(DATABASE_URL, sql_query, ()) # Params might not be needed for distinct select like this for some drivers
                unique_val_results = result_data.get("results", []) if result_data.get("status") == "success" else []

                current_col_info = col_meta.copy() # Start with existing metadata

                if unique_val_results:
                    if len(unique_val_results) <= 10: # Threshold for categorical
                        current_col_info['column_type'] = 'categorical'
                        # Ensure the column name exists in the results
                        if unique_val_results and column_name in unique_val_results[0]:
                             current_col_info['unique_values'] = str([row[column_name] for row in unique_val_results if row[column_name] is not None])
                        else:
                             current_col_info['unique_values'] = "" # Or handle error appropriately
                    else:
                        current_col_info['column_type'] = 'not categorical'
                        current_col_info['unique_values'] = str([str(row[column_name]) for row in unique_val_results[:20] if row[column_name] is not None]) # Or a sample, or count
                else:
                    current_col_info['column_type'] = 'empty' # Or 'no_unique_values_found'
                    current_col_info['unique_values'] = ""

                final_results_with_values[table_name].append(current_col_info)

            except Exception as e:
                print(f"Error fetching unique values for {table_name}.{column_name}: {e}")
                failed_col_info = col_meta.copy()
                failed_col_info['column_type'] = 'error_fetching_values'
                failed_col_info['unique_values'] = f"Error: {e}"
                final_results_with_values[table_name].append(failed_col_info)

    return json.dumps(final_results_with_values, indent=4)


tools = [extract_schema_and_unique_values]
llm_with_tools = llm.bind_tools(tools)

async def extractor(state: StateDataAnalyst):
    """
    This node handles the logic for extracting keywords and calling the schema_extractor tool.
    It also manages the iteration count for refinement.
    """
    messages = state["messages"]
    iterations = state["iterations"]

    system_prompt = f"""
    You are a helpful data extractor assistant. Your goal is to identify relevant tables and columns from a database based on a user's query.
    You have access to a `extract_schema_and_unique_values` tool that can find semantically similar tables and columns and also the datatypes and unique values stored in those columns if the column is categorical, or some sample values if the column is not categorical.

    Follow these steps:
    1. **Initial Keyword Extraction**: From the user query, identify key terms or phrases that could directly map to tables or columns in a database.
       Call the `extract_schema_and_unique_values` tool with these keywords.
    2. **Analyze Tool Output**: Examine the output from the `extract_schema_and_unique_values` tool.
    3. **Refine and Re-extract (if necessary)**:
       - If the extracted tables/columns seem incomplete or don't fully address the user's query, identify new keywords or refine existing ones.
       - You can iterate this process (call the tool again) at most twice in total (including the initial call). The current iteration count is {iterations}.
       - If you need to make another call, provide a concise follow-up query to the `extract_schema_and_unique_values` tool.
    4. **It is very important to keep all the columns and tables that will be useful for writing an SQL query to extract the data that is requested in the user query**
    5. **Final Output**: Once you are confident that you have identified all relevant tables and columns, or if you have reached the maximum iterations ({2}),
       output the final result in the exact JSON format as specified:
       {{
           "table_name1": [
                {{
                    "column_name": "Name of the column",
                    "data_type": "Data type stored in column",
                    "nullable": True or False,
                    "description": "description of column"
                    "column_type": "categorical" or "not categorical",
                    "unique_values": list of unique values or sample values
                }}, .../ add more if there are multiple columns from same table
           ],
           "table_name2": [
                {{
                    "column_name": "Name of the column",
                    "data_type": "Data type stored in column",
                    "nullable": True or False,
                    "description": "description of column",
                    "column_type": "categorical" or "not categorical",
                    "unique_values": list of unique values or sample values
                }}, .../ add more if there are multiple columns from same table,
           // include as many keys as there are distinct tables extracted
       }}
    6. **Error Handling**: If the tool fails to extract any information, return an empty JSON object {{}}.
    7. **No relevant tables/columns found**: If no relevant tables or columns are found, return an empty JSON object {{}}.
    """

    # Add the system prompt to the messages if it's the first turn or to guide the LLM
    if not messages or not any(isinstance(msg, SystemMessage) for msg in messages):
        messages = [SystemMessage(content=system_prompt)] + messages
    else:
        # Update existing system message
        messages = [
            SystemMessage(content=system_prompt) if isinstance(msg, SystemMessage) else msg
            for msg in messages
        ]

    # Increment iteration count
    new_iterations = iterations + 1

    # First, let the LLM generate tool calls based on the current messages
    ai_message = await llm_with_tools.ainvoke(messages)

    return {"messages": ai_message, "iterations": new_iterations}

graph_builder = StateGraph(StateDataAnalyst)

tool_node = ToolNode(tools=tools)
graph_builder.add_node("agent", extractor)
graph_builder.add_node("tools", tool_node)

graph_builder.add_conditional_edges("agent", tools_condition)
graph_builder.add_edge("tools", "agent")
graph_builder.add_edge(START, "agent")