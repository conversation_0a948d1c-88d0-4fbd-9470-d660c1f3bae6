from openai import AsyncOpenAI
import chromadb
import asyncio

from .models import (
    probe_merchant,
    probe_financials,
    probe_authorized_signatories,
    probe_epfo_establishments,
    probe_open_charges
)

from .models2 import (
    probe_industry_segments,
    ExternalInsights,
    merchant_metrics
)

# List of your SQLAlchemy models
SQL_MODELS = [
    probe_merchant,
    probe_authorized_signatories,
    probe_financials,
    probe_epfo_establishments,
    probe_open_charges,
    probe_industry_segments,
    ExternalInsights,
    merchant_metrics
]

OPENAI_API_KEY= "********************************************************************************************************************************************************************"
openai_client = AsyncOpenAI(api_key=OPENAI_API_KEY)


# --- 3. ChromaDB Client Setup (MODIFIED) ---
# Use AsyncHttpClient to connect to the Dockerized ChromaDB server
chroma_client = None # Initialize as None, will be awaited later
collection_name = "column_embeddings_async"
collection = None # Initialize collection globally

async def initialize_chroma_client():
    global chroma_client
    if chroma_client is None:
        chroma_client = await chromadb.AsyncHttpClient(host='localhost', port=8000) # Corrected: Removed await
        print("ChromaDB AsyncHttpClient initialized.")

async def get_or_create_collection_async():
    global collection
    await initialize_chroma_client() # Ensure client is initialized first
    try:
        # Using the async client's method directly
        collection = await chroma_client.get_or_create_collection(name=collection_name)
        print(f"Collection '{collection_name}' ready.")
    except Exception as e:
        print(f"Error accessing or creating collection: {e}")
        raise # Re-raise to stop if DB connection fails

# --- 4. Function to generate embeddings using OpenAI asynchronously ---
async def get_embedding_async(text, model="text-embedding-ada-002"):
    """Generates an embedding for the given text using OpenAI's async API."""
    try:
        text = text.replace("\n", " ")
        response = await openai_client.embeddings.create(input=[text], model=model)
        return response.data[0].embedding
    except Exception as e:
        print(f"An error occurred during async embedding generation: {e}")
        return None
    
# --- 5. Extract schema info, generate embeddings, and store in ChromaDB asynchronously (MODIFIED) ---
async def populate_vector_db_from_schema_async(models):
    """
    Extracts column comments, generates embeddings, and stores them in ChromaDB asynchronously.
    """
    await get_or_create_collection_async() # Ensure collection is ready

    documents_to_add = []
    metadatas_to_add = []
    ids_to_add = []
    embedding_tasks = []
    column_infos = [] # To store (document_string, metadata_dict, id) before getting embeddings

    current_id = 0

    for model_class in models:
        table_name = model_class.__tablename__
        schema_name = model_class.__table_args__.get('schema', 'public')

        for column in model_class.__table__.columns:
            column_name = column.name
            column_type = str(column.type)
            comment = column.comment
            is_nullable = column.nullable

            if comment:
                document = f"Table: {schema_name}.{table_name}, Column: {column_name}, Description: {comment}"
                metadata_item = {
                    "table_name": f"{schema_name}.{table_name}",
                    "column_name": column_name,
                    "data_type": column_type,
                    "is_nullable": is_nullable,
                    "comment": comment
                }
                current_col_id = f"col_{current_id}"

                embedding_tasks.append(get_embedding_async(document))
                column_infos.append((document, metadata_item, current_col_id))
                current_id += 1
            else:
                print(f"No comment found for column: {schema_name}.{table_name}.{column_name}. Skipping.")

    if not embedding_tasks:
        print("No columns with comments found to process for embeddings.")
        return

    print(f"Initiating {len(embedding_tasks)} async embedding calls...")
    embeddings = await asyncio.gather(*embedding_tasks)

    # Process results after all embeddings are generated
    for i, (document, metadata_item, col_id) in enumerate(column_infos):
        embedding = embeddings[i] # Get the pre-generated embedding
        if embedding is not None:
            documents_to_add.append(document)
            metadatas_to_add.append(metadata_item)
            ids_to_add.append(col_id)
        else:
            print(f"Skipping storage for {metadata_item['table_name']}.{metadata_item['column_name']} due to embedding error.")

    if documents_to_add:
        print(f"Adding {len(documents_to_add)} documents to ChromaDB...")
        # Use await with the `add` method of the async client
        await collection.add(
            embeddings=embeddings, # Use the already generated embeddings list
            documents=documents_to_add,
            metadatas=metadatas_to_add,
            ids=ids_to_add
        )
        print("Embeddings successfully added to ChromaDB.")
    else:
        print("No documents with comments found to add to ChromaDB.")

async def search_keywords(query_text):
    """
    Perform a semantic search in the vector database using the provided query text.
    """
    await get_or_create_collection_async() # Ensure collection is ready

    query_embedding = await get_embedding_async(query_text)
    if query_embedding:
        results = await collection.query(
            query_embeddings=[query_embedding],
            n_results=3, # Get top 5 most similar columns
        )
        # print(f"\nSearch results for '{query_text}':")

        # if results and results.get('documents') and results['documents'][0]:
        #     for i, (doc, meta) in enumerate(zip(results['documents'][0], results['metadatas'][0])):
        #         print(f"--- Result {i+1} ---")
        #         # print(f"Document: {doc}")
        #         print(f"Table: {meta['table_name']}")
        #         print(f"Column Name: {meta['column_name']}")
        #         print(f"Data Type: {meta['data_type']}")
        #         print(f"Comment: {meta['comment']}")
        #         print("-" * 20)
        # else:
        #     print("No results found for the query.")
        
        return results
    else:
        print("Could not generate embedding for the query. Cannot perform search.")
        return None

# --- Asynchronous Main Execution ---
async def main():
    print("Starting process to populate vector database asynchronously...")
    # await get_or_create_collection_async() # Ensure collection is ready

    await populate_vector_db_from_schema_async(SQL_MODELS) # This will also call get_or_create_collection_async
    print("Vector database population complete.")

    # --- Example Semantic Search (Async) ---
    print("\n--- Performing a semantic search example (async) ---")
    query_text = "find columns related to company financials"
    query_embedding = await get_embedding_async(query_text)

    if query_embedding:
        # Use await with the `query` method of the async client
        results = await collection.query(
            query_embeddings=[query_embedding],
            n_results=5, # Get top 5 most similar columns
        )

        print(f"\nSearch results for '{query_text}':")
        # Ensure results structure matches what's returned by AsyncHttpClient
        # It's usually results['documents'][0] for the first query result
        if results and results.get('documents') and results['documents'][0]:
            for i, (doc, meta) in enumerate(zip(results['documents'][0], results['metadatas'][0])):
                print(f"--- Result {i+1} ---")
                print(f"Table: {meta['table_name']}")
                print(f"Column Name: {meta['column_name']}")
                print(f"Data Type: {meta['data_type']}")
                print(f"Comment: {meta['comment']}")
                print("-" * 20)
        else:
            print("No results found for the query.")
    else:
        print("Could not generate embedding for the query. Cannot perform search.")

    print("\n--- Another semantic search example (async) ---")
    query_text_2 = "merchant contact information"
    query_embedding_2 = await get_embedding_async(query_text_2)

    if query_embedding_2:
        results_2 = await collection.query(
            query_embeddings=[query_embedding_2],
            n_results=3,
        )

        print(f"\nSearch results for '{query_text_2}':")
        if results_2 and results_2.get('documents') and results_2['documents'][0]:
            for i, (doc, meta) in enumerate(zip(results_2['documents'][0], results_2['metadatas'][0])):
                print(f"--- Result {i+1} ---")
                print(f"Table: {meta['table_name']}")
                print(f"Column Name: {meta['column_name']}")
                print(f"Data Type: {meta['data_type']}")
                print(f"Comment: {meta['comment']}")
                print("-" * 20)
        else:
            print("No results found for the query.")
    else:
        print("Could not generate embedding for the query. Cannot perform search.")

# Run the asynchronous main function
if __name__ == "__main__":
    asyncio.run(main())
    # asyncio.run(search_keywords("merchant contact information"))