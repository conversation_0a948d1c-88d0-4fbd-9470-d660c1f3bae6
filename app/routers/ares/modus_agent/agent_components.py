from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import MessagesState
from . import prompts
import json
from decimal import Decimal
from datetime import date, datetime
from pydantic import BaseModel, Field
from typing import List

from langchain.chat_models import init_chat_model

from .schema_extractor_agent import graph_builder as schema_extractor_graph
from .db_agent import graph_builder as codegen
from .db_agent_all import graph_builder as codegen_all
from .report_agent_component import graph as report_graph

from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"



class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

llm = init_chat_model("openai:gpt-4.1")
llm_search = init_chat_model("perplexity:sonar-pro")

class State(MessagesState):
    is_relevant: bool
    in_context: bool
    is_greeting: str
    improved_query: str
    classification: str
    sources: List[str]
    iterations: int
    tables_and_columns: str
    merchant_id: str
    code_status: str
    code: str
    data: List
    modified_query: str
    vis_specs: dict
    chat_id: str
    report: dict

async def init_state(state: State):
    """
    Reset the state of the conversation.
    """
    return {
        "is_relevant": False,
        "in_context": False,
        "is_greeting": False,
        "improved_query": "",
        "classification": "",
        "sources": [],
        "iterations": 0,
        "tables_and_columns": "",
        "code_status": "",
        "code": "",
        "data": [],
        "modified_query": "",
        "viz_specs": {},
        "report": {},
    }


# 1. Define your Pydantic Schema
class RelevanceResponse(BaseModel):
    """Information about a person."""
    is_relevant: bool = Field(
        ...,
        description="Whether the query is relevant to the context of finance and fraud investigation, detection.",
    )

async def check_relevance(state: State):
    """
    Check if the user query is relevant to the context of conversation.

    Args:
        state (State): The current state of the conversation.
    """
    response = await llm.with_structured_output(RelevanceResponse).ainvoke(
        [
            SystemMessage(
                content=prompts.RELEVANCE_SYSTEM_PROMPT
            ),
            HumanMessage(
                content=state["messages"][-1].content
            ),
        ]
    )
    return {"is_relevant": response.is_relevant}


class GreetingResponse(BaseModel):
    """Identify if the user query is a greeting."""
    is_greeting: bool= Field(
        ...,
        description="Whether the query is a greeting.",
    )

async def check_greeting(state: State):
    """
    Check if the user query is a greeting.

    Args:
        state (State): The current state of the conversation.
    """
    sytem_prompt = "You are a helpful assistant. You will be given a user query. If the query is a greeting, respond with 'True'. Otherwise, respond with 'False'. Greetings include 'hello', 'hi', 'hey', 'good morning', 'good afternoon', and 'good evening' or any other similar phrases."
    response = await llm.with_structured_output(GreetingResponse).ainvoke(
        [
            SystemMessage(
                content=sytem_prompt
            ),
            HumanMessage(
                content=state["messages"][-1].content
            ),
        ]
    )

    return {"is_greeting": response.is_greeting}


async def handle_greeting(state: State):
    """
    Handle the greeting response.

    Args:
        state (State): The current state of the conversation.
    """
    system_prompt = "You are a helpful assistant. You will be given a greetin message by user. Respond with an appropriate greeting message."
    response = await llm.ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            HumanMessage(
                content=state["messages"][-1].content
            ),
        ]
    )
    return {"messages": response}


async def handle_ooc_messages(state: State):
    """
    Handle out-of-context messages.

    Args:
        state (State): The current state of the conversation.
    """
    response = "Sorry, I cannot assist with this, it is out of my context and capabilities. Please ask me something different."
    return {"messages": AIMessage(content=response)}


class ContextCheckResponse(BaseModel):
    """Check if the user query is relevant to the context of conversation or a totally new query."""
    in_context: bool = Field(
        ...,
        description="Whether the query is relevant to the current context of conversation or a new query.",
    )

async def context_check(state: State):
    """
    Check if the user query is relevant to the context of conversation or a new query.

    Args:
        state (State): The current state of the conversation.
    """
    system_prompt = prompts.CONTEXT_CHECK_SYSTEM_PROMPT
    response = await llm.with_structured_output(ContextCheckResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            *state["messages"]
        ]
    )

    return {"in_context": response.in_context}


class ImprovedQueryResponse(BaseModel):
    """Identify if the user query is a greeting."""
    improved_query: str = Field(
        ...,
        description="The improved query based on the context of conversation.",
    )

async def improve_query(state: State) -> State:
    """
    Improve the user query based on the context of conversation.

    Args:
        state (State): The current state of the conversation.
    """
    system_prompt = prompts.IMPROVE_MESSAGE_SYSTEM_PROMPT
    response = await llm.with_structured_output(ImprovedQueryResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            *state["messages"],
        ]
    )
    return {"improved_query": response.improved_query}


class ClassificationResponse(BaseModel):
    """Identify the classification of the user query."""
    classification: str = Field(
        ...,
        description="The classification of the user query into different categories [LLM_QUERY, DB_QUERY, VISUALIZATION_QUERY, REPORT_QUERY, NONE]",
    )

async def classify_query(state: State) -> State:
    """
    Classify the user query into different categories [LLM_QUERY, DB_QUERY, VISUALIZATION_QUERY, REPORT_QUERY]

    Args:
        state (State): The current state of the conversation.
    """
    system_prompt = prompts.CLASSIFICATION_SYSTEM_PROMPT
    response = await llm.with_structured_output(ClassificationResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            HumanMessage(
                content=state["improved_query"]
            ),
        ]
    )
    return {"classification": response.classification}


class LLMQueryResponse(BaseModel):
    """Identify the classification of the user query."""
    answer: str = Field(
        ...,
        description="The answer to the user query."
        )
    sources: List[str] = Field(
        ...,
        description="The sources of the answer to the user query."
    )

async def llm_query(state: State):
    response = await llm.with_structured_output(LLMQueryResponse).ainvoke(
        [
            SystemMessage(
                content=prompts.QUESTION_ANSWER_SYSTEM_PROMPT
            ),
            HumanMessage(
                content=state["improved_query"]
            ),
        ]
    )
    return {"messages": AIMessage(content=response.answer), "sources": response.sources}


async def extract_schema(state: State):
    """
    Extract the schema from the database.

    Args:
        state (State): The current state of the conversation.
    """
    config_schema = {
        "configurable": {
            "thread_id": state["chat_id"]+"_schema"
        }
    }

    async with AsyncPostgresSaver.from_conn_string(DATABASE_URL) as checkpointer:
        # await checkpointer.setup()
        agent_schema = schema_extractor_graph.compile(checkpointer=checkpointer)

        inputs = {"messages": [HumanMessage(content=state["improved_query"])], "iterations": 0}

        response = await agent_schema.ainvoke(inputs, config_schema)

        messages = response["messages"]
        tables_and_columns = messages[-1].content
        # get the last message
        return {"tables_and_columns": tables_and_columns}

async def extract_schema_vis(state: State):
    """
    Extract the schema from the database.

    Args:
        state (State): The current state of the conversation.
    """
    config_schema = {
        "configurable": {
            "thread_id": state["chat_id"]+"_schema_vis"
        }
    }

    # compile the async graph for schema extraction
    async with AsyncPostgresSaver.from_conn_string(DATABASE_URL) as checkpointer:
        # await checkpointer.setup()
        agent_schema = schema_extractor_graph.compile(checkpointer=checkpointer)

        inputs = {"messages": [HumanMessage(content=state["modified_query"])], "iterations": 0}

        response = await agent_schema.ainvoke(inputs, config_schema)
        messages = response["messages"]
        tables_and_columns = messages[-1].content
        # get the last message
        return {"tables_and_columns": tables_and_columns}


async def handle_no_columns(state: State):
    """
    Handle the case when no columns are found.

    Args:
        state (State): The current state of the conversation.
    """
    response = "Sorry, I cannot assist with this, no columns related to the asked query exist in my knowledge. Please ask me something different."

    return {"messages": AIMessage(content=response)}


async def handle_code_success(state: State):
    """
    Handle the case when the code execution is successful.

    Args:
        state (State): The current state of the conversation.
    """
    response = "The code executed successfully. You can find the results below:\n"
    return {"messages": AIMessage(content=response)}


async def handle_code_failure(state: State):
    """
    Handle the case when the code execution fails.

    Args:
        state (State): The current state of the conversation.
    """
    response = "The code execution failed. Please check the code and try again."
    return {"messages": AIMessage(content=response)}


async def new_chat(state: State):
    """
    Start a new chat.

    Args:
        state (State): The current state of the conversation.
    """
    response = "This query is not relevant to the current context of conversation. Please start a new chat."
    return {"messages": AIMessage(content=response)}


async def db_query(state: State):
    """
    Query the database.

    Args:
        state (State): The current state of the conversation.
    """
    tables_and_columns = state["tables_and_columns"]
    merchant_id = state["merchant_id"]

    config = {
        "configurable": {
            "thread_id": state["chat_id"]+"_db_query"
        }
    }
    # compile the async graph for code generation
    async with AsyncPostgresSaver.from_conn_string(DATABASE_URL) as checkpointer:
        # await checkpointer.setup()
        codegen_agent = codegen.compile(checkpointer=checkpointer)

        inputs = {"messages": [HumanMessage(content=state["improved_query"])], "iterations": 0, "tables_and_columns": tables_and_columns, "merchant_id": merchant_id, "code": "", "data": [], "status": ""}

        response = await codegen_agent.ainvoke(inputs, config)

        if response["status"] == "success":
            return {"messages": AIMessage(content="Your query executed successfully."), "code_status": response["status"], "code": response["code"], "data": response["data"]}
        else:
            return {"messages": AIMessage(content="Your query failed to execute. Please try some other query"), "code_status": response["status"], "code": response["code"], "data": response["data"]}

class ModifiedQueryResponse(BaseModel):
    """Identify the classification of the user query."""
    modified_query: str = Field(
        ...,
        description="The modified query to fetch raw data for visualization."
        )
    
async def query_modifier(state: State):
    """
    This function will modify a query from visualization to a database query.
    """
    system_prompt = prompts.MODIFY_QUERY_SYSTEM_PROMPT
    response = await llm.with_structured_output(ModifiedQueryResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            HumanMessage(
                content=state["improved_query"]
            ),
        ]
    )
    return {"modified_query": response.modified_query}

async def visualization_query(state: State):
    """
    Given the extracted data from the database, this function will generate a visualization configuration.
    """
    system_message = SystemMessage(
        content=prompts.IMPROVED_GRAPH_SPEC_PROMPT
    )
    user_message = f"""
    USER QUERY: {state["improved_query"]}
    
    SAMPLE DATA: {json.dumps(state["data"][:5], ensure_ascii=False, cls=CustomJSONEncoder)}
    """
    response = await llm.ainvoke(
        [
            system_message,
            HumanMessage(
                content=user_message
            ),
        ]
    )
    # extract the json from the response by splitting on "```json" and "```"
    chart_specifications = response.content.split("```json")[1].split("```")[0].strip()
    # parse the json
    try:
        chart_specifications = json.loads(chart_specifications)
    except json.JSONDecodeError:
        chart_specifications = {}

    print("Chart Specifications:", chart_specifications)
    return {"vis_specs": chart_specifications}

async def handle_viz_end(state: State):
    """
    Handle the end of the visualization query.

    Args:
        state (State): The current state of the conversation.
    """
    if state["vis_specs"]:
        response = "The visualization query has been successfully processed. You can see the visualization below:\n"
        return {"messages": AIMessage(content=response)}
    else:
        response = "The visualization query is not clear. Please provide more details or try a different query."
        return {"messages": AIMessage(content=response)}

async def report_query(state: State):
    """
    Handle the report query.
    Args:
        state (State): The current state of the conversation.
    """

    # compile the async graph for report query
    configs_report = {
        "configurable": {
            "thread_id": state["chat_id"]+"_report"
        }
    }

    async with AsyncPostgresSaver.from_conn_string(DATABASE_URL) as checkpointer:
            # await checkpointer.setup()
            agent_report = report_graph.compile(checkpointer=checkpointer)

            inputs = {"messages": state["messages"], "merchant_id": state["merchant_id"]}

            response = await agent_report.ainvoke(inputs, configs_report)
            report = response["report"]
            message = response["messages"][-1].content
    return {"messages": AIMessage(content=message), "report": report}

# Routers -------------------START----------------------
def relevance_router(state: State):
    """
    Route the relevance check based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    
    if state["is_relevant"]:
        return "context_check"
    else:
        return "check_greeting"
    
def context_router(state: State):
    """
    Route the context check based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    if state["in_context"]:
        return "improve_query"
    else:
        return "new_chat"
    
def greeting_router(state: State):
    """
    Route the greeting check based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    if state["is_greeting"]:
        return "handle_greeting"
    else:
        return "handle_ooc_messages"
    
def classification_router(state: State):
    """
    Route the classification check based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    if state["classification"] == "LLM_QUERY":
        return "llm_query"
    elif state["classification"] == "DB_QUERY":
        return "db_query"
    elif state["classification"] == "VISUALIZATION_QUERY":
        return "visualization_query"
    elif state["classification"] == "REPORT_QUERY":
        return "report_query"
    else:
        return "handle_ooc_messages"
    
def extract_schema_router(state: State):
    """
    Route the schema extraction based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    tables_and_columns = json.loads(state["tables_and_columns"])
    if tables_and_columns:
        return "db_query"
    else:
        return "handle_no_columns"
    
def code_result_router(state: State):
    """
    Route the code result based on the state.

    Args:
        state (State): The current state of the conversation.
    """
    if state["code_status"] == "success":
        if state["classification"].lower() == "visualization_query":
            return "visualization_query"
        return "handle_code_success"
    else:
        return "handle_code_failure"

# Routers -------------------END----------------------


# =====================================================================================
# This part will contain code for editing dashboard visualizations
# =====================================================================================
class EditVisState(MessagesState):
    vis_specs: dict = Field(
        ...,
        description="The specifications of the visualization to be edited."
    )
    data: List = Field(
        ...,
        description="The data samples to be used for the visualization."
    )
    new_specs: dict = Field(
        ...,
        description="The new specifications of the visualization after editing."
    )
    new_data: List = Field(
        ...,
        description="The new data samples to be used for the visualization after editing."
    )
    transformed_query: str = Field(
        ...,
        description="The transformed query to be used for fetching new data for the visualization."
    )
    existing_query: str = Field(
        ...,
        description="The existing sql query used for fetching data for the visualization."
    )
    new_query: str = Field(
        ...,
        description="The new sql query to be used for fetching data for the visualization after editing."
    )
    tables_and_columns: str = Field(
        ...,
        description="The tables and columns in the database that are needed to fulfill the request."
    )
    is_relevant: bool = Field(
        ...,
        description="Whether the user query is relevant to the context of editing visualizations.",
    )
    is_data: bool = Field(
        ...,
        description="Whether the user query is asking about changing the style of the visualization or if the data also needs to be changed. If data needs to be changed, return True, otherwise return False.",
    )
    code_status: str = Field(
        ...,
        description="The status of the code execution. It can be 'success' or 'failure'.",
    )

async def init_edit_vis_state(state: EditVisState):
    """
    Initialize the state for editing visualizations.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    return {
        "new_specs": {},
        "new_data": [],
        "transformed_query": "",
        "new_query": "",
        "tables_and_columns": "",
        "is_relevant": False,
        "is_data": False,
        "code_status": "",
    }

async def enrich_node(state: EditVisState):
    """
    Enrich the node with the visualization specifications and data samples.

    Args:
        state (EditVisState): The current state of the conversation.
    """
 
    user_message = f"""
    CURRENT VISUALIZATION SPECIFICATIONS: {json.dumps(state["vis_specs"], ensure_ascii=False, cls=CustomJSONEncoder)}

    CURRENT SAMPLE DATA: {json.dumps(state["data"][:5], ensure_ascii=False, cls=CustomJSONEncoder)}

    CURRENT SQL QUERY: {state["existing_query"]}
    """
    return {"messages": HumanMessage(content=user_message)}

class IdentifyRelevanceResponse(BaseModel):
    """Identify if the user query is relevant to the visualization editing context."""
    is_relevant: bool = Field(
        ...,
        description="Whether the query is relevant to the context of editing visualizations.",
    )

async def identify_relevance(state: EditVisState):
    """
    Identify if the user query is relevant to the visualization editing context.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    system_prompt = """
    You are a helpful assistant. You will be given a user message history. If the latest query is relevant to the context of editing visualizations, respond with 'True'. Otherwise, respond with 'False'.
    
    Use the following rules to determine relevance:
    1. True:
        - The query is about editing, modifying, or updating visualizations.
        - The query is related to the specifications of the visualization.
        - The query is asking for changes to the visualization's data or appearance.
        - The query is about adding or removing elements from the visualization.
        - The query is about changing the data that is being visualized.
        - Other queries that are directly related to the visualization context.
    
    2. False:
        - Anything else that does not fit the above criteria.
        - It also includes greetings, out-of-context queries, or general questions that are not related to the visualization editing context.
    
    NOTE: Use the full context of the conversation to determine relevance but only answer the relavance of the latest query.

    Only respond with 'True' or 'False' without any additional text.
    {{
        "is_relevant": True or False
    }}
    """
    response = await llm.with_structured_output(IdentifyRelevanceResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            *state["messages"],
        ]
    )
    return {"is_relevant": response.is_relevant}


class IdentifyStyleOrDataResponse(BaseModel):
    """Identify if the user query is about styling or data of the visualization."""
    is_data: bool = Field(
        ...,
        description="Whether the query is asking about changing the style of the visualization or if the data also needs to be changed. If data needs to be changed, return True, otherwise return False.",
    )

async def style_or_data(state: EditVisState):
    """
    Identify if the user query is about styling or data of the visualization.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    system_prompt = """
    You are a helpful assistant. You will be given a user conversation. Identify if the latest query is asking about changing the style of the visualization or the data being visualized also needs to be changed. You will also be given with the existing visualization specifications and data samples. Think and identify if the last query can be fulfilled by just changing the style of the visualization or if the data needs to be changed as well.

    NOTE: Use the full context of the conversation to determine if the query is about styling or data, but only answer for the latest query.
    Answer with True if the data also needs to be changed, otherwise answer with False.
    
    {{
        "is_data": True or False
    }}

    Only return the JSON response without any additional text.
    """

    response = await llm.with_structured_output(IdentifyStyleOrDataResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            *state["messages"]
        ]
    )
    return {"is_data": response.is_data}


# if it is just styling
async def handle_style(state: EditVisState):
    """
    Handle the styling of the visualization.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    system_prompt = prompts.EDIT_VISUALIZATION_STYLE_SYSTEM_PROMPT
    response = await llm.ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            *state["messages"]
        ]
    )
    chart_specifications = response.content
    # extract the json from the response by splitting on "```json" and "```"
    if "```" in chart_specifications:
        chart_specifications = response.content.split("```json")[1].split("```")[0].strip()
    # parse the json
    try:
        chart_specifications = json.loads(chart_specifications)
    except json.JSONDecodeError:
        chart_specifications = {}
    return {"new_specs": chart_specifications, "messages": AIMessage(content="The visualization has been successfully updated with the new style.")}

class TransformQueryResponse(BaseModel):
    """Transform the user query from visualization to new data extraction query."""
    new_user_message: str = Field(
        ...,
        description="The transformed user query to be used for fetching new data for the visualization."
    )

async def query_transformer(state: EditVisState):
    """
    Transform the user quer from visualization to new data extraction query.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    system_prompt = prompts.QUERY_TRANSFORIMING_SYSTEM_PROMPT
    response = await llm.with_structured_output(TransformQueryResponse).ainvoke(
        [
            SystemMessage(
                content=system_prompt
            ),
            *state["messages"],
        ]
    )
    return {"transformed_query": response.new_user_message}

async def extract_schema_vis_edit(state: EditVisState):
    """
    Extract the schema from the database.n

    Args:
        state (State): The current state of the conversation.
    """
    response = await graph.ainvoke(
        {"messages": [HumanMessage(content=state["transformed_query"])], "iterations": 0},
        config=config
    )
    messages = response["messages"]
    tables_and_columns = messages[-1].content
    # get the last message
    return {"tables_and_columns": tables_and_columns}

async def extract_data_vis_edit(state: EditVisState):
    """
    Extract the data from the database.

    Args:
        state (EditVisState): The current state of the conversation.
    """

    config_vis_edit = {
        "configurable": {
            "thread_id": state["chat_id"]+"_vis_edit"
        }
    }

    async with AsyncPostgresSaver.from_conn_string(DATABASE_URL) as checkpointer:
            # await checkpointer.setup()
            code_gen_agent_all = codegen_all.compile(checkpointer=checkpointer)

            inputs = {"messages": [HumanMessage(content=state["transformed_query"])], "iterations": 0, "tables_and_columns": state["tables_and_columns"], "code": "", "data": [], "status": ""}

            response = await code_gen_agent_all.ainvoke(inputs, config_vis_edit)
            if response["status"] == "success":
                return {"new_data": response["data"], "code_status": response["status"], "new_query": response["code"]}
            else:
                return {"new_data": [], "code_status": response["status"], "new_query": response["code"]}
    
async def visualization_query_vis_edit(state: EditVisState):
    """
    Given the extracted data from the database, this function will generate a visualization configuration.
    """
    system_message = SystemMessage(
        content=prompts.IMPROVED_GRAPH_SPEC_EDIT_VIS_PROMPT
    )
    user_message = f"""
    SAMPLE DATA: {json.dumps(state["new_data"][:5], ensure_ascii=False, cls=CustomJSONEncoder)}
    """
    response = await llm.ainvoke(
        [
            system_message,
            *state["messages"],
            HumanMessage(
                content=user_message
            ),
        ]
    )
    # extract the json from the response by splitting on "```json" and "```"
    chart_specifications = response.content.split("```json")[1].split("```")[0].strip()
    # parse the json
    try:
        chart_specifications = json.loads(chart_specifications)
    except json.JSONDecodeError:
        chart_specifications = {}

    print("Chart Specifications:", chart_specifications)
    return {"new_specs": chart_specifications}

async def handle_vis_end_vis_edit(state: EditVisState):
    """
    Handle the end of the visualization query.

    Args:
        state (State): The current state of the conversation.
    """
    if state["new_specs"]:
        response = "The visualization query has been successfully processed. You can see the visualization below:\n"
        return {"messages": AIMessage(content=response)}
    else:
        response = "The visualization query is not clear. Please provide more details or try a different query."
        return {"messages": AIMessage(content=response)}
    
async def handle_no_columns_vis_edit(state: EditVisState):
    """
    Handle the case when no columns are found in the visualization editing context.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    response = "Sorry, I cannot assist with this, no columns related to the asked data exist in my knowledge. Please ask me something different."
    
    return {"messages": AIMessage(content=response)}

async def handle_no_data_edit_vis(state: EditVisState):
    """
    Handle the case when no data is found in the visualization editing context.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    response = "Sorry, I cannot assist with this, no data was found related to the asked query in my knowledge. Please ask me something different."
    
    return {"messages": AIMessage(content=response)}

async def handle_irrelevant_query(state: EditVisState):
    """
    Handle the case when the user query is irrelevant to the visualization editing context.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    response = "Sorry, I cannot relate this query to the context of editing visualizations. Please ask me something different or try to rephrase your query."
    return {"messages": AIMessage(content=response)}

# ------------------------------------------------------ROUTERS------------------------------------------------------
def identify_relevance_router(state: EditVisState):
    """
    Route the relevance check based on the state.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    if state["is_relevant"]:
        return "style_or_data"
    else:
        return "handle_irrelevant_query"

def style_or_data_router(state: EditVisState):
    """
    Route the styling or data check based on the state.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    if state["is_data"]:
        return "query_transformer"
    else:
        return "handle_style"
    
def schema_router_vis_edit(state: EditVisState):
    """
    Route the schema extraction based on the state.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    try:
        tables_and_columns = json.loads(state["tables_and_columns"])
    except Exception as e:
        print(f"Error parsing tables_and_columns: {e}")
        tables_and_columns = {}
    if tables_and_columns:
        return "extract_data_vis_edit"
    else:
        return "handle_no_columns"

def data_extraction_router_vis_edit(state: EditVisState):
    """
    Route the data extraction based on the state.

    Args:
        state (EditVisState): The current state of the conversation.
    """
    if state["code_status"] == "success" and state["new_data"]:
        return "visualization_query_vis_edit"
    else:
        return "handle_no_data"