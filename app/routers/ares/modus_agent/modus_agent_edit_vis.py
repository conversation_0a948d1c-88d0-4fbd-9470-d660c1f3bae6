from langgraph.graph import StateGraph, END
# from langgraph.checkpoint.memory import MemorySaver

DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"

from . import agent_components as ac


workflow = StateGraph(ac.EditVisState)

workflow.add_node("init_state", ac.init_edit_vis_state)
workflow.add_node("enrich_node", ac.enrich_node)
workflow.add_node("identify_relevance", ac.identify_relevance)
workflow.add_node("handle_irrelevant_query", ac.handle_irrelevant_query)
workflow.add_node("style_or_data", ac.style_or_data)
workflow.add_node("handle_style", ac.handle_style)
workflow.add_node("query_transformer", ac.query_transformer)
workflow.add_node("extract_schema_vis_edit", ac.extract_schema_vis_edit)
workflow.add_node("handle_no_columns_vis_edit", ac.handle_no_columns_vis_edit)
workflow.add_node("extract_data_vis_edit", ac.extract_data_vis_edit)
workflow.add_node("handle_no_data_vis_edit", ac.handle_no_data_edit_vis)
workflow.add_node("visualization_query_vis_edit", ac.visualization_query_vis_edit)
workflow.add_node("handle_vis_end_vis_edit", ac.handle_vis_end_vis_edit)


workflow.set_entry_point("init_state")
workflow.add_edge("init_state", "enrich_node")
workflow.add_edge("enrich_node", "identify_relevance")
workflow.add_conditional_edges("identify_relevance", ac.identify_relevance_router,
    {
        "handle_irrelevant_query": "handle_irrelevant_query",
        "style_or_data": "style_or_data",
    }
)
workflow.add_conditional_edges("style_or_data", ac.style_or_data_router,
    {
        "query_transformer": "query_transformer",
        "handle_style": "handle_style",
    }
)
workflow.add_edge("handle_style", "handle_vis_end_vis_edit")

workflow.add_edge("query_transformer", "extract_schema_vis_edit")
workflow.add_conditional_edges("extract_schema_vis_edit", ac.schema_router_vis_edit,
    {
        "handle_no_columns": "handle_no_columns_vis_edit",
        "extract_data_vis_edit": "extract_data_vis_edit",
    }
)
workflow.add_conditional_edges("extract_data_vis_edit", ac.data_extraction_router_vis_edit,
    {
        "visualization_query_vis_edit": "visualization_query_vis_edit",
        "handle_no_data": "handle_no_data_vis_edit",
    }
        )

workflow.add_edge("visualization_query_vis_edit", "handle_vis_end_vis_edit")
workflow.add_edge("handle_vis_end_vis_edit", END)
workflow.add_edge("handle_irrelevant_query", END)
workflow.add_edge("handle_no_columns_vis_edit", END)
workflow.add_edge("handle_no_data_vis_edit", END)