from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END
# from langgraph.checkpoint.memory import MemorySaver
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

DATABASE_URL = "postgresql://postgres:<EMAIL>:5432/postgres"

from . import agent_components as ac
import asyncio



workflow = StateGraph(ac.State)

workflow.add_node("init_state", ac.init_state)
workflow.add_node("check_relevance", ac.check_relevance)
workflow.add_node("context_check", ac.context_check)
workflow.add_node("new_chat", ac.new_chat)
workflow.add_node("check_greeting", ac.check_greeting)
workflow.add_node("handle_greeting", ac.handle_greeting)
workflow.add_node("handle_ooc_messages", ac.handle_ooc_messages)
workflow.add_node("improve_query", ac.improve_query)
workflow.add_node("classify_query", ac.classify_query)
workflow.add_node("llm_query", ac.llm_query)
workflow.add_node("db_query", ac.db_query)
workflow.add_node("visualization_query", ac.visualization_query)
workflow.add_node("report_query", ac.report_query)
workflow.add_node("extract_schema", ac.extract_schema)
workflow.add_node("handle_no_columns", ac.handle_no_columns)
workflow.add_node("handle_code_success", ac.handle_code_success)
workflow.add_node("handle_code_failure", ac.handle_code_failure)
workflow.add_node("query_modifier", ac.query_modifier)
workflow.add_node("extract_schema_vis", ac.extract_schema_vis)
workflow.add_node("handle_viz_end", ac.handle_viz_end)

workflow.set_entry_point("init_state")
workflow.add_edge("init_state", "check_relevance")
workflow.add_conditional_edges("check_relevance", ac.relevance_router, {
    "check_greeting": "check_greeting",
    "context_check": "context_check",
}
)
workflow.add_conditional_edges("context_check", ac.context_router, {
    "improve_query": "improve_query",
    "new_chat": "new_chat",
}
)
workflow.add_conditional_edges("check_greeting", ac.greeting_router, {
    "handle_greeting": "handle_greeting",
    "handle_ooc_messages": "handle_ooc_messages",
}
)
workflow.add_edge("handle_greeting", END)
workflow.add_edge("handle_ooc_messages", END)
workflow.add_edge("improve_query", "classify_query")
workflow.add_conditional_edges("classify_query", ac.classification_router, {
    "llm_query": "llm_query",
    "db_query": "extract_schema",
    "visualization_query": "query_modifier",
    "report_query": "report_query",
    "handle_ooc_messages": "handle_ooc_messages",
}
)
workflow.add_conditional_edges("extract_schema", ac.extract_schema_router,
    {
        "db_query": "db_query",
        "handle_no_columns": "handle_no_columns",
    }
)
workflow.add_conditional_edges("extract_schema_vis", ac.extract_schema_router,
    {
        "db_query": "db_query",
        "handle_no_columns": "handle_no_columns",
    }
)
workflow.add_edge("query_modifier", "extract_schema_vis")

workflow.add_conditional_edges("db_query", ac.code_result_router,
                               {
        "handle_code_success": "handle_code_success",
        "visualization_query": "visualization_query",
        "handle_code_failure": "handle_code_failure",
    }
)
workflow.add_edge("handle_no_columns", END)
workflow.add_edge("handle_code_success", END)
workflow.add_edge("handle_code_failure", END)
workflow.add_edge("llm_query", END)
workflow.add_edge("visualization_query", "handle_viz_end")
workflow.add_edge("handle_viz_end", END)
workflow.add_edge("report_query", END)


async def main():
    async with AsyncPostgresSaver.from_conn_string(DATABASE_URL) as checkpointer:
        await checkpointer.setup()
        agent = workflow.compile(checkpointer=checkpointer)
        """Asynchronous main function to run the agent."""
        print("Starting asynchronous agent. Type 'exit' or 'quit' to stop.")

        while True:
            try:
                user_input = await asyncio.to_thread(input, "You: ") # Run input in a separate thread
            except RuntimeError:
                # This can happen if the event loop is closing during input
                print("Exiting due to event loop issue.")
                break

            if user_input.lower() in ["exit", "quit"]:
                print("Exiting the agent.")
                break

            # Prepare the input for the agent
            # The exact input structure depends on your State definition.
            # Assuming your initial state takes messages.
            # For subsequent turns, the state (including previous messages) is managed by the checkpointer.
            inputs = {"messages": [HumanMessage(content=user_input)], "merchant_id": "your_merchant_id"} # Add merchant_id if needed by the flow directly

            print("\nThinking...")
            last_ai_message_content = None

            # Stream through the agent's execution
            async for event in agent.astream(inputs, config=ac.config): # Use your chosen config

                for node_name, node_data in event.items():
                    if node_name == "__end__":
                        if not last_ai_message_content and isinstance(node_data, dict) and "messages" in node_data:
                            final_messages_in_state = node_data.get("messages", [])
                            if final_messages_in_state:
                                # Get the content of the last AIMessage in the final state
                                for msg in reversed(final_messages_in_state):
                                    if isinstance(msg, AIMessage):
                                        last_ai_message_content = msg.content
                                        break
                        continue # Stop processing this event if it's __end__

                    # Check if this node's output contains an AIMessage
                    if isinstance(node_data, dict) and "messages" in node_data:
                        messages_from_node = node_data["messages"]
                        # Ensure messages_from_node is a list, as it might be a single message
                        if not isinstance(messages_from_node, list):
                            messages_from_node = [messages_from_node]

                        for message_obj in messages_from_node:
                            if isinstance(message_obj, AIMessage):
                                # Capture the content of the latest AIMessage
                                last_ai_message_content = message_obj.content

            # After all events for this input have been processed, print the final AI response
            print("\n-----------------------------") # Separator for clarity
            if last_ai_message_content:
                print(f"AI: {last_ai_message_content}")
            else:
                print("AI: Sorry, I couldn't generate a specific response for that.")
            print("-----------------------------\n")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Agent stopped by user.")