#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to run a command and check its exit status
run_command() {
    echo -e "${GREEN}Running: $1${NC}"
    eval $1
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Command failed${NC}"
        exit 1
    fi
    echo -e "${G<PERSON><PERSON>}Successfully completed: $1${NC}\n"
}

# Create data directory if it doesn't exist
mkdir -p data

# Run data generation
run_command "python3 data_generation/generate_real.py"

# Run feature generation
run_command "python3 feature_generation/incremental_feature_gen.py \
    --transactions_path data/transactions.csv \
    --merchants_path data/merchants.csv \
    --output_path data/transactions_with_features.csv"

# Run model training
run_command "python3 model/train.py --epochs 200 --learning_rate 0.001"

echo -e "${GREEN}Pipeline completed successfully!${NC}"
