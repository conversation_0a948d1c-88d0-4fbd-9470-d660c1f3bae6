import pandas as pd
import numpy as np
from datetime import timed<PERSON><PERSON>
from sklearn.preprocessing import StandardScaler
from tqdm import tqdm
import os
import sys
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
from config.feature_gen_config import features_to_keep, transformed_columns_lt, transformed_columns_7d

def add_temporal_features(transactions_df):
    """Add cyclical temporal features"""
    hour_of_day = transactions_df['txn_timestamp'].dt.hour / 23.0
    day_of_week = transactions_df['txn_timestamp'].dt.dayofweek / 6.0
    month_of_year = (transactions_df['txn_timestamp'].dt.month - 1) / 11.0
    
    for unit, val in [('hour', hour_of_day), 
                     ('day', day_of_week), 
                     ('month', month_of_year)]:
        transactions_df[f'{unit}_sin'] = np.sin(2 * np.pi * val)
        transactions_df[f'{unit}_cos'] = np.cos(2 * np.pi * val)
    
    return transactions_df

def get_empty_window_features():
    """Return zero values for all 7-day window features"""
    return {
        'txn_cnt_7d': 0,
        'total_amount_7d': 0,
        'txn_amt_avg_7d': 0,  
        'std_amount_7d': 0,
        'chargeback_txn_cnt_pct_7d': 0,  
        'international_txn_cnt_pct_7d': 0,     
        'failed_txn_cnt_pct_7d': 0,      
        'cancelled_txn_cnt_pct_7d': 0,      
        'cx_complaint_txn_pct_7d': 0,  
        'name_mismatch_txn_cnt_pct_7d': 0,  
        'avg_cx_pii_score_7d': 0,   
        'unique_cards_7d': 0,
        'unique_devices_7d': 0,
        'unique_ips_7d': 0,
        'max_txn_per_card_7d': 0,
        'ip_density_7d': 0,
        'device_id_density_7d': 0,
        'card_num_density_7d': 0,
        'cx_density_7d': 0,
        'max_txn_per_device_7d': 0,
        'max_txn_per_ip_7d': 0,
        'num_distinct_currency_used_7d': 0,
        'curr_diversity_score_7d': 0,
        'risky_cx_txn_cnt_pct_7d': 0
    }


def calculate_window_features(window_txs):
    n = len(window_txs)
    if n == 0:
        return get_empty_window_features()
    
    # Calculate txn_amount statistics
    mean = window_txs['txn_amount'].mean()
    std_dev = window_txs['txn_amount'].std()
    
    result = {
        'txn_cnt_7d': n,
        'total_amount_7d': window_txs['txn_amount'].sum(),
        'txn_amt_avg_7d': mean,
        'std_amount_7d': std_dev,
        'chargeback_txn_cnt_pct_7d': window_txs['is_chargeback'].mean() * 100,
        'failed_txn_cnt_pct_7d': (~window_txs['txn_status']).mean() * 100,
        'international_txn_cnt_pct_7d': window_txs['is_cx_international'].mean() * 100,
        'cancelled_txn_cnt_pct_7d': window_txs['is_cancelled'].mean() * 100,
        'cx_complaint_txn_pct_7d': window_txs['has_cx_complaint'].mean() * 100,
        'name_mismatch_txn_cnt_pct_7d': (~window_txs['is_cardholder_name_match']).mean() * 100,
        'avg_cx_pii_score_7d': window_txs['cx_pii_linkage_score'].mean(),
        'unique_cards_7d': window_txs['cx_card_number'].nunique(),
        'unique_devices_7d': window_txs['cx_device_id'].nunique(),
        'unique_ips_7d': window_txs['cx_ip'].nunique(),
        'num_distinct_currency_used_7d': window_txs['txn_currency'].nunique(),
        'curr_diversity_score_7d': calculate_diversity_score(
            window_txs['txn_currency'].value_counts().to_dict()
        ),
        'risky_cx_txn_cnt_pct_7d': window_txs['is_cx_risky'].mean() * 100,
        'round_txn_cnt_pct_7d': window_txs['txn_amount'].apply(is_round_number).mean() * 100
    }
    
    # Add density metrics
    result.update({
        'ip_density_7d': n / max(1, result['unique_ips_7d']),
        'device_id_density_7d': n / max(1, result['unique_devices_7d']),
        'card_num_density_7d': n / max(1, result['unique_cards_7d']),
        'cx_density_7d': n / max(1, window_txs['cx_id'].nunique())
    })
    
    # Add maximum transactions per entity
    result.update({
        'max_txn_per_card_7d': window_txs['cx_card_number'].value_counts().max(),
        'max_txn_per_device_7d': window_txs['cx_device_id'].value_counts().max(),
        'max_txn_per_ip_7d': window_txs['cx_ip'].value_counts().max()
    })
    
    return result

def calculate_diversity_score(currency_counts):
    """Calculate Shannon diversity index for txn_currency usage"""
    if not currency_counts:
        return 0
    total = sum(currency_counts.values())
    proportions = [count/total for count in currency_counts.values()]
    return -sum(p * np.log(p) for p in proportions)

# Normalization is not being used currently
def normalize_features(features_df):
    """Normalize features using StandardScaler (zero mean, unit variance)"""
    
    # Columns to exclude from normalization
    exclude_cols = [
        # ID and metadata columns
        'txn_id', 'mer_id', 'txn_timestamp', 'mer_business_industry',
        
        # Count-based features (should remain as raw counts)
        'txn_cnt_lt', 'txn_cnt_7d',
        'num_distinct_currency_used_lt', 'num_distinct_currency_used_7d',
        
        # Percentage features (already normalized 0-100)
        'chargeback_txn_cnt_pct_lt', 'chargeback_txn_cnt_pct_7d',
        'international_txn_cnt_pct_lt', 'international_txn_cnt_pct_7d',
        'round_txn_cnt_pct_lt', 'round_txn_cnt_pct_7d',
        'cancelled_txn_cnt_pct_lt', 'cancelled_txn_cnt_pct_7d',
        'name_mismatch_txn_cnt_pct_lt', 'name_mismatch_txn_cnt_pct_7d',
        'international_txn_cnt_pct', 'cx_complaint_txn_pct_7d',
        'risky_cx_txn_cnt_pct_lt', 'risky_cx_txn_cnt_pct_7d',
        'failed_txn_cnt_pct_lt', 'failed_txn_cnt_pct_7d',
        'invoice_and_txn_amt_diff_pct',
        
        # Density features (already ratios between 0-1)
        'ip_density_lt', 'ip_density_7d',
        'device_id_density_lt', 'device_id_density_7d',
        'card_num_density_lt', 'card_num_density_7d',
        'cx_density_lt', 'cx_density_7d',
        
        # Diversity scores (already normalized)
        'curr_diversity_score_lt', 'curr_diversity_score_7d'
    ]
    
    # Get numeric columns to normalize
    numeric_cols = features_df.select_dtypes(include=[np.number]).columns
    cols_to_normalize = [col for col in numeric_cols if col not in exclude_cols]
    
    # Log transform specific features
    for col in ['hrs_since_last_transaction', 'txn_amount', 'txn_amt_avg_lt', 'txn_amt_avg_7d']:
        if col in cols_to_normalize:
            features_df[col] = np.log1p(features_df[col])
            
    # Initialize scaler
    scaler = StandardScaler()
    
    # Fit and transform
    features_df[cols_to_normalize] = scaler.fit_transform(features_df[cols_to_normalize])
    
    print(f"Features being normalized:")
    for col in cols_to_normalize:
        print(f"{col}: {features_df[col].min():.3f} to {features_df[col].max():.3f}")
    
    return features_df

def is_round_number(txn_amount):
    """Check if a txn_amount is a round number using multiple criteria"""
    # Check common round number patterns
    if txn_amount % 100 == 0:  # Divisible by 100 (e.g., 500, 1000)
        return True
    if txn_amount % 50 == 0:   # Divisible by 50 (e.g., 150, 250)
        return True
    if txn_amount % 1000 == 0: # Divisible by 1000 (e.g., 1000, 2000)
        return True
        
    # Check if txn_amount ends in zeros
    str_amount = str(int(txn_amount))
    if str_amount.endswith('0' * 2):  # Ends in 00
        return True
        
    # Check psychological pricing (e.g., 999, 1999)
    if str_amount.endswith('99') or str_amount.endswith('999'):
        return True
        
    return False

class MerchantState:
    def __init__(self, mer_id, mer_onboarding_timestamp):
        self.mer_id = mer_id
        self.mer_onboarding_timestamp = mer_onboarding_timestamp
        
        # Lifetime counters
        self.txn_cnt_lt = 0
        self.total_amount_lt = 0
        self.chargeback_count_lt = 0
        self.failed_txn_cnt_pct_count_lt = 0
        self.int_txn_cnt_lt = 0
        self.round_txn_cnt_lt = 0
        self.cancel_count_lt = 0
        self.name_mismatch_count_lt = 0
        self.complaint_count_lt = 0
        self.pii_score_sum_lt = 0
        self.risky_customer_count_lt = 0
        
        # Unique entity tracking
        self.unique_ips_lt = set()
        self.unique_devices_lt = set()
        self.unique_cards_lt = set()
        self.unique_customers_lt = set()
        self.unique_currencies_lt = set()
        
        # txn_currency tracking for diversity score
        self.currency_counts_lt = {}
        
        # 7-day window transactions
        self.window_transactions = []
        self.last_transaction_time = None
        
        # Additional tracking for 7-day window
        self.window_amount_sum = 0
        self.window_amount_squared_sum = 0  # For std calculation
        self.window_chargeback_count = 0
        self.window_failed_count = 0
        self.window_int_count = 0
        self.window_late_night_count = 0
        self.window_complaint_count = 0
        self.window_reversal_count = 0
        self.window_risky_customer_count = 0
        
        # Window unique entity tracking
        self.window_unique_cards = set()
        self.window_unique_devices = set()
        self.window_unique_ips = set()
        self.window_unique_currencies = set()
        self.window_unique_customers = set()
        
        # Window txn_currency tracking
        self.window_currency_counts = {}
        
        # Add running totals for incremental updates
        self.txn_amt_avg_lt = 0
        self.chargeback_txn_cnt_pct_lt = 0
        self.international_txn_cnt_pct_lt = 0
        self.failed_txn_cnt_pct_pct_lt = 0
        self.round_txn_cnt_pct_lt = 0
        self.cancel_pct_lt = 0
        self.name_mismatch_pct_lt = 0
        self.complaint_pct_lt = 0
        self.avg_cx_pii_score_lt = 0
        self.risky_cx_txn_cnt_pct_lt = 0
        
        # Add density ratios
        self.ip_density_lt = 0
        self.device_density_lt = 0
        self.card_density_lt = 0
        self.cx_density_lt = 0
        
        # Initialize counters for late night transactions
        self.late_night_txn_cnt_lt = 0
        self.late_night_txn_amount_lt = 0
        
        # 7-day window late night transaction tracking
        self.window_late_night_txn_cnt = 0
        self.window_late_night_txn_amount = 0

def update_merchant_state(state, current_txn):
    """Update merchant state with new transaction"""
    
    # First add the IP to ensure it's counted in the denominator
    state.unique_ips_lt.add(current_txn['cx_ip'])
    
    # Then increment transaction count
    state.txn_cnt_lt += 1
    
    # Calculate density after both updates
    state.ip_density_lt = state.txn_cnt_lt / max(1, len(state.unique_ips_lt))
    
    # Update txn_amount statistics incrementally
    prev_total = state.total_amount_lt
    state.total_amount_lt += current_txn['txn_amount']
    # Update average incrementally using: new_avg = old_avg + (new_value - old_avg) / new_count
    state.txn_amt_avg_lt = (prev_total + current_txn['txn_amount']) / state.txn_cnt_lt
    
    # Update percentages incrementally
    if current_txn['is_chargeback']:
        state.chargeback_count_lt += 1
    state.chargeback_txn_cnt_pct_lt = (state.chargeback_count_lt / state.txn_cnt_lt) * 100
    
    if current_txn['is_cx_international']:
        state.int_txn_cnt_lt += 1
    state.international_txn_cnt_pct_lt = (state.int_txn_cnt_lt / state.txn_cnt_lt) * 100
    
    # Update failed transaction percentage
    if not current_txn['txn_status']:
        state.failed_txn_cnt_pct_count_lt += 1
    state.failed_txn_cnt_pct_pct_lt = (state.failed_txn_cnt_pct_count_lt / state.txn_cnt_lt) * 100
    
    # Update round transaction percentage
    if is_round_number(current_txn['txn_amount']):
        state.round_txn_cnt_lt += 1
    state.round_txn_cnt_pct_lt = (state.round_txn_cnt_lt / state.txn_cnt_lt) * 100
    
    # Update cancellation percentage
    if current_txn['is_cancelled']:
        state.cancel_count_lt += 1
    state.cancelled_txn_cnt_pct_lt = (state.cancel_count_lt / state.txn_cnt_lt) * 100
    
    # Update name mismatch percentage
    if not current_txn['is_cardholder_name_match']:
        state.name_mismatch_count_lt += 1
    state.name_mismatch_txn_cnt_pct_lt = (state.name_mismatch_count_lt / state.txn_cnt_lt) * 100
    
    # Update customer complaint percentage
    if current_txn['has_cx_complaint']:
        state.complaint_count_lt += 1
    state.international_txn_cnt_pct = (state.complaint_count_lt / state.txn_cnt_lt) * 100
    
    # Update risky customer percentage
    if current_txn['is_cx_risky']:
        state.risky_customer_count_lt += 1
    state.risky_cx_txn_cnt_pct_lt = (state.risky_customer_count_lt / state.txn_cnt_lt) * 100
    
    # Update PII score incrementally
    prev_pii_total = state.pii_score_sum_lt
    state.pii_score_sum_lt += current_txn['cx_pii_linkage_score']
    state.avg_cx_pii_score_lt = state.pii_score_sum_lt / state.txn_cnt_lt
    
    # Update density metrics incrementally
    state.unique_ips_lt.add(current_txn['cx_ip'])
    state.unique_devices_lt.add(current_txn['cx_device_id'])
    state.unique_cards_lt.add(current_txn['cx_card_number'])
    state.unique_customers_lt.add(current_txn['cx_id'])
    
    # Update lifetime density metrics - match gen_features.py calculation
    state.ip_density_lt = state.txn_cnt_lt / max(1, len(state.unique_ips_lt))
    state.device_id_density_lt = state.txn_cnt_lt / max(1, len(state.unique_devices_lt))
    state.card_num_density_lt = state.txn_cnt_lt / max(1, len(state.unique_cards_lt))
    state.cx_density_lt = state.txn_cnt_lt / max(1, len(state.unique_customers_lt))
    
    # Update 7-day window
    current_time = current_txn['txn_timestamp']
    seven_days_ago = current_time - timedelta(days=7)
    
    # Clear expired entities from window sets
    expired_txs = [tx for tx in state.window_transactions if tx['txn_timestamp'] <= seven_days_ago]
    for old_tx in expired_txs:
        if old_tx['cx_ip'] in state.window_unique_ips:
            # Only remove if no other transaction in window uses this IP
            if not any(tx['cx_ip'] == old_tx['cx_ip'] 
                      for tx in state.window_transactions 
                      if tx['txn_timestamp'] > seven_days_ago):
                state.window_unique_ips.remove(old_tx['cx_ip'])
        
        # Similar checks for other entities
        if old_tx['cx_device_id'] in state.window_unique_devices:
            if not any(tx['cx_device_id'] == old_tx['cx_device_id']
                      for tx in state.window_transactions 
                      if tx['txn_timestamp'] > seven_days_ago):
                state.window_unique_devices.remove(old_tx['cx_device_id'])
                
        if old_tx['cx_card_number'] in state.window_unique_cards:
            if not any(tx['cx_card_number'] == old_tx['cx_card_number']
                      for tx in state.window_transactions 
                      if tx['txn_timestamp'] > seven_days_ago):
                state.window_unique_cards.remove(old_tx['cx_card_number'])
                
        if old_tx['cx_id'] in state.window_unique_customers:
            if not any(tx['cx_id'] == old_tx['cx_id']
                      for tx in state.window_transactions 
                      if tx['txn_timestamp'] > seven_days_ago):
                state.window_unique_customers.remove(old_tx['cx_id'])
    
    # Remove expired transactions from window
    state.window_transactions = [tx for tx in state.window_transactions 
                               if tx['txn_timestamp'] > seven_days_ago]
    
    # Add current transaction's contribution
    state.window_amount_sum += current_txn['txn_amount']
    state.window_amount_squared_sum += current_txn['txn_amount'] ** 2
    if current_txn['is_chargeback']:
        state.window_chargeback_count += 1
    if not current_txn['txn_status']:
        state.window_failed_count += 1
    if current_txn['is_cx_international']:
        state.window_int_count += 1
    if current_txn['txn_timestamp'].hour in [23, 0, 1, 2, 3, 4]:
        state.window_late_night_count += 1
    if current_txn['has_cx_complaint']:
        state.window_complaint_count += 1
    if current_txn['is_cancelled']:
        state.window_reversal_count += 1
    if current_txn['is_cx_risky']:
        state.window_risky_customer_count += 1
        
    # Update window txn_currency counts
    curr = current_txn['txn_currency']
    state.unique_currencies_lt.add(curr)
    state.window_currency_counts[curr] = state.window_currency_counts.get(curr, 0) + 1
    state.window_unique_currencies.add(curr)

    if curr in state.currency_counts_lt:
        state.currency_counts_lt[curr] += 1
    else:
        state.currency_counts_lt[curr] = 1
    
    # Add after window counters update
    state.window_unique_cards.add(current_txn['cx_card_number'])
    state.window_unique_devices.add(current_txn['cx_device_id'])
    state.window_unique_ips.add(current_txn['cx_ip'])
    state.window_unique_customers.add(current_txn['cx_id'])

    # Add current transaction to window
    state.window_transactions.append(current_txn)    
    
    # Calculate window densities - match gen_features.py calculation
    window_txn_cnt = len(state.window_transactions)
    state.ip_density_7d = window_txn_cnt / max(1, len(state.window_unique_ips))
    state.device_id_density_7d = window_txn_cnt / max(1, len(state.window_unique_devices))
    state.card_num_density_7d = window_txn_cnt / max(1, len(state.window_unique_cards))
    state.cx_density_7d = window_txn_cnt / max(1, len(state.window_unique_customers))
    
    # Check if the transaction is during late night hours (1 AM to 4 AM)
    if 1 <= current_txn['txn_timestamp'].hour < 4:
        state.late_night_txn_cnt_lt += 1
        state.late_night_txn_amount_lt += current_txn['txn_amount']
    
    # Update 7-day window late night transaction statistics
    if 1 <= current_txn['txn_timestamp'].hour < 4:
        state.window_late_night_txn_cnt += 1
        state.window_late_night_txn_amount += current_txn['txn_amount']
    
    return state

def calculate_features_at_transaction(current_txn, merchant_state, merchant_onboarding_time):
    """Calculate features incrementally using merchant state"""
    
    # Update merchant state with current transaction
    merchant_state = update_merchant_state(merchant_state, current_txn)
    
    # Get 7-day window transactions
    current_time = current_txn['txn_timestamp']
    seven_days_ago = current_time - timedelta(days=7)
    window_txs = pd.DataFrame(merchant_state.window_transactions)
    hours_since_onboarding = max(1, (current_txn['txn_timestamp'] - merchant_onboarding_time).total_seconds() / 3600)
    
    # Calculate hrs_since_last_transaction
    if merchant_state.last_transaction_time:
        hrs_since_last_transaction = (current_txn['txn_timestamp'] - merchant_state.last_transaction_time).total_seconds() / 3600
    else:
        hrs_since_last_transaction = (current_txn['txn_timestamp'] - merchant_onboarding_time).total_seconds() / 3600

    # Update last transaction time
    merchant_state.last_transaction_time = current_txn['txn_timestamp']

    # Calculate percentage difference
    if current_txn['txn_amount'] != 0:
        pct_diff = abs(current_txn['txn_amount'] - current_txn['invoice_amount']) / current_txn['txn_amount'] * 100
    else:
        pct_diff = 0 if current_txn['invoice_amount'] == 0 else 100

    # Calculate late night transaction averages
    late_night_txn_amt_avg_lt = (
        merchant_state.late_night_txn_amount_lt / merchant_state.late_night_txn_cnt_lt 
        if merchant_state.late_night_txn_cnt_lt > 0 else 0
    )
    
    # Get 7-day window transactions
    current_time = current_txn['txn_timestamp']
    seven_days_ago = current_time - timedelta(days=7)
    window_txs = pd.DataFrame(merchant_state.window_transactions)
    
    late_night_window_txs = window_txs[
        (window_txs['txn_timestamp'].dt.hour >= 1) & 
        (window_txs['txn_timestamp'].dt.hour < 4)
    ]
    
    late_night_txn_amt_avg_7d = (
        late_night_window_txs['txn_amount'].mean() 
        if not late_night_window_txs.empty else 0
    )
    
    # Calculate features
    features = {
        # Lifetime density features
        # Density features - use the updated state values
        'ip_density_lt': merchant_state.ip_density_lt,
        'device_id_density_lt': merchant_state.device_id_density_lt,
        'card_num_density_lt': merchant_state.card_num_density_lt,
        'cx_density_lt': merchant_state.cx_density_lt,

        'ip_density_7d': merchant_state.ip_density_7d,
        'device_id_density_7d': merchant_state.device_id_density_7d,
        'card_num_density_7d': merchant_state.card_num_density_7d,
        'cx_density_7d': merchant_state.cx_density_7d,
        
        # Transaction counts
        'txn_cnt_lt': merchant_state.txn_cnt_lt,
        'txn_cnt_7d': len(window_txs),
        
        # Velocity metrics
        'velocity_transaction_lt': merchant_state.txn_cnt_lt / hours_since_onboarding,
        'velocity_transaction_7d': len(merchant_state.window_transactions) / 168,  # 168 hours in 7 days
        
        # Risk metrics (lifetime)
        'chargeback_txn_cnt_pct_lt': (merchant_state.chargeback_count_lt / merchant_state.txn_cnt_lt) * 100,
        'international_txn_cnt_pct_lt': (merchant_state.int_txn_cnt_lt / merchant_state.txn_cnt_lt) * 100,
        'failed_txn_cnt_pct_lt': (merchant_state.failed_txn_cnt_pct_count_lt / merchant_state.txn_cnt_lt) * 100,
        'risky_cx_txn_cnt_pct_lt': (merchant_state.risky_customer_count_lt / merchant_state.txn_cnt_lt) * 100,
        'round_txn_cnt_pct_lt': (merchant_state.round_txn_cnt_lt / merchant_state.txn_cnt_lt) * 100,
        'cancelled_txn_cnt_pct_lt': (merchant_state.cancel_count_lt / merchant_state.txn_cnt_lt) * 100,
        'name_mismatch_txn_cnt_pct_lt': (merchant_state.name_mismatch_count_lt / merchant_state.txn_cnt_lt) * 100,
        'international_txn_cnt_pct': (merchant_state.complaint_count_lt / merchant_state.txn_cnt_lt) * 100,
        
        # txn_amount statistics
        'txn_amt_avg_lt': merchant_state.total_amount_lt / merchant_state.txn_cnt_lt,
        'avg_cx_pii_score_lt': merchant_state.pii_score_sum_lt / merchant_state.txn_cnt_lt,
        
        # txn_currency metrics
        'num_distinct_currency_used_lt': len(merchant_state.unique_currencies_lt),
        'curr_diversity_score_lt': calculate_diversity_score(merchant_state.currency_counts_lt),
        
        # Additional features referenced in original code
        'hrs_since_last_transaction': hrs_since_last_transaction,
        'invoice_and_txn_amt_diff_pct': pct_diff,
        'txn_timestamp': current_time,
        'late_night_txn_amt_avg_lt': late_night_txn_amt_avg_lt,
        'late_night_txn_amt_avg_7d': late_night_txn_amt_avg_7d,
        'late_night_txn_cnt_pct_lt': (merchant_state.late_night_txn_cnt_lt / merchant_state.txn_cnt_lt) * 100,
        'late_night_txn_cnt_pct_7d': (merchant_state.window_late_night_txn_cnt / len(window_txs)) * 100,
    }
    
    # Add 7-day window features if we have transactions
    if len(window_txs) > 0:
        window_features = calculate_window_features(window_txs)
        features.update(window_features)
    else:
        # Add zero values for window features
        features.update(get_empty_window_features())
    
    return pd.Series(features)

def calculate_all_features(transactions_df, merchants_df):
    """Calculate all features for each transaction"""
    
    # Sort transactions by txn_timestamp
    transactions_df = transactions_df.sort_values('txn_timestamp')
    
    # Get merchant onboarding times
    merchant_onboarding_times = merchants_df.set_index('mer_id')['mer_onboarding_timestamp'].to_dict()
    
    # Initialize features list
    all_features = []
    
    print(transactions_df.columns)
    # Group transactions by mer_id
    merchant_groups = transactions_df.groupby('mer_id')
    
    # Process each merchant's transactions
    for mer_id, merchant_txs in tqdm(merchant_groups, desc="Processing Merchants", total=len(merchant_groups)):
        merchant_txs = merchant_txs.sort_values('txn_timestamp')
        onboarding_time = pd.to_datetime(merchant_onboarding_times[mer_id])
        
        # Initialize merchant state
        merchant_state = MerchantState(mer_id, onboarding_time)
        
        # Process each transaction for this merchant
        for idx, transaction in merchant_txs.iterrows():
            # Calculate time features
            hour_of_day = transaction['txn_timestamp'].hour / 23.0
            day_of_week = transaction['txn_timestamp'].dayofweek / 6.0
            month_of_year = (transaction['txn_timestamp'].month - 1) / 11.0
            
            # Calculate cumulative features
            cumulative_features = calculate_features_at_transaction(
                transaction,
                merchant_state,
                onboarding_time
            )
            
            # Combine all features
            combined_features = pd.concat([
                pd.Series({
                    'txn_id': transaction['txn_id'],
                    'mer_id': mer_id,
                    'mer_business_industry': transaction['mer_business_industry'],
                    'hour_sin': np.sin(2 * np.pi * hour_of_day),
                    'hour_cos': np.cos(2 * np.pi * hour_of_day),
                    'day_sin': np.sin(2 * np.pi * day_of_week),
                    'day_cos': np.cos(2 * np.pi * day_of_week),
                    'month_sin': np.sin(2 * np.pi * month_of_year),
                    'month_cos': np.cos(2 * np.pi * month_of_year),
                    'txn_amount': transaction['txn_amount']
                }),
                cumulative_features
            ])
            
            all_features.append(combined_features)
    
    # Create final DataFrame
    features_df = pd.DataFrame(all_features)
    
    return features_df

def log_transform_and_multiply(df, density_columns, txn_count_columns, suffix):
    epsilon = 1e-10
    for density_col, txn_count_col in zip(density_columns, txn_count_columns):
        new_col_name = f"{density_col}_{suffix}"
        df[new_col_name] = np.log(df[density_col] + epsilon) * np.log(df[txn_count_col] + epsilon)
        df.drop(columns=[density_col], inplace=True)
    return df

def main(transactions_path=None, merchants_path=None, output_path=None):
    # Set default paths if not provided
    transactions_path = transactions_path or '.ares/data/transactions.csv'
    merchants_path = merchants_path or '.ares/data/merchants.csv'
    output_path = output_path or '.ares/data/transactions_with_features.csv'
    
    try:
        # Read data
        transactions_df = pd.read_csv(transactions_path)
        merchants_df = pd.read_csv(merchants_path)
    except FileNotFoundError as e:
        print(f"\nError: Could not find input file: {e.filename}")
        return
    # Check and drop fraud_type column if it exists
    if 'fraud_type' in transactions_df.columns:
        transactions_df = transactions_df.drop(columns=['fraud_type'])

    # Convert Y/N to boolean True/False
    try:
        # dummy columns        
        transactions_df['txn_currency'] = 'INR'
        transactions_df['invoice_amount'] = transactions_df['txn_amount']
        
    except Exception as e:
        print(f"\nError in adding dummy data: {str(e)}")
        return
    # Convert timestamps
    transactions_df['txn_timestamp'] = pd.to_datetime(transactions_df['txn_timestamp'])
    merchants_df['mer_onboarding_timestamp'] = pd.to_datetime(merchants_df['mer_onboarding_timestamp'])
    
    # print("chayan1")
    # Calculate features
    features_df = calculate_all_features(transactions_df, merchants_df)
    
    # Remove extra features before saving
    # 'hour_sin', 'hour_cos',
    #     'day_sin', 'day_cos', 'month_sin', 'month_cos', 'txn_amount', 'velocity_transaction_lt',
        # 'velocity_transaction_7d', 'num_distinct_currency_used_lt', 'num_distinct_currency_used_7d',
        # 'curr_diversity_score_lt', 'curr_diversity_score_7d', 'invoice_and_txn_amt_diff_pct',
    # Add these features if needed for model training

    # # Remove extra features before saving
    # features_to_keep = [
    #     'txn_id', 'mer_id', 'business_type', 'hour_sin', 'hour_cos',
    #     'day_sin', 'day_cos', 'month_sin', 'month_cos', 'txn_amount', 'ip_density_lt',
    #     'device_id_density_lt', 'card_num_density_lt', 'cx_density_lt',
    #     'ip_density_7d', 'device_id_density_7d', 'card_num_density_7d',
    #     'cx_density_7d', 'txn_cnt_lt', 'txn_cnt_7d', 'velocity_transaction_lt',
    #     'velocity_transaction_7d', 'txn_amt_avg_lt', 'txn_amt_avg_7d', 'chargeback_txn_cnt_pct_lt',
    #     'chargeback_txn_cnt_pct_7d', 'interntational_txn_cnt_pct_lt', 'interntational_txn_cnt_pct_7d', 'failed_txn_cnt_pct_lt',
    #     'failed_txn_cnt_pct_7d', 'risky_cx_txn_cnt_pct_lt', 'risky_cx_txn_cnt_pct_7d',
    #     'round_txn_cnt_pct_lt', 'round_txn_cnt_pct_7d', 'cancelled_txn_cnt_pct_lt', 'cancelled_txn_cnt_pct_7d',
    #     'name_mismatch_txn_cnt_pct_lt', 'name_mismatch_txn_cnt_pct_7d', 'num_distinct_currency_used_lt', 'num_distinct_currency_used_7d',
    #     'curr_diversity_score_lt', 'curr_diversity_score_7d', 'interntational_txn_cnt_pct',
    #     'cx_complainterntational_txn_cnt_pct_7d', 'avg_cx_pii_score_lt', 'avg_cx_pii_score_7d',
    #     'invoice_and_txn_amt_diff_pct', 'hrs_since_last_transaction', 'txn_timestamp', 'late_night_txn_amt_avg_lt',
    #     'late_night_txn_amt_avg_7d', 'late_night_txn_cnt_lt', 'late_night_txn_cnt_7d'
    # ]
    features_df = features_df[features_to_keep]
    
    txn_count_columns_lt = ['txn_cnt_lt'] * len(transformed_columns_lt)
    txn_count_columns_7d = ['txn_cnt_7d'] * len(transformed_columns_7d)

    features_df = log_transform_and_multiply(features_df, transformed_columns_lt, txn_count_columns_lt, 'score_lt')
    features_df = log_transform_and_multiply(features_df, transformed_columns_7d, txn_count_columns_7d, 'score_7d')
    
    try:
        # Save features
        features_df.to_csv(output_path, index=False)
    except Exception as e:
        print(f"\nError saving output file to {output_path}: {str(e)}")
        return
    
    print("\nFeature Generation Complete!")
    print(f"Total features generated: {len(features_df.columns)}")
    print(f"Output saved to: {output_path}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Generate features for fraud detection')
    parser.add_argument('--transactions_path', type=str, 
                       help='Path to input transactions CSV file (default: data/transactions.csv)')
    parser.add_argument('--merchants_path', type=str,
                       help='Path to input merchants CSV file (default: data/merchants.csv)')
    parser.add_argument('--output_path', type=str,
                       help='Path to output features CSV file (default: data/transactions_with_features.csv)')
    
    args = parser.parse_args()
    main(args.transactions_path, args.merchants_path, args.output_path)



