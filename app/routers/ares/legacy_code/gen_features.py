import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler

def add_temporal_features(transactions_df):
    """Add cyclical temporal features"""
    hour_of_day = transactions_df['timestamp'].dt.hour / 23.0
    day_of_week = transactions_df['timestamp'].dt.dayofweek / 6.0
    month_of_year = (transactions_df['timestamp'].dt.month - 1) / 11.0
    
    for unit, val in [('hour', hour_of_day), 
                     ('day', day_of_week), 
                     ('month', month_of_year)]:
        transactions_df[f'{unit}_sin'] = np.sin(2 * np.pi * val)
        transactions_df[f'{unit}_cos'] = np.cos(2 * np.pi * val)
    
    return transactions_df

def normalize_features(features_df):
    """Normalize features using StandardScaler (zero mean, unit variance)"""
    
    # Columns to exclude from normalization
    exclude_cols = [
        # ID and metadata columns
        'txn_id', 'mer_id', 'txn_timestamp', 'mer_business_industry',
        
        # Count-based features (should remain as raw counts)
        'txn_cnt_lt', 'txn_cnt_7d',
        'num_distinct_currency_used_lt', 'num_distinct_currency_used_7d',
        
        # Percentage features (already normalized 0-100)
        'chargeback_txn_cnt_pct_lt', 'chargeback_txn_cnt_pct_7d',
        'interntational_txn_cnt_pct_lt', 'interntational_txn_cnt_pct_7d',
        'round_txn_cnt_pct_lt', 'round_txn_cnt_pct_7d',
        'cancelled_txn_cnt_pct_lt', 'cancelled_txn_cnt_pct_7d',
        'name_mismatch_txn_cnt_pct_lt', 'name_mismatch_txn_cnt_pct_7d',
        'interntational_txn_cnt_pct', 'cx_complaint_txn_pct_7d',
        'risky_cx_txn_cnt_pct_lt', 'risky_cx_txn_cnt_pct_7d',
        'failed_txn_cnt_pct_lt', 'failed_txn_cnt_pct_7d',
        'invoice_and_txn_amt_diff_pct',
        
        # Density features (already ratios between 0-1)
        'ip_density_lt', 'ip_density_7d',
        'device_id_density_lt', 'device_id_density_7d',
        'card_num_density_lt', 'card_num_density_7d',
        'cx_density_lt', 'cx_density_7d',
        
        # Diversity scores (already normalized)
        'curr_diversity_score_lt', 'curr_diversity_score_7d'
    ]
    
    # Get numeric columns to normalize
    numeric_cols = features_df.select_dtypes(include=[np.number]).columns
    cols_to_normalize = [col for col in numeric_cols if col not in exclude_cols]
    
    # Log transform specific features
    for col in ['hrs_since_last_transaction', 'amount', 'txn_amt_avg_lt', 'txn_amt_avg_7d']:
        if col in cols_to_normalize:
            features_df[col] = np.log1p(features_df[col])
            
    # Initialize scaler
    scaler = StandardScaler()
    
    # Fit and transform
    features_df[cols_to_normalize] = scaler.fit_transform(features_df[cols_to_normalize])
    
    print(f"Features being normalized:")
    for col in cols_to_normalize:
        print(f"{col}: {features_df[col].min():.3f} to {features_df[col].max():.3f}")
    
    return features_df

def is_round_number(amount):
    """Check if an amount is a round number using multiple criteria"""
    # Check common round number patterns
    if amount % 100 == 0:  # Divisible by 100 (e.g., 500, 1000)
        return True
    if amount % 50 == 0:   # Divisible by 50 (e.g., 150, 250)
        return True
    if amount % 1000 == 0: # Divisible by 1000 (e.g., 1000, 2000)
        return True
        
    # Check if amount ends in zeros
    str_amount = str(int(amount))
    if str_amount.endswith('0' * 2):  # Ends in 00
        return True
        
    # Check psychological pricing (e.g., 999, 1999)
    if str_amount.endswith('99') or str_amount.endswith('999'):
        return True
        
    return False

def calculate_features_at_transaction(current_tx, historical_txs, merchant_onboarding_time):
    """Calculate features for a transaction based on all previous transactions"""
    
    # Include current transaction in historical data
    current_tx_df = pd.DataFrame([current_tx])
    all_txs = pd.concat([historical_txs, current_tx_df])
    
    # Get all transactions up to and including current transaction
    historical_txs = all_txs[all_txs['timestamp'] <= current_tx['timestamp']]
    
    # Get 7-day window transactions
    seven_days_ago = current_tx['timestamp'] - timedelta(days=7)
    window_txs = historical_txs[
        (historical_txs['timestamp'] >= seven_days_ago) &
        (historical_txs['timestamp'] <= current_tx['timestamp'])
    ]
    
    # Basic counts
    txn_cnt_lt = len(historical_txs)
    txn_cnt_7d = len(window_txs)
    
    # Calculate unique counts including current transaction
    unique_ips_lt = historical_txs['cx_ip'].nunique()
    unique_devices_lt = historical_txs['cx_device_id'].nunique()
    unique_cards_lt = historical_txs['cx_card_number'].nunique()
    unique_customers_lt = historical_txs['cx_id'].nunique()
    
    # For 7-day window
    unique_ips_7d = window_txs['cx_ip'].nunique() if txn_cnt_7d > 0 else 1
    unique_devices_7d = window_txs['cx_device_id'].nunique() if txn_cnt_7d > 0 else 1
    unique_cards_7d = window_txs['cx_card_number'].nunique() if txn_cnt_7d > 0 else 1
    unique_customers_7d = window_txs['cx_id'].nunique() if txn_cnt_7d > 0 else 1
    
    # Enhanced dormant time calculation
    if len(historical_txs) > 1:
        timestamps = historical_txs[historical_txs['timestamp'] < current_tx['timestamp']]['timestamp'].sort_values()
        
        # Find the longest gap in transaction history
        max_gap = timedelta(hours=0)
        recent_gap = timedelta(hours=0)
        
        for i in range(1, len(timestamps)):
            gap = timestamps.iloc[i] - timestamps.iloc[i-1]
            if gap > max_gap:
                max_gap = gap
        
        # Check gap between last historical transaction and current transaction
        if len(timestamps) > 0:
            current_gap = current_tx['timestamp'] - timestamps.iloc[-1]
            recent_gap = current_gap
            max_gap = max(max_gap, current_gap)
        
        # Calculate weighted dormant time that emphasizes recent gaps
        hrs_since_last_transaction = max(
            max_gap.total_seconds() / 3600,  # Convert to hours
            recent_gap.total_seconds() / 3600 * 1.5  # Amplify recent gaps
        )
        
        # Additional weight if the gap is suspiciously long (> 30 days)
        if hrs_since_last_transaction > 460:  # 30 days in hours
            hrs_since_last_transaction *= 1.5
    else:
        # For first transaction, use time since merchant onboarding
        hrs_since_last_transaction = (current_tx['timestamp'] - merchant_onboarding_time).total_seconds() / 3600
    
    # txn_currency metrics
    num_distinct_currency_used_lt = historical_txs['txn_currency'].nunique()
    num_distinct_currency_used_7d = window_txs['txn_currency'].nunique() if txn_cnt_7d > 0 else 0
    
    # Calculate txn_currency diversity scores
    currency_counts_lt = historical_txs['txn_currency'].value_counts(normalize=True)
    currency_counts_7d = window_txs['txn_currency'].value_counts(normalize=True) if txn_cnt_7d > 0 else pd.Series([1.0])
    
    # Shannon diversity index for txn_currency usage
    curr_diversity_score_lt = -(currency_counts_lt * np.log(currency_counts_lt)).sum()
    curr_diversity_score_7d = -(currency_counts_7d * np.log(currency_counts_7d)).sum()
    
    # Calculate round number percentages with improved detection
    round_txn_lt = historical_txs['amount'].apply(is_round_number).mean() * 100
    round_txn_7d = window_txs['amount'].apply(is_round_number).mean() * 100 if txn_cnt_7d > 0 else 0
    
    # Calculate percentage difference with safe division
    if current_tx['txn_amount'] != 0:
        pct_diff = abs(current_tx['txn_amount'] - current_tx['invoice_amount']) / current_tx['txn_amount'] * 100
    else:
        pct_diff = 0 if current_tx['invoice_amount'] == 0 else 100  # 100% difference if invoice amount exists
    
    features = {
        # Lifetime density features
        'ip_density_lt': txn_cnt_lt / max(1, unique_ips_lt),
        'device_id_density_lt': txn_cnt_lt / max(1, unique_devices_lt),
        'card_num_density_lt': txn_cnt_lt / max(1, unique_cards_lt),
        'cx_density_lt': txn_cnt_lt / max(1, unique_customers_lt),
        
        # 7-day window density features
        'ip_density_7d': txn_cnt_7d / unique_ips_7d,
        'device_id_density_7d': txn_cnt_7d / unique_devices_7d,
        'card_num_density_7d': txn_cnt_7d / unique_cards_7d,
        'cx_density_7d': txn_cnt_7d / unique_customers_7d,
        
        # Transaction counts
        'txn_cnt_lt': txn_cnt_lt,
        'txn_cnt_7d': txn_cnt_7d,
        
        # Velocity metrics
        'velocity_transaction_lt': txn_cnt_lt / max(1, (current_tx['timestamp'] - merchant_onboarding_time).total_seconds() / 3600),
        'velocity_transaction_7d': txn_cnt_7d / 168,  # 168 hours in 7 days
        
        # Amount statistics
        'txn_amt_avg_lt': historical_txs['amount'].mean(),
        'txn_amt_avg_7d': window_txs['amount'].mean() if txn_cnt_7d > 0 else 0,
        
        # Risk metrics
        'chargeback_txn_cnt_pct_lt': historical_txs['is_chargeback'].mean() * 100,
        'chargeback_txn_cnt_pct_7d': window_txs['is_chargeback'].mean() * 100 if txn_cnt_7d > 0 else 0,
        'interntational_txn_cnt_pct_lt': historical_txs['is_cx_international'].mean() * 100,
        'interntational_txn_cnt_pct_7d': window_txs['is_cx_international'].mean() * 100 if txn_cnt_7d > 0 else 0,
        'failed_txn_cnt_pct_lt': (~historical_txs['txn_status']).mean() * 100,
        'failed_txn_cnt_pct_7d': (~window_txs['txn_status']).mean() * 100 if txn_cnt_7d > 0 else 0,
        
        # Customer risk metrics
        'risky_cx_txn_cnt_pct_lt': historical_txs['is_cx_risky'].mean() * 100,
        'risky_cx_txn_cnt_pct_7d': window_txs['is_cx_risky'].mean() * 100 if txn_cnt_7d > 0 else 0,
        
        # Round number transactions with improved detection
        'round_txn_cnt_pct_lt': round_txn_lt,
        'round_txn_cnt_pct_7d': round_txn_7d,
        
        # Cancellation metrics
        'cancelled_txn_cnt_pct_lt': historical_txs['is_cancelled'].mean() * 100,
        'cancelled_txn_cnt_pct_7d': window_txs['is_cancelled'].mean() * 100 if txn_cnt_7d > 0 else 0,
        
        # Name mismatch metrics
        'name_mismatch_txn_cnt_pct_lt': (~historical_txs['is_cardholder_name_match']).mean() * 100,
        'name_mismatch_txn_cnt_pct_7d': (~window_txs['is_cardholder_name_match']).mean() * 100 if txn_cnt_7d > 0 else 0,
        
        # txn_currency metrics
        'num_distinct_currency_used_lt': num_distinct_currency_used_lt,
        'num_distinct_currency_used_7d': num_distinct_currency_used_7d,
        'curr_diversity_score_lt': curr_diversity_score_lt,
        'curr_diversity_score_7d': curr_diversity_score_7d,
        
        # Customer complaint metrics
        'interntational_txn_cnt_pct': historical_txs['has_cx_complaint'].mean() * 100,
        'cx_complaint_txn_pct_7d': window_txs['has_cx_complaint'].mean() * 100 if txn_cnt_7d > 0 else 0,
        
        # PII score metrics
        'avg_cx_pii_score_lt': historical_txs['cx_pii_linkage_score'].mean(),
        'avg_cx_pii_score_7d': window_txs['cx_pii_linkage_score'].mean() if txn_cnt_7d > 0 else 0,
        
        # Invoice amount difference
        'invoice_and_txn_amt_diff_pct': pct_diff,
        
        # Add actual timestamp
        'txn_timestamp': current_tx['timestamp'],
        
        # Dormant time
        'hrs_since_last_transaction': hrs_since_last_transaction
    }
    
    return pd.Series(features)

def calculate_all_features(transactions_df, merchants_df):
    """Calculate all features for each transaction"""
    
    # Sort transactions by timestamp
    transactions_df = transactions_df.sort_values('timestamp')
    
    # Get merchant onboarding times
    merchant_onboarding_times = merchants_df.set_index('mer_id')['mer_onboarding_timestamp'].to_dict()
    
    # Initialize features list
    all_features = []
    
    # Group transactions by mer_id
    merchant_groups = transactions_df.groupby('mer_id')
    
    # Process each merchant's transactions
    for mer_id, merchant_txs in merchant_groups:
        merchant_txs = merchant_txs.sort_values('timestamp')
        onboarding_time = pd.to_datetime(merchant_onboarding_times[mer_id])
        
        # Process each transaction for this merchant
        for idx, transaction in merchant_txs.iterrows():
            # Calculate time features
            hour_of_day = transaction['timestamp'].hour / 23.0
            day_of_week = transaction['timestamp'].dayofweek / 6.0
            month_of_year = (transaction['timestamp'].month - 1) / 11.0
            
            # Calculate cumulative features
            cumulative_features = calculate_features_at_transaction(
                transaction,
                merchant_txs.loc[:idx],  # Include all transactions up to current one
                onboarding_time
            )
            
            # Combine all features
            combined_features = pd.concat([
                pd.Series({
                    'txn_id': transaction['txn_id'],
                    'mer_id': mer_id,
                    'mer_business_industry': transaction['mer_business_industry'],
                    'hour_sin': np.sin(2 * np.pi * hour_of_day),
                    'hour_cos': np.cos(2 * np.pi * hour_of_day),
                    'day_sin': np.sin(2 * np.pi * day_of_week),
                    'day_cos': np.cos(2 * np.pi * day_of_week),
                    'month_sin': np.sin(2 * np.pi * month_of_year),
                    'month_cos': np.cos(2 * np.pi * month_of_year),
                    'amount': transaction['amount']
                }),
                cumulative_features
            ])
            
            all_features.append(combined_features)
    
    # Create final DataFrame
    features_df = pd.DataFrame(all_features)
    
    # Normalize features
    features_df = normalize_features(features_df)
    
    return features_df

def main(transactions_path=None, merchants_path=None, output_path=None):
    # Set default paths if not provided
    transactions_path = transactions_path or 'data/transactions.csv'
    merchants_path = merchants_path or 'data/merchants.csv'
    output_path = output_path or 'data/transactions_with_features.csv'
    
    try:
        # Read data
        transactions_df = pd.read_csv(transactions_path)
        merchants_df = pd.read_csv(merchants_path)
    except FileNotFoundError as e:
        print(f"\nError: Could not find input file: {e.filename}")
        return
    
    # Convert timestamps
    transactions_df['timestamp'] = pd.to_datetime(transactions_df['timestamp'])
    merchants_df['onboarding_timestamp'] = pd.to_datetime(merchants_df['onboarding_timestamp'])
    
    # Calculate features
    features_df = calculate_all_features(transactions_df, merchants_df)
    
    try:
        # Save features
        features_df.to_csv(output_path, index=False)
    except Exception as e:
        print(f"\nError saving output file to {output_path}: {str(e)}")
        return
    
    print("\nFeature Generation Complete!")
    print(f"Total features generated: {len(features_df.columns)}")
    print(f"Output saved to: {output_path}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Generate features for fraud detection')
    parser.add_argument('--transactions_path', type=str, 
                       help='Path to input transactions CSV file (default: data/transactions.csv)')
    parser.add_argument('--merchants_path', type=str,
                       help='Path to input merchants CSV file (default: data/merchants.csv)')
    parser.add_argument('--output_path', type=str,
                       help='Path to output features CSV file (default: data/transactions_with_features.csv)')
    
    args = parser.parse_args()
    main(args.transactions_path, args.merchants_path, args.output_path)
