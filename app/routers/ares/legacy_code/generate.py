import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import os
from tqdm import tqdm

random.seed(42)
np.random.seed(42)

def generate_merchant_data(n_merchants=1000):
    """Generate synthetic merchant dataset with 20% anomalous merchants and specific red flags"""
    
    merchants = []
    mer_business_industrys = ['Restaurant', 'Retail', 'Gambling']
    
    # Enhanced business type specific configurations
    business_configs = {
        'Restaurant': {
            'avg_transaction_range': (25, 150),
            'typical_employees': (5, 30),
            'expected_monthly_revenue': (500000, 2000000)
        },
        'Retail': {
            'avg_transaction_range': (30, 300),
            'typical_employees': (3, 50),
            'expected_monthly_revenue': (1000000, 5000000)
        },
        'Entertainment': {
            'avg_transaction_range': (40, 200),
            'typical_employees': (10, 100),
            'expected_monthly_revenue': (2000000, 8000000)
        },
        'Services': {
            'avg_transaction_range': (50, 400),
            'typical_employees': (5, 40),
            'expected_monthly_revenue': (800000, 3000000)
        },
        'Online Shop': {
            'avg_transaction_range': (20, 250),
            'typical_employees': (2, 15),
            'expected_monthly_revenue': (500000, 4000000)
        },
        'Gambling': {
            'avg_transaction_range': (100, 1000),
            'typical_employees': (20, 200),
            'expected_monthly_revenue': (5000000, 20000000)
        }
    }
    
    n_fraud = int(n_merchants * 0.2)  # 20% anomalous merchants
    
    # Generate base timestamp range (last 2 years)
    current_date = datetime.now()
    date_range_start = current_date - timedelta(days=730)  # 2 years ago
    
    for i in range(n_merchants):
        is_fraud = i < n_fraud
        mer_business_industry = np.random.choice(mer_business_industrys)
        config = business_configs[mer_business_industry]
        
        # Generate onboarding timestamp
        if is_fraud:
            # Fraudulent merchants tend to be newer (last 3-12 months)
            days_ago = random.randint(90, 365)
        else:
            # Legitimate merchants can be older (up to 2 years)
            days_ago = random.randint(30, 730)
        
        onboarding_timestamp = current_date - timedelta(days=days_ago)
        
        # Base transaction size and monthly revenue
        avg_transaction_size = np.random.uniform(*config['avg_transaction_range'])
        monthly_revenue = np.random.uniform(*config['expected_monthly_revenue'])
        
        # Generate correlated risk factors for fraudulent merchants
        is_online_business = mer_business_industry == 'Online Shop'
        num_employees = int(np.random.uniform(*config['typical_employees']))
        
        # Risk multiplier for fraud merchants
        risk_multiplier = 0.8 if is_fraud else 0.1
        
        # GST related flags
        monthly_gst_threshold = 2000000  # 20 Lacs
        should_have_gst = monthly_revenue > monthly_gst_threshold
        gst_risk_flag = False
        
        if is_fraud and should_have_gst:
            gst_risk_flag = random.random() < 0.8  # 80% chance of GST issues for fraud merchants
        
        # Generate merchant data with specific red flags
        merchant = {
            'mer_id': f'M{str(i).zfill(6)}',
            'mer_onboarding_timestamp': onboarding_timestamp,
            'mer_incorporation_date': onboarding_timestamp - timedelta(days=random.randint(90, 1825)),
            'mer_first_txn_date': onboarding_timestamp + timedelta(days=random.randint(7, 30)),
            'mer_lst_txn_date': current_date - timedelta(days=random.randint(1, 30)),
            'mer_industry': mer_business_industry,
            'mer_industry_mca': mer_business_industry,
            'mer_fraud_flag': is_fraud,
            'mer_avg_txn_size': avg_transaction_size,
            'mer_total_txn': int(monthly_revenue / avg_transaction_size * random.uniform(1, 12)),
            'mer_total_txn_fy': int(monthly_revenue / avg_transaction_size * random.uniform(1, 3)),
            'mer_gst_risk_flag': gst_risk_flag,
            'mer_mca_fillings_risk_flag': is_fraud and random.random() < 0.6,
            'mer_directors_risk_flag': is_fraud and random.random() < 0.5,
            'mer_num_employees': num_employees,
            'mer_epfo_reg_status': not (is_fraud and num_employees > 20 and random.random() < 0.8),
            'mer_is_sanctioned': is_fraud and random.random() < 0.3,
            'mer_is_online_business': is_online_business,
            'mer_online_presence_flag': not (is_fraud and is_online_business and random.random() < 0.7),
            'mer_tax_irregularity_flag': not (is_fraud and random.random() < 0.7),
            'mer_is_pan_compatible': not (is_fraud and random.random() < 0.6),
            'mer_is_address_compatible': not (is_fraud and random.random() < 0.5),
            'mer_prior_fraud_investigation_flag': is_fraud and random.random() < 0.4,
            'mer_is_MCA_submission_taken': not (is_fraud and random.random() < 0.7),
            'mer_udyam_cert_flag' : not (is_fraud and random.random() < 0.7),
            'mer_high_late_night_txn': is_fraud and random.random() < 0.3  # 30% of fraud merchants
        }
        
        # Add logical correlations between red flags
        if merchant['mer_is_sanctioned']:
            merchant['mer_prior_fraud_investigation_flag'] = True
            merchant['mer_directors_risk_flag'] = True
        
        if merchant['mer_num_employees'] > 20 and not merchant['mer_epfo_reg_status']:
            merchant['mer_gst_risk_flag'] = True
        
        if merchant['mer_is_online_business'] and not merchant['mer_online_presence_flag']:
            merchant['mer_gst_risk_flag'] = True
            merchant['mer_is_address_compatible'] = False
        

        
        merchants.append(merchant)
    
    return pd.DataFrame(merchants)

def generate_transaction_time(is_fraud, mer_business_industry, base_date=None, high_late_night_txn=False):
    """Generate transaction timestamp based on merchant fraud status and business type"""
    
    if base_date is None:
        base_date = datetime.now().date()
    
    # Business-specific hour distributions
    if (high_late_night_txn and is_fraud) or mer_business_industry == 'Gambling':
        # 70% chance of transactions between 1 AM and 4 AM
        if random.random() < 0.7:
            hour = random.randint(1, 4)
        else:
            hour = random.randint(0, 23)
    # elif mer_business_industry == 'Gambling':
    #     hour = random.randint(0, 23)
    elif mer_business_industry in ['Restaurant', 'Entertainment']:
        hour = random.randint(11, 23)
    else:  # Retail, Services, Online Shop
        hour = random.randint(9, 18)
    
    minute = random.randint(0, 59)
    second = random.randint(0, 59)
    
    return datetime.combine(base_date, 
                          datetime.min.time().replace(hour=hour, 
                                                    minute=minute, 
                                                    second=second))

def generate_transaction_data(merchant_df, n_transactions=100000):
    """Generate synthetic transaction dataset with various fraud patterns"""
    
    merchant_pattern = {
        'transaction_count': 0,
        'last_transaction_time': None,
        'total_value': 0,
        'chargeback_count': 0,
        'failed_count': 0,
        'customer_frequencies': {},
        'device_frequencies': {},
        'ip_frequencies': {},
        'card_frequencies': {}
    }
    
    transactions = []
    currencies = ['INR', 'USD', 'EUR', 'GBP']
    ip_pools = {
        'domestic': [f'192.168.{i}.{j}' for i in range(1, 10) for j in range(1, 20)],
        'international': [f'10.{i}.{j}.{k}' for i in range(1, 5) for j in range(1, 5) for k in range(1, 10)]
    }
    
    # Create customer pools
    n_customers = 10000
    customers = []
    high_risk_customer_ratio = 0.15  # 15% of customers will have high PII linkage

    for i in range(n_customers):
        # Assign high PII linkage score to some customers
        is_high_pii = random.random() < high_risk_customer_ratio
        pii_linkage_score = random.uniform(0.85, 1.0) if is_high_pii else random.uniform(0, 0.3)
        
        customers.append({
            'customer_id': f'C{str(i).zfill(6)}',
            'device_id': f'D{str(random.randint(0, 999)).zfill(6)}',
            'card_number': f'4{str(random.randint(0, 4999)).zfill(15)}',
            'ip_address': np.random.choice(ip_pools['domestic']),
            'pii_linkage_score': pii_linkage_score,
            'is_high_pii': is_high_pii
        })
    
    # Create merchant-specific customer pools
    merchant_customers = {}
    for _, merchant in merchant_df.iterrows():
        if merchant['mer_fraud_flag']:
            # Fraud merchants: determine if they target high-PII customers
            targets_high_pii = random.random() < 0.2  # 20% of fraud merchants target high-PII customers
            
            if targets_high_pii:
                # Select more high-PII customers for this merchant
                high_pii_customers = [c for c in customers if c['is_high_pii']]
                normal_customers = [c for c in customers if not c['is_high_pii']]
                
                # Select customers with bias towards high-PII ones
                n_merchant_customers = random.randint(50, 200)
                high_pii_count = int(n_merchant_customers * 0.7)  # 70% high-PII customers
                normal_count = n_merchant_customers - high_pii_count
                
                merchant_pool = (
                    random.sample(high_pii_customers, min(high_pii_count, len(high_pii_customers))) +
                    random.sample(normal_customers, min(normal_count, len(normal_customers)))
                )
            else:
                # Normal customer distribution
                n_merchant_customers = random.randint(50, 200)
                merchant_pool = random.sample(customers, n_merchant_customers)
        else:
            # Legitimate merchants get random customer distribution
            n_merchant_customers = random.randint(50, 200)
            merchant_pool = random.sample(customers, n_merchant_customers)
        
        merchant_customers[merchant['mer_id']] = {
            'customers': merchant_pool,
            'is_high_density': len(merchant_pool) <= 20,  # Track if this merchant has high customer density
            'targets_high_pii': merchant['mer_fraud_flag'] and targets_high_pii
        }

    
    for idx in range(n_transactions):
        # Select merchant
        if random.random() < 0.4:
            merchant = merchant_df[merchant_df['mer_fraud_flag']].sample(n=1).iloc[0]
        else:
            merchant = merchant_df[~merchant_df['mer_fraud_flag']].sample(n=1).iloc[0]
        
        # Select customer from merchant's pool
        merchant_pool = merchant_customers[merchant['mer_id']]
        customer = random.choice(merchant_pool['customers'])
        
        # Higher chance of fraud transactions for high-density merchants
        if merchant['mer_fraud_flag'] and merchant_pool['is_high_density']:
            is_fraud_transaction = random.random() < 1  # 100% chance of fraud for high-density merchants
        elif merchant['mer_fraud_flag']:
            is_fraud_transaction = random.random() < 1  # 100% chance of fraud for normal-density fraud merchants
        else:
            is_fraud_transaction = random.random() < 0.00  # 0% chance for legitimate merchants
        
        # Generate base txn_amount
        txn_amount = np.random.normal(merchant['mer_avg_txn_size'], merchant['mer_avg_txn_size'] * 0.2)
        
        # Round number pattern for transactions
        if merchant['mer_fraud_flag'] and random.random() < 0.5:  # 50% chance for fraud merchants
            txn_amount = round(txn_amount / 100) * 100 # Round to nearest 100
            is_fraud_transaction = True
        elif random.random() < 0.1:  # 10% chance for normal merchants
            txn_amount = round(txn_amount / 100) * 100  # Round to nearest 100
        
        # Generate timestamp...
        merchant_onboarding = pd.to_datetime(merchant['mer_onboarding_timestamp'])
        
        if merchant['mer_fraud_flag']:
            min_delay_days = random.randint(30, 90)
            base_timestamp = merchant_onboarding + timedelta(days=min_delay_days)
        else:
            min_delay_days = random.randint(1, 14)
            base_timestamp = merchant_onboarding + timedelta(days=min_delay_days)
        
        base_timestamp = min(base_timestamp, datetime.now())
        timestamp = generate_transaction_time(merchant['mer_fraud_flag'], 
                                           merchant['mer_industry'],
                                           base_date=base_timestamp.date(),
                                           high_late_night_txn=merchant['mer_high_late_night_txn'])
        
        # Rest of the transaction generation logic remains the same...
        # Generate customer details
        is_international = random.random() < (0.7 if is_fraud_transaction else 0.1)
        customer_id = customer['customer_id']
        ip_address = customer['ip_address']
        device_id = customer['device_id']
        card_number = customer['card_number']
        
        # Generate transaction status and related flags
        is_chargeback = random.random() < (0.5 if is_fraud_transaction else 0.02)
        txn_status = random.random() > (0.5 if is_fraud_transaction else 0.05)
        reversal_or_cancellation = random.random() < (0.5 if is_fraud_transaction else 0.05)
        customer_complain = random.random() < (0.5 if is_fraud_transaction else 0.01)
        is_card_holder_name_mismatch = is_fraud_transaction and random.random() > 0.7
        is_cx_risky = is_international or is_fraud_transaction
        
        # Calculate PII linkage score
        pii_linkage_score = customer['pii_linkage_score']
        
        transaction = {
            'txn_id': f'T{str(idx).zfill(8)}',
            'mer_id': merchant['mer_id'],
            'mer_business_industry': merchant['mer_industry'],
            'txn_timestamp': timestamp,
            'txn_amount': txn_amount,
            'is_fraud_transaction': is_fraud_transaction,
            'cx_id': customer_id,
            'cx_ip': ip_address,
            'cx_device_id': device_id,
            'cx_card_number': card_number,
            'cx_pii_linkage_score': pii_linkage_score,
            'is_cardholder_name_match': not is_card_holder_name_mismatch,
            'is_chargeback': is_chargeback,
            'is_cx_international': is_international,
            'txn_status': txn_status,
            'is_cx_risky': is_cx_risky,
            'invoice_amount': txn_amount * (random.uniform(0.5, 2.0) if is_fraud_transaction else random.uniform(0.9, 1.1)),
            'is_cancelled': reversal_or_cancellation,
            'txn_currency': np.random.choice(currencies) if is_international else 'INR',
            'has_cx_complaint': customer_complain
        }
        
        # Update merchant patterns
        merchant_pattern['transaction_count'] += 1
        merchant_pattern['last_transaction_time'] = timestamp
        merchant_pattern['total_value'] += txn_amount
        if is_chargeback:
            merchant_pattern['chargeback_count'] += 1
        if not txn_status:
            merchant_pattern['failed_count'] += 1
            
        merchant_pattern['customer_frequencies'][customer_id] = merchant_pattern['customer_frequencies'].get(customer_id, 0) + 1
        merchant_pattern['device_frequencies'][device_id] = merchant_pattern['device_frequencies'].get(device_id, 0) + 1
        merchant_pattern['ip_frequencies'][ip_address] = merchant_pattern['ip_frequencies'].get(ip_address, 0) + 1
        merchant_pattern['card_frequencies'][card_number] = merchant_pattern['card_frequencies'].get(card_number, 0) + 1
        
        transactions.append(transaction)
    
    # Create DataFrame and sort by timestamp
    df = pd.DataFrame(transactions)
    df = df.sort_values('txn_timestamp')
    
    return df

def main():
    # Generate datasets
    merchant_df = generate_merchant_data()
    transaction_df = generate_transaction_data(merchant_df)
    os.makedirs('data', exist_ok=True)

    # Drop the 'mer_high_late_night_txn' column before saving
    merchant_df = merchant_df.drop(columns=['mer_high_late_night_txn'])

    # Save to CSV
    merchant_df.to_csv('data/merchants.csv', index=False)
    transaction_df.to_csv('data/transactions.csv', index=False)

    # Print general statistics
    print("\nDataset Statistics:")
    print(f"Total merchants: {len(merchant_df)}")
    print(f"Anomalous merchants: {len(merchant_df[merchant_df['mer_fraud_flag']])}")
    print(f"Total transactions: {len(transaction_df)}")
    print(f"Fraudulent transactions: {len(transaction_df[transaction_df['is_fraud_transaction']])}")

if __name__ == "__main__":
    main()