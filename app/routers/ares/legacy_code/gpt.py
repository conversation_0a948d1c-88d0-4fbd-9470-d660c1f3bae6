import os
import json
from pathlib import Path
from dotenv import load_dotenv
from groq import Groq
import sys
import subprocess
import tempfile

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
from config.dataframe_schema import Schema

# Load environment variables
load_dotenv()

# Context file for storing conversation history
CONTEXT_FILE = os.path.join(project_root, "data", "context.json")

def initialize_context():
    """Initialize context file if it doesn't exist."""
    if not os.path.exists(CONTEXT_FILE):
        os.makedirs(os.path.dirname(CONTEXT_FILE), exist_ok=True)  
        with open(CONTEXT_FILE, "w") as file:   
            json.dump({"history": []}, file)

def update_context(user_message, llm_response):
    """Update the context with the user's message and the LLM's response."""
    with open(CONTEXT_FILE, "r") as file:
        context = json.load(file)

    context["history"].append({"user": user_message, "llm": llm_response})

    with open(CONTEXT_FILE, "w") as file:
        json.dump(context, file, indent=4)

def get_context():
    """Retrieve the conversation history."""
    with open(CONTEXT_FILE, "r") as file:
        context = json.load(file)
    return context["history"]

def generate_code_prompt(user_prompt):
    """Generate a prompt for the LLM to create code based on the datasets and user input."""
    guide_path = Path(__file__).parent / "gpt_guide.py"
    with open(guide_path, 'r') as file:
        example_code = file.read()

    context = get_context()
    context_string = "\n".join([
        f"User: {item['user']}\nLLM: {item['llm']}" for item in context
    ])

    return f"""
Conversation History:
{context_string}

Generate Python code to do the following: {user_prompt}.
Requirements:
- Input: transactions_df, merchants_df
- Example guide that shows how output code should look like:
{example_code}
Do not explain the code, only generate executable Python code.
"""

def get_llm_output(prompt):
    """Get the generated code from Groq API and stream to console."""
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        raise ValueError("GROQ_API_KEY not found in environment variables")

    client = Groq(api_key=api_key)
    system_prompt = f"""
You are a Python code generation assistant specializing in data analysis with pandas. Your task is to generate code that processes transaction and merchant data. Follow these rules:
- No greetings or other text. Just the code.
- Always use pandas for data operations
- Include all necessary imports
- Use efficient pandas operations (vectorized over loops)
- Handle potential data type issues
- Add clear comments explaining logic
- Return only executable code without explanations

Schema:
Transactions DataFrame:
{Schema['Transactions']}

Merchants DataFrame:
{Schema['Merchants']}
"""

    try:
        stream = client.chat.completions.create(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            model="llama-3.3-70b-versatile",
            temperature=0.2,
            max_tokens=30000,
            top_p=1,
            stream=True
        )

        full_response = ""
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                print(content, end='', flush=True)
                full_response += content
        cleaned_response = full_response.strip()
        if cleaned_response.startswith("```python"):
            cleaned_response = cleaned_response[9:]  # Remove ```python
        if cleaned_response.startswith("```"):
            cleaned_response = cleaned_response[3:]  # Remove ```
        if cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-3]  # Remove trailing ```

        return cleaned_response.strip()

    except Exception as e:
        raise RuntimeError(f"Failed to get LLM output: {e}")
    
def write_to_file(code):
    """Write the generated code to a temporary file."""
    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".py") as temp_file:
            temp_file.write(code.encode())
            temp_file_path = temp_file.name
        
        print(f"\nCode has been written to temporary file '{temp_file_path}'")
        return temp_file_path
        
    except Exception as e:
        print(f"\nError writing to file: {e}")
        raise

def execute_generated_code(file_path):
    """Execute the generated Python file and capture its stdout."""
    try:
        result = subprocess.run(
            ["python3", str(file_path)], capture_output=True, text=True, check=True
        )
        print(f"\nExecution Output:\n{result.stdout}")
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"\nError executing file: {e.stderr}")
        raise RuntimeError("Failed to execute the generated code.")
    finally:
        # Remove the temporary file after execution
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"Temporary file '{file_path}' has been removed.")
def main():    
    initialize_context()
    while True:  # Start an infinite loop
        user_prompt = input("\nWhat kind of insights would you like? ").strip()
        if user_prompt.lower() == "quit":  # Check for the quit command
            print("Exiting the chat.")
            break  

        prompt = generate_code_prompt(user_prompt)
        generated_code = get_llm_output(prompt)

        # Store user prompt and LLM response in context
        update_context(user_prompt, generated_code)

        # Write code to file and get the file path
        output_file = write_to_file(generated_code)

        # Execute the generated script
        execution_output = execute_generated_code(output_file)

        # Optionally: Get feedback from LLM or other post-execution steps
        print("\nScript executed successfully.")

if __name__ == "__main__":
    main()
