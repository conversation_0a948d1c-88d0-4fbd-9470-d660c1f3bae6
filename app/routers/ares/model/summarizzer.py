import pandas as pd
import os
from groq import Groq
from dotenv import load_dotenv
from app.routers.ares.model.inference import load_model, predict_transaction
from app.routers.ares.model.llm_transaction_analysis import generate_analysis
from app.routers.ares.model.merchant_analysis import main as analyze_merchant
from colorama import Fore, Style, init

load_dotenv()

# Initialize colorama
init(autoreset=True)

def format_merchant_flags(merchant_flags):
    """Format merchant red flags with risk levels for the prompt"""
    formatted_flags = []
    
    # Unpack the returned tuples into readable format with more descriptive names
    flag_names = ['Goods and Services Tax Violation', 'Discrepancy in Revenue Reporting', 'Risk Associated with Directors', 
                 'Employees Provident Fund Organisation Violation', 'Mismatch in EPFO Transactions', 'Appearance in Sanction Lists',
                 'Inconsistency in Online Business Presence', 'Discrepancy in Permanent Account Number', 'Inconsistency in Business Address',
                 'Non-Compliance with Mandatory Corporate Affairs Submission', 'History of Prior Financial Fraud', 'Missing Udyam Registration Certificate']
    
    for flag, (has_violation, risk_level) in zip(flag_names, merchant_flags):
        if has_violation:
            formatted_flags.append(f"[{risk_level.value}] {flag}")
    
    return "\n".join(formatted_flags) if formatted_flags else "No merchant red flags detected."

def generate_summary_prompt(merchant_data, llm_flags, merchant_flags):
    """Generate an optimized summary prompt including all relevant data sources."""
    if merchant_data is None:
        return f"""
You are analyzing a merchant with no transaction history. Your response should be a concise, 4-line summary covering:

1. **Business Overview**:
   - No transaction data available

2. **Transaction Analysis**:
   - Status: No transactions found
   - Key Indicators: {llm_flags}

3. **Merchant Risk Factors**:
   - Red Flags: {merchant_flags}

**Task**: Provide a detailed but concise summary of potential risks and anomalies, focusing on:
- Business compliance issues and deviations from norms.
- Key red flags affecting the merchant's risk profile.

Format your response as a structured risk assessment using clear, direct language. Avoid generic terms like "model verdict" and focus on specific, measurable indicators.
"""
    
    return f"""
You are analyzing a merchant and a transaction for potential fraud. Your response should be a concise, 4-line summary covering:

1. **Business Overview**:
   - Merchant ID: {merchant_data['mer_id'].iloc[0]}
   - Industry: {merchant_data['mer_business_industry'].iloc[0]}

2. **Transaction Analysis**:
   - Status: {'Normal'}
   - Key Indicators: {llm_flags}

3. **Merchant Risk Factors**:
   - Red Flags: {merchant_flags}

**Task**: Provide a detailed but concise summary of potential risks and anomalies, including:
- Business compliance issues and deviations from norms.
- Patterns of anomalous transaction behavior.
- Key red flags affecting the merchant's risk profile.

Format your response as a structured risk assessment using clear, direct language. Avoid generic terms like "model verdict" and focus on specific, measurable indicators.
"""

def get_merchant_summary(prompt):
    """Get summary from Groq LLM API"""
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        raise ValueError("GROQ_API_KEY not found in environment variables")
        
    client = Groq(api_key=api_key)
    
    system_message = """You are a fraud analyst summarizer, helping the novice understand the merchant and transaction data. Summarize the merchant and transaction data, including below points:
1. Business profile and KYC status
2. Communication patterns
3. Transaction patterns
4. Key red flags

No greetings.
Be concise and focus on risk indicators. 
Don't mention technical terms. Just use red flags.
Be human like in the summary. Keep it a short paragraph.

Example bad response: "a 97.5% reconstruction error", "a high reconstruction error"
"""

    chat_completion = client.chat.completions.create(
        messages=[
            {
                "role": "system",
                "content": system_message
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        model="llama3-8b-8192",
        temperature=0.2,
        max_tokens=1024,
        top_p=1,
        stream=False
    )
    
    return chat_completion.choices[0].message.content

def main(mer_id, feature_file, transactions_file, merchants_file, tolerance=0):
    # Load model and data
    model, scaler, threshold, business_columns = load_model()
    
    try:
        # Load transaction features
        transactions_with_features_df = pd.read_csv(feature_file)
        
        # Load transactions with is_fraud flag
        transactions_df = pd.read_csv(transactions_file)
        if 'fraud_type' in transactions_df.columns:
            transactions_df.drop(columns=['fraud_type', 'red_flags', 'fraud_indicators'], inplace=True) 
        
        # Merge the two dataframes on txn_id to get the is_fraud flag in transactions_with_features_df
        transactions_with_features_df = transactions_with_features_df.merge(transactions_df[['txn_id', 'is_fraud_transaction']], on='txn_id', how='left')
        
        # Select the last transaction for the given merchant ID
        if mer_id:
            # Filter transactions for the given merchant ID
            merchant_transactions = transactions_with_features_df[transactions_with_features_df['mer_id'] == mer_id]
            if len(merchant_transactions) == 0:
                # No transactions found for merchant
                transaction = None
                llm_flags = "No transaction history available for this merchant."
            else:
                print(merchant_transactions)
                # Sort by timestamp and select the last transaction
                last_transaction = merchant_transactions.sort_values('txn_timestamp').iloc[-1]
                txn_id = last_transaction['txn_id']
                transaction = transactions_with_features_df[transactions_with_features_df['txn_id'] == txn_id]
                mer_business_industry = transaction['mer_business_industry'].values[0]
                
                # Convert is_fraud_transaction to boolean
                transactions_with_features_df['is_fraud_transaction'] = transactions_with_features_df['is_fraud_transaction'].astype(bool)

                # Filter normal transactions to be of the same business type
                normal_transactions = transactions_with_features_df[(~transactions_with_features_df['is_fraud_transaction'])]
                print(Fore.BLUE + f"Number of normal transactions for business type {mer_business_industry}: {len(normal_transactions)}")
                transactions_with_features_df = transactions_with_features_df.drop(columns=['is_fraud_transaction'])
                
                # One-hot encode mer_business_industry for normal transactions
                normal_mer_business_industrys = pd.get_dummies(normal_transactions['mer_business_industry'], prefix='business')
                for col in business_columns:
                    if col not in normal_mer_business_industrys.columns:
                        normal_mer_business_industrys[col] = 0
                normal_mer_business_industrys = normal_mer_business_industrys[business_columns]
                transaction.drop(columns=['is_fraud_transaction'], inplace=True)
                
                # Drop non-feature columns and concatenate one-hot encoded business types
                normal_data = normal_transactions.drop(columns=['mer_id', 'txn_id', 'mer_business_industry', 'txn_timestamp', 'is_fraud_transaction'])
                normal_data = pd.concat([normal_data, normal_mer_business_industrys], axis=1)
                
                llm_flags = "No significant red flags detected in transaction patterns."
        else:
            # If no merchant ID is provided, select a random transaction
            last_transaction = transactions_with_features_df.sample(n=1)
            txn_id = last_transaction['txn_id']
            transaction = transactions_with_features_df[transactions_with_features_df['txn_id'] == txn_id]
            llm_flags = "No significant red flags detected in transaction patterns."
            
    except Exception as e:
        print(f"Error processing transactions: {str(e)}")
        transaction = None
        llm_flags = "Error processing transaction data."
    
    merchant_flags = analyze_merchant(merchants_file, transactions_file, mer_id)
    formatted_merchant_flags = format_merchant_flags(merchant_flags)
    
    # Generate comprehensive summary
    summary_prompt = generate_summary_prompt(transaction, llm_flags, formatted_merchant_flags)
    summary = get_merchant_summary(summary_prompt)
    
    # Print results with color
    print(Fore.CYAN + "\nMerchant and Transaction Summary:")
    print(Fore.YELLOW + "-" * 80)
    print(Fore.GREEN + summary)
    print(Fore.YELLOW + "-" * 80)
    return summary

def summer_is_mine(mer_id):
    summary = main(mer_id, '.ares/data/transactions_with_features.csv','.ares/data/transactions.csv', '.ares/data/merchants.csv')
    return summary

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Summarize merchant and transaction data')
    parser.add_argument('mer_id', type=str, help='Merchant ID to analyze')
    parser.add_argument('feature_file', type=str, help='Path to feature dataset', default='data/transactions_with_features.csv', nargs='?')
    parser.add_argument('transactions_file', type=str, help='Path to transactions dataset', default='data/transactions.csv', nargs='?')
    parser.add_argument('merchants_file', type=str, help='Path to merchants dataset', default='data/merchants.csv', nargs='?')
    
    args = parser.parse_args()
    main(args.mer_id, args.feature_file, args.transactions_file, args.merchants_file)