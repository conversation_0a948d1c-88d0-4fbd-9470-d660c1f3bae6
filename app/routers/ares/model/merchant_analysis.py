import os
import sys
import pandas as pd

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from config.merchant_flag_configs import THRESHOLD, RISK_MAPPINGS


def load_data(merchants_path, transactions_path):
    merchants = pd.read_csv(merchants_path)
    transactions = pd.read_csv(transactions_path)
    return merchants, transactions

def check_merchant_gst_violation(merchants, mer_id):
    """
    Checks if a specific merchant has GST violation
    """
    
    # Get merchant data
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    # Check if merchant exceeds threshold and has GST risk
    has_violation = (merchant['mer_total_txn'].iloc[0] > THRESHOLD['GST_THRESHOLD']) and \
                   (merchant['mer_gst_risk_flag'].iloc[0] == 1)
    
    return has_violation

def check_revenue_discrepancy(merchants, mer_id):
    """
    Checks if merchant's transactional revenue is significantly higher than MCA reported revenue
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
        discrepancy_threshold: Threshold for acceptable difference (default 20%)
    Returns:
        bool: True if significant discrepancy exists, False otherwise
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    return merchant['mer_mca_fillings_risk_flag'].iloc[0]

def check_director_risk(merchants, mer_id):
    """
    Checks if merchant's directors are associated with flagged/blacklisted companies
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if directors are flagged, False otherwise
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    return merchant['mer_directors_risk_flag'].iloc[0]

def check_epfo_violation(merchants, mer_id):
    """
    Checks if merchant lacks EPFO registration despite having >20 employees
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if EPFO violation exists, False otherwise
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    num_employees = merchant['mer_num_employees'].iloc[0]
    has_epfo = merchant['mer_epfo_reg_status'].iloc[0] == 1
    
    return num_employees > THRESHOLD['EMPLOYEE_THRESHOLD'] and not has_epfo

def check_epfo_transaction_mismatch(merchants, mer_id):
    """
    Checks if merchant has high transaction volume but no EPFO registration
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if there's a mismatch between transactions and EPFO status
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    avg_txn_size = merchant['mer_avg_txn_size'].iloc[0]
    has_epfo = merchant['mer_epfo_reg_status'].iloc[0] == 1
    
    return avg_txn_size > THRESHOLD['EPFO_TXN_THRESHOLD'] and not has_epfo

def check_merchant_sanctions(merchants, mer_id):
    """
    Checks if merchant appears in any sanction lists (FATF, OFAC, RBI defaulter lists)
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if merchant is sanctioned, False otherwise
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    return merchant['mer_is_sanctioned'].iloc[0]

def check_online_presence(merchants, mer_id):
    """
    Checks if merchant claiming online business has no/inactive online presence
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if merchant claims online business but has no presence
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    is_online_business = merchant['mer_is_online_business'].iloc[0]
    has_online_presence = merchant['mer_online_presence_flag'].iloc[0]
    
    return is_online_business and not has_online_presence

def check_pan_discrepancy(merchants, mer_id):
    """
    Checks if there's a discrepancy between PAN details in Razorpay and GST records
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if PAN details don't match between systems
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    return not merchant['mer_is_pan_compatible'].iloc[0]

def check_address_compatibility(merchants, mer_id):
    """
    Checks if merchant's address is compatible with their business segment
    (e.g., manufacturing unit in residential area)
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if address is incompatible with business type
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    return not merchant['mer_is_address_compatible'].iloc[0]

def check_mca_submission(merchants, mer_id):
    """
    Checks if merchant has completed mandatory MCA submissions
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if merchant has not completed MCA submissions
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    return not merchant['mer_is_MCA_submission_taken'].iloc[0]

def check_prior_fraud(merchants, mer_id):
    """
    Checks if business or directors are linked to prior financial fraud cases
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if merchant has prior fraud investigation history
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    return merchant['mer_prior_fraud_investigation_flag'].iloc[0] == 1

def check_udyam_certificate(merchants, mer_id):
    """
    Checks if merchant claiming MSME benefits has Udyam Registration Certificate
    Args:
        merchants: DataFrame containing merchant data
        mer_id: ID of merchant to check
    Returns:
        bool: True if merchant claims MSME benefits but lacks Udyam certificate
    """
    merchant = merchants[merchants['mer_id'] == mer_id]
    
    if merchant.empty:
        raise ValueError(f"Merchant ID {mer_id} not found")
    
    return not merchant['mer_udyam_cert_flag'].iloc[0]

def main(merchants_path, transactions_path, mer_id):
    merchants, transactions = load_data(merchants_path, transactions_path)
    
    # Run all checks and store results with risk levels
    risk_results = {
        'gst_violation': (check_merchant_gst_violation(merchants, mer_id), 
                         RISK_MAPPINGS['gst_violation']),
        'revenue_discrepancy': (check_revenue_discrepancy(merchants, mer_id),
                               RISK_MAPPINGS['revenue_discrepancy']),
        'director_risk': (check_director_risk(merchants, mer_id),
                         RISK_MAPPINGS['director_risk']),
        'epfo_violation': (check_epfo_violation(merchants, mer_id),
                          RISK_MAPPINGS['epfo_violation']),
        'epfo_txn_mismatch': (check_epfo_transaction_mismatch(merchants, mer_id),
                             RISK_MAPPINGS['epfo_txn_mismatch']),
        'sanctions': (check_merchant_sanctions(merchants, mer_id),
                     RISK_MAPPINGS['sanctions']),
        'online_presence': (check_online_presence(merchants, mer_id),
                          RISK_MAPPINGS['online_presence']),
        'pan_discrepancy': (check_pan_discrepancy(merchants, mer_id),
                           RISK_MAPPINGS['pan_discrepancy']),
        'address_mismatch': (check_address_compatibility(merchants, mer_id),
                            RISK_MAPPINGS['address_mismatch']),
        'mca_submission': (check_mca_submission(merchants, mer_id),
                          RISK_MAPPINGS['mca_submission']),
        'prior_fraud': (check_prior_fraud(merchants, mer_id),
                       RISK_MAPPINGS['prior_fraud']),
        'udyam_missing': (check_udyam_certificate(merchants, mer_id),
                         RISK_MAPPINGS['udyam_missing'])
    }
    
    # Print results with risk levels
    for flag_name, (has_violation, risk_level) in risk_results.items():
        if has_violation:
            if flag_name == 'gst_violation':
                print(f"[{risk_level.value}] Merchant {mer_id} has GST violation")
            elif flag_name == 'revenue_discrepancy':
                print(f"[{risk_level.value}] Merchant {mer_id} has revenue discrepancy with MCA filings")
            elif flag_name == 'director_risk':
                print(f"[{risk_level.value}] Merchant {mer_id} has directors associated with flagged companies")
            elif flag_name == 'epfo_violation':
                print(f"[{risk_level.value}] Merchant {mer_id} lacks EPFO registration despite having >20 employees")
            elif flag_name == 'epfo_txn_mismatch':
                print(f"[{risk_level.value}] Merchant {mer_id} has high transaction volume but no EPFO registration")
            elif flag_name == 'sanctions':
                print(f"[{risk_level.value}] Merchant {mer_id} appears in sanction lists")
            elif flag_name == 'online_presence':
                print(f"[{risk_level.value}] Merchant {mer_id} claims online business but has no/inactive online presence")
            elif flag_name == 'pan_discrepancy':
                print(f"[{risk_level.value}] Merchant {mer_id} has PAN details mismatch between Razorpay and GST records")
            elif flag_name == 'address_mismatch':
                print(f"[{risk_level.value}] Merchant {mer_id} has address incompatible with business segment")
            elif flag_name == 'mca_submission':
                print(f"[{risk_level.value}] Merchant {mer_id} has not completed mandatory MCA submissions")
            elif flag_name == 'prior_fraud':
                print(f"[{risk_level.value}] Merchant {mer_id} or directors linked to prior financial fraud cases")
            elif flag_name == 'udyam_missing':
                print(f"[{risk_level.value}] Merchant {mer_id} lacks Udyam Registration Certificate while claiming MSME benefits")

    # Return results as tuples of (violation_status, risk_level)
    return (
        risk_results['gst_violation'],
        risk_results['revenue_discrepancy'],
        risk_results['director_risk'],
        risk_results['epfo_violation'],
        risk_results['epfo_txn_mismatch'],
        risk_results['sanctions'],
        risk_results['online_presence'],
        risk_results['pan_discrepancy'],
        risk_results['address_mismatch'],
        risk_results['mca_submission'],
        risk_results['prior_fraud'],
        risk_results['udyam_missing']
    )

# Example usage
if __name__ == "__main__":
    import argparse
    
    # Create argument parser
    parser = argparse.ArgumentParser(description='Analyze merchant risk factors')
    parser.add_argument('--mer_id', type=str, required=True, help='Merchant ID to analyze')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Call main function with the merchant ID
    main('data/merchants.csv', 'data/transactions.csv', args.mer_id)
