import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent.parent))

from groq import Groq
from dotenv import load_dotenv
from model.inference import load_model, predict_transaction
from config.feature_descriptions import FEATURE_DESCRIPTIONS

# Load environment variables from .env file
load_dotenv()

def generate_analysis_prompt(transaction_data, prediction_result):
    """Generate a structured prompt for LLM analysis with actual feature values"""
    # Extract basic transaction info
    txn_id = transaction_data['txn_id'].values[0]
    mer_id = transaction_data['mer_id'].values[0]

    feature_importance = prediction_result['feature_importance']
    max_importance = max(feature_importance.values())
    min_importance = min(feature_importance.values())
    normalized_importance = {feature: (importance - min_importance) / (max_importance - min_importance) for feature, importance in feature_importance.items()}
    # Get top 5 most influential features and their values
    sorted_features = sorted(
        normalized_importance.items(),
        key=lambda x: (x[1]),
        reverse=True
    )[:5]
    
    # Format feature importance explanations with actual values and descriptions
    feature_explanations = []
    for feature, importance in sorted_features:
        direction = "increased" if importance > 0 else "decreased"
        feature_value = transaction_data[feature].values[0]
        
        # Get feature name with timeframe
        description = FEATURE_DESCRIPTIONS.get(feature)
        
        feature_explanations.append(
            f"- {description} (importance: {importance:.4f}): "
            f"This feature {direction} the likelihood of this being an anomalous transaction"
        )

    # Create the prompt
    prompt = f"""
Please analyze this transaction for potential fraud:

Model Verdict: anomalous transaction

Top Contributing Features with Their Values and Importances:
{chr(10).join(feature_explanations)}

Based on these factors, please:
Importances are just normalized scores that show how much each feature contributed to the model's decision. They are not percentages.
Provide a concise  focusing only on the most suspicious patterns. Start with the highest risk indicator.
Put each red flag in a new line.
"""
    return prompt

def get_llm_analysis(prompt):
    """Get analysis from Groq LLM API"""
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        raise ValueError("GROQ_API_KEY not found in environment variables")
        
    client = Groq(api_key=api_key)
    
    system_message = """You are a fraud detection expert. Your task is to analyze transaction features and provide a concise summary of why a transaction is anomalous.

Rules:
1. Go over features in order of importance 
2. Respond in a new line for each red flag, don't use words like "also" or "and"
3. Focus only on the features that increase chance to be anomalous and mention at least 4 patterns
4. Use simple, direct language
5. Use business terms, not technical feature names
6. Just give your analysis, don't include any other text

Example good response: "- A lot of transactions occur during late night hours (1-4 AM)
- Suspiciously high use of foreign cards"

PII score of 0.9 indicates customers are highly connected, suggesting a small group of repeat buyers

Don't use words like "reconstruction error" or "anomalous transaction".
Bad response: "95.4% Reconstruction Error indicates a significant mismatch between expected and actual transaction patterns"
"""

    try:
        chat_completion = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": system_message
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            model="llama3-8b-8192",
            temperature=0.1,
            max_tokens=1024,
            top_p=1,
            stream=False
        )
        return chat_completion.choices[0].message.content
    except Exception as e:
        raise

# To be used in front end
def parse_llm_output(llm_output):
    """Parse LLM output and return a list of strings without leading dashes."""
    return [line.strip()[2:] for line in llm_output.strip().split('\n') if line.startswith('-')]

def generate_analysis(transaction_data, prediction_result):
    """Generate a structured analysis based on the prediction result"""
    feature_importance = prediction_result['feature_importance']
    sorted_features = sorted(
        feature_importance.items(),
        key=lambda x: (x[1]),
        reverse=True
    )[:5]
    
    # Feature name to description mapping
    normal_feature_descriptions = {
        'txn_cnt_lt': 'Merchant has made {} transactions in total',
        'txn_cnt_7d': 'Merchant has made {} transactions in the last 7 days',
        'txn_amt_avg_lt': 'Average transaction amount is ${:.4f}',
        'txn_amt_avg_7d': 'Average transaction amount in the last 7 days is ${:.4f}',
        'international_txn_cnt_pct': '{:.4f}% of transactions are international',
        'avg_cx_pii_score_lt': 'Average customer PII score is {:.4f}',
        'avg_cx_pii_score_7d': 'Average customer PII score in the last 7 days is {:.4f}',
        'hrs_since_last_transaction': '{:.4f} hours since the last transaction',
        'late_night_txn_amt_avg_lt': 'Average transaction amount between 1-4 AM is ${:.4f}',
        'late_night_txn_amt_avg_7d': 'Average transaction amount between 1-4 AM in the last 7 days is ${:.4f}',
    }

    transformed_feature_descriptions = {
        'ip_density_lt_score_lt': 'IP density is {:.4f}',
        'device_id_density_lt_score_lt': 'Device ID density is {:.4f}',
        'card_num_density_lt_score_lt': 'Card number density is {:.4f}',
        'cx_density_lt_score_lt': 'Customer density is {:.4f}',
        'chargeback_txn_cnt_pct_lt_score_lt': '{:.4f}% of transactions are chargebacks',
        'international_txn_cnt_pct_lt_score_lt': '{:.4f}% of transactions are international',
        'failed_txn_cnt_pct_lt_score_lt': '{:.4f}% of transactions are failed',
        'cancelled_txn_cnt_pct_lt_score_lt': '{:.4f}% of transactions are cancelled',
        'name_mismatch_txn_cnt_pct_lt_score_lt': '{:.4f}% of transactions have name mismatch',
        'risky_cx_txn_cnt_pct_lt_score_lt': '{:.4f}% of transactions are from risky customers',
        'round_txn_cnt_pct_lt_score_lt': '{:.4f}% of transactions have round amounts',
        'late_night_txn_cnt_pct_lt_score_lt': '{:.4f}% of transactions occur between 1-4 AM',
        'ip_density_7d_score_7d': 'IP density in the last 7 days is {:.4f}',
        'device_id_density_7d_score_7d': 'Device ID density in the last 7 days is {:.4f}',
        'card_num_density_7d_score_7d': 'Card number density in the last 7 days is {:.4f}',
        'cx_density_7d_score_7d': 'Customer density in the last 7 days is {:.4f}',
        'chargeback_txn_cnt_pct_7d_score_7d': '{:.4f}% of transactions in the last 7 days are chargebacks',
        'international_txn_cnt_pct_7d_score_7d': '{:.4f}% of transactions in the last 7 days are international',
        'failed_txn_cnt_pct_7d_score_7d': '{:.4f}% of transactions in the last 7 days are failed',
        'cancelled_txn_cnt_pct_7d_score_7d': '{:.4f}% of transactions in the last 7 days are cancelled',
        'name_mismatch_txn_cnt_pct_7d_score_7d': '{:.4f}% of transactions in the last 7 days have name mismatch',
        'risky_cx_txn_cnt_pct_7d_score_7d': '{:.4f}% of transactions in the last 7 days are from risky customers',
        'round_txn_cnt_pct_7d_score_7d': '{:.4f}% of transactions in the last 7 days have round amounts',
        'late_night_txn_cnt_pct_7d_score_7d': '{:.4f}% of transactions in the last 7 days occur between 1-4 AM',
        'cx_complaint_txn_pct_7d_score_7d': '{:.4f}% of transactions in the last 7 days are from customers with complaints',
    }

    def reverse_log_transform(value, y, epsilon=1e-10):
        """Reverse the log transform multiplication"""
        return np.exp(value / np.log(y + epsilon)) - epsilon

    analysis_points = []
    for feature_name, importance in sorted_features:
        if feature_name not in transaction_data.columns:
            continue
            
        value = transaction_data[feature_name].iloc[0] # Get scalar value
        
        if feature_name in normal_feature_descriptions:            
            description = normal_feature_descriptions[feature_name].format(float(value))
        elif feature_name in transformed_feature_descriptions:
            untransformed_value = reverse_log_transform(value, transaction_data['txn_cnt_lt'].iloc[0])
            description = transformed_feature_descriptions[feature_name].format(float(untransformed_value))
        else:
            description = f"Unknown feature: {feature_name}"
            
        # Convert importance to float if it's a numpy array
        importance_val = float(importance) if hasattr(importance, 'item') else importance
        formatted_point = f"{description} (importance: {importance_val:.4f})"
        data = {
            "metric": feature_name,
            "description": description,
            "importance": importance_val
        }
        analysis_points.append(data)
    
    return analysis_points

def main(mer_id=None, transactions_path=None, merchants_path=None, method='deterministic', tolerance=0):
    model, scaler, threshold, business_columns = load_model()
    
    try:
        # Load transaction features
        transactions_with_features_df = pd.read_csv('.ares/data/transactions_with_features.csv')
        
        # Load transactions with is_fraud flag
        transactions_df = pd.read_csv('.ares/data/transactions.csv')
        if 'fraud_type' in transactions_df.columns:
            transactions_df.drop(columns=['fraud_type', 'red_flags', 'fraud_indicators'], inplace=True)
        
        # Merge the two dataframes
        transactions_with_features_df = transactions_with_features_df.merge(
            transactions_df[['txn_id', 'is_fraud_transaction']], 
            on='txn_id', 
            how='left'
        )
        
        # Select transaction by ID if provided
        txn_ids = transactions_df[transactions_df['mer_id'] == str(mer_id)]['txn_id']
        txn_id = txn_ids.iloc[-1] if not txn_ids.empty else None
        
        if txn_id:
            transaction = transactions_with_features_df[transactions_with_features_df['txn_id'] == txn_id]
            if len(transaction) == 0:
                raise ValueError(f"Transaction ID {txn_id} not found")
        else:
            transaction = transactions_with_features_df.sample(n=1)
        
        # Get business type and filter normal transactions
        mer_business_industry = transaction['mer_business_industry'].values[0]
        normal_transactions = transactions_with_features_df[(~transactions_with_features_df['is_fraud_transaction'])]
        
        transactions_with_features_df = transactions_with_features_df.drop(columns=['is_fraud_transaction'])
        
        # Process data for prediction
        normal_mer_business_industrys = pd.get_dummies(normal_transactions['mer_business_industry'], prefix='business')
        for col in business_columns:
            if col not in normal_mer_business_industrys.columns:
                normal_mer_business_industrys[col] = 0
        normal_mer_business_industrys = normal_mer_business_industrys[business_columns]
        transaction.drop(columns=['is_fraud_transaction'], inplace=True)
        
        normal_data = normal_transactions.drop(columns=['mer_id', 'txn_id', 'mer_business_industry', 'txn_timestamp', 'is_fraud_transaction'])
        normal_data = pd.concat([normal_data, normal_mer_business_industrys], axis=1)
        
        # Make prediction
        result = predict_transaction(transaction, model, scaler, threshold, business_columns, normal_data, tolerance)
        
        analysis = None
        if result['is_anomalous']:
            if method == 'llm':
                prompt = generate_analysis_prompt(transaction, result)
                analysis = get_llm_analysis(prompt)
            elif method == 'deterministic':
                analysis = generate_analysis(transaction, result)
        
        return analysis
        
    except Exception as e:
        raise

def run_this_please(mer_id=None):
    return main(mer_id)
