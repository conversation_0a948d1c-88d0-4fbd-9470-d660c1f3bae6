import torch
import pandas as pd
import numpy as np
import shap
from pathlib import Path
import sys
import warnings
from .train import Autoencoder, compute_reconstruction_error
warnings.filterwarnings("ignore")

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

import shap
import torch
import numpy as np
import pandas as pd

class ModelWrapper(torch.nn.Module):
    def __init__(self, model):
        super().__init__()
        self.model = model
    
    def forward(self, x):
        reconstruction = self.model(x)
        error = torch.sum((reconstruction - x)**2, dim=1).unsqueeze(1)
        return error
    
def compute_feature_importance(model, scaled_features, feature_columns, normal_data):
    """Compute feature importance using SHAP's GradientExplainer with PyTorch."""
    # Prepare model wrapper
    model.eval()
    wrapped_model = ModelWrapper(model)
    
    # Prepare background data
    if normal_data.empty:
        raise ValueError("normal_data is empty. Cannot sample from an empty DataFrame.")
    background_data = normal_data.sample(n=2000, random_state=42, replace=True)
    background_tensor = torch.FloatTensor(background_data.values.astype(np.float32)).to(device)
    
    with torch.set_grad_enabled(True):
        try:
            explainer = shap.GradientExplainer(wrapped_model, background_tensor)
            
            scaled_features_tensor = torch.FloatTensor(scaled_features.astype(np.float32)).to(device)
            shap_values = explainer.shap_values(scaled_features_tensor)
            
            if isinstance(shap_values, list):
                shap_values = shap_values[0]
            
            mean_shap = np.abs(shap_values).mean(axis=0)
            
            feature_importance = dict(zip(feature_columns, mean_shap))
            std_importance = np.std(list(feature_importance.values()))
            
            normalized_importance = {
                feature: (importance) / std_importance if std_importance != 0 else 0
                for feature, importance in feature_importance.items()
            }
            
            return normalized_importance
            
        except Exception as e:
            raise

def load_model():
    """Load trained model and associated data"""
    try:
        checkpoint = torch.load('app/routers/ares/model/autoencoder_20241224_170224.pth', map_location=device)
        
        model = Autoencoder(checkpoint['input_dim'])
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(device)
        model.eval()
        
        return model, checkpoint['scaler_state'], checkpoint['threshold'], checkpoint['business_columns']
    
    except Exception as e:
        raise

def predict_transaction(transaction_data, model, scaler, threshold, business_columns, normal_data, tolerance):
    """Predict if a transaction is anomalous and explain the prediction"""
    try:
        features = transaction_data.copy()
        
        mer_business_industrys = pd.get_dummies(features['mer_business_industry'], prefix='business')

        for col in business_columns:
            if col not in mer_business_industrys.columns:
                mer_business_industrys[col] = 0
        mer_business_industrys = mer_business_industrys[business_columns]
        
        columns_to_drop = ['mer_id', 'txn_id', 'mer_business_industry', 'txn_timestamp']
        features = features.drop(columns=columns_to_drop)
        
        features = pd.concat([features, mer_business_industrys], axis=1)
        
        features = features.fillna(0)
        
        feature_columns = features.columns
        
        features = features.values
        
        scaled_features = scaler.transform(features)
        
        feature_importance = compute_feature_importance(model, scaled_features, feature_columns, normal_data)
        
        error = compute_reconstruction_error(model, scaled_features)
        
        is_anomalous = bool(error > threshold*(1+tolerance))
        
        result = {
            'is_anomalous': is_anomalous,
            'reconstruction_error': float(error[0]),
            'threshold': float(threshold),
            'feature_importance': feature_importance,
        }
        
        return result
        
    except Exception as e:
        raise

def main(txn_id=None, tolerance=0, is_test=None):
    try:
        if is_test == 0:
            model, scaler, threshold, business_columns = load_model()
            
            transactions_with_features_df = pd.read_csv('data/transactions_with_features.csv')
            
            transactions_df = pd.read_csv('data/transactions.csv')
            
            if 'fraud_type' in transactions_df.columns:
                transactions_df.drop(columns=['fraud_type', 'red_flags', 'fraud_indicators'], inplace=True)
            
            transactions_with_features_df = transactions_with_features_df.merge(
                transactions_df[['txn_id', 'is_fraud_transaction']], 
                on='txn_id', 
                how='left'
            )
            
            if txn_id:
                transaction = transactions_with_features_df[transactions_with_features_df['txn_id'] == txn_id]
                if len(transaction) == 0:
                    raise ValueError(f"Transaction ID {txn_id} not found")
            else:
                transaction = transactions_with_features_df.sample(n=1)
            
            mer_business_industry = transaction['mer_business_industry'].values[0]
            
            normal_transactions = transactions_with_features_df[(~transactions_with_features_df['is_fraud_transaction'])]
            
            transactions_with_features_df = transactions_with_features_df.drop(columns=['is_fraud_transaction'])
            
            normal_mer_business_industrys = pd.get_dummies(normal_transactions['mer_business_industry'], prefix='business')
            for col in business_columns:
                if col not in normal_mer_business_industrys.columns:
                    normal_mer_business_industrys[col] = 0
            normal_mer_business_industrys = normal_mer_business_industrys[business_columns]
            
            transaction.drop(columns=['is_fraud_transaction'], inplace=True)
            
            normal_data = normal_transactions.drop(columns=['mer_id', 'txn_id', 'mer_business_industry', 'txn_timestamp', 'is_fraud_transaction'])
            normal_data = pd.concat([normal_data, normal_mer_business_industrys], axis=1)
            
            result = predict_transaction(transaction, model, scaler, threshold, business_columns, normal_data, tolerance)
            
            sorted_features = sorted(
                result['feature_importance'].items(), 
                key=lambda x: (x[1]), 
                reverse=True
            )
            
        else:
            model, scaler, threshold, business_columns = load_model()
            
            transactions_df = pd.read_csv('data/test_transactions.csv')
            
            if txn_id:
                transaction = transactions_df[transactions_df['txn_id'] == int(txn_id)]
                if len(transaction) == 0:
                    raise ValueError(f"Transaction ID {txn_id} not found")
            else:
                transaction = transactions_df.sample(n=1)
            
            mer_business_industry = transaction['mer_business_industry'].values[0]
            
            normal_transactions = transactions_df[
                (~transactions_df['is_fraud_transaction']) & 
                (transactions_df['mer_business_industry'] == mer_business_industry)
            ]
            
            transactions_df = transactions_df.drop(columns=['is_fraud_transaction'])
            
            normal_mer_business_industrys = pd.get_dummies(normal_transactions['mer_business_industry'], prefix='business')
            for col in business_columns:
                if col not in normal_mer_business_industrys.columns:
                    normal_mer_business_industrys[col] = 0
            normal_mer_business_industrys = normal_mer_business_industrys[business_columns]
            
            transaction.drop(columns=['is_fraud_transaction'], inplace=True)
            
            normal_data = normal_transactions.drop(columns=['mer_id', 'txn_id', 'mer_business_industry', 'txn_timestamp', 'is_fraud_transaction'])
            normal_data = pd.concat([normal_data, normal_mer_business_industrys], axis=1)
            
            result = predict_transaction(transaction, model, scaler, threshold, business_columns, normal_data, tolerance)
            
            sorted_features = sorted(
                result['feature_importance'].items(), 
                key=lambda x: (x[1]), 
                reverse=True
            )
    
    except Exception as e:
        raise

if __name__ == "__main__":
    try:
        txn_id = input("Enter transaction ID (press Enter for random): ").strip()
        txn_id = txn_id if txn_id else None
        
        is_test = input("Enter 1 for test data, 0 for real data: ").strip()
        is_test = int(is_test) if is_test else None
        
        main(txn_id=txn_id, is_test=is_test)
        
    except Exception as e:
        raise
