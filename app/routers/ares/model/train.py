import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_curve, auc, classification_report, confusion_matrix, f1_score
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
from scipy.stats import norm
import os
from tqdm import tqdm
from datetime import datetime
import seaborn as sns

def setup_gpu():
    if not torch.cuda.is_available():
        return False, "cpu", {
            'batch_size': 32,  # Default batch size for CPU
            'workers': 1,      # Default number of workers for CPU
            'pin_memory': False,
            'mixed_precision': False,
            'memory_fraction': 1.0
        }
    
    # GPU Configuration
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.enabled = True
    torch.cuda.set_device(0)
    
    # MX550 Specific Settings
    memory_fraction = 0.7
    torch.cuda.empty_cache()
    torch.cuda.memory.set_per_process_memory_fraction(memory_fraction)
    
    # Optimal workers for MX550
    num_workers = min(4, os.cpu_count())
    
    # Training parameters
    batch_size = 64 if torch.cuda.get_device_properties(0).total_memory > 2000000000 else 32
    
    config = {
        'device': "cuda" if torch.cuda.is_available() else "cpu",
        'workers': num_workers,
        'batch_size': batch_size,
        'pin_memory': True,
        'mixed_precision': True,
        'memory_fraction': memory_fraction
    }
    print(f"GPU Configuration:")
    print(f"CUDA {torch.version.cuda} | {torch.cuda.get_device_name(0)}")
    print(f"Memory Allocation: {memory_fraction*100}%")
    print(f"Batch Size: {batch_size}")
    print(f"Workers: {num_workers}")
    print(f"Mixed Precision: {config['mixed_precision']}")
    torch.cuda.set_stream(torch.cuda.Stream())
    return True, config['device'], config

# Initialize
device_available, device_type, training_config = setup_gpu()
device = torch.device(device_type)

# Global configs
BATCH_SIZE = training_config['batch_size']
MIXED_PRECISION = training_config['mixed_precision']
PIN_MEMORY = training_config['pin_memory']
NUM_WORKERS = training_config['workers']
print(f'Using device: {device} in training')

class Autoencoder(nn.Module):
    def __init__(self, input_dim):
        super(Autoencoder, self).__init__()
        
        # Encoder
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 8),
            nn.ReLU()
        )
        
        # Decoder
        self.decoder = nn.Sequential(
            nn.Linear(8, 16),
            nn.ReLU(),
            nn.Linear(16, 32),
            nn.ReLU(),
            nn.Linear(32, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, input_dim),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        x = self.encoder(x)
        x = self.decoder(x)
        return x

def prepare_data(transactions_path, merchants_path):
    # Load datasets from provided paths
    try:
        transactions = pd.read_csv(transactions_path)
        merchants = pd.read_csv(merchants_path)
    except FileNotFoundError as e:
        print(f"\nError: Could not find input file: {e.filename}")
        return None, None, None, None
    
    # Convert mer_fraud_flag to boolean 
    merchants['mer_fraud_flag'] = merchants['mer_fraud_flag'].astype(bool)
    
    # Get merchant IDs for both normal and anomalous
    normal_mer_ids = merchants[~merchants['mer_fraud_flag']]['mer_id'].values
    fraud_mer_ids = merchants[merchants['mer_fraud_flag']]['mer_id'].values
    
    print(f"Normal Merchants: {len(normal_mer_ids)} | Fraud Merchants: {len(fraud_mer_ids)}")
    # Split transactions
    normal_transactions = transactions[
        transactions['mer_id'].isin(normal_mer_ids)
    ]
    fraud_transactions = transactions[
        transactions['mer_id'].isin(fraud_mer_ids)
    ]
    
    # One-hot encode mer_business_industry for ALL transactions
    mer_business_industrys_all = pd.get_dummies(transactions['mer_business_industry'], prefix='business')
    business_columns = mer_business_industrys_all.columns

    # Drop non-feature columns and concatenate one-hot encoded business types
    columns_to_drop = ['mer_id', 'txn_id', 'mer_business_industry', 'txn_timestamp']
    if 'is_fraud_transaction' in transactions.columns:
        columns_to_drop.append('is_fraud_transaction')
    
    # Process normal transactions
    normal_features = pd.concat([
        normal_transactions.drop(columns=columns_to_drop),
        pd.get_dummies(normal_transactions['mer_business_industry'], prefix='business').reindex(columns=business_columns, fill_value=0)
    ], axis=1)
    
    # Process fraud transactions
    fraud_features = pd.concat([
        fraud_transactions.drop(columns=columns_to_drop),
        pd.get_dummies(fraud_transactions['mer_business_industry'], prefix='business').reindex(columns=business_columns, fill_value=0)
    ], axis=1)

    print(f"Normal Transactions: {normal_features.shape} | Fraud Transactions: {fraud_features.shape}")
    
    # Convert to numpy and handle missing values
    normal_features = normal_features.fillna(0).values
    fraud_features = fraud_features.fillna(0).values

    # Split normal data into train, validation, and test
    train_idx = np.random.choice(
        len(normal_features), 
        size=int(0.7*len(normal_features)), 
        replace=False
    )
    remaining_idx = np.setdiff1d(np.arange(len(normal_features)), train_idx)
    val_idx = np.random.choice(
        remaining_idx, 
        size=int(0.2*len(normal_features)), 
        replace=False
    )
    test_idx = np.setdiff1d(remaining_idx, val_idx)

    # Standardize features
    scaler = StandardScaler()
    train_features = scaler.fit_transform(normal_features[train_idx])
    val_features = scaler.transform(normal_features[val_idx])
    test_normal_features = scaler.transform(normal_features[test_idx])
    test_fraud_features = scaler.transform(fraud_features)

    return train_features, val_features, test_normal_features, test_fraud_features, scaler, business_columns

def compute_reconstruction_error(model, data):
    model.eval()
    errors = []
    batch_size = 512  # Process in batches to avoid OOM
    with torch.no_grad():
        for i in range(0, len(data), batch_size):
            batch = torch.FloatTensor(data[i:i+batch_size]).to(device)
            reconstructed = model(batch)
            mse = nn.MSELoss(reduction='none')
            error = mse(reconstructed, batch).mean(dim=1)
            errors.append(error.cpu())
    return torch.cat(errors).numpy()

def calculate_auc_roc(normal_errors, fraud_errors):
    y_true = np.concatenate([np.zeros(len(normal_errors)), np.ones(len(fraud_errors))])
    y_scores = np.concatenate([normal_errors, fraud_errors])
    fpr, tpr, _ = roc_curve(y_true, y_scores)
    return auc(fpr, tpr)

def log_and_print(message, filepath='model/current_performance.txt'):
    """Print message and append to log file with timestamp"""
    formatted_message = f"{message}\n"
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(filepath)), exist_ok=True)
    
    print(message)
    
    # Append to txt file
    with open(filepath, 'a') as f:
        f.write(formatted_message)

def evaluate_autoencoder(model, scaler, normal_data, fraud_data):
    """Comprehensive evaluation of autoencoder performance"""
    torch.cuda.empty_cache()
    # Compute reconstruction errors
    normal_errors = compute_reconstruction_error(model, normal_data)
    fraud_errors = compute_reconstruction_error(model, fraud_data)
    
    # Calculate autoencoder metrics
    normal_mean = np.mean(normal_errors)
    normal_std = np.std(normal_errors)
    fraud_mean = np.mean(fraud_errors)
    separation_factor = (fraud_mean - normal_mean) / normal_std
    
    log_and_print("\nAutoencoder Metrics:")
    log_and_print(f"Normal Mean Error: {normal_mean:.6f}")
    log_and_print(f"Normal Std Error: {normal_std:.6f}")
    log_and_print(f"Fraud Mean Error: {fraud_mean:.6f}")
    log_and_print(f"Separation Factor: {separation_factor:.2f}")
    
    # Prepare labels and predictions
    y_true = np.concatenate([np.zeros(len(normal_errors)), np.ones(len(fraud_errors))])
    errors = np.concatenate([normal_errors, fraud_errors])
    
    fpr, tpr, thresholds = roc_curve(y_true, errors)
    # Find the optimal threshold (Youden's J statistic)
    optimal_idx = np.argmax(tpr - fpr)
    optimal_threshold = thresholds[optimal_idx]
    
    # Get final predictions using optimal threshold
    y_pred_optimal = (errors > optimal_threshold).astype(int)
    auc_roc_score = calculate_auc_roc(y_true, errors)

    # Print confusion matrix, classification report, and AUC-ROC score
    log_and_print("\nConfusion Matrix:")
    log_and_print(str(confusion_matrix(y_true, y_pred_optimal)))
    log_and_print("\nClassification Report:")
    log_and_print(str(classification_report(y_true, y_pred_optimal)))
    log_and_print("\nAUC-ROC Score:")
    log_and_print(f"{auc_roc_score:.4f}")
    
    # Plot error distributions
    plt.figure(figsize=(16, 10))
    sns.kdeplot(normal_errors, label='Normal Errors', shade=True, color='blue')
    sns.kdeplot(fraud_errors, label='Fraud Errors', shade=True, color='red')

    # Calculate appropriate x-axis limits
    x_min = min(min(normal_errors), min(fraud_errors))
    x_max = max(max(normal_errors), max(fraud_errors))
    center_point = 0
    margin = (x_max - x_min) * 0.2  # 20% margin

    plt.xlim(center_point - margin, center_point + margin)
    plt.grid(True, alpha=0.3)
    plt.title('Reconstruction Error Distribution', fontsize=14, pad=20)
    plt.xlabel('Reconstruction Error', fontsize=12)
    plt.ylabel('Density', fontsize=12)
    plt.legend(fontsize=12)
    plt.tight_layout()
    plt.savefig('model/error_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return optimal_threshold

def train_autoencoder(features, val_features, model=None, epochs=300, learning_rate=0.0005, batch_size=BATCH_SIZE):
    # Enable mixed precision training
    scaler = torch.amp.GradScaler(device_type)
    
    # Optimize data loading
    train_dataset = TensorDataset(torch.FloatTensor(features), torch.FloatTensor(features))
    val_dataset = TensorDataset(torch.FloatTensor(val_features), torch.FloatTensor(val_features))
    train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, 
                                pin_memory=PIN_MEMORY, num_workers=NUM_WORKERS, persistent_workers=True, prefetch_factor=2)
    val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, 
                              pin_memory=PIN_MEMORY, num_workers=NUM_WORKERS, persistent_workers=True, prefetch_factor=2)
    
    if model is None:
        input_dim = features.shape[1]
        model = Autoencoder(input_dim)
    
    model = model.to(device)
    
    # Optimize for GPU training
    torch.backends.cudnn.benchmark = True
    
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
    train_losses = []
    val_losses = []
    
    epoch_pbar = tqdm(range(epochs), desc='Training Progress')
    for epoch in epoch_pbar:
        model.train()
        total_train_loss = 0
        train_pbar = tqdm(train_dataloader, leave=False, desc=f'Training batch')
        for batch_features, _ in train_pbar:
            batch_features = batch_features.to(device, non_blocking=True)
            
            # Mixed precision training
            with torch.amp.autocast(device_type):
                outputs = model(batch_features)
                loss = criterion(outputs, batch_features)
            
            # Scaled backward pass
            optimizer.zero_grad(set_to_none=True)
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
            
            total_train_loss += loss.item()
            train_pbar.set_postfix({'loss': f'{loss.item():.6f}'})
            # Clear cache periodically
            if torch.cuda.is_available() and torch.cuda.memory_allocated() > 0.8 * torch.cuda.get_device_properties(0).total_memory:
                torch.cuda.empty_cache()
        
        train_loss = total_train_loss / len(train_dataloader)
        train_losses.append(train_loss)
        
        # Validation loop
        model.eval()
        total_val_loss = 0
        with torch.no_grad():
            val_pbar = tqdm(val_dataloader, leave=False, desc=f'Validation batch')
            for batch_features, _ in val_pbar:
                batch_features = batch_features.to(device, non_blocking=True)
                with torch.amp.autocast(device_type):
                    outputs = model(batch_features)
                    loss = criterion(outputs, batch_features)
                total_val_loss += loss.item()
                val_pbar.set_postfix({'loss': f'{loss.item():.6f}'})
                
        val_loss = total_val_loss / len(val_dataloader)
        val_losses.append(val_loss)
        
        epoch_pbar.set_postfix({
            'train_loss': f'{train_loss:.6f}',
            'val_loss': f'{val_loss:.6f}'
        })
        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{epochs}], Train Loss: {train_losses[-1]:.6f}, Val Loss: {val_losses[-1]:.6f}')
            if torch.cuda.is_available():
                print(f'GPU Memory: {torch.cuda.memory_allocated(0)//1024**2}MB / {torch.cuda.memory_reserved(0)//1024**2}MB')

    # Plot training and validation loss vs epochs
    plt.figure(figsize=(10, 5))
    plt.plot(range(1, epochs + 1), train_losses, label='Training Loss')
    plt.plot(range(1, epochs + 1), val_losses, label='Validation Loss')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss vs. Epochs')
    plt.legend()
    plt.grid(True)
    plt.savefig('model/training_validation_loss.png')  # Save the plot
    plt.show()
    
    return model, criterion

def prepare_features_for_model(features_df):
    """Prepare features for model training/inference"""
    
    # Columns to drop before training/inference
    cols_to_drop = ['txn_id', 'mer_id', 'mer_business_industry', 'txn_timestamp']
    
    # Drop non-feature columns
    model_features = features_df.drop(columns=cols_to_drop)
    
    return model_features

def main(transactions_path=None, merchants_path=None, epochs=300, learning_rate=0.0005):
    # Set default paths if not provided
    transactions_path = transactions_path or 'data/transactions_with_features.csv'
    merchants_path = merchants_path or 'data/merchants.csv'
    
    # Print GPU status
    print(f"Using device: {device}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
    
    # Clear GPU cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # Prepare data
    train_features, val_features, test_normal, test_fraud, scaler, business_columns = prepare_data(transactions_path, merchants_path)
    if train_features is None:
        return
    
    # Ask user if they want to continue training from a loaded model
    continue_training = input("Do you want to continue training from a loaded model? (y/n): ").strip().lower()
    if continue_training == 'y' and os.path.exists('model/autoencoder.pth'):
        try:
            checkpoint = torch.load('app/ares/model/autoencoder_20241224_170224.pth', map_location=device)
            model = Autoencoder(checkpoint['input_dim']).to(device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            print(f"Loaded model from 'model/autoencoder.pth' to {device}")
        except Exception as e:
            print(f"Error loading model: {e}")
            model = None
    else:
        model = None
    
    try:
        # Train model with provided hyperparameters
        model, criterion = train_autoencoder(
            train_features, 
            val_features,
            model=model,
            epochs=epochs, 
            learning_rate=learning_rate, 
            batch_size=64
        )
        
        # Move model to CPU for evaluation
        model = model.to(device)
        
        # Evaluate model
        metrics = evaluate_autoencoder(model, scaler, test_normal, test_fraud)
        
        # Print evaluation results
        log_and_print("\nModel Evaluation Results:")
        log_and_print(f"Optimal Threshold: {metrics:.6f}")
        
        # Save model to CPU state for portability
        model = model.cpu()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        model_state = {
            'model_state_dict': model.state_dict(),
            'input_dim': train_features.shape[1],
            'scaler_state': scaler,
            'threshold': metrics,
            'metrics': metrics,
            'business_columns': business_columns
        }
        timestamped_path = f'model/autoencoder_{timestamp}.pth'
        torch.save(model_state, timestamped_path)
        
        print(f"\nModel training completed and saved to {timestamped_path}")
        
    except RuntimeError as e:
        print(f"GPU error during training: {e}")
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        return
    
    finally:
        # Clean up GPU memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Train autoencoder model for fraud detection')
    parser.add_argument('--transactions_path', type=str, 
                       help='Path to transactions CSV file (default: data/transactions_with_features.csv)')
    parser.add_argument('--merchants_path', type=str,
                       help='Path to merchants CSV file (default: data/merchants.csv)')
    parser.add_argument('--epochs', type=int, default=300,
                       help='Number of training epochs (default: 300)')
    parser.add_argument('--learning_rate', type=float, default=0.0005,
                       help='Learning rate for optimizer (default: 0.0005)')
    
    args = parser.parse_args()
    main(
        args.transactions_path, 
        args.merchants_path, 
        args.epochs, 
        args.learning_rate
    )
