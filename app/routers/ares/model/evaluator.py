import torch
import pandas as pd
import numpy as np
from pathlib import Path
import sys
from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")

sys.path.append(str(Path(__file__).parent.parent))
from model.train import Autoencoder, compute_reconstruction_error
from inference import load_model

def predict_transaction(transaction_data, model, scaler, threshold, business_columns, normal_data):
    """Predict if a transaction is anomalous without feature importance"""
    
    # Create a copy of transaction_data to avoid modifying the original
    features = transaction_data.copy()
    
    # One-hot encode mer_business_industry first (before dropping columns)
    mer_business_industrys = pd.get_dummies(features['mer_business_industry'], prefix='business')
    # Ensure all expected business columns exist
    for col in business_columns:
        if col not in mer_business_industrys.columns:
            mer_business_industrys[col] = 0
    # Keep only the business columns from training
    mer_business_industrys = mer_business_industrys[business_columns]
    
    # Drop non-feature columns (exact same as training)
    columns_to_drop = ['mer_id', 'txn_id', 'mer_business_industry', 'txn_timestamp']
    features = features.drop(columns=columns_to_drop)
    
    # Concatenate features with business types
    features = pd.concat([features, mer_business_industrys], axis=1)
    
    # Fill missing values with 0
    features = features.fillna(0)
    
    # Convert to numpy array
    features = features.values
    
    # Scale features using the same scaler from training
    scaled_features = scaler.transform(features)
    
    error = compute_reconstruction_error(model, scaled_features)
    tolerance = 1
    
    return {
        'is_anomalous': bool(error > threshold*(1+tolerance)),
        'reconstruction_error': float(error[0]),
        'threshold': float(threshold),
    }

def main():
    # Load model and associated data
    model, scaler, threshold, business_columns = load_model()
    
    # Load transaction features
    transactions_with_features_df = pd.read_csv('data/transactions_with_features.csv')
    
    # Load transactions with is_fraud flag
    transactions_df = pd.read_csv('data/transactions.csv')
    
    # Merge the two dataframes on txn_id to get the is_fraud flag in transactions_with_features_df
    transactions_with_features_df = transactions_with_features_df.merge(transactions_df[['txn_id', 'is_fraud_transaction']], on='txn_id', how='left')
    
    normal_transactions = transactions_with_features_df[~transactions_with_features_df['is_fraud_transaction']]

    transactions_with_features_df = transactions_with_features_df.drop(columns=['is_fraud_transaction'])    
    
    # One-hot encode mer_business_industry for normal transactions
    normal_mer_business_industrys = pd.get_dummies(normal_transactions['mer_business_industry'], prefix='business')
    for col in business_columns:
        if col not in normal_mer_business_industrys.columns:
            normal_mer_business_industrys[col] = 0
    normal_mer_business_industrys = normal_mer_business_industrys[business_columns]
    
    # Drop non-feature columns and concatenate one-hot encoded business types
    normal_data = normal_transactions.drop(columns=['mer_id', 'txn_id', 'mer_business_industry', 'txn_timestamp', 'is_fraud_transaction'])
    normal_data = pd.concat([normal_data, normal_mer_business_industrys], axis=1)
    
    mismatches = []
    
    for txn_id in tqdm(transactions_with_features_df['txn_id'], desc="Processing transactions"):
        transaction = transactions_with_features_df[transactions_with_features_df['txn_id'] == txn_id]
        actual_label = transactions_df[transactions_df['txn_id'] == txn_id]['is_fraud_transaction'].values[0]
        
        # Make prediction
        result = predict_transaction(transaction, model, scaler, threshold, business_columns, normal_data)
        predicted_label = result['is_anomalous']
        
        if predicted_label != actual_label:
            mismatches.append(txn_id)
    
    print("Mismatched transaction IDs:")
    print(mismatches)
    print(f"Number of label mismatches: {len(mismatches)}")

if __name__ == "__main__":
    main()