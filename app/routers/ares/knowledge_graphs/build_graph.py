import pandas as pd
import networkx as nx
import os
import matplotlib.pyplot as plt
import torch
from torch_geometric.data import Data
from torch_geometric.utils import from_networkx
from torch_geometric.nn import SAGEConv
import torch.nn.functional as F
from sklearn.preprocessing import LabelEncoder
from tqdm import tqdm
import joblib
import umap

# Check for GPU availability
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Get project root directory (one level up from current file)
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Use data directory in project root
data_dir = os.path.join(project_root, 'data')
os.makedirs(data_dir, exist_ok=True)

# Load datasets
transactions = pd.read_csv(os.path.join(data_dir, 'transactions.csv'))
merchants = pd.read_csv(os.path.join(data_dir, 'merchants.csv'))

# Encode customer and merchant IDs
le_merchant = LabelEncoder()
le_customer = LabelEncoder()
transactions['mer_id'] = le_merchant.fit_transform(transactions['mer_id'])
transactions['cx_id'] = le_customer.fit_transform(transactions['cx_id'])
merchants['mer_id'] = le_merchant.transform(merchants['mer_id'])

# Save the LabelEncoders
joblib.dump(le_merchant, os.path.join(data_dir, 'le_merchant.pkl'))
joblib.dump(le_customer, os.path.join(data_dir, 'le_customer.pkl'))

# Initialize an empty graph
G = nx.Graph()

# Create embeddings directory inside data
embedding_save_dir = os.path.join(data_dir, 'graph_embeddings')
os.makedirs(embedding_save_dir, exist_ok=True)

# Build the graph from transactions
for idx, txn in tqdm(transactions.iterrows(), total=transactions.shape[0]):
    cx_id = txn['cx_id']
    mer_id = txn['mer_id']

    # Add customer and merchant nodes with default attributes
    G.add_node(cx_id, type='customer', attr1=0, attr2='unknown')
    merchant_attributes = merchants.loc[merchants['mer_id'] == mer_id].squeeze().to_dict()
    merchant_attributes.setdefault('attr1', 0)
    merchant_attributes.setdefault('attr2', 'unknown')
    G.add_node(mer_id, type='merchant', **merchant_attributes)

    # Add edge with transaction attributes
    G.add_edge(cx_id, mer_id, txn_amount=txn['txn_amount'], is_fraud_transaction=txn['is_fraud_transaction'])

# Ensure all nodes have the same attributes
def ensure_uniform_node_attributes(G):
    all_attributes = set()
    for _, attributes in G.nodes(data=True):
        all_attributes.update(attributes.keys())
    for node, attributes in G.nodes(data=True):
        for attr in all_attributes:
            attributes.setdefault(attr, None)

ensure_uniform_node_attributes(G)

# Add 'node_type' and 'node_id' attributes to each node in G
for node in G.nodes():
    G.nodes[node]['node_type'] = 1 if G.nodes[node]['type'] == 'customer' else 0  # Assign node_type (e.g., 0 for merchants, 1 for customers)
    G.nodes[node]['node_id'] = node   # Assign node_id (could be the same as the node identifier)

# Visualize the graph
def visualize_graph(G, output_path=os.path.join(data_dir, 'graph_viz.png')):
    plt.figure(figsize=(20, 20))
    colors = ['red' if G.nodes[node]['type'] == 'customer' else 'blue' for node in G.nodes()]
    pos = nx.spring_layout(G, k=1, iterations=50)
    nx.draw(G, pos, node_color=colors, node_size=100, alpha=0.6, with_labels=False)
    plt.savefig(output_path)
    plt.close()
    print(f"Graph visualization saved to {output_path}")

visualize_graph(G)

# Save the graph in PyG format
def save_as_pyg(G, output_path=os.path.join(data_dir, 'graph.pt')):
    data = from_networkx(G)
    node_type = torch.tensor([1 if G.nodes[node]['type'] == 'customer' else 0 for node in G.nodes()]).to(device)
    data.node_type = node_type
    edge_attr = torch.tensor([[G.edges[edge]['txn_amount'], float(G.edges[edge]['is_fraud_transaction'])] for edge in G.edges()]).to(device)
    data.edge_attr = edge_attr
    data.node_ids = torch.tensor([n for n in G.nodes()], dtype=torch.long)
    data.node_type = torch.tensor([G.nodes[n]['node_type'] for n in G.nodes()], dtype=torch.long)
    data = data.to(device)
    torch.save(data, output_path)
    print(data)
    print(f"Graph saved in PyG format to {output_path}")

save_as_pyg(G)

# Define a GraphSAGE model
class GraphSAGE(torch.nn.Module):
    def __init__(self, in_channels, hidden_channels, out_channels):
        super(GraphSAGE, self).__init__()
        self.conv1 = SAGEConv(in_channels, hidden_channels)
        self.conv2 = SAGEConv(hidden_channels, out_channels)

    def forward(self, x, edge_index):
        x = self.conv1(x, edge_index)
        x = F.relu(x)
        x = self.conv2(x, edge_index)
        return x

# Generate Node Features
def generate_node_features(G):
    # Create a feature matrix for nodes
    num_nodes = G.number_of_nodes()
    features = torch.zeros((num_nodes, 10))  # Example: 10 features per node
    for i, node in enumerate(G.nodes()):
        features[i, 0] = G.nodes[node]['node_type']  # Use node_type as one of the features
        features[i, 1] = G.nodes[node].get('attr1', 0)  # Example feature from node attribute
        features[i, 2] = 1 if G.nodes[node].get('attr2', 'unknown') == 'known' else 0  # Example binary feature
        for j in range(3, 10):
            features[i, j] = torch.rand(1).item()  # Random feature for demonstration
    return features

# Prepare data for GraphSAGE
def prepare_data(G):
    edge_index = torch.tensor(list(G.edges), dtype=torch.long).t().contiguous()
    x = generate_node_features(G)
    return x, edge_index

# Train the GraphSAGE model
def train_graphsage(G, num_epochs=100, hidden_channels=64):
    x, edge_index = prepare_data(G)
    model = GraphSAGE(in_channels=x.size(1), hidden_channels=hidden_channels, out_channels=10).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)

    model.train()
    for epoch in range(num_epochs):
        optimizer.zero_grad()
        out = model(x.to(device), edge_index.to(device))
        loss = F.mse_loss(out, x.to(device))  # Example loss; adjust as needed
        loss.backward()
        optimizer.step()
        print(f'Epoch {epoch + 1}, Loss: {loss.item():.4f}')

    return out.detach().cpu().numpy()

# Generate better embeddings using GraphSAGE
embeddings = train_graphsage(G)

# Save embeddings to a CSV file
pd.DataFrame(embeddings).to_csv(os.path.join(embedding_save_dir, 'better_embeddings.csv'), index=False)
print(f"Better embeddings saved to {os.path.join(embedding_save_dir, 'better_embeddings.csv')}")

# Add UMAP visualization function
def visualize_embeddings_with_umap(embeddings, output_path=os.path.join(data_dir, 'embeddings_viz.png')):
    # Apply UMAP to the embeddings
    umap_reducer = umap.UMAP(n_components=2, random_state=42)
    umap_results = umap_reducer.fit_transform(embeddings)

    # Create a scatter plot
    plt.figure(figsize=(10, 10))
    colors = ['red' if G.nodes[node]['type'] == 'customer' else 'blue' for node in G.nodes()]
    plt.scatter(umap_results[:, 0], umap_results[:, 1], c=colors, alpha=0.6)
    plt.title('UMAP Visualization of Node Embeddings')
    plt.xlabel('UMAP Component 1')
    plt.ylabel('UMAP Component 2')
    plt.savefig(output_path)
    plt.close()
    print(f"UMAP visualization saved to {output_path}")

visualize_embeddings_with_umap(embeddings)

# Save the PyG data object
final_graph_path = os.path.join(data_dir, 'dynamic_graph.pt')
save_as_pyg(G, final_graph_path)
print(f"Final graph saved to {final_graph_path}")
