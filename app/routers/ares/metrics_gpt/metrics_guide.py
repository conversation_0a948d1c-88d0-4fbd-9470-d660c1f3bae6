import json
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Any
from app.database import get_db
from app.api.endpoints.transaction_metrics import get_transaction_metrics as get_txn_metrics
from app.api.endpoints.financial_metrics import get_financial_metrics as get_fin_metrics

def example_metrics_analysis(merchant_id: str = "123456"):
    """
    Example function showing how to analyze merchant metrics
    
    Args:
        merchant_id: ID of the merchant to analyze
        
    Returns:
        Dict containing analysis results
    """
    try:
        # Get database session
        db = next(get_db())
        
        try:
            # Get transaction metrics
            transaction_metrics = get_txn_metrics(merchant_id, db)
            
            # Get financial metrics
            financial_metrics = get_fin_metrics(merchant_id, db)
            
            # Process transaction metrics
            transaction_analysis = {}
            if transaction_metrics:
                # Extract key metrics
                for metric in transaction_metrics:
                    label = metric.get("label")
                    value = metric.get("value")
                    transaction_analysis[label] = value
                    
                # Calculate additional metrics
                if "Charge Back Rate" in transaction_analysis:
                    chargeback_rate = float(transaction_analysis["Charge Back Rate"].replace("%", ""))
                    transaction_analysis["Chargeback Risk"] = "Low" if chargeback_rate < 2 else "High"
                    
                if "Daily Transactions" in transaction_analysis:
                    daily_txns = int(transaction_analysis["Daily Transactions"].replace(",", ""))
                    transaction_analysis["Transaction Volume"] = "High" if daily_txns > 300 else "Medium" if daily_txns > 100 else "Low"
            
            # Process financial metrics
            financial_analysis = {}
            if financial_metrics:
                # Extract key metrics
                for metric in financial_metrics:
                    label = metric.get("label")
                    value = metric.get("value")
                    financial_analysis[label] = value
                    
                # Calculate additional metrics
                if "Current Ratio" in financial_analysis:
                    current_ratio = float(financial_analysis["Current Ratio"])
                    financial_analysis["Liquidity Status"] = "Good" if current_ratio > 1.5 else "Adequate" if current_ratio > 1 else "Poor"
                    
                if "Altman Z-Score" in financial_analysis:
                    z_score = float(financial_analysis["Altman Z-Score"])
                    financial_analysis["Bankruptcy Risk"] = "Low" if z_score > 2.99 else "Medium" if z_score > 1.81 else "High"
            
            # Combine analyses
            combined_analysis = {
                "transaction_metrics": transaction_metrics,
                "financial_metrics": financial_metrics,
                "transaction_analysis": transaction_analysis,
                "financial_analysis": financial_analysis,
                "overall_assessment": {}
            }
            
            # Overall merchant assessment
            if transaction_analysis and financial_analysis:
                # Financial health assessment
                if "Current Ratio" in financial_analysis and "Working Capital" in financial_analysis:
                    current_ratio = float(financial_analysis["Current Ratio"])
                    working_capital = financial_analysis["Working Capital"]
                    
                    if current_ratio > 1.5 and not working_capital.startswith("-"):
                        combined_analysis["overall_assessment"]["financial_health"] = "Strong"
                    elif current_ratio > 1:
                        combined_analysis["overall_assessment"]["financial_health"] = "Stable"
                    else:
                        combined_analysis["overall_assessment"]["financial_health"] = "Weak"
                
                # Transaction pattern assessment
                if "Charge Back Rate" in transaction_analysis and "Daily Transactions" in transaction_analysis:
                    chargeback_rate = float(transaction_analysis["Charge Back Rate"].replace("%", ""))
                    daily_txns = int(transaction_analysis["Daily Transactions"].replace(",", ""))
                    
                    if chargeback_rate < 1 and daily_txns > 300:
                        combined_analysis["overall_assessment"]["transaction_pattern"] = "Excellent"
                    elif chargeback_rate < 2:
                        combined_analysis["overall_assessment"]["transaction_pattern"] = "Good"
                    else:
                        combined_analysis["overall_assessment"]["transaction_pattern"] = "Concerning"
                        
            return combined_analysis
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"Error in metrics analysis: {str(e)}")
        return {"error": str(e)}

if __name__ == "__main__":
    # Example usage
    merchant_id = "123456"  # Replace with a real merchant ID
    results = example_metrics_analysis(merchant_id)
    
    # Print results
    print("\nTransaction Metrics:")
    for metric in results.get("transaction_metrics", []):
        print(f"  {metric['label']}: {metric['value']}")
    
    print("\nFinancial Metrics:")
    for metric in results.get("financial_metrics", []):
        print(f"  {metric['label']}: {metric['value']}")
    
    print("\nOverall Assessment:")
    for key, value in results.get("overall_assessment", {}).items():
        print(f"  {key}: {value}") 