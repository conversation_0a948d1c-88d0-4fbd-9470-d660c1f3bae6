import torch
import os

class GPUConfig:
    @staticmethod
    def setup():
        if not torch.cuda.is_available():
            return False, "CPU", {}
        
        config = {
            'device': "cuda",
            'workers': min(4, os.cpu_count()),
            'batch_size': 64 if torch.cuda.get_device_properties(0).total_memory > 2000000000 else 32,
            'pin_memory': True,
            'mixed_precision': True,
            'memory_fraction': 0.7
        }
        
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.enabled = True
        torch.cuda.set_device(0)
        torch.cuda.empty_cache()
        
        return True, config['device'], config