from dataclasses import dataclass, field
from typing import List

@dataclass
class DataConfig:
    COLUMNS_TO_DROP: List[str] = field(default_factory=lambda: ['mer_id', 'txn_id', 'mer_business_industry', 'txn_timestamp'])
    CATEGORICAL_COLUMNS: List[str] = field(default_factory=lambda: ['mer_business_industry'])
    NUMERICAL_COLUMNS: List[str] = None  # Will be set dynamically
    TRANSACTIONS_PATH: str = 'data/transactions.csv'
    MERCHANTS_PATH: str = 'data/merchants.csv'
    FEATURES_PATH: str = 'data/transactions_with_features.csv'