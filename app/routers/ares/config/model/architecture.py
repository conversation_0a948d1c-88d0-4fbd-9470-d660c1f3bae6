import torch.nn as nn

class AutoencoderConfig:
    ENCODER_DIMS = [128, 64, 32, 16, 8]
    DECODER_DIMS = [8, 16, 32, 64, 128]
    DROPOUT_RATE = 0.2
    ACTIVATION = 'relu'

class Autoencoder(nn.Module):
    def __init__(self, input_dim):
        super(Autoencoder, self).__init__()
        config = AutoencoderConfig()
        
        # Encoder
        encoder_layers = []
        prev_dim = input_dim
        for dim in config.ENCODER_DIMS:
            encoder_layers.extend([
                nn.Linear(prev_dim, dim),
                nn.ReLU(),
                nn.Dropout(config.DROPOUT_RATE)
            ])
            prev_dim = dim
        self.encoder = nn.Sequential(*encoder_layers)
        
        # Decoder
        decoder_layers = []
        prev_dim = config.DECODER_DIMS[0]
        for dim in config.DECODER_DIMS[1:]:
            decoder_layers.extend([
                nn.Linear(prev_dim, dim),
                nn.ReLU(),
                nn.Dropout(config.DROPOUT_RATE)
            ])
            prev_dim = dim
        decoder_layers.extend([
            nn.Linear(prev_dim, input_dim),
            nn.Sigmoid()
        ])
        self.decoder = nn.Sequential(*decoder_layers)
    
    def forward(self, x):
        x = self.encoder(x)
        x = self.decoder(x)
        return x