# Remove extra features before saving
    # 'hour_sin', 'hour_cos',
    #     'day_sin', 'day_cos', 'month_sin', 'month_cos', 'txn_amount', 'velocity_transaction_lt',
        # 'velocity_transaction_7d', 'num_distinct_currency_used_lt', 'num_distinct_currency_used_7d',
        # 'curr_diversity_score_lt', 'curr_diversity_score_7d', 'invoice_and_txn_amt_diff_pct', 'late_night_txn_amt_avg_lt',
        # 'late_night_txn_amt_avg_7d'
        #  'international_txn_cnt_pct',
    # Add these features if needed for model training
features_to_keep = [
        'txn_id', 'mer_id', 'mer_business_industry', 'ip_density_lt',
        'device_id_density_lt', 'card_num_density_lt', 'cx_density_lt',
        'ip_density_7d', 'device_id_density_7d', 'card_num_density_7d',
        'cx_density_7d', 'txn_cnt_lt', 'txn_cnt_7d', 'txn_amt_avg_lt', 'txn_amt_avg_7d', 'chargeback_txn_cnt_pct_lt',
        'chargeback_txn_cnt_pct_7d', 'international_txn_cnt_pct_lt', 'international_txn_cnt_pct_7d', 'failed_txn_cnt_pct_lt',
        'failed_txn_cnt_pct_7d', 'risky_cx_txn_cnt_pct_lt', 'risky_cx_txn_cnt_pct_7d',
        'round_txn_cnt_pct_lt', 'round_txn_cnt_pct_7d', 'cancelled_txn_cnt_pct_lt', 'cancelled_txn_cnt_pct_7d',
        'name_mismatch_txn_cnt_pct_lt', 'name_mismatch_txn_cnt_pct_7d',
        'cx_complaint_txn_pct_7d', 'avg_cx_pii_score_lt', 'avg_cx_pii_score_7d',
         'hrs_since_last_transaction', 'txn_timestamp','late_night_txn_cnt_pct_lt', 'late_night_txn_cnt_pct_7d'
    ]

# Apply log transformation and multiplication to relevant features
transformed_columns_lt = ['ip_density_lt', 'device_id_density_lt', 
                        'card_num_density_lt', 'cx_density_lt', 
                        'chargeback_txn_cnt_pct_lt', 'international_txn_cnt_pct_lt', 
                        'failed_txn_cnt_pct_lt', 'cancelled_txn_cnt_pct_lt', 
                        'name_mismatch_txn_cnt_pct_lt', 
                        'risky_cx_txn_cnt_pct_lt', 'round_txn_cnt_pct_lt', 
                        'late_night_txn_cnt_pct_lt']

transformed_columns_7d = ['ip_density_7d', 'device_id_density_7d', 
                        'card_num_density_7d', 'cx_density_7d', 
                        'chargeback_txn_cnt_pct_7d', 'international_txn_cnt_pct_7d', 
                        'failed_txn_cnt_pct_7d', 'cancelled_txn_cnt_pct_7d', 
                        'cx_complaint_txn_pct_7d', 'name_mismatch_txn_cnt_pct_7d', 
                        'risky_cx_txn_cnt_pct_7d', 'round_txn_cnt_pct_7d', 
                        'late_night_txn_cnt_pct_7d']
