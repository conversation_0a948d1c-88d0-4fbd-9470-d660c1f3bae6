Schema = {
    'Transactions': """
- txn_id: str                    # Transaction identifier
- merchant_id: str               # Merchant identifier
- business_type: str             # Business types
- mer_business_industry: str             # Business types
- txn_timestamp: str             # Transaction timestamp
- txn_amount: float              # Transaction amount
- is_fraud_transaction: bool     # Fraud flag, tells if this transaction is fraud
- cx_id: str                     # Customer identifier
- cx_ip: str                     # Customer IP address
- cx_device_id: str              # Customer device ID
- cx_card_number: int            # Customer card number
- cx_pii_linkage_score: float    # Customer PII linkage score
- is_cardholder_name_match: bool # True if cardholder name match with the name in the transaction or receipt
- is_chargeback: bool            # True if the transaction is  a chargeback
- is_cx_international: bool      # True if customer is international
- txn_status: bool               # True if transaction did not fail
- is_cx_risky: bool             # True if customer was risky
- invoice_amount: float          # Amount in invoice
- is_cancelled: bool            # True if transaction was cancelled
- txn_currency: str             # currency of transaction
- has_cx_complaint: bool        # True if customer has complaint
""",

    'Merchants': """
- mer_id: str                   # Merchant identifier
- mer_onboarding_timestamp: str # Onboarding timestamp
- mer_business_industry: str   # Business types
- mer_incorporation_date: str   # Incorporation date of merchant
- mer_first_txn_date: str      # First transaction date
- mer_lst_txn_date: str        # Last transaction date
- mer_industry: str            # Merchant industry
- mer_industry_mca: str        # MCA industry classification
- mer_fraud_flag: bool         # True if merchant is fraud
- mer_avg_txn_size: float      # Average transaction size
- mer_total_txn: int          # Total transactions
- mer_total_txn_fy: int       # Total transactions this Financial Year
- mer_gst_risk_flag: bool     # If the merchant has not registered for gst
- mer_mca_fillings_risk_flag: bool  # if merchant has no mca fillings
- mer_directors_risk_flag: bool     # If the directors are risky or are in watchlist
- mer_num_employees: int           # Number of employees
- mer_epfo_reg_status: bool       # True if EPFO registration status is active
- mer_is_sanctioned: bool         # True if merchant is sanctioned
- mer_is_online_business: bool    # True if merchant has online business
- mer_online_presence_flag: bool  # True if merchant has online presence
- mer_tax_irregularity_flag: bool #  True if any tax irregularity is found
- mer_is_pan_compatible: bool     #  True if PAN compatibility is found
- mer_is_address_compatible: bool # True if address compatibility is found
- mer_prior_fraud_investigation_flag: bool  # True if merchant has prior fraud investigation
- mer_is_MCA_submission_taken: bool         # True if MCA submission is taken
- mer_udyam_cert_flag: bool                # True if merchant has Udyam certification
""",

    'Investigations': """
- investigation_id: str         # Unique identifier for the investigation
- case_number: str             # Case reference number
- investigation_status: str     # Current status of investigation
- title: str                   # Title of the investigation
- description: str             # Detailed description of the case
- priority: str                # Priority level of the investigation
- assignee_Name: str           # Name of person assigned to the case
- assignee_Email: str          # Email of the assignee
- merchant_id: str             # ID of merchant under investigation
- merchant_name: str           # Name of merchant under investigation
- last_updated: str            # Last update timestamp
- sla_deadline: str            # Service Level Agreement deadline
"""
}
