from enum import Enum
    
class RiskLevel(Enum):
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"

THRESHOLD = {
    'GST_THRESHOLD': 2000000,
    'EMPLOYEE_THRESHOLD': 20,
    'EPFO_TXN_THRESHOLD': 100,

}

# Define risk levels for each flag type
RISK_MAPPINGS = {
    'gst_violation': RiskLevel.HIGH,
    'revenue_discrepancy': RiskLevel.HIGH,
    'director_risk': RiskLevel.HIGH,
    'epfo_violation': RiskLevel.HIGH,
    'epfo_txn_mismatch': RiskLevel.HIGH,
    'sanctions': RiskLevel.HIGH,
    'online_presence': RiskLevel.MEDIUM,
    'pan_discrepancy': RiskLevel.HIGH,
    'address_mismatch': RiskLevel.MEDIUM,
    'mca_submission': RiskLevel.HIGH,
    'prior_fraud': RiskLevel.HIGH,
    'udyam_missing': RiskLevel.MEDIUM
}
