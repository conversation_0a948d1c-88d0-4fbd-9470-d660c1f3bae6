#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}Starting OCR processing...${NC}"
python3 RAG/ocr_pdf.py
OCR_STATUS=$?

if [ $OCR_STATUS -eq 0 ]; then
    echo -e "${GREEN}OCR processing completed successfully${NC}"
    
    echo -e "${BLUE}Starting Docker containers...${NC}"
    sudo docker compose up -d
    DOCKER_STATUS=$?
    
    if [ $DOCKER_STATUS -eq 0 ]; then
        echo -e "${GREEN}Docker containers started successfully${NC}"
        echo -e "${BLUE}Starting document embedding in existing collection...${NC}"
        
        # Try first embedding command
        python3 RAG/embed_docs.py -i default_collection
        EMBED_STATUS=$?
        
        if [ $EMBED_STATUS -ne 0 ]; then
            echo -e "${BLUE}First embedding attempt failed, trying with new collection...${NC}"
            # Try second embedding command
            python3 RAG/embed_docs.py -m default_collection
            EMBED_STATUS=$?
        fi
        
        if [ $EMBED_STATUS -eq 0 ]; then
            echo -e "${GREEN}Document embedding completed successfully${NC}"
        else
            echo -e "${RED}Error: Document embedding failed${NC}"
            exit 1
        fi
    else
        echo -e "${RED}Error: Failed to start Docker containers${NC}"
        exit 1
    fi
else
    echo -e "${RED}Error: OCR processing failed${NC}"
    exit 1
fi