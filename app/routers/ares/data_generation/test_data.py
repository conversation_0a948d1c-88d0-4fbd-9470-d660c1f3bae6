import pandas as pd
import numpy as np

# Set random seed for reproducibility
np.random.seed(42)

# Number of transactions per merchant
transactions_per_merchant = 1000
num_merchants = 11
num_transactions = transactions_per_merchant * num_merchants

# Generate normal transaction amounts
normal_txn_amounts = np.random.normal(loc=50, scale=10, size=num_transactions - 2 * transactions_per_merchant)

# Introduce one merchant with very high transaction amounts
high_txn_amounts = np.random.normal(loc=500, scale=50, size=transactions_per_merchant)

# Introduce another merchant with both high transaction amounts and abnormally high f1 values
abnormal_txn_amounts = np.random.normal(loc=500, scale=50, size=transactions_per_merchant)

# Generate transaction IDs
txn_ids = np.arange(1, num_transactions + 1)

# Generate merchant IDs
mer_ids = np.array(['merchant_' + str(i) for i in range(1, num_merchants - 1)] * transactions_per_merchant + 
                   ['merchant_10'] * transactions_per_merchant + 
                   ['merchant_11'] * transactions_per_merchant)

# Generate features f1 and f2
f1_normal = np.random.normal(loc=0, scale=1, size=num_transactions - 2 * transactions_per_merchant)
f1_merchant10 = np.random.normal(loc=0, scale=1, size=transactions_per_merchant)
f1_merchant11 = np.random.normal(loc=100, scale=1, size=transactions_per_merchant)
f1 = np.concatenate((f1_normal, f1_merchant10, f1_merchant11))
f2 = np.random.normal(loc=0, scale=1, size=num_transactions)

# Create the dataset
data = {
    'txn_id': txn_ids,
    'txn_amount': np.concatenate((normal_txn_amounts, high_txn_amounts, abnormal_txn_amounts)),
    'mer_id': mer_ids,
    'f1': f1,
    'f2': f2,
    'mer_business_industry': ['retail'] * num_transactions,
    'txn_timestamp': pd.date_range(start='2023-01-01', periods=num_transactions, freq='min'),
    'is_fraud_transaction': np.concatenate((np.full(num_transactions - 2 * transactions_per_merchant, False),
                                          np.full(transactions_per_merchant, True),
                                          np.full(transactions_per_merchant, True)))
}

df = pd.DataFrame(data)

# Save the transactions dataset
df.to_csv('data/test_transactions.csv', index=False)

# Create and save the merchants dataset
merchants_data = {
    'mer_id': ['merchant_' + str(i) for i in range(1, num_merchants - 1)] + ['merchant_10', 'merchant_11'],
    'mer_fraud_flag': [False] * (num_merchants - 2) + [True, True],
    'mer_business_industry': ['retail'] * num_merchants
}

merchants_df = pd.DataFrame(merchants_data)
merchants_df.to_csv('data/test_merchants.csv', index=False)

# Create and save transactions with features
transactions_with_features_df = df.drop(columns=['is_fraud_transaction'])
transactions_with_features_df.to_csv('data/test_transactions_with_features.csv', index=False)

print("Datasets generated and saved successfully")