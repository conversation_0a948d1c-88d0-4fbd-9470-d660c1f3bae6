Overall goal:
Project Goal: To generate synthetic transacitons for merchants for a payment aggregator. The goal is to train an autoencoder model to detect anomalies in the transaction data and then use SHAP values to explain the anomalies.

Steps: First figure out the merchants and then generate the transactions.

Transaction Goal: to create a list of transactions, corresponding to the merchants.

| Column Name | Data Type | Description |
|-------------|-----------|-------------|
| txn_id | string | Unique identifier for each transaction | (T004_M0460)
| mer_id | string | Merchant identifier linking to merchant table | (M0460)
| mer_business_industry | string | Type of business/industry of the merchant | (Retail)
| mer_onboarding_timestamp | TIMESTAMP | Date and time when merchant was onboarded to the platform | (2021-06-12 10:55:00)
| mer_fraud_flag | BOOLEAN | Flag indicating if merchant has been involved in fraud | (true/ false)
| txn_timestamp | datetime | Date and time when transaction occurred | (2021-06-12 10:55:00)
| txn_amount | float | Transaction amount in base currency | (123.90)
| txn_currency | string | Currency code of the transaction | (INR)
| txn_invoice_amt | float | Original invoice amount in transaction currency | (124.90)
| cx_id | string | Unique customer identifier | (CX75808)
| cx_ip | string | IP address used for transaction | (***********)
| cx_device_id | string | Unique identifier for customer's device | (D745584)
| cx_card_number | string | Masked/hashed credit card number | (CARD_8002)
| cx_pii_linkage_score | float | Score indicating confidence in customer identity match (0-1) | (0.8)
| is_cardholder_name_match | boolean | Whether cardholder name matches account | (true/false)
| is_chargeback | boolean | Whether transaction was charged back | (true/false)
| is_cx_international | boolean | Whether customer is from different country than merchant | (true/false)
| txn_status | boolean | Current status of transaction | (true/false)
| is_cx_risky | boolean | Whether customer is flagged as risky  | (true/false)
| is_cancelled | boolean | Whether transaction was cancelled  | (true/false)
| has_cx_complaint | boolean | Whether customer filed a complaint  | (true/false)
| is_fraud_transaction | boolean | Whether transaction was confirmed fraudulent  | (true/false)
| txn_red_flags | string | List of red flags injected for this merchant till now only for fraud merchants | ([Amount, IP])

txn_red_flags = [] for normal merchants

Derived metrics:
| Feature Name | Description | Calculation Method | Formula |
|--------------|-------------|--------------------|---------|
| txn_cnt_lt | Merchant has made {} transactions in total | Count of all transactions | COUNT(txn_id) |
| txn_cnt_7d | Merchant has made {} transactions in the last 7 days | Count of transactions in the last 7 days | COUNT(txn_id) WHERE txn_timestamp >= NOW() - INTERVAL '7 days' |
| txn_amt_avg_lt | Average transaction amount is ${:.4f} | Average of all transaction amounts | AVG(txn_amount) |
| txn_amt_avg_7d | Average transaction amount in the last 7 days is ${:.4f} | Average of transaction amounts in the last 7 days | AVG(txn_amount) WHERE txn_timestamp >= NOW() - INTERVAL '7 days' |
| ip_density_lt_score_lt | IP density is {:.4f} | Density of unique IP addresses | COUNT(DISTINCT cx_ip) / COUNT(txn_id) |
| device_id_density_lt_score_lt | Device ID density is {:.4f} | Density of unique device IDs | COUNT(DISTINCT cx_device_id) / COUNT(txn_id) |
| card_num_density_lt_score_lt | Card number density is {:.4f} | Density of unique card numbers | COUNT(DISTINCT cx_card_number) / COUNT(txn_id) |

Data parameters
- Number of normal merchants = 9
- Min Number of transactions per merchant = 5
- Number of fraud merchants = 3
- Probaility of fraud transaction given merchant is fraud = 100%
- Probability of fraud transaction given merchant is not fraud = 0%

Notes
- is_fraud_transaction is only true if the mer_fraud_flag is True, but vice versa may not be true
- The merchant id of a single merchant should be same across transactions,
- the transactions on any merchant should only begin after the merchant was onboarded
- if is_chargeback is true, then has_cx_complaint should be true
- Each merchant has unique merchant id, business industry, onboarding timestamp
- each transaction can have 1 to 5 red flags. Collect the list of red flags in the txn_red_flags column

Red flags
- One fraud merchant has abnormally high txn_count
- One fraud merchant has same customer ip for all transactions
