# pip install gretel-client
import os
from pathlib import Path
from dotenv import load_dotenv
from gretel_client import Gretel

# Load environment variables from .env file
load_dotenv()

# Get API key with error handling
api_key = os.environ.get("GRETEL_API_KEY")
if not api_key:
    raise ValueError(
        "GRETEL_API_KEY not found. Please set it in environment or .env file"
    )

# Read prompt from data generation file
prompt_file = Path(__file__).parent / "data_generation_prompt.md"
with open(prompt_file, "r") as f:
    prompt = f.read()

# Initialize Gretel client
gretel = Gretel(api_key=api_key)

# list available backend models for Navigator Tabular 
print(gretel.factories.get_navigator_model_list("tabular"))

# the `backend_model` argument is optional and defaults "gretelai/auto"
tabular = gretel.factories.initialize_navigator_api(
    "tabular", backend_model="gretelai/auto"
)

# generate tabular data from the prompt file
df = tabular.generate(prompt, num_records=300)

# save the generated data to a CSV file
df.to_csv("data/gretel_data.csv", index=False)