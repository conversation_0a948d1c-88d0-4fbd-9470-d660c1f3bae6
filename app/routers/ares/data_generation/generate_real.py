import pandas as pd
import numpy as np
import random
import argparse
from datetime import datetime, timedelta

# Add argument parser
parser = argparse.ArgumentParser(description='Generate synthetic transaction data')
parser.add_argument('--num_merchants', type=int, default=1000, help='Number of merchants')
parser.add_argument('--txns_per_merchant', type=int, default=100, help='Transactions per merchant')
args = parser.parse_args()

np.random.seed(42)
random.seed(42)

def generate_random_ip():
    return f"{random.randint(1,255)}.{random.randint(0,255)}.{random.randint(0,255)}.{random.randint(1,255)}"

def generate_card_number():
    # Common card prefixes
    prefixes = ['4', '51', '34', '6011']  # Visa, Mastercard, Amex, Discover
    prefix = random.choice(prefixes)
    remaining_length = 16 - len(prefix)
    rest = ''.join([str(random.randint(0,9)) for _ in range(remaining_length)])
    return prefix + rest

# Generate merchant IDs
mer_ids = [f'M{str(i).zfill(6)}' for i in range(args.num_merchants)]

# Generate random timestamps between 2020-2024 with time component
def random_timestamp():
    start = datetime(2020, 1, 1, 0, 0, 0)
    end = datetime(2024, 1, 1, 23, 59, 59)
    delta = end - start
    random_seconds = random.randrange(int(delta.total_seconds()))
    return start + timedelta(seconds=random_seconds)

# Create industry distribution
industries = ['Retail', 'Gambling', 'Restaurant']
mer_industries = random.choices(industries, k=args.num_merchants)

# Create fraud flags - 10% merchants will be fraud
num_fraud = int(args.num_merchants * 0.02)
fraud_flags = [False] * (args.num_merchants - num_fraud) + [True] * num_fraud
random.shuffle(fraud_flags)

# Ensure fraud merchants are distributed across industries
fraud_indices = [i for i, flag in enumerate(fraud_flags) if flag == 1]
industry_counts = {'Retail': 0, 'Gambling': 0, 'Restaurant': 0}
for idx in fraud_indices:
    industry = mer_industries[idx]
    industry_counts[industry] += 1

# Adjust if distribution is too skewed
while max(industry_counts.values()) > 8:  # No more than 8 frauds per industry
    i1, i2 = random.sample(range(args.num_merchants), 2)
    if fraud_flags[i1] == 1 and fraud_flags[i2] == 0:
        if industry_counts[mer_industries[i1]] > industry_counts[mer_industries[i2]]:
            mer_industries[i1], mer_industries[i2] = mer_industries[i2], mer_industries[i1]
            industry_counts[mer_industries[i1]] += 1
            industry_counts[mer_industries[i2]] -= 1

# Create DataFrame with boolean fraud flags
merchants_df = pd.DataFrame({
    'mer_id': mer_ids,
    'mer_fraud_flag': fraud_flags, 
    'mer_onboarding_timestamp': [random_timestamp() for _ in range(args.num_merchants)],
    'mer_business_industry': mer_industries
})

# Save to CSV
merchants_df.to_csv('data/merchants.csv', index=False)
print(f"Data saved to 'data/merchants.csv'")

# After saving merchants.csv, generate transactions
print("Generating transactions...")

# Initialize lists for transaction data
all_txns = []

# Define amount distributions per industry
amount_params = {
    'Retail': {'mean': 100, 'std': 20},
    'Gambling': {'mean': 800, 'std': 200},
    'Restaurant': {'mean': 300, 'std': 50}
}

currencies = ['INR', 'USD', 'JPY']

# Instead of selecting 5 specific fraud merchants, distribute all fraud merchants
# Instead of selecting 5 specific fraud merchants, distribute all fraud merchants
fraud_merchants = merchants_df[merchants_df['mer_fraud_flag']].copy()
fraud_types = ['high_amount', 'night_txn', 'chargeback', 'same_ip', 'same_device']

# Assign fraud types to merchants
fraud_merchants['fraud_type'] = random.choices(fraud_types, k=len(fraud_merchants))

# Create sets for each fraud type
high_amount_merchants = set(fraud_merchants[fraud_merchants['fraud_type'] == 'high_amount']['mer_id'])
night_txn_merchants = set(fraud_merchants[fraud_merchants['fraud_type'] == 'night_txn']['mer_id'])
chargeback_merchants = set(fraud_merchants[fraud_merchants['fraud_type'] == 'chargeback']['mer_id'])
same_ip_merchants = set(fraud_merchants[fraud_merchants['fraud_type'] == 'same_ip']['mer_id'])
same_device_merchants = set(fraud_merchants[fraud_merchants['fraud_type'] == 'same_device']['mer_id'])

# Generate transactions for each merchant
for _, merchant in merchants_df.iterrows():
    for i in range(args.txns_per_merchant):
        # Generate transaction timestamp after merchant onboarding
        onboarding_ts = pd.to_datetime(merchant['mer_onboarding_timestamp'])
        max_hours_after = (datetime(2024, 1, 1) - onboarding_ts).total_seconds() / 3600
        hours_after = random.uniform(1, min(max_hours_after, 120))  # max 5 days after
        txn_timestamp = onboarding_ts + timedelta(hours=hours_after)
        
        # Generate amount based on industry
        industry = merchant['mer_business_industry']
        amount = np.random.normal(
            amount_params[industry]['mean'],
            amount_params[industry]['std']
        )
        amount = max(10.0, round(amount, 2))  # Ensure minimum amount of 10.0
        
        # Initialize chargeback and complaint flags
        is_chargeback = False
        has_complaint = False

        # Apply fraud patterns based on merchant type
        if merchant['mer_id'] in high_amount_merchants:
            amount *= 10  # Abnormally high transaction amounts
        
        if merchant['mer_id'] in night_txn_merchants:
            if random.random() < 0.9:  # 90% of transactions
                txn_timestamp = txn_timestamp.replace(hour=random.randint(1, 4))
        
        if merchant['mer_id'] in chargeback_merchants:
            if random.random() < 0.9:  # 90% of transactions
                is_chargeback = True
                has_complaint = True
        else:
            is_chargeback = random.choice([True, False])
            has_complaint = True if is_chargeback else random.choice([True, False])

        if merchant['mer_id'] in same_ip_merchants:
            ip_address = random.choice(['***********', '***********', '***********'])
        else:
            ip_address = generate_random_ip()

        if merchant['mer_id'] in same_device_merchants:
            device_id = random.choice(['D-00001', 'D-00002', 'D-00003'])
        else:
            device_id = f"D-{str(random.randint(0, 99999)).zfill(5)}"
        
        # Generate transaction with additional boolean fields
        txn = {
            'txn_id': f'T{str(len(all_txns)).zfill(8)}',
            'mer_id': merchant['mer_id'],
            'mer_business_industry': merchant['mer_business_industry'],
            'txn_timestamp': txn_timestamp,
            'txn_amount': amount,
            'txn_invoice_amt': amount,
            'txn_currency': random.choice(currencies),
            'cx_id': f'C{str(random.randint(0, 9999)).zfill(6)}',
            'cx_ip': ip_address,
            'cx_device_id': device_id,
            'cx_card_number': generate_card_number(),
            'cx_pii_linkage_score': round(random.uniform(0, 1), 2),
            'is_fraud_transaction': merchant['mer_fraud_flag'],
            'is_cardholder_name_match': random.choice([True, False]),
            'is_chargeback': is_chargeback,
            'is_cx_international': random.choice([True, False]),
            'txn_status': random.choice([True, False]),
            'is_cx_risky': random.choice([True, False]),
            'is_cancelled': random.choice([True, False]),
            'has_cx_complaint': has_complaint
        }
        all_txns.append(txn)

# Create transactions DataFrame
txns_df = pd.DataFrame(all_txns)

# Sort by timestamp
txns_df = txns_df.sort_values('txn_timestamp')

# Save transactions to CSV
txns_df.to_csv('data/transactions.csv', index=False)
print(f"Generated {len(txns_df)} transactions for {args.num_merchants} merchants")
print(f"({args.txns_per_merchant} transactions per merchant)")
print("Data saved to 'data/transactions.csv'")

# Print fraud merchant type distribution
print("\nFraud Merchant Type Distribution:")
print(f"High amount merchants: {(high_amount_merchants)}")
print(f"Night transaction merchants: {(night_txn_merchants)}")
print(f"High chargeback merchants: {(chargeback_merchants)}")
print(f"Same IP merchants: {(same_ip_merchants)}")
print(f"Same device merchants: {(same_device_merchants)}")

