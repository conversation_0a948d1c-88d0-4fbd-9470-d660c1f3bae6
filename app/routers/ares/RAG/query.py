import os
import sys
from dotenv import load_dotenv
from pymilvus import MilvusClient
from sentence_transformers import SentenceTransformer
from pymilvus.model import dense
from groq import Groq
import argparse
import time
import warnings
import torch
warnings.filterwarnings("ignore")

load_dotenv()
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
data_dir = os.path.join(project_root, 'data')
os.makedirs(data_dir, exist_ok=True)
MILVUS_URI = os.getenv("MILVUS_URI", "http://localhost:19530")
MILVUS_TOKEN = os.getenv("MILVUS_TOKEN", "")
LLM_MODEL = "llama-3.3-70b-versatile"

parser = argparse.ArgumentParser(description="Manage Milvus collection and insert data.")
parser.add_argument("-c", "--collection", metavar="COLLECTION_NAME", help="Specify the collection name.")
args = parser.parse_args()

collection_name = args.collection

def get_llm_answer(query, search_results):
    """Get answer from Groq LLM API"""
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        raise ValueError("GROQ_API_KEY not found in environment variables")
        
    client = Groq(api_key=api_key)
    
    system_message = """You are a question answering RAG mchine that answers questions based on the context provided. 
    Provide accurate answers using only the provided context. 
    If the answer cannot be found in the context, say so."""

    prompt = f"""Based on the following context items answer the query. 
    Give yourself room to think by extracting relevant passages from the context before answering the query.
    Don't return the thinking, return only the answer.
    Make sure your answers are as explanatory as possible. 
    Context_items: {search_results}
    Look over the whole context and take all relevant parts for answering the query.
    Query : {query}
    Make sure your answer covers the whole context
    """
    # print(prompt)

    chat_completion = client.chat.completions.create(
        messages=[
            {
                "role": "system", 
                "content": system_message
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        model=LLM_MODEL,
        temperature=0.7,
        max_tokens=1024,
        top_p=1,
        stream=False
    )
    
    return chat_completion.choices[0].message.content

client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)

if not client.has_collection(collection_name=collection_name):
    print(f"Collection {collection_name} not found. Please create the collection first.")
    sys.exit(1)

try:
    client.load_collection(collection_name=collection_name, replica_number=1)
    print(f"Collection {collection_name} loaded successfully")
except Exception as e:
    print(f"Failed to load collection: {e}")
    sys.exit(1)

model = SentenceTransformer('all-mpnet-base-v2')
device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
model = model.to(device)

def encode_query(query):
    return model.encode([query])[0]

while True:
    query = input("Enter the query, type 'quit' to quit: ")
    if "quit" in query.lower().strip():
        break
    start_time = time.time()
    embed_start = time.time()
    query_embeddings = model.encode([query])
    embed_time = time.time() - embed_start

    # Print dimension and shape of embeddings
    print("Dim:", model.dim, query_embeddings[0].shape)

    search_start = time.time()
    query_vector = encode_query(query)
    try:
        res = client.search(
            collection_name=collection_name,
            data=[query_vector],
            anns_field="vector",
            param={"metric_type": "IP", "params": {"nprobe": 15}},
            limit=30,
            output_fields=["text"]
        )
    except Exception as e:
        print(f"Search failed: {e}")
        sys.exit(1)
    search_time = time.time() - search_start

    search_results = []
    for idx, result in enumerate(res[0]):
        search_results.append(result["entity"]["text"])

    rerank_start = time.time()
    reranker = model.reranker.CrossEncoderRerankFunction(
        model_name="cross-encoder/ms-marco-MiniLM-L-6-v2",  # Specify the model name. Defaults to `BAAI/bge-reranker-v2-m3`.
        device="cuda:0", # Specify the device to use, e.g., 'cpu' or 'cuda:0'
        batch_size=32,  # Specify the batch size for reranking
    )
    # No need for gpu as total results are very less before reranking

    reranked_results = reranker(
        query=query,
        documents=search_results,
        top_k=10
    )
    rerank_time = time.time() - rerank_start

    formatted_results = ""
    for result in reranked_results:
        formatted_results += f"- {result.text}\n"

    # Get LLM answer
    llm_start = time.time()
    answer = get_llm_answer(query, formatted_results)
    llm_time = time.time() - llm_start

    total_time = time.time() - start_time

    print("\nAnswer from LLM:")
    print("-" * 80)
    print(answer)
    print("-" * 80)
    print("\nTiming Breakdown:")
    print(f"Embedding Generation: {embed_time:.2f}s")
    print(f"Vector Search: {search_time:.2f}s")
    print(f"Reranking: {rerank_time:.2f}s")
    print(f"LLM Response: {llm_time:.2f}s")
    print(f"Total Time: {total_time:.2f}s")


