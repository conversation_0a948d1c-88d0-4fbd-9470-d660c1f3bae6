import pytesseract
from pdf2image import convert_from_path
from fpdf import FPDF
import os
from tqdm import tqdm
import logging

logger = logging.getLogger(__name__)

def convert_pdf_to_images(pdf_path):
    """
    Convert PDF to a list of PIL images
    """
    try:
        # Convert PDF to images with progress bar
        print("Converting PDF to images...")
        images = convert_from_path(pdf_path)
        return list(tqdm(images, desc="Converting pages"))
    except Exception as e:
        print(f"Error converting PDF to images: {e}")
        return None

def check_tesseract_installation():
    """Check if tesseract is installed and accessible"""
    try:
        pytesseract.get_tesseract_version()
        return True
    except Exception:
        return False

def perform_ocr(images):
    """
    Perform OCR on a list of images and return the extracted text
    """
    if not check_tesseract_installation():
        raise RuntimeError(
            "Tesseract is not installed or not in PATH. Please install tesseract-ocr "
            "and ensure it's properly configured."
        )
    
    extracted_text = []
    
    # Add progress bar for OCR processing
    for image in tqdm(images, desc="Performing OCR"):
        try:
            text = pytesseract.image_to_string(image)
            extracted_text.append(text)
        except Exception as e:
            logger.error(f"Error performing OCR on page: {str(e)}")
            extracted_text.append("")
    
    return extracted_text

def save_text_to_pdf(text_list, output_path):
    """
    Save extracted text to a new PDF file
    """
    try:
        pdf = FPDF()
        pdf.set_auto_page_break(auto=True, margin=15)
        pdf.add_page()
        pdf.set_font("Arial", size=12)
        
        # Add progress bar for PDF creation
        for page_text in tqdm(text_list, desc="Creating PDF"):
            lines = page_text.split('\n')
            for line in lines:
                try:
                    pdf.multi_cell(0, 10, txt=line.encode('latin-1', 'replace').decode('latin-1'))
                except Exception as e:
                    print(f"Error writing line to PDF: {e}")
                    continue
            
            if page_text != text_list[-1]:
                pdf.add_page()
        
        pdf.output(output_path)
        return True
    except Exception as e:
        print(f"Error saving PDF: {e}")
        return False

def process_pdf(input_pdf_path, output_pdf_path):
    """
    Main function to process PDF and perform OCR
    """
    # Convert PDF to images
    images = convert_pdf_to_images(input_pdf_path)
    if not images:
        return False
    
    # Perform OCR on images
    extracted_text = perform_ocr(images)
    if not extracted_text:
        return False
    
    # Save extracted text to new PDF
    success = save_text_to_pdf(extracted_text, output_pdf_path)
    return success

def output_file_exists(output_path):
    """Check if output PDF file already exists"""
    return os.path.exists(output_path)

if __name__ == "__main__":
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    data_dir = os.path.join(project_root, 'data')
    document_dir = os.path.join(data_dir, "documents")
    ocr_dir = os.path.join(document_dir, "ocr")

    # Get all PDF files from ocr directory
    pdf_files = [f for f in os.listdir(ocr_dir) if f.lower().endswith('.pdf')]
    
    if not pdf_files:
        print("No PDF files found in the OCR directory")
        exit()

    # Process each PDF file with progress bar
    for pdf_file in tqdm(pdf_files, desc="Processing PDFs"):
        input_pdf = os.path.join(ocr_dir, pdf_file)
        # Create output filename by inserting _text before .pdf
        output_filename = pdf_file[:-4] + "_text.pdf"
        output_pdf = os.path.join(document_dir, output_filename)
        
        # Skip if output file already exists
        if output_file_exists(output_pdf):
            print(f"\nSkipping {pdf_file} - output file already exists")
            continue
            
        print(f"\nProcessing: {pdf_file}")
        if process_pdf(input_pdf, output_pdf):
            print(f"Successfully processed {pdf_file} and saved results to {output_filename}")
        else:
            print(f"Error processing {pdf_file}")