import os
from pydoc import text
import sys
import json
import hashlib
from pathlib import Path
import datetime
from dotenv import load_dotenv
from tqdm import tqdm
from langchain_community.document_loaders import PyMuPDFLoader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from pymilvus import MilvusClient, model, DataType
import argparse
import warnings
warnings.filterwarnings("ignore")

load_dotenv()
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
data_dir = os.path.join(project_root, 'data')
os.makedirs(data_dir, exist_ok=True)
document_dir = os.path.join(data_dir, "documents")
tracking_file = os.path.join(data_dir, "processed_files.json")
MILVUS_URI = os.getenv("MILVUS_URI")
MILVUS_TOKEN = os.getenv("MILVUS_TOKEN")

def remove_tracking_file():
    """Remove the tracking file if it exists"""
    if os.path.exists(tracking_file):
        os.remove(tracking_file)
        print("Tracking file removed.")

parser = argparse.ArgumentParser(description="Manage Milvus collection and insert data.")
parser.add_argument("-m", "--modify", metavar="COLLECTION_NAME", help="Drop and recreate the collection.")
parser.add_argument("-i", "--insert", metavar="COLLECTION_NAME", help="Insert into the existing collection.")
parser.add_argument("-l", "--list", action="store_true", help="List all collections available in Milvus.")
parser.add_argument("-d", "--delete", metavar="COLLECTION_NAME", help="Delete from the existing collection.")
args = parser.parse_args()

if not args.modify and not args.insert and not args.list and not args.delete:
    parser.print_help()
    sys.exit(1)

collection_name = args.modify or args.insert or args.delete
client = MilvusClient(uri=MILVUS_URI, token=MILVUS_TOKEN)

if args.modify:
    if client.has_collection(collection_name=collection_name):
        client.drop_collection(collection_name=collection_name)
        remove_tracking_file()

    schema = MilvusClient.create_schema(
        auto_id=False,
        enable_dynamic_field=True,
        clustering_key="id"
    )
    schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True)
    schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=768)        

    client.create_collection(
        collection_name=collection_name,
        schema=schema,
        consistency_level="Bounded"
    )
    print(f"New collection {collection_name} created ")

if args.delete:
    if not client.has_collection(collection_name=collection_name):
        print("Collection does not exist. Cannot be deleted")
        sys.exit(1)
    client.drop_collection(collection_name=collection_name)
    remove_tracking_file()
    print(f"Collection {collection_name} deleted.")
    sys.exit(0)

if args.insert:
    if not client.has_collection(collection_name=collection_name):
        print("Collection does not exist. Use --modify or -m to create a new collection.")
        sys.exit(1)

if args.list:
    collections = client.list_collections()
    print("Collections available in Milvus:")
    for collection in collections:
        print(collection)
    sys.exit(0)

sentence_transformer_ef = model.dense.SentenceTransformerEmbeddingFunction(
    model_name='all-mpnet-base-v2', 
    # model_name='multi-qa-mpnet-base-dot-v1',
    device='cuda:0',  # Specify the device to use, e.g., 'cpu' or 'cuda:0'
    normalize_embeddings=True
)

print("Loading docs...")
def get_file_hash(filepath):
    """Calculate SHA-256 hash of file"""
    sha256_hash = hashlib.sha256()
    with open(filepath, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()

def load_processed_files(tracking_file):
    """Load list of processed files from JSON"""
    if os.path.exists(tracking_file):
        with open(tracking_file, 'r') as f:
            return json.load(f)
    return {}

def save_processed_files(tracking_file, processed_files):
    """Save list of processed files to JSON"""
    with open(tracking_file, 'w') as f:
        json.dump(processed_files, f, indent=2)

processed_files = load_processed_files(tracking_file)
pdf_files = [f for f in os.listdir(document_dir) if f.lower().endswith('.pdf')]
txt_files = [f for f in os.listdir(document_dir) if f.lower().endswith('.txt')]

text_documents = []

for pdf_file in tqdm(pdf_files, desc="Loading PDFs"):
    pdf_path = os.path.join(document_dir, pdf_file)
    file_hash = get_file_hash(pdf_path)
    
    if file_hash in processed_files:
        print(f"Skipping {pdf_file} - already processed")
        continue
        
    loader = PyMuPDFLoader(pdf_path)
    docs = loader.load()
    text_documents.extend(docs)
    
    # Record processed file
    processed_files[file_hash] = {
        'filename': pdf_file,
        'processed_date': str(datetime.datetime.now()),
        'collection': collection_name
    }

for txt_file in tqdm(txt_files, desc="Loading Text files"):
    txt_path = os.path.join(document_dir, txt_file)
    file_hash = get_file_hash(txt_path)
    
    if file_hash in processed_files:
        print(f"Skipping {txt_file} - already processed")
        continue
        
    loader = TextLoader(txt_path, encoding = 'UTF-8')
    docs = loader.load()
    text_documents.extend(docs)
    
    # Record processed file
    processed_files[file_hash] = {
        'filename': txt_file,
        'processed_date': str(datetime.datetime.now()),
        'collection': collection_name
    }

if not text_documents:
    print("No new documents to process. Exiting...")
    sys.exit(0)

print("Splitting text into chunks for retrieval...")
text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=100)
doc_chunks = text_splitter.split_documents(text_documents)
docs = list(tqdm([doc.page_content for doc in doc_chunks], desc="Splitting documents"))

print("Creating embeddings for document chunks...")
vectors = []
for doc in tqdm(docs, desc="Creating embeddings"):
    vector = sentence_transformer_ef.encode_documents([doc])
    vectors.append(vector[0])
print("Dim:", sentence_transformer_ef.dim, vectors[0].shape)

data = [
    {"id": i, "vector": vectors[i], "text": docs[i]}
    for i in range(len(vectors))
]

print("Data has", len(data), "entities, each with fields: ", data[0].keys())
print("Vector dim:", len(data[0]["vector"]))

batch_size = 1000
for i in range(0, len(data), batch_size):
    batch = data[i:i + batch_size]
    client.insert(collection_name=collection_name, data=batch)
    client.flush(collection_name)
    
print(f"Data inserted successfully into {collection_name}")
index_params = MilvusClient.prepare_index_params()
index_params.add_index(
    field_name="vector",
    metric_type="IP",
    index_type="GPU_IVF_PQ",
    index_name="vector_index",
    params={ 
        "nlist": 128, 
        "m": 64,
        "nbits": 8,
        "gpu_id": 0,
        "cache_dataset_on_device": "true"
    }
)
client.create_index(
    collection_name=collection_name,
    index_params=index_params,
    sync=True # Whether to wait for index creation to complete before returning. Defaults to True.
)
res_index = client.describe_index(
    collection_name=collection_name,
    index_name="vector_index"
)
print(res_index)

if res_index:
    save_processed_files(tracking_file, processed_files)

