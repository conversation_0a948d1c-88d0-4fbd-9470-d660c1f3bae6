# Merchant Fraud Detection System

## Overview
This project implements an end-to-end merchant fraud detection system leveraging synthetic data generation, feature engineering, and anomaly detection using autoencoders. The system incorporates LLM-based analysis for explainable fraud detection, providing enhanced insights.

## Project Structure

### config
- **feature_descriptions.py**: Contains descriptions of features provided to the **Red Flag LLM** for analysis. These features may not necessarily appear in the CSV files.
- **merchant_flag_configs.py**: Contains configs related to merchant features and external data red flags

### data
- Directory for all generated CSV files.

### data_generation
- **generate.py**: Generates both merchant and transaction datasets, injecting fraudulent patterns into normal transactions.

### feature_generation
- **feature_names.md**: Describes features calculated for the transaction dataset. Not all features are reflected in the CSV files.
- **gen_features.py**: Generates features for the entire transaction dataset. (Use **incremental_feature_gen.py** instead for efficiency.)
- **incremental_feature_gen.py**: Calculates features incrementally for the transaction dataset, which are utilized for autoencoder training.

### model
- **train.py**: Trains the autoencoder model, saves the model's state dictionary, and displays evaluation metrics.
- **inference.py**: Performs inference using the saved autoencoder model and computes `SHAP values` to highlight important predictive features.
- **llm_transaction_analysis.py**: Uses the **Red Flag LLM** to generate a detailed response for a specific transaction ID, identifying potential red flags.
- **summarizer.py**: Combines **Red Flag LLM** analysis with merchant profiles to generate key insights using the **Summarizer LLM**.
- **merchant_analysis.py**: Calculates rule based external data red flags on merchant level.
- **autoencoder.pth**: The saved autoencoder model.
- **current_performance.txt**: A record of the model's current performance metrics as evaluated on the test dataset.

### for_lazy_llm_coder
- **llm_lord.py**: The **Data Generation LLM**, which generates code based on specified fraud types to inject into normal transactions.
- **llm_lord_helper_guide.py**: A guide file fed into the **Data Generation LLM** to facilitate fraud pattern generation.
- **llm_lord_sermon.py**: Code generated by the **Data Generation LLM**.
- **summon_llm_lord.py**: Executes the **Data Generation LLM** to generate data scripts, runs them to create datasets, and passes the results to the **Red Flag LLM** for analysis.

### run_pipeline.sh
- Automates the pipeline: generates synthetic data, calculates features, and trains the autoencoder model.

## How to Use

1. **Generate Synthetic Data**:
   ```bash
   python3 data_generation/generate.py
   ```
   Datasets are saved at `data/merchants.csv` and `data/transactions.csv`.

2. **Generate Features**:
   ```bash
   python3 feature_generation/incremental_feature_gen.py --transactions_path data/transactions.csv --merchants_path data/merchants.csv --output_path data/transactions_features.csv
   ```
   Features are saved at `data/transactions_features.csv`.

3. **Train the Model**:
   ```bash
   python3 model/train.py --epochs 150 --learning_rate 0.001
   ```
   The trained model is saved as `data/autoencoder.pth`.

4. **Analyze Transactions**:
   ```bash
   python3 model/llm_transaction_analysis.py --txn_id T00012974 --transactions_path data/transactions_with_features.csv --merchants_path data/merchants.csv
   ```

5. **Generate LLM-based Code**:
   ```bash
   python3 for_lazy_llm_coder/summon_llm_lord.py
   ```
   Datasets are saved in the `data/` directory.

## Environment Setup

Install the required packages:
```bash
pip install -r requirements.txt
```

## Notes
- Ensure the `GROQ_API_KEY` is set in the `.env` file for LLM analysis.

