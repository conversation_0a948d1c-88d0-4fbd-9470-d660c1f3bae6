import os
from pathlib import Path
from dotenv import load_dotenv
from groq import Groq

# Load environment variables
load_dotenv(dotenv_path=Path(__file__).parent / '.env')

def get_user_input():
    """Get the type of transaction fraud to inject from the user."""
    while True:
        fraud_type = input("Enter the type of transaction fraud to inject (e.g., high chargebacks, unusual transaction amounts): ").strip()
        if fraud_type:
            return fraud_type
        print("Fraud type cannot be empty. Please try again.")

def generate_injection_prompt(existing_code, fraud_type):
    """Generate a prompt for the LLM to inject fraud patterns into the existing code."""
    return f"""
You are tasked to modify the follwing Python code to inject the {fraud_type} fraud pattern in the transactions:
Remeber to make is_fraud = True in the code and is_fraud_transaction = random.random() < 1
Here's the code to modify:
{existing_code}
Rules:
- Output ONLY the modified Python code and make sure you return the whole code including all functions and imports.
- No explanations outside code comments
- Keep comments brief and focused on fraud logic
- No greetings or other text
"""

def get_llm_injection_output(prompt):
    """Get the modified code from the Groq LLM API and stream output to standard output."""
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        raise ValueError("GROQ_API_KEY not found in environment variables")
        
    client = Groq(api_key=api_key)
    try:
        stream = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "You are a Python developer tasked with modifying code."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            model="llama-3.3-70b-versatile",
            temperature=0.2,
            max_tokens=30000,
            top_p=1,
            stream=True
        )

        full_response = ""
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                print(content, end='', flush=True)
                full_response += content

        # Clean the response by removing markdown code block syntax
        cleaned_response = full_response.strip()
        if cleaned_response.startswith("```python"):
            cleaned_response = cleaned_response[9:]  # Remove ```python
        if cleaned_response.startswith("```"):
            cleaned_response = cleaned_response[3:]  # Remove ```
        if cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-3]  # Remove trailing ```
            
        return cleaned_response.strip()
    except Exception as e:
        raise RuntimeError(f"Failed to fetch LLM output: {e}")

def write_to_file(output_code):
    """Write the modified code to a new file."""
    try:
        output_file_path = Path(__file__).parent / "llm_lord_sermon.py"
        # Ensure the output code is not empty
        if not output_code or not output_code.strip():
            raise ValueError("Generated code is empty")
            
        with open(output_file_path, "w") as file:
            file.write(output_code)
        print(f"\nThe modified code has been written to '{output_file_path}'.")
    except Exception as e:
        print(f"\nError writing to file: {e}")

def main():
    existing_code_path = Path(__file__).parent / 'llm_lord_helper_guide.py' 
    with open(existing_code_path, 'r') as file:
        existing_code = file.read()

    fraud_type = get_user_input()
    prompt = generate_injection_prompt(existing_code, fraud_type)
    modified_code = get_llm_injection_output(prompt)
    write_to_file(modified_code)

if __name__ == "__main__":
    main()
