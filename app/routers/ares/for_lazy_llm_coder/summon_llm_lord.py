import os
import sys
from pathlib import Path
import importlib.util
import time

def import_module_from_path(module_name, file_path):
    """Import a module from file path."""
    try:
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec is None:
            raise ImportError(f"Could not load spec for module {module_name}")
        
        module = importlib.util.module_from_spec(spec)
        if spec.loader is None:
            raise ImportError(f"Could not load module {module_name}")
            
        spec.loader.exec_module(module)
        return module
    except Exception as e:
        raise ImportError(f"Failed to import {module_name}: {str(e)}")

def ensure_data_directory():
    """Ensure data directory exists."""
    data_dir = Path(__file__).parent.parent / 'data'
    data_dir.mkdir(exist_ok=True)
    return data_dir

def run_llm_lord():
    """Run LLM Lord to generate the sermon file."""
    try:
        llm_lord = import_module_from_path('llm_lord', Path(__file__).parent / 'llm_lord.py')
        llm_lord.main()
    except Exception as e:
        raise RuntimeError(f"Failed to run LLM Lord: {str(e)}")

def run_generated_code():
    """Run the generated sermon code to create fake datasets."""
    try:
        data_dir = ensure_data_directory()  # Get data directory path
        sermon = import_module_from_path('llm_lord_sermon', Path(__file__).parent / 'llm_lord_sermon.py')
        
        # Pass data directory to the sermon's main function
        sermon.main()
    except Exception as e:
        print(f"Error in generated code: {str(e)}")  # Add more detailed error message
        raise RuntimeError(f"Failed to run generated code: {str(e)}")

def run_feature_generation():
    """Run incremental feature generation on the fake datasets."""
    try:
        feature_gen = import_module_from_path(
            'incremental_feature_gen', 
            Path(__file__).parent.parent / 'feature_generation/incremental_feature_gen.py'
        )
        
        data_dir = ensure_data_directory()
        feature_gen.main(
            transactions_path=str(data_dir / 'fake_transactions.csv'),
            merchants_path=str(data_dir / 'fake_merchants.csv'),
            output_path=str(data_dir / 'fake_transactions_with_features.csv')
        )
    except Exception as e:
        raise RuntimeError(f"Failed to generate features: {str(e)}")

def run_transaction_analysis(txn_id='FT00000000'):
    """Run LLM transaction analysis on the generated features."""
    try:
        analysis = import_module_from_path(
            'llm_transaction_analysis',
            Path(__file__).parent.parent / 'model/llm_transaction_analysis.py'
        )
        
        data_dir = ensure_data_directory()
        analysis.main(
            txn_id = txn_id,
            transactions_path=str(data_dir / 'fake_transactions_with_features.csv'),
            merchants_path=str(data_dir / 'fake_merchants.csv')
        )
    except Exception as e:
        raise RuntimeError(f"Failed to run transaction analysis: {str(e)}")

def verify_file_saved(file_path, timeout=30):
    """Verify that a file exists, is readable, and has content."""
    start_time = time.time()
    while True:
        if time.time() - start_time > timeout:
            raise TimeoutError(f"Timeout waiting for file to be saved: {file_path}")
        
        if file_path.exists() and os.access(file_path, os.R_OK):
            # Check if file has content (size > 0)
            if file_path.stat().st_size > 0:
                return True
        time.sleep(0.5)
        print(".", end="", flush=True)

def main():
    """Main orchestration function."""
    try:
        print("\nStarting workflow...")
        data_dir = ensure_data_directory()
        print(f"Data directory created/verified at: {data_dir}")
        
        print("\n1. Running LLM Lord to generate fraud patterns...")
        run_llm_lord()
        
        # Verify sermon file
        sermon_file = Path(__file__).parent / 'llm_lord_sermon.py'
        print(f"\nWaiting for sermon file at: {sermon_file}")
        verify_file_saved(sermon_file)
        print(f"\nSermon file created and saved successfully")
        
        print("\n2. Running generated code to create fake datasets...")
        run_generated_code()
        
        # Verify dataset files
        merchant_file = data_dir / 'fake_merchants.csv'
        transactions_file = data_dir / 'fake_transactions.csv'
        
        print(f"\nWaiting for dataset files at:\n{merchant_file}\n{transactions_file}")
        verify_file_saved(merchant_file)
        verify_file_saved(transactions_file)
        print(f"\nDataset files created and saved successfully")
        
        print("\n3. Generating features from fake datasets...")
        run_feature_generation()
        
        # Verify features file
        features_file = data_dir / 'fake_transactions_with_features.csv'
        print(f"\nWaiting for features file at: {features_file}")
        verify_file_saved(features_file)
        print(f"\nFeatures file created and saved successfully")
        
        print("\n4. Running transaction analysis...")
        while True:
            txn_id = input("Enter transaction ID to analyze (or 'exit' to quit): ")
            if txn_id.lower() == 'exit':
                break
            try:
                run_transaction_analysis(txn_id)
            except Exception as e:
                print(f"Error analyzing transaction {txn_id}: {str(e)}")
        
    except Exception as e:
        print(f"\nError in workflow: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
