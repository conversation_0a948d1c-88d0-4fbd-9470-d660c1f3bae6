import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from langchain.chains import <PERSON><PERSON>hain
from langchain_core.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    MessagesPlaceholder,
)
from langchain_core.messages import SystemMessage
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain_groq import ChatGroq
import tempfile
import subprocess
import json

import warnings
warnings.filterwarnings("ignore")

load_dotenv()

# project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# sys.path.append(project_root)
from config.dataframe_schema import Schema

def generate_code_prompt(user_prompt):
    """Generate a prompt for the LLM to create code based on the datasets and user input."""
    guide_path = Path(__file__).parent / "gpt_guide.py"
    with open(guide_path, 'r') as file:
        example_code = file.read()
    
    code_prompt = f"""
Generate Python code to do the following: {user_prompt}.
Requirements:
- Input: transactions_df, merchants_df
- Example guide that shows how output code should look like:
{example_code}
Do not explain the code, only generate executable Python code.
"""
    return code_prompt

def write_to_file(code):
    """Write the generated code to a temporary file."""
    try:
        base_dir = Path(__file__).resolve().parent.parent.parent.parent.parent
        data_dir = base_dir / '.ares' / 'data'
        os.makedirs(data_dir, exist_ok=True)  # Ensure the directory exists
        temp_file_path = data_dir / 'temp_code.py'
        
        with open(temp_file_path, 'w') as temp_file:
            temp_file.write(code)
        
        print(f"\nCode has been written to temporary file '{temp_file_path}'")
        return temp_file_path
        
    except Exception as e:
        print(f"\nError writing to file: {e}")
        raise

def execute_generated_code(file_path):
    """Execute the generated Python file and capture its stdout."""
    try:
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        result = subprocess.run(
            ["python3", str(file_path)], capture_output=True, text=True, check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"\nError executing file: {e.stderr}")
        raise RuntimeError("Failed to execute the generated code.")
    finally:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"Temporary file '{file_path}' has been removed.")

def initialize_context(chat_id):
    """Initialize context file if it doesn't exist."""
    base_dir = Path(__file__).resolve().parent.parent.parent.parent.parent # Go up to project root
    data_dir = base_dir / '.ares' / 'data'
    os.makedirs(data_dir, exist_ok=True)  # Ensure the directory exists
    context_file = data_dir / f"context_{chat_id}.json"
    if not os.path.exists(context_file):
        with open(context_file, "w") as file:
            json.dump({"history": []}, file)
    print(f"Context file initialized at: {context_file}")
    return context_file

def initialize_context_frontend(chat_id):
    """Initialize context file if it doesn't exist."""
    base_dir = Path(__file__).resolve().parent.parent.parent.parent.parent # Go up to project root
    data_dir = base_dir / '.ares' / 'data'
    os.makedirs(data_dir, exist_ok=True)  # Ensure the directory exists
    context_file = data_dir / f"context_frontend_{chat_id}.json"
    if not os.path.exists(context_file):
        with open(context_file, "w") as file:
            json.dump({"history": []}, file)
    print(f"Context file initialized at: {context_file}")
    return context_file


def load_existing_context(context_file, max_history=5):
    """Load existing conversation history from JSON file."""
    try:
        with open(context_file, "r") as file:
            context = json.load(file)
            # Limit the history to the most recent interactions
            return context.get("history", [])[-max_history:]
    except FileNotFoundError:
        return []

def update_context(context_file, user_message, llm_response):
    """Update the context with the user's message and the LLM's response."""
    with open(context_file, "r") as file:
        context = json.load(file)
    if len(context["history"]) >= 10:
        context["history"] = context["history"][-9:]
    context["history"].append({"user": user_message, "llm": llm_response})
    with open(context_file, "w") as file:
        json.dump(context, file, indent=4)

def update_context_frontend(context_file, user_message, llm_response):
    """Update the context with the user's message and the LLM's response."""
    with open(context_file, "r") as file:
        context = json.load(file)
    if len(context["history"]) >= 10:
        context["history"] = context["history"][-9:]
    context["history"].append({"user": user_message, "llm": llm_response})
    with open(context_file, "w") as file:
        json.dump(context, file, indent=4)

def main():
    groq_api_key = os.environ['GROQ_API_KEY2']
    model = 'llama-3.3-70b-versatile'
    groq_chat = ChatGroq(
            groq_api_key=groq_api_key, 
            model_name=model
    )

    # Initialize context file and memory
    context_file = initialize_context()
    memory = ConversationBufferWindowMemory(
        k=15,
        memory_key="chat_history",
        return_messages=True
    )

    # Load existing context into memory
    existing_history = load_existing_context(context_file)
    for interaction in existing_history:
        memory.chat_memory.add_user_message(interaction["user"])
        memory.chat_memory.add_ai_message(interaction["llm"])

    system_prompt = f"""
    You are a Python code generation assistant specializing in data analysis with pandas. Your task is to generate code that processes transaction and merchant data. Follow these rules:
    - No greetings or other text. Just the code.
    - Always use pandas for data operations
    - Include all necessary imports
    - Use efficient pandas operations (vectorized over loops)
    - Handle potential data type issues
    - Add clear comments explaining logic
    - Return only executable code without explanations
    - Important: Use 'merchant_id' (not 'mer_id') for joining transactions and merchants DataFrames

    Make sure to go over the context before answering questions.
    transactions_df columns: {Schema['Transactions']}
    merchants_df columns: {Schema['Merchants']}


    """
    
    while True:
        user_question = input("Please enter a question or type 'quit' to exit: ")
        if not user_question:
            break

        if user_question.lower() in ['quit', 'exit', '']:
            print("Goodbye!")
            break
            
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content=system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            HumanMessagePromptTemplate.from_template("{human_input}")
        ])

        conversation = LLMChain(
            llm=groq_chat,
            prompt=prompt,
            verbose=False,
            memory=memory
        )
        
        try:
            use_prompt = generate_code_prompt(user_question)
            response = conversation.predict(human_input=use_prompt)
            if not response:
                print("No response from LLM.")
                continue
            cleaned_response = response.strip()
            if cleaned_response.startswith("```python"):
                cleaned_response = cleaned_response[9:]  # Remove ```python
            if cleaned_response.startswith("```"):
                cleaned_response = cleaned_response[3:]  # Remove ```
            if cleaned_response.endswith("```"):
                cleaned_response = cleaned_response[:-3]  # Remove trailing ```
            output = cleaned_response.strip()

            # Update memory with the interaction
            memory.chat_memory.add_user_message(use_prompt)
            memory.chat_memory.add_ai_message(output)
            update_context(context_file, use_prompt, output)
            
            # Process and execute the generated code
            print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
            print(output)
            print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
            output_file = write_to_file(output)
            execution_output = execute_generated_code(output_file)
            print(execution_output)
        except Exception as e:
            print(f"Error during execution: {e}")
            continue


def use_it(chat_id, user_question):
    groq_api_key = os.environ['GROQ_API_KEY2']
    print(groq_api_key)
    model = 'llama-3.3-70b-versatile'
    groq_chat = ChatGroq(
            groq_api_key=groq_api_key, 
            model_name=model
    )

    # Initialize context file and memory
    context_file = initialize_context(chat_id)
    context_file_frontend = initialize_context_frontend(chat_id)
    memory = ConversationBufferWindowMemory(
        k=15,
        memory_key="chat_history",
        return_messages=True
    )

     # Load existing context into memory
    existing_history = load_existing_context(context_file)
    for interaction in existing_history:
        memory.chat_memory.add_user_message(interaction["user"])
        memory.chat_memory.add_ai_message(interaction["llm"])

    system_prompt = f"""
    You are a Python code generation assistant specializing in data analysis with pandas. Your task is to generate code that processes transaction and merchant data. Follow these rules:
    - No greetings or other text. Just the code.
    - Always use pandas for data operations
    - Include all necessary imports
    - Use efficient pandas operations (vectorized over loops)
    - Handle potential data type issues
    - Add clear comments explaining logic
    - Return only executable code without explanations
    -  Whenever presenting tabular data or dataframe-like structures, always print them in CSV format using `to_csv(index=False)` for clean and concise output.
    Make sure to go over the context before answering questions.
    transactions_df columns: {Schema['Transactions']}
    merchants_df columns: {Schema['Merchants']}
    investigations_df columns: {Schema['Investigations']}
     when ever you need merchant data and transaction data use the path
     base_dir = Path(__file__).resolve().parent.parent
    transactions_path = base_dir / 'data' / 'transactions.csv'
    merchants_path = base_dir / 'data' / 'merchants.csv'
    investigations_path = base_dir / 'data' / 'investigations.csv'
    Context:
    -> please do use pd.to_datatime() function to convert the date columns to datetime
    ->when ever you need to print some dataframe only print most important columns unlesss you are asked to print all columns
    ->and always try to print things in a very pretty way it should be easy to read and understand
    ->very very important: please give us the csv format comman separated values if there is table/dataframe or anything of the similar to print if the response is not related to table then use print string
    """

    prompt = ChatPromptTemplate.from_messages([
        SystemMessage(content=system_prompt),
        MessagesPlaceholder(variable_name="chat_history"),
        HumanMessagePromptTemplate.from_template("{human_input}")
    ])
    print(prompt)
    conversation = LLMChain(
            llm=groq_chat,
            prompt=prompt,
            verbose=False,
            memory=memory
        )
    
    try:
        use_prompt = generate_code_prompt(user_question)
        response = conversation.predict(human_input=use_prompt)
        if not response:
            print("No response from LLM.")
            return
        cleaned_response = response.strip()
        if cleaned_response.startswith("```python"):
            cleaned_response = cleaned_response[9:]
        if cleaned_response.startswith("```"):
            cleaned_response = cleaned_response[3:]
        if cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-3]
        output = cleaned_response.strip()

        # Update memory with the interaction
        memory.chat_memory.add_user_message(use_prompt)
        memory.chat_memory.add_ai_message(output)
        update_context(context_file, use_prompt, output)
        
        # Process and execute the generated code
        print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
        print(output)
        print("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
        output_file = write_to_file(output)
        execution_result = execute_generated_code(output_file)
        update_context_frontend(context_file_frontend, user_question, execution_result)
        return execution_result if execution_result else "No output generated"
        
    except Exception as e:
        error_message = f"Error during execution: {str(e)}"
        print(error_message)
        return error_message
    

if __name__ == "__main__":
    main()
