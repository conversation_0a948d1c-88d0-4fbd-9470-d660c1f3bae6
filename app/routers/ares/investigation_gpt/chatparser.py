from langchain_core.output_parsers import JsonOutputParser

relevance_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "is_relevant": {"type": "string"},
    },
})

msg_improvement_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "improved_message": {"type": "string"},
    },
})

classification_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "classification": {"type": "string"},
    },
})

question_answer_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "answer": {"type": "string"},
        "sources": {
            "type": "array",
            "items": {
                "type": "string",
            },
        },
    },
})

visualization_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "modified_message": {"type": "string"},
    },
})