import os
from pprint import pprint
from langchain_groq import ChatGroq
from langchain_core.output_parsers import StrOutputParser
from langchain_core.output_parsers import JsonOutputParser
from langchain.schema import BaseOutputParser
from langchain.schema import Document
from langgraph.graph import END, StateGraph
from langgraph.checkpoint.memory import MemorySaver
from typing_extensions import TypedDict
from typing import List
import sys
import os
import subprocess
import tempfile
from langchain_community.tools.tavily_search import TavilySearchResults

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
from config.dataframe_schema import Schema
from config.prompts_agentic import relevance_prompt, choose_prompt, coding_prompt, hallucination_checker, search_hallucination_checker, rewriter_prompt, search_keyword_prompt, search_summary_prompt
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()  

GROQ_API_KEY = os.getenv('GROQ_API_KEY')
TAVILY_API_KEY = os.getenv('TAVILY_API_KEY')
GROQ_LLM = ChatGroq(
            model="llama3-70b-8192",
        )
memory = MemorySaver()

# Add new tool after existing LLM definitions
web_search_tool = TavilySearchResults(api_key=TAVILY_API_KEY)

class CodeOutputParser(BaseOutputParser):
    """Parser to remove markdown code blocks and clean up code output."""
    
    def parse(self, text: str) -> str:
        # Remove markdown code blocks if present
        text = text.strip()
        if text.startswith('```python\n'):
            text = text[9:]
        elif text.startswith('```\n'):
            text = text[4:]
        if text.endswith('\n```'):
            text = text[:-4]
        return text.strip()

def read_code():
    guide_path = Path(__file__).parent / "gpt_guide.py"
    with open(guide_path, 'r') as file:
        example_code = file.read()
    return example_code


input_relevance_checker = relevance_prompt | GROQ_LLM | JsonOutputParser()

requirement_checker = choose_prompt | GROQ_LLM | JsonOutputParser()

coder = coding_prompt | GROQ_LLM | CodeOutputParser()

hallucination_chain = hallucination_checker | GROQ_LLM | JsonOutputParser()

rewriter = rewriter_prompt | GROQ_LLM | CodeOutputParser()

keyword_chain = search_keyword_prompt | GROQ_LLM | JsonOutputParser()

summary_chain = search_summary_prompt | GROQ_LLM | StrOutputParser()

# Add new chain with other chain definitions
search_hallucination_chain = search_hallucination_checker | GROQ_LLM | JsonOutputParser()

class GraphState(TypedDict):
    """
    Represents the state of our fraud investigation graph.

    Attributes:
        user_prompt: Original user query
        is_relevant: Whether query is relevant to fraud investigation
        requires_code: Whether query requires code generation
        generated_code: Code generated by LLM
        has_hallucination: Whether code contains hallucinations
        search_keywords: Keywords for web search if needed
        search_results: List of web search results
        num_steps: Number of steps taken in workflow
        execution_status: Status of code execution
    """
    user_prompt: str
    is_relevant: bool
    requires_code: bool
    generated_code: str
    has_hallucination: bool
    search_keywords: List[str]
    search_results: List[Document]
    num_steps: int
    execution_status: dict
    execution_output: str

# Nodes in the graph
def generate_code(state: GraphState) -> dict:
    """Generate Python code for data analysis"""
    print("---GENERATING CODE---")
    example_code = read_code()
    result = coder.invoke({
        'user_prompt': state['user_prompt'],
        'example_code': example_code,
        'Merchant_schema': Schema['Merchants'],
        'Transaction_schema': Schema['Transactions']
    })
    print("---CODE GENERATED---")
    print(result)
    print("---CODE GENERATED---")
    return {
        "generated_code": result,
        "num_steps": state['num_steps'] + 1
    }

def fix_hallucinations(state: GraphState):
    """Fix hallucinated code if needed"""
    print("---FIXING HALLUCINATIONS---")
    result = rewriter.invoke({
        'user_prompt': state['user_prompt'],
        'Merchant_schema': Schema['Merchants'],
        'Transaction_schema': Schema['Transactions'],
        'generated_code': state['generated_code']
    })
    return {
        "generated_code": result,
        "num_steps": state['num_steps'] + 1
    }

def execute_generated_code(state: GraphState):
    """Execute the generated code and store output in state."""
    try:
        # Create temporary file with generated code
        with tempfile.NamedTemporaryFile(delete=False, suffix=".py") as temp_file:
            temp_file.write(state['generated_code'].encode())
            temp_file_path = temp_file.name
        
        # Execute the code and capture output
        result = subprocess.run(
            ["python3", str(temp_file_path)], 
            capture_output=True, 
            text=True, 
            check=True
        )        
        # Store output in state
        state['execution_output'] = result.stdout
        
    except subprocess.CalledProcessError as e:
        state['execution_output'] = f"Error executing code: {e.stderr}"
    except Exception as e:
        state['execution_output'] = f"Error: {str(e)}"
    finally:
        # Clean up temporary file
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.remove(temp_file_path)
    return {
        "execution_output": state['execution_output'],
        "num_steps": state['num_steps'] + 1,
    }

def search_preparation(state: GraphState):
    """Generate search keywords for web research"""
    print("---PREPARING SEARCH---")
    result = keyword_chain.invoke({
        'user_prompt': state['user_prompt']
    })
    return {
        "search_keywords": result['keywords'],
        "num_steps": state['num_steps'] + 1
    }

def final_output(state: GraphState):
    """Final step to output search results"""
    print("---FINAL OUTPUT---")
    return {
        "execution_output": state['execution_output'],
        "num_steps": state['num_steps'] + 1
    }

def state_printer(state: GraphState):
    """Print the current state of the graph"""
    print(f"Current state: {state}")
    return state

# Conditional edges in the graph
def route_by_relevance(state: GraphState):
    """Router to check if query is relevant to fraud investigation"""
    print("---ROUTING BY RELEVANCE---")
    result = input_relevance_checker.invoke({"user_prompt": state["user_prompt"]})
    is_relevant = result["relevance"] == "Relevant"
    if is_relevant:
        print("---QUERY IS RELEVANT---")
    else:
        print("---QUERY IS NOT RELEVANT---")
    # Return both state updates and next step
    return {
        "is_relevant": is_relevant,
        "num_steps": state["num_steps"] + 1,
        "next": "relevant" if is_relevant else "not_relevant"
    }

def route_by_requirement(state: GraphState):
    """Router to determine if query needs code generation or web search"""
    print("---ROUTING BY REQUIREMENT---")
    result = requirement_checker.invoke({"user_prompt": state["user_prompt"]})
    requires_code = result["requirement"] == "coded_output"
    
    return {
        "requires_code": requires_code,
        "num_steps": state["num_steps"] + 1,
        "next": "generate_code" if requires_code else "search_preparation"
    }

def route_by_hallucination(state: GraphState):
    """Router to check if generated code has hallucinations"""
    print("---CHECKING FOR HALLUCINATIONS---")
    result = hallucination_chain.invoke({
        "user_prompt": state["user_prompt"],
        "Merchant_schema": Schema["Merchants"],
        "Transaction_schema": Schema["Transactions"],
        "generated_code": state["generated_code"]
    })
    has_hallucination = result["hallucination"] == "Yes"
    
    return {
        "has_hallucination": has_hallucination,
        "num_steps": state["num_steps"] + 1,
        "next": "fix_hallucinations" if has_hallucination else "execute_code"
    }

def web_search(state: GraphState) -> dict:
    """Perform web search using generated keywords"""
    print("---PERFORMING WEB SEARCH---")
    full_searches = []
    
    for keyword in state['search_keywords'][:3]:  
        print(f"Searching for: {keyword}")
        search_results = web_search_tool.invoke({"query": keyword})
        web_results = "\n".join([d["content"] for d in search_results])
        full_searches.append(Document(page_content=web_results))
    
    return {
        "search_results": full_searches,
        "num_steps": state['num_steps'] + 1
    }

def parse_search_results(state: GraphState) -> dict:
    """Parse and summarize search results based on user prompt"""
    print("---PARSING SEARCH RESULTS---")
    
    # Combine all search results into one text
    combined_content = "\n".join([doc.page_content for doc in state['search_results']])
    
    # Use the summary chain
    summary = summary_chain.invoke({
        "user_prompt": state['user_prompt'],
        "search_results": combined_content
    })
    
    return {
        "execution_output": summary,
        "num_steps": state['num_steps'] + 1
    }

def route_by_search_hallucination(state: GraphState):
    """Router to check if search summary has hallucinations"""
    print("---CHECKING SEARCH RESULTS FOR HALLUCINATIONS---")
    result = search_hallucination_chain.invoke({
        "user_prompt": state["user_prompt"],
        "generated_summary": state["execution_output"],
        "search_results": "\n".join([doc.page_content for doc in state['search_results']])
    })
    has_hallucination = result["hallucination"] == "Yes"
    
    return {
        "has_hallucination": has_hallucination,
        "num_steps": state["num_steps"] + 1,
        "next": "search_preparation" if has_hallucination else "final_output"
    }

# Define the graph
workflow = StateGraph(GraphState)

workflow.add_node("relevance_check", route_by_relevance)
workflow.add_node("requirement_check", route_by_requirement)
workflow.add_node("generate_code", generate_code)
workflow.add_node("check_hallucinations", route_by_hallucination)
workflow.add_node("fix_hallucinations", fix_hallucinations)
workflow.add_node("search_preparation", search_preparation)
workflow.add_node("final_output", final_output)
workflow.add_node("state_printer", state_printer)
workflow.add_node("execute_code", execute_generated_code)
workflow.add_node("web_search", web_search)
workflow.add_node("parse_search_results", parse_search_results)
workflow.add_node("check_search_hallucinations", route_by_search_hallucination)

workflow.add_conditional_edges(
    "relevance_check",
    lambda x: x["next"],
    {
        "relevant": "requirement_check",
        "not_relevant": END
    }
)

workflow.add_conditional_edges(
    "requirement_check",
    lambda x: x["next"],
    {
        "generate_code": "generate_code",
        "search_preparation": "search_preparation"
    }
)

workflow.add_edge("generate_code", "check_hallucinations")

workflow.add_conditional_edges(
    "check_hallucinations", 
    lambda x: x["next"],
    {
        "fix_hallucinations": "fix_hallucinations",
        "execute_code": "execute_code" 
    }
)

workflow.add_edge("fix_hallucinations", "execute_code")
workflow.add_edge("execute_code", "final_output")
workflow.add_edge("search_preparation", "web_search")
workflow.add_edge("web_search", "parse_search_results")
workflow.add_edge("parse_search_results", "check_search_hallucinations")

workflow.add_conditional_edges(
    "check_search_hallucinations",
    lambda x: x["next"],
    {
        "search_preparation": "search_preparation", 
        "final_output": "final_output" 
    }
)

workflow.add_edge("final_output", "state_printer")
workflow.add_edge("state_printer", END)

# Set entry point
workflow.set_entry_point("relevance_check")
config = {"configurable": {"thread_id": "1"}}

app = workflow.compile(
    checkpointer=memory,
    store=True,
    )

while True:
    user_prompt = input("Enter your question (or type 'exit' to quit): ")
    if user_prompt.lower().strip() in ['exit', 'quit', 'q']:
        break
    
    inputs = {
        "user_prompt": user_prompt,
        "num_steps": 0,
        "is_relevant": False,
        "requires_code": False,
        "generated_code": "",
        "has_hallucination": False,
        "search_keywords": [],
        "search_results": [],
        "execution_status": {},
        "execution_output": ""
    }

    output = app.invoke(inputs, config=config)

    print(output['execution_output'])