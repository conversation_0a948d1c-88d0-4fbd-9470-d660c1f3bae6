import os
import sys
from pathlib import Path
from langchain_groq import ChatGroq
from langchain_core.output_parsers import StrOutputParser
from langchain_core.output_parsers import JsonOutputParser
from langchain.schema import BaseOutputParser
from langchain.schema import Document
from langgraph.graph import END, StateGraph, MessagesState
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from psycopg import AsyncConnection
from typing import List
import subprocess
import tempfile
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage, RemoveMessage
from colorama import init, Fore, Style
import argparse
import asyncio
import signal
from types import FrameType
import uuid
from datetime import datetime
import json
from pprint import pprint
from app.database import get_db
from app.models import models
import platform

# Add the project root to Python path
project_root = str(Path(__file__).resolve().parent.parent.parent.parent.parent)
sys.path.append(project_root)

# Now import using the full path
from app.routers.ares.config.dataframe_schema import Schema
from app.routers.ares.config.prompts_agentic import relevance_prompt, requirement_prompt, coding_prompt, hallucination_checker_prompt, rewriter_prompt, search_keyword_prompt, search_summary_prompt, chat_prompt, context_check_prompt, chat_code_output_prompt, summary_prompt, graph_prompt
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()  

GROQ_API_KEY = os.getenv('GROQ_API_KEY')
TAVILY_API_KEY = os.getenv('TAVILY_API_KEY')
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_HOST = os.getenv('DB_HOST')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')
MAX_TOKENS = 1000
TEMPERATURE = 0.7
MAX_RETRIES = 3
MAX_MESSAGES = 2
TRIGGER_SUMMARY_LENGTH = 5

def validate_environment() -> None:
    """Validate that all required environment variables are set"""
    required_vars = {
        'GROQ_API_KEY': GROQ_API_KEY,
        'TAVILY_API_KEY': TAVILY_API_KEY,
        'DB_USER': DB_USER,
        'DB_PASSWORD': DB_PASSWORD,
        'DB_HOST': DB_HOST,
        'DB_PORT': DB_PORT,
        'DB_NAME': DB_NAME
    }
    
    missing = [key for key, value in required_vars.items() if not value]
    if missing:
        print(f"{Fore.RED}Error: Missing required environment variables: {', '.join(missing)}{Style.RESET_ALL}")
        sys.exit(1)

# Add this call after load_dotenv()
validate_environment()

# DB_URI = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?sslmode=disable"
DB_URI = "postgresql://postgres:<EMAIL>:5432/postgres"

GROQ_LLM = ChatGroq(
            model="llama3-70b-8192",
            temperature=TEMPERATURE,
            max_tokens=MAX_TOKENS,
            max_retries=MAX_RETRIES,
        )

connection_kwargs = {
    "autocommit": True,
    "prepare_threshold": 0,
}

web_search_tool = TavilySearchResults(api_key=TAVILY_API_KEY)

# Initialize colorama for cross-platform color support
init()


class CodeOutputParser(BaseOutputParser):
    """Parser to remove markdown code blocks and clean up code output."""
    
    def parse(self, text: str) -> str:
        text = text.strip()
        if text.startswith('```python\n'):
            text = text[9:]
        elif text.startswith('```\n'):
            text = text[4:]
        if text.endswith('\n```'):
            text = text[:-4]
        return text.strip()

json_parser = JsonOutputParser()
code_parser = CodeOutputParser()
str_parser = StrOutputParser()

def print_section(title: str, content: str = None):
    """Helper function to print formatted sections"""
    print(f"\n{Fore.CYAN}{'='*50}")
    print(f"{Fore.GREEN}{title.center(50)}")
    print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    if content:
        print(f"{Fore.WHITE}{content}{Style.RESET_ALL}\n")

def read_code(guide_type: str = "code") -> str:
    """Read example code from the appropriate guide file.
    
    Args:
        guide_type (str): Type of guide to read ('code' or 'graph')
    
    Returns:
        str: Content of the guide file
    """
    if guide_type == "graph":
        # Return a stub for graph visualization
        return 'print("Graph visualization functionality is not supported in InvestigationGPT.")'
    
    # Use the normal file reading for code guides
    guide_filename = "gpt_guide.py"
    guide_path = Path(__file__).parent / guide_filename
    with open(guide_path, 'r') as file:
        example_code = file.read()
    return example_code

class GraphState(MessagesState):
    is_relevant: bool
    execution_output: str
    requires: str
    generated_code: str
    search_keywords: List[str]
    search_results: List[Document]
    summary: str
    graph_status: str = ""
    graph_output: dict = {}
    message_history: List[dict] = []

def create_messages_with_context(messages: List, summary: str = "") -> List:
    """Helper function to prepend summary context as a system message"""
    if not summary:
        return messages
    
    context_message = SystemMessage(content=f"Previous conversation context: {summary}")
    return [context_message] + messages

def chat_output(state: GraphState) -> dict:
    print_section("CHAT OUTPUT")
    try:
        chat = chat_prompt | GROQ_LLM
        messages_with_context = create_messages_with_context(state["messages"], state.get("summary", ""))
        result = chat.invoke({
            "messages": messages_with_context
        })
        result = str_parser.parse(result.content)
        return {
            "messages": state["messages"] + [AIMessage(content=result)],
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Network error in chat output: {str(e)}{Style.RESET_ALL}")
        return {
            "messages": state["messages"],
            "graph_status": "Failed due to network error"
        }

def generate_code(state: GraphState) -> dict:
    print_section("GENERATING CODE")
    try:
        example_code = read_code(guide_type="code")
        coder = coding_prompt | GROQ_LLM
        messages_with_context = create_messages_with_context(state["messages"], state.get("summary", ""))
        result = coder.invoke({
            'messages': messages_with_context,
            'example_code': example_code,
            'Merchant_schema': Schema['Merchants'],
            'Transaction_schema': Schema['Transactions']
        })
        result = code_parser.parse(result.content)
        print_section("CODE GENERATED", result)
        return {
            "generated_code": result,
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Network error in code generation: {str(e)}{Style.RESET_ALL}")
        return {
            "generated_code": "",
            "graph_status": "Failed due to network error"
        }

def ensure_ares_directory():
    """Ensure the .ares directory exists in the project root."""
    base_dir = Path(__file__).resolve().parent.parent.parent.parent.parent
    ares_dir = base_dir / '.ares'
    temp_dir = ares_dir / 'data'
    temp_dir.mkdir(parents=True, exist_ok=True)
    return temp_dir

def execute_generated_code(state: GraphState) -> dict:
    print_section("EXECUTING CODE")
    temp_dir = ensure_ares_directory()
    temp_file_path = None
    
    try:
        # Create a unique filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"generated_code_{timestamp}.py"
        temp_file_path = temp_dir / filename
        
        # Write the code to file
        with open(temp_file_path, 'w') as temp_file:
            temp_file.write(state['generated_code'])
        
        print(f"{Fore.YELLOW}Running the generated code...{Style.RESET_ALL}")
        
        # Use 'python' on Windows and 'python3' on other platforms
        python_command = "python" if platform.system() == "Windows" else "python3"
        
        result = subprocess.run(
            [python_command, str(temp_file_path)], 
            capture_output=True, 
            text=True, 
            check=True
        )        
        state['execution_output'] = result.stdout
        print(f"{Fore.GREEN}✓ Code executed successfully!{Style.RESET_ALL}")
        
    except subprocess.CalledProcessError as e:
        error_msg = f"Error executing code: {e.stderr}"
        print(f"{Fore.RED}✗ Execution failed with error:{Style.RESET_ALL} {error_msg}")
        state['execution_output'] = error_msg
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(f"{Fore.RED}✗ Execution failed with unexpected error:{Style.RESET_ALL} {error_msg}")
        state['execution_output'] = error_msg
    finally:
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.remove(temp_file_path)
    
    if state['execution_output'].strip():
        print(f"\n{Fore.CYAN}{'─'*50}")
        print(f"{Fore.GREEN}Execution Output:{Style.RESET_ALL}")
        print(state['execution_output'])
        print(f"{Fore.CYAN}{'─'*50}{Style.RESET_ALL}")
    
    return {
        "messages": state['messages'] + [AIMessage(content=f"[CODED OUTPUT]\n{state['execution_output']}")],
        "execution_output": state['execution_output']
    }

def search_preparation(state: GraphState) -> dict:
    print_section("PREPARING SEARCH")
    try:
        keyword_chain = search_keyword_prompt | GROQ_LLM
        messages_with_context = create_messages_with_context(state["messages"], state.get("summary", ""))
        result = keyword_chain.invoke({
            'messages': messages_with_context
        })
        result = json_parser.parse(result.content)
        return {
            "search_keywords": result['keywords'],
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Network error in search preparation: {str(e)}{Style.RESET_ALL}")
        return {
            "search_keywords": [],
            "graph_status": "Failed due to network error"
        }

def web_search(state: GraphState) -> dict:
    print_section("PERFORMING WEB SEARCH")
    try:
        full_searches = []
        for keyword in state['search_keywords'][:3]:  
            print(f"{Fore.YELLOW}Searching for:{Style.RESET_ALL} {keyword}")
            search_results = web_search_tool.invoke({"query": keyword})
            web_results = "\n".join([d["content"] for d in search_results])
            full_searches.append(Document(page_content=web_results))
        return {
            "search_results": full_searches,
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Network error in web search: {str(e)}{Style.RESET_ALL}")
        return {
            "search_results": [],
            "graph_status": "Failed due to network error"
        }

def parse_search_results(state: GraphState) -> dict:
    print_section("SUMMARIZING SEARCH RESULTS")
    try:
        combined_content = "\n".join([doc.page_content for doc in state['search_results']])
        summary_chain = search_summary_prompt | GROQ_LLM
        messages_with_context = create_messages_with_context(state["messages"], state.get("summary", ""))
        summary = summary_chain.invoke({
            "messages": messages_with_context,
            "search_results": combined_content
        })
        summary = str_parser.parse(summary.content)
        return {
            "messages": state['messages'] + [AIMessage(content=summary)],
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Network error in parsing search results: {str(e)}{Style.RESET_ALL}")
        return {
            "messages": state['messages'],
            "graph_status": "Failed due to network error"
        }

def fix_hallucinations(state: GraphState) -> dict:
    print_section("FIXING HALLUCINATIONS")
    try:
        hallucination_fix = rewriter_prompt | GROQ_LLM
        messages_with_context = create_messages_with_context(state["messages"], state.get("summary", ""))
        result = hallucination_fix.invoke({
            "messages": messages_with_context,
            "generated_code": state["generated_code"],
            "Merchant_schema": Schema["Merchants"],
            "Transaction_schema": Schema["Transactions"]
        })
        result = code_parser.parse(result.content)
        print_section("FIXED CODE", result)
        return {
            "generated_code": result,
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Error in hallucination fixing: {str(e)}{Style.RESET_ALL}")
        return {
            "generated_code": state["generated_code"],
            "graph_status": "Failed due to error"
        }

def chat_code_output(state: GraphState) -> dict:
    print_section("CREATING CODE RESPONSE")
    try:
        code_output_chat = chat_code_output_prompt | GROQ_LLM
        messages_with_context = create_messages_with_context(state["messages"], state.get("summary", ""))
        result = code_output_chat.invoke({
            "messages": messages_with_context,
            "execution_output": state["execution_output"],
            "generated_code": state["generated_code"]
        })
        result = str_parser.parse(result.content)
        return {
            "messages": state["messages"] + [AIMessage(content=result)],
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Error in creating code response: {str(e)}{Style.RESET_ALL}")
        return {
            "messages": state["messages"],
            "graph_status": "Failed due to error"
        }

def route_by_relevance(state: GraphState) -> dict:
    print_section("CHECKING QUERY RELEVANCE")
    try:
        input_relevance_checker = relevance_prompt | GROQ_LLM
        messages_with_context = create_messages_with_context(state["messages"], state.get("summary", ""))
        result = input_relevance_checker.invoke({"messages": messages_with_context})
        result = json_parser.parse(result.content)
        is_relevant = result["relevance"] == "relevant"
        
        if is_relevant:
            print(f"{Fore.GREEN}✓ Query is relevant to fraud investigation{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.RED}╭{'═'*69}╮")
            print(f"{Fore.RED}║{Style.RESET_ALL}                    ⚠ WARNING: INVALID QUERY ⚠                       {Fore.RED}║")
            print(f"{Fore.RED}║{Style.RESET_ALL}  This query is outside the authorized scope of fraud investigation  {Fore.RED}║")
            print(f"{Fore.RED}║{Style.RESET_ALL}  and cannot be processed. Please limit your questions to:           {Fore.RED}║")
            print(f"{Fore.RED}║{Style.RESET_ALL}                                                                     {Fore.RED}║")
            print(f"{Fore.RED}║{Style.RESET_ALL}    • Transaction Analysis and Monitoring                            {Fore.RED}║")
            print(f"{Fore.RED}║{Style.RESET_ALL}    • Merchant Risk Investigation                                    {Fore.RED}║")
            print(f"{Fore.RED}║{Style.RESET_ALL}    • Fraud Pattern Detection                                        {Fore.RED}║")
            print(f"{Fore.RED}║{Style.RESET_ALL}    • Risk Assessment and Scoring                                    {Fore.RED}║")
            print(f"{Fore.RED}║{Style.RESET_ALL}                                                                     {Fore.RED}║")
            print(f"{Fore.RED}╰{'═'*69}╯{Style.RESET_ALL}\n")
        
        return {
            "is_relevant": is_relevant,
            "next": "relevant" if is_relevant else "not_relevant",
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Network error in relevance check: {str(e)}{Style.RESET_ALL}")
        return {
            "is_relevant": False,
            "next": "not_relevant",
            "graph_status": "Failed due to network error"
        }

def route_by_requirement(state: GraphState) -> dict:
    print_section("DETERMINING QUERY TYPE")
    try:
        requirement_checker = requirement_prompt | GROQ_LLM
        messages_with_context = create_messages_with_context(state["messages"], state.get("summary", ""))
        result = requirement_checker.invoke({"messages": messages_with_context})
        requirement = json_parser.parse(result.content)
        
        requirement_type = requirement['requirement']
        print(f"{Fore.YELLOW}Query type detected:{Style.RESET_ALL} ", end="")
        if requirement_type == "coded_output":
            print(f"{Fore.GREEN}Code Generation Required{Style.RESET_ALL}")
            return {
                "requires": "coded_output",
                "next": "generate_code",
                "graph_status": "Completed"
            }
        elif requirement_type == "web_search":
            print(f"{Fore.GREEN}Web Search Required{Style.RESET_ALL}")
            return {
                "requires": "web_search",
                "next": "search_preparation",
                "graph_status": "Completed"
            }
        elif requirement_type == "chat":
            print(f"{Fore.GREEN}Simple Chat Response{Style.RESET_ALL}")
            return {
                "requires": "chat",
                "next": "chat_output",
                "graph_status": "Completed"
            }
        elif requirement_type == "graph":
            print(f"{Fore.GREEN}Graph Generation Request (Not Supported){Style.RESET_ALL}")
            # Treat graph requests as chat requests and let the chat_output handle it
            # We'll modify the messages to include a notice that graph generation is not supported
            graph_notice = HumanMessage(content="Note: Graph visualization is not supported by InvestigationGPT.")
            state["messages"].append(graph_notice)
            return {
                "requires": "chat",
                "next": "chat_output",
                "graph_status": "Not supported"
            }   
    except Exception as e:
        print(f"{Fore.RED}Network error in requirement check: {str(e)}{Style.RESET_ALL}")
        return {
            "requires": "chat",
            "next": "chat_output",
            "graph_status": "Failed due to network error"
        }

def route_by_hallucination(state: GraphState) -> dict:
    print_section("VALIDATING CODE")
    try:
        hallucination_chain = hallucination_checker_prompt | GROQ_LLM
        messages_with_context = create_messages_with_context(state["messages"], state.get("summary", ""))
        result = hallucination_chain.invoke({
            "messages": messages_with_context,
            "Merchant_schema": Schema["Merchants"],
            "Transaction_schema": Schema["Transactions"],
            "generated_code": state["generated_code"]
        })
        result = json_parser.parse(result.content)
        has_hallucination = result["hallucination"] == "Yes"
        
        if has_hallucination:
            print(f"{Fore.YELLOW}⚠ Potential issues detected in code. Fixing...{Style.RESET_ALL}")
        else:
            print(f"{Fore.GREEN}✓ Code validation passed{Style.RESET_ALL}")
        
        return {
            "has_hallucination": has_hallucination,
            "next": "fix_hallucinations" if has_hallucination else "execute_generated_code",
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Network error in hallucination check: {str(e)}{Style.RESET_ALL}")
        return {
            "has_hallucination": True,
            "next": "fix_hallucinations",
            "graph_status": "Failed due to network error"
        }

def summarize_conversation(state: GraphState) -> dict:
    print_section("SUMMARIZING CONVERSATION")
    try:
        if len(state["messages"]) <= TRIGGER_SUMMARY_LENGTH:
            return {
                "messages": state["messages"],
                "summary": state.get("summary", ""),
                "next": END,
                "graph_status": "Completed"
            }
        
        summarizer = summary_prompt | GROQ_LLM
        result = summarizer.invoke({
            "messages": state["messages"],
            "current_summary": state.get("summary", "")
        })
        
        new_summary = str_parser.parse(result.content)
        kept_messages = [RemoveMessage(id=m.id) for m in state["messages"][:-MAX_MESSAGES]]
        
        return {
            "messages": kept_messages,
            "summary": new_summary,
            "next": END,
            "graph_status": "Completed"
        }
    except Exception as e:
        print(f"{Fore.RED}Network error in summarizing conversation: {str(e)}{Style.RESET_ALL}")
        return {
            "messages": state["messages"],
            "summary": state.get("summary", ""),
            "next": END,
            "graph_status": "Failed due to network error"
        }

workflow = StateGraph(GraphState)

workflow.add_node("chat_output", chat_output)
workflow.add_node("requirement_check", route_by_requirement)
workflow.add_node("relevance_check", route_by_relevance)
workflow.add_node("generate_code", generate_code)
workflow.add_node("search_preparation", search_preparation)
workflow.add_node("execute_generated_code", execute_generated_code)
workflow.add_node("parse_search_results", parse_search_results)
workflow.add_node("web_search", web_search)
workflow.add_node("fix_hallucinations", fix_hallucinations)
workflow.add_node("hallucination_check", route_by_hallucination)
workflow.add_node("chat_code_output", chat_code_output)
workflow.add_node("summarize", summarize_conversation)

workflow.set_entry_point("relevance_check")

workflow.add_conditional_edges(
    "relevance_check",
    lambda x: x["next"],
    {
        "relevant": "requirement_check",
        "not_relevant": END
    }
)
workflow.add_conditional_edges(
    "requirement_check",
    lambda x: x["next"],
    {
        "generate_code": "generate_code",
        "search_preparation": "search_preparation",
        "chat_output": "chat_output"
    }
)
workflow.add_conditional_edges(
    "hallucination_check",
    lambda x: x["next"],
    {
        "fix_hallucinations": "fix_hallucinations",
        "execute_generated_code": "execute_generated_code"
    }
)

workflow.add_edge("generate_code", "hallucination_check")
workflow.add_edge("execute_generated_code", "chat_code_output")
workflow.add_edge("chat_code_output", "summarize")
workflow.add_edge("chat_output", "summarize")
workflow.add_edge("fix_hallucinations", "execute_generated_code")
workflow.add_edge("search_preparation", "web_search")
workflow.add_edge("web_search", "parse_search_results")
workflow.add_edge("parse_search_results", "summarize")
workflow.add_edge("summarize", END)

async def delete_thread_data(thread_id: str) -> None:
    """Delete rows related to the specified thread_id from the database."""
    try:
        async with await AsyncConnection.connect(DB_URI, **connection_kwargs) as conn:
            async with conn.cursor() as cur:
                await cur.execute("""
                    DELETE FROM checkpoint_writes 
                    WHERE thread_id = %s
                """, (thread_id,))
                print(f"{Fore.CYAN}Deleted {cur.rowcount} rows from checkpoint_writes for thread_id:{Style.RESET_ALL} {thread_id}")

                await cur.execute("""
                    DELETE FROM checkpoint_blobs 
                    WHERE thread_id = %s
                """, (thread_id,))
                print(f"{Fore.CYAN}Deleted {cur.rowcount} rows from checkpoint_blobs for thread_id:{Style.RESET_ALL} {thread_id}")

                await cur.execute("""
                    DELETE FROM checkpoints 
                    WHERE thread_id = %s
                """, (thread_id,))
                print(f"{Fore.CYAN}Deleted {cur.rowcount} rows from checkpoints for thread_id:{Style.RESET_ALL} {thread_id}")

            await conn.commit()
    except Exception as e:
        print(f"{Fore.RED}Error deleting thread data: {str(e)}{Style.RESET_ALL} for thread_id: {thread_id}")

def handle_shutdown(signum: int, frame: FrameType) -> None:
    """Handle graceful shutdown on CTRL+C"""
    print(f"\n{Fore.YELLOW}Received shutdown signal. Cleaning up...{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Shutdown complete. Exiting...{Style.RESET_ALL}")
    sys.exit(0)

def is_new_context(message: str, previous_messages: List[str]) -> bool:
    """Determine if the new message is based on the previous context using a language model."""
    context_checker = context_check_prompt | GROQ_LLM
    
    input_data = {
        "current_message": message,
        "previous_messages": previous_messages
    }
    
    result = context_checker.invoke(input_data)
    context_decision = json_parser.parse(result.content)

    return context_decision["is_new_context"]

signal.signal(signal.SIGINT, handle_shutdown)

async def main(chat_id: str) -> None:
    init()
    
    conversation_summary = ""
    message_count = 0
    message_history = []  

    async with await AsyncConnection.connect(DB_URI, **connection_kwargs) as conn:
        checkpointer = AsyncPostgresSaver(conn)
        await checkpointer.setup()

        config = {"configurable": {"thread_id": chat_id}}
        graph = workflow.compile(checkpointer=checkpointer)
    
        previous_messages = []

        while True:
            print(f"\n{Fore.MAGENTA}{'─'*50}")
            print(f"Current Message Count: {message_count}")
            print(f"Summary Length: {len(conversation_summary)}")
            if message_history:
                print("\nMessage History:")
                for idx, msg in enumerate(message_history):
                    timestamp = msg['timestamp'].strftime('%H:%M:%S')
                    print(f"{idx + 1}. [{timestamp}] {msg['content'][:50]}...")
            print(f"{Fore.MAGENTA}{'─'*50}{Style.RESET_ALL}\n")
            
            print(f"\n{Fore.CYAN}╭{'─'*48}╮")
            print(f"{Fore.CYAN}│{Style.RESET_ALL} Enter your question (or commands):                {Fore.CYAN}│")
            print(f"{Fore.CYAN}│{Style.RESET_ALL} - 'exit' to quit                                 {Fore.CYAN}│")
            print(f"{Fore.CYAN}│{Style.RESET_ALL} - 'edit N' to edit message N                     {Fore.CYAN}│")
            user_prompt = input(f"{Fore.CYAN}├{'─'*48}┤\n│{Style.RESET_ALL} ")
            print(f"{Fore.CYAN}╰{'─'*48}╯{Style.RESET_ALL}")
            
            if user_prompt.lower().strip() in ['exit', 'quit', 'q']:
                break

            # Handle edit command
            if user_prompt.lower().startswith('edit '):
                try:
                    edit_index = int(user_prompt.split()[1]) - 1
                    if 0 <= edit_index < len(message_history):
                        print(f"\n{Fore.YELLOW}Current message at index {edit_index + 1}:")
                        print(f"Timestamp: {message_history[edit_index]['timestamp'].strftime('%H:%M:%S')}")
                        print(f"Content: {message_history[edit_index]['content']}{Style.RESET_ALL}")
                        
                        new_message = input(f"\n{Fore.CYAN}Enter new message:{Style.RESET_ALL} ")
                        
                        print(f"\n{Fore.YELLOW}Message History Before Edit:")
                        for idx, msg in enumerate(message_history):
                            print(f"{idx + 1}. {msg['content'][:50]}...")
                        
                        # Update the message history
                        message_history = message_history[:edit_index]
                        message_history.append({
                            'content': new_message,
                            'timestamp': datetime.now()
                        })
                        
                        print(f"\n{Fore.YELLOW}Message History After Edit (Truncated):")
                        for idx, msg in enumerate(message_history):
                            print(f"{idx + 1}. {msg['content'][:50]}...")
                        
                        messages = []
                        if response and "messages" in response:
                            messages.extend([RemoveMessage(id=m.id) for m in response["messages"][(edit_index*2-2):]])
                            messages.append(HumanMessage(content=new_message))
                        
                        print(f"\n{Fore.YELLOW}Number of messages being processed: {len(messages)}")
                        
                        new_summary = ""
                        if len(messages) > TRIGGER_SUMMARY_LENGTH:
                            print(f"{Fore.YELLOW}Generating new summary for {len(messages)} messages...")
                            summarizer = summary_prompt | GROQ_LLM
                            result = summarizer.invoke({
                                "messages": messages,
                                "current_summary": conversation_summary
                            })
                            new_summary = str_parser.parse(result.content)
                            print(f"{Fore.GREEN}New summary generated. Length: {len(new_summary)}")
                        
                        input_state = {
                            "messages": messages,
                            "summary": new_summary
                        }
                        
                        print(f"\n{Fore.YELLOW}Processing edited conversation...")
                        print(f"Input state contains {len(input_state['messages'])} messages")
                        print(f"Summary length: {len(input_state['summary'])}{Style.RESET_ALL}")
                        
                        response = await graph.ainvoke(input_state, config)
                        conversation_summary = response.get("summary", "")
                        message_count = len(response.get("messages", []))
                        
                        if isinstance(response["messages"][-1], AIMessage):
                            print(f"{Fore.GREEN}Assistant Response:{Style.RESET_ALL} {response['messages'][-1].content}\n")
                        
                        print(f"\n{Fore.GREEN}Edit processing complete:")
                        print(f"New message count: {message_count}")
                        print(f"New summary length: {len(conversation_summary)}{Style.RESET_ALL}")
                        
                        print(f"\n{Fore.CYAN}{'─'*50}")
                        print(f"{Fore.YELLOW}Current Summary:")
                        if conversation_summary:
                            print(f"{Fore.WHITE}{conversation_summary}")
                        else:
                            print(f"{Fore.WHITE}No summary generated yet")
                        print(f"{Fore.CYAN}{'─'*50}{Style.RESET_ALL}\n")
                        continue
                    else:
                        print(f"{Fore.RED}Invalid message index{Style.RESET_ALL}")
                        continue
                except (ValueError, IndexError):
                    print(f"{Fore.RED}Invalid edit command format. Use 'edit N' where N is the message number.{Style.RESET_ALL}")
                    continue

            message_history.append({
                'content': user_prompt,
                'timestamp': datetime.now()
            })
            print(f"\n{Fore.CYAN}Added new message to history. Total messages: {len(message_history)}{Style.RESET_ALL}")

            if previous_messages and is_new_context(user_prompt, previous_messages):
                print(f"{Fore.YELLOW}New context detected. Do you want to start a new chat session? (y/n):{Style.RESET_ALL} ", end="")
                start_new_chat = input().lower().strip()
                if start_new_chat in ['y', 'yes']:
                    chat_id = str(uuid.uuid4())
                    config["configurable"]["thread_id"] = chat_id
                    previous_messages = []
                    conversation_summary = ""
                    message_count = 0
                    print(f"{Fore.GREEN}New chat session started with chat_id: {chat_id}{Style.RESET_ALL}")
                    continue

            previous_messages.append(user_prompt)
            if len(previous_messages) > 5:
                previous_messages.pop(0)

            input_message = HumanMessage(content=user_prompt)
            input_state = {
                "messages": [input_message],
                "summary": conversation_summary
            }
            print(f"Input state contains {len(input_state['messages'])} messages")
            print(f"\n{Fore.YELLOW}Processing request...{Style.RESET_ALL}")
            response = await graph.ainvoke(input_state, config)
            
            conversation_summary = response.get("summary", conversation_summary)
            message_count = len(response.get("messages", []))
            
            if isinstance(response["messages"][-1], AIMessage):
                print(f"{Fore.GREEN}Assistant Response:{Style.RESET_ALL} {response['messages'][-1].content}\n")
                
            print(f"\n{Fore.CYAN}{'─'*50}")
            print(f"{Fore.YELLOW}Current Summary:")
            if conversation_summary:
                print(f"{Fore.WHITE}{conversation_summary}")
            else:
                print(f"{Fore.WHITE}No summary generated yet")
            print(f"{Fore.CYAN}{'─'*50}{Style.RESET_ALL}\n")

        print(f"\n{Fore.YELLOW}Would you like to save the conversation history? (y/n):{Style.RESET_ALL} ", end="")
        save_db = input().lower().strip()
        if save_db not in ['y', 'yes']:
            print(f"\n{Fore.YELLOW}Cleaning up database...{Style.RESET_ALL}")
            await delete_thread_data(chat_id)
            print(f"{Fore.GREEN}✓ Database cleaned up successfully for thread_id: {chat_id}{Style.RESET_ALL}")


async def edit_and_respond(chat_id: str, user_prompt: str, message_id: str):
    response_tracking = {
        "intermediate_steps": [],
        "processing_status": "pending"
    }
    
    try:
        # Initial connection confirmation
        yield {
            "type": "init",
            "content": "Initializing message edit...",
            "is_final": False,
            "step": "initialization"
        }
        await asyncio.sleep(0.1)
        
        # Initialize database connection and state
        db = next(get_db())
        conversation = db.query(models.SummaryContext).filter(models.SummaryContext.chat_id == chat_id).first()
        
        if not conversation:
            yield {
                "type": "error",
                "content": "Conversation not found",
                "is_final": True,
                "step": "error"
            }
            return
            
        # Load and parse message history
        message_history = json.loads(conversation.message_history) if conversation.message_history else []
        
        # Find the index of the message to edit
        edit_index = -1
        for idx, msg in enumerate(message_history):
            if msg.get('id') == message_id:
                edit_index = idx
                break
                
        if edit_index == -1:
            yield {
                "type": "error",
                "content": "Message not found",
                "is_final": True,
                "step": "error"
            }
            return
            
        # Truncate message history and add edited message
        message_history = message_history[:edit_index]
        message_history.append({
            'content': user_prompt,
            'timestamp': str(datetime.now()),
            'id': message_id
        })
        
        # Update conversation state
        conversation_summary = ""
        previous_messages = [m['content'] for m in message_history[-5:]]
        
        async with await AsyncConnection.connect(DB_URI, **connection_kwargs) as conn:
            checkpointer = AsyncPostgresSaver(conn)
            await checkpointer.setup()
            config = {"configurable": {"thread_id": chat_id}}
            graph = workflow.compile(checkpointer=checkpointer)
            
            # Context Switch Check
            if previous_messages and is_new_context(user_prompt, previous_messages):
                response_tracking["intermediate_steps"].append("context_switch_detected")
                yield {
                    "type": "context_switch",
                    "content": "New conversation context detected",
                    "is_final": False,
                    "step": "context_check"
                }
            
            # Process edited message
            input_message = HumanMessage(content=user_prompt)
            input_state = {
                "messages": [input_message],
                "summary": conversation_summary
            }
            
            # Relevance Check Phase
            response_tracking["intermediate_steps"].append("relevance_check")
            yield {
                "type": "status",
                "content": "Checking query relevance...",
                "is_final": False,
                "step": "relevance_check"
            }
            
            response = await graph.ainvoke(input_state, config)
            
            if not response.get("is_relevant", True):
                response_tracking["intermediate_steps"].append("relevance_failed")
                yield {
                    "type": "warning",
                    "content": "This query is outside the authorized scope of fraud investigation",
                    "is_final": False,
                    "step": "scope_warning"
                }
                # Add an additional assistant message that will replace the "Processing..." indicator
                yield {
                    "type": "assistant",
                    "content": "I'm sorry, but I can only assist with fraud investigation-related queries. Please provide a question or request that is relevant to fraud investigations.",
                    "is_final": True,
                    "step": "final_response",
                    "processing_history": response_tracking["intermediate_steps"]
                }
                return
            
            # Process response based on requirement type
            requirement_type = response.get("requires", "chat")
            
            if requirement_type == "web_search":
                # Handle web search path
                for step in ["search_prep", "search_execution", "search_analysis"]:
                    yield {
                        "type": step,
                        "content": f"Processing {step}...",
                        "is_final": False,
                        "step": step
                    }
                    await asyncio.sleep(0.1)
                    
            elif requirement_type == "coded_output":
                # Handle code generation path
                for step in ["code_gen", "validation", "execution"]:
                    yield {
                        "type": step,
                        "content": f"Processing {step}...",
                        "is_final": False,
                        "step": step
                    }
                    await asyncio.sleep(0.1)
                    
            elif requirement_type == "graph":
                response_tracking["intermediate_steps"].append("graph_request_rejected")
                # Return a message stating that graph visualization is not supported
                graph_not_supported_message = "I'm sorry, but InvestigationGPT cannot generate graph visualizations at this time. Please try asking for a different type of analysis or information."
                
                # Add the message to the final response
                yield {
                    "type": "assistant", 
                    "content": graph_not_supported_message,
                    "is_final": True,
                    "step": "final_response",
                    "processing_history": response_tracking["intermediate_steps"]
                }
                
                # Exit early to prevent further processing
                return
            
            # Simple Chat Path
            elif response.get("requires") == "chat":
                response_tracking["intermediate_steps"].append("chat_path")
                yield {
                    "type": "chat_status", 
                    "content": "Preparing response...",
                    "is_final": False,
                    "step": "chat_preparation"
                }
            
            # Final Response
            if isinstance(response.get("messages", [])[-1], AIMessage):
                response_tracking["final_response"] = response["messages"][-1].content
                response_tracking["processing_status"] = "completed"
                yield {
                    "type": "assistant", 
                    "content": response["messages"][-1].content,
                    "is_final": True,
                    "step": "final_response",
                    "processing_history": response_tracking["intermediate_steps"]
                }
            
            # Summary Generation
            if len(response.get("messages", [])) > TRIGGER_SUMMARY_LENGTH:
                response_tracking["intermediate_steps"].append("summary_generation")
                yield {
                    "type": "summary_status", 
                    "content": "Generating conversation summary...",
                    "is_final": False,
                    "step": "summary_generation"
                }
                if response.get("summary"):
                    yield {"type": "summary", "content": response["summary"]}
            
            # Database Update
            try:
                if conversation:
                    conversation.summary = response.get("summary", "")
                    conversation.message_count = len(response.get("messages", []))
                    conversation.message_history = json.dumps(message_history)
                    conversation.last_updated = datetime.now()
                    conversation.last_updated_by = "system"
                else:
                    new_conversation = models.SummaryContext(
                        chat_id=chat_id,
                        summary=response.get("summary", ""),
                        message_count=len(response.get("messages", [])),
                        message_history=json.dumps(message_history),
                        last_updated=datetime.now(),
                        last_updated_by="system"
                    )
                    db.add(new_conversation)
                db.commit()
                yield {
                    "type": "db_status", 
                    "content": "Conversation state updated",
                    "is_final": False,
                    "step": "database_update"
                }
            except Exception as e:
                yield {
                    "type": "db_error", 
                    "content": f"Database error: {str(e)}",
                    "is_final": False,
                    "step": "database_error"
                }
                
    except Exception as e:
        yield {
            "type": "error",
            "content": f"Error processing edit: {str(e)}",
            "is_final": True,
            "step": "error",
            "processing_history": response_tracking.get("intermediate_steps", [])
        }

async def chat(chat_id: str, user_prompt: str, message_id: str):
    response_tracking = {
        "intermediate_steps": [],
        "processing_status": "pending"
    }
    
    try:
        # Check if this is a visualization editing message
        if user_prompt.startswith("Editing Visualization:"):
            # Extract visualization ID - it's expected to be after the colon
            viz_id_match = user_prompt.split("Editing Visualization:")
            viz_id = viz_id_match[1].strip() if len(viz_id_match) > 1 else "unknown"
            
            # Return a custom message without making an LLM call
            response_tracking["intermediate_steps"].append("visualization_edit_detected")
            yield {
                "type": "assistant",
                "content": f"Happy to help you customize the visualization {viz_id}.\nYou can either give me instructions or use the UI box below",
                "is_final": True,
                "step": "final_response",
                "processing_history": response_tracking["intermediate_steps"]
            }
            return
            
        # Yield immediately for connection confirmation
        yield {
            "type": "init",
            "content": "Initializing conversation...",
            "is_final": False,
            "step": "initialization"
        }
        await asyncio.sleep(0.1)  # Small delay to ensure client receives initial message
        
        # Process in smaller chunks with immediate yields
        for step in ["relevance_check", "requirement_analysis", "processing"]:
            yield {
                "type": "status",
                "content": f"Processing {step}...",
                "is_final": False,
                "step": step
            }
            await asyncio.sleep(0.1)  # Allow client to receive each status update
            
            # Add progress indicators for long-running operations
            if step == "processing":
                for i in range(3):  # Show progress during long operations
                    yield {
                        "type": "progress",
                        "content": f"Processing... {(i+1)*33}%",
                        "is_final": False,
                        "step": f"processing_step_{i+1}"
                    }
                    await asyncio.sleep(0.1)
        
        # Initialize database connection and state
        db = next(get_db())
        conversation = db.query(models.SummaryContext).filter(models.SummaryContext.chat_id == chat_id).first()
        
        # Initialize conversation state
        conversation_summary = ""
        message_count = 0
        message_history = []
        previous_messages = []
        
        if conversation:
            conversation_summary = conversation.summary
            message_count = conversation.message_count
            message_history = json.loads(conversation.message_history) if conversation.message_history else []
            previous_messages = [m['content'] for m in message_history[-5:]]
        
        message_history.append({
            'content': user_prompt,
            'timestamp': str(datetime.now())
        })
        
        # Add a response tracking dictionary
        response_tracking = {
            "intermediate_steps": [],
            "final_response": None,
            "processing_status": "pending"
        }
        print("DB URI: ", DB_URI)
        async with await AsyncConnection.connect(DB_URI, **connection_kwargs) as conn:
            print("Failed to connect to db")
            checkpointer = AsyncPostgresSaver(conn)
            await checkpointer.setup()
            config = {"configurable": {"thread_id": chat_id}}
            graph = workflow.compile(checkpointer=checkpointer)
            
            # # Context Switch Check
            # if previous_messages and is_new_context(user_prompt, previous_messages):
            #     response_tracking["intermediate_steps"].append("context_switch_detected")
            #     yield {
            #         "type": "context_switch", 
            #         "content": "New conversation context detected",
            #         "is_final": False,
            #         "step": "context_check"
            #     }
            #     yield {
            #         "type": "action_required", 
            #         "content": "Would you like to start a new chat session?",
            #         "is_final": True,
            #         "step": "user_action_required"
            #     }
            #     message_history.push("New conversation context detected")
            #     if conversation:
            #         conversation.summary = response.get("summary", "")
            #         conversation.message_count = len(response.get("messages", []))
            #         conversation.message_history = json.dumps(message_history)
            #         conversation.last_updated = datetime.now()
            #         conversation.last_updated_by = "system"
            #     else:
            #         new_conversation = models.SummaryContext(
            #             chat_id=chat_id,
            #             summary=response.get("summary", ""),
            #             message_count=len(response.get("messages", [])),
            #             message_history=json.dumps(message_history),
            #             last_updated=datetime.now(),
            #             last_updated_by="system"
            #         )
            #         db.add(new_conversation)
            #     db.commit()
            #     return
            print("working : 1")
            input_message = HumanMessage(content=user_prompt)
            input_state = {
                "messages": [input_message],
                "summary": conversation_summary
            }
            print("working : 2")
            # Relevance Check Phase
            response_tracking["intermediate_steps"].append("relevance_check")
            yield {
                "type": "status", 
                "content": "Checking query relevance...",
                "is_final": False,
                "step": "relevance_check"
            }
            print("working : 3")
            response = await graph.ainvoke(input_state, config)
            print("working : 4")
            if not response.get("is_relevant", True):
                response_tracking["intermediate_steps"].append("relevance_failed")
                yield {
                    "type": "warning", 
                    "content": "This query is outside the authorized scope of fraud investigation",
                    "is_final": False,
                    "step": "scope_warning"
                }
                # Add an additional assistant message that will replace the "Processing..." indicator
                yield {
                    "type": "assistant",
                    "content": "I'm sorry, but I can only assist with fraud investigation-related queries. Please provide a question or request that is relevant to fraud investigations.",
                    "is_final": True,
                    "step": "final_response",
                    "processing_history": response_tracking["intermediate_steps"]
                }
                return
            
            # Requirement Check Phase
            response_tracking["intermediate_steps"].append("requirement_check")
            yield {
                "type": "status", 
                "content": "Determining query type...",
                "is_final": False,
                "step": "requirement_analysis"
            }
            
            # Web Search Path
            if response.get("requires") == "web_search":
                response_tracking["intermediate_steps"].append("web_search_path")
                yield {
                    "type": "search_prep", 
                    "content": "Preparing search keywords...",
                    "is_final": False,
                    "step": "search_preparation"
                }
                if response.get("search_keywords"):
                    yield {
                        "type": "search_keywords", 
                        "content": response["search_keywords"],
                        "is_final": False,
                        "step": "keyword_generation"
                    }
                
                # Search Execution
                yield {"type": "search_status", "content": "Executing web search..."}
                if response.get("search_results"):
                    # Serialize the Document objects
                    serialized_results = []
                    for doc in response["search_results"]:
                        result = {}
                        # Get document attributes safely
                        for attr in ['title', 'content', 'url', 'score']:
                            if hasattr(doc, attr):
                                result[attr] = getattr(doc, attr)
                            elif isinstance(doc, dict) and attr in doc:
                                result[attr] = doc[attr]
                            else:
                                result[attr] = None
                        # Ensure at least some content is present
                        if not result.get('content') and hasattr(doc, 'page_content'):
                            result['content'] = doc.page_content
                        serialized_results.append(result)
                    yield {"type": "search_results", "content": serialized_results}
                
                # Search Analysis
                yield {"type": "search_analysis", "content": "Analyzing search results..."}
                if response.get("search_summary"):
                    yield {"type": "search_summary", "content": response["search_summary"]}
            
            # Code Generation Path
            elif response.get("requires") == "coded_output":
                response_tracking["intermediate_steps"].append("code_generation_path")
                # Code generation steps with labels...
                yield {
                    "type": "code_gen", 
                    "content": "Generating code solution...",
                    "is_final": False,
                    "step": "code_generation"
                }
                if response.get("generated_code"):
                    yield {"type": "code", "content": response["generated_code"]}
                
                # Hallucination Check
                yield {"type": "validation", "content": "Validating code..."}
                if response.get("has_hallucination"):
                    yield {"type": "fix_status", "content": "Fixing code issues..."}
                    if response.get("fixed_code"):
                        yield {"type": "code", "content": response["fixed_code"]}
                
                # Code Execution
                yield {"type": "execution_status", "content": "Executing code..."}
                if response.get("execution_output"):
                    yield {"type": "execution_result", "content": response["execution_output"]}
                    if response.get("execution_error"):
                        yield {"type": "execution_error", "content": response["execution_error"]}
            
            # Graph Generation Path
            elif response.get("requires") == "graph":
                response_tracking["intermediate_steps"].append("graph_request_rejected")
                # Return a message stating that graph visualization is not supported
                graph_not_supported_message = "I'm sorry, but InvestigationGPT cannot generate graph visualizations at this time. Please try asking for a different type of analysis or information."
                
                # Add the message to the final response
                yield {
                    "type": "assistant", 
                    "content": graph_not_supported_message,
                    "is_final": True,
                    "step": "final_response",
                    "processing_history": response_tracking["intermediate_steps"]
                }
                
                # Exit early to prevent further processing
                return
            
            # Simple Chat Path
            elif response.get("requires") == "chat":
                response_tracking["intermediate_steps"].append("chat_path")
                yield {
                    "type": "chat_status", 
                    "content": "Preparing response...",
                    "is_final": False,
                    "step": "chat_preparation"
                }
            
            # Final Response
            if isinstance(response.get("messages", [])[-1], AIMessage):
                response_tracking["final_response"] = response["messages"][-1].content
                response_tracking["processing_status"] = "completed"
                yield {
                    "type": "assistant", 
                    "content": response["messages"][-1].content,
                    "is_final": True,
                    "step": "final_response",
                    "processing_history": response_tracking["intermediate_steps"]
                }
            
            # Summary Generation
            if len(response.get("messages", [])) > TRIGGER_SUMMARY_LENGTH:
                response_tracking["intermediate_steps"].append("summary_generation")
                yield {
                    "type": "summary_status", 
                    "content": "Generating conversation summary...",
                    "is_final": False,
                    "step": "summary_generation"
                }
                if response.get("summary"):
                    yield {"type": "summary", "content": response["summary"]}
            
            # Database Update
            try:
                if conversation:
                    conversation.summary = response.get("summary", "")
                    conversation.message_count = len(response.get("messages", []))
                    conversation.message_history = json.dumps(message_history)
                    conversation.last_updated = datetime.now()
                    conversation.last_updated_by = "system"
                else:
                    new_conversation = models.SummaryContext(
                        chat_id=chat_id,
                        summary=response.get("summary", ""),
                        message_count=len(response.get("messages", [])),
                        message_history=json.dumps(message_history),
                        last_updated=datetime.now(),
                        last_updated_by="system"
                    )
                    db.add(new_conversation)
                db.commit()
                yield {
                    "type": "db_status", 
                    "content": "Conversation state updated",
                    "is_final": False,
                    "step": "database_update"
                }
            except Exception as e:
                yield {
                    "type": "db_error", 
                    "content": f"Database error: {str(e)}",
                    "is_final": False,
                    "step": "database_error"
                }
                
    except Exception as e:
        yield {
            "type": "system_error", 
            "content": f"System error: {str(e)}",
            "is_final": True,
            "step": "system_error",
            "processing_history": response_tracking.get("intermediate_steps", [])
        }


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Run the agentic workflow.")
    parser.add_argument('-c', '--chat_id', type=str, default='5b0fcdf4-0044-4bcb-8b34-32fa70f9c7af', 
                       help='Give chat id to save or continue the conversation history')
    
    try:
        args = parser.parse_args()
        asyncio.run(main(args.chat_id))  # Run the async main function
    except SystemExit:
        parser.print_help()
        sys.exit(1)

