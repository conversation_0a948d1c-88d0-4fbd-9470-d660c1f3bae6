import pandas as pd
import numpy as np
import os
from pathlib import Path


# Load datasets


# Example of expected code structure
def example_analysis():
    try:
        transactions_df = pd.read_csv("data/transactions.csv")
        merchants_df = pd.read_csv("data/merchants.csv")
        investigations_df = pd.read_csv("data/investigations.csv")
        # Verify required columns exist in dataframes
        required_columns = {
            'transactions': ['mer_id'],
            'merchants': ['mer_id'],
            'investigations': ['mer_id', 'investigation_status']
        }
        # always print want other files are present in the same directory
        print("Checking if other files are present in the same directory...")
        # print all the other files in the same directory
        print(f"Other files in the same directory: {os.listdir()}")
        
        for col in required_columns['transactions']:
            if col not in transactions_df.columns:
                print(f"Error: Required column '{col}' not found in transactions dataset")
                return None, 0, 0, 0
                
        for col in required_columns['merchants']:
            if col not in merchants_df.columns:
                print(f"Error: Required column '{col}' not found in merchants dataset")
                return None, 0, 0, 0
                
        for col in required_columns['investigations']:
            if col not in investigations_df.columns:
                print(f"Error: Required column '{col}' not found in investigations dataset")
                return None, 0, 0, 0

        # Check if input dataframes are empty
        if transactions_df.empty:
            print("Warning: Transactions dataset is empty. Please verify your data source.")
            return None, 0, 0, 0
            
        if merchants_df.empty:
            print("Warning: Merchants dataset is empty. Please verify your data source.")
            return None, 0, 0, 0
            
        if investigations_df.empty:
            print("Warning: Investigations dataset is empty. Please verify your data source.")
            return None, 0, 0, 0

        # Merge transactions with merchant data and investigations
        merged_df = transactions_df.merge(
            merchants_df,
            how='left',
            left_on='mer_id',
            right_on='mer_id'
        ).merge(
            investigations_df,
            how='left',
            on='mer_id'
        )
        
        # Enhanced empty result handling
        if merged_df.empty:
            print("\nWarning: No matching results found!")
            print("Possible reasons:")
            print("- Check if merchant IDs exist in all datasets")
            print("- Verify the spelling and case of your search criteria")
            print("- Confirm that the data isn't filtered too restrictively")
            return None, 0, 0, 0
            
        # Calculate basic statistics with enhanced null/zero handling
        total_transactions = len(transactions_df)
        if total_transactions == 0:
            print("Warning: No transactions found in the dataset")
            
        unique_merchants = merchants_df['mer_id'].nunique()
        if unique_merchants == 0:
            print("Warning: No unique merchants found in the dataset")
            
        total_investigations = len(investigations_df)
        if total_investigations == 0:
            print("Warning: No investigations found in the dataset")
        
        # Check for empty or zero results in key metrics
        if merged_df['sum_amount_lifetime'].sum() == 0:
            print("Warning: Total transaction amount is zero. Please verify data accuracy.")
            
        if merged_df['count_transactions'].sum() == 0:
            print("Warning: Transaction count is zero. Please verify data accuracy.")
            
        # Investigation statistics
        open_investigations = investigations_df[investigations_df['investigation_status'] == 'open'].shape[0]
        closed_investigations = investigations_df[investigations_df['investigation_status'] == 'closed'].shape[0]
        
        print(f"\nResults summary:")
        print(f"Total transactions: {total_transactions}")
        print(f"Unique merchants: {unique_merchants}")
        print(f"\nInvestigation Summary:")
        print(f"Total investigations: {total_investigations}")
        print(f"Open investigations: {open_investigations}")
        print(f"Closed investigations: {closed_investigations}")
        
        return merged_df, total_transactions, unique_merchants, total_investigations
        
    except Exception as e:
        print(f"An error occurred during analysis: {str(e)}")
        return None, 0, 0, 0

if __name__ == "__main__":
    example_analysis()
