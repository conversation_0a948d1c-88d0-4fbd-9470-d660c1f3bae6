import os
from psycopg import Connection, AsyncConnection
from colorama import init, Fore, Style
from dotenv import load_dotenv
import argparse
import sys
import asyncio

# Load environment variables
load_dotenv()

# Initialize colorama for cross-platform color support
init()

# Retrieve database connection details from environment variables
DB_USER = os.getenv('DB_USER')
DB_PASSWORD = os.getenv('DB_PASSWORD')
DB_HOST = os.getenv('DB_HOST')
DB_PORT = os.getenv('DB_PORT')
DB_NAME = os.getenv('DB_NAME')

# Construct the database URI
DB_URI = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?sslmode=disable"

# Connection configuration
connection_kwargs = {
    "autocommit": True,
    "prepare_threshold": 0,
    "connect_timeout": 10,  # Add timeout in seconds
    "keepalives": 1,       # Enable keepalive
    "keepalives_idle": 30  # Seconds between keepalive probes
}

def delete_all_tables() -> None:
    """Function to delete all tables in the database."""
    try:
        with Connection.connect(DB_URI, **connection_kwargs) as conn:
            with conn.cursor() as cur:
                # Fetch all table names
                cur.execute("""
                    SELECT tablename 
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                """)
                tables = cur.fetchall()
                
                for (table,) in tables:
                    # Drop each table
                    cur.execute(f"DROP TABLE IF EXISTS {table} CASCADE")
                    print(f"{Fore.RED}Deleted table:{Style.RESET_ALL} {table}")
    except Exception as e:
        print(f"{Fore.RED}Error deleting tables: {str(e)}{Style.RESET_ALL}")

def print_all_tables() -> None:
    """Print all tables in the database."""
    try:
        with Connection.connect(DB_URI, **connection_kwargs) as conn:
            with conn.cursor() as cur:
                unique_thread_ids = set()

                # Print unique thread_id from checkpoints
                cur.execute("SELECT DISTINCT thread_id FROM checkpoints")
                rows = cur.fetchall()
                print(f"{Fore.CYAN}Chat IDs in database:{Style.RESET_ALL}")
                for row in rows:
                    if row[0] not in unique_thread_ids:
                        unique_thread_ids.add(row[0])
                        print(row)

    except Exception as e:
        print(f"{Fore.RED}Error retrieving data: {str(e)}{Style.RESET_ALL}")

async def delete_thread_data(thread_id: str) -> None:
    """Delete rows related to the specified thread_id from the database."""
    try:
        async with await AsyncConnection.connect(DB_URI, **connection_kwargs) as conn:
            async with conn.cursor() as cur:
                await cur.execute("""
                    DELETE FROM checkpoint_writes 
                    WHERE thread_id = %s
                """, (thread_id,))
                print(f"{Fore.CYAN}Deleted {cur.rowcount} rows from checkpoint_writes for thread_id:{Style.RESET_ALL} {thread_id}")

                await cur.execute("""
                    DELETE FROM checkpoint_blobs 
                    WHERE thread_id = %s
                """, (thread_id,))
                print(f"{Fore.CYAN}Deleted {cur.rowcount} rows from checkpoint_blobs for thread_id:{Style.RESET_ALL} {thread_id}")

                await cur.execute("""
                    DELETE FROM checkpoints 
                    WHERE thread_id = %s
                """, (thread_id,))
                print(f"{Fore.CYAN}Deleted {cur.rowcount} rows from checkpoints for thread_id:{Style.RESET_ALL} {thread_id}")

            await conn.commit()
    except Exception as e:
        print(f"{Fore.RED}Error deleting thread data: {str(e)}{Style.RESET_ALL} for thread_id: {thread_id}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Delete thread data from database.")
    parser.add_argument('-d', '--delete', type=str, help='Thread ID to delete from database')
    parser.add_argument('-a', '--all', action='store_true', help='Delete all tables in the database')
    parser.add_argument('-l', '--list', action='store_true', help='List all tables in the database')
    
    try:
        args = parser.parse_args()
        if args.delete:
            asyncio.run(delete_thread_data(args.delete))
        elif args.all:
            delete_all_tables()
        elif args.list:
            print_all_tables()
        else:
            parser.print_help()
    except SystemExit:
        parser.print_help()
        sys.exit(1)
