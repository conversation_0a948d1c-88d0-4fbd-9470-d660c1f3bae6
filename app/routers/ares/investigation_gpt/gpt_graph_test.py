import pandas as pd
import json
import numpy as np
from pathlib import Path

base_dir = Path(__file__).resolve().parent.parent
merchants_path = base_dir / 'data' / 'merchants.csv'

# Load datasets
merchants_df = pd.read_csv(merchants_path)

def generate_graph_specs():
    try:
        # Calculate total transaction volume for each merchant
        merchants_df['txn_volume'] = merchants_df['mer_total_txn'] * merchants_df['mer_avg_txn_size']
        
        # Put merchants into buckets based on total transaction volume
        buckets = [0, 1000, 10000, 100000, 1000000, float('inf')]
        labels = ['0-1000', '1000-10000', '10000-100000', '100000-1000000', 'More than 1000000']
        merchants_df['bucket'] = pd.cut(merchants_df['txn_volume'], bins=buckets, labels=labels)
        
        # Calculate the count of merchants in each bucket
        bucket_counts = merchants_df['bucket'].value_counts().reset_index()
        bucket_counts.columns = ['bucket', 'count']
        
        # Generate graph specification
        graph_specs = {
            "graph_specs": {
                "title": "Merchant Distribution by Total Transaction Volume",
                "x_axis": {
                    "title": "Total Transaction Volume",
                    "min": 0,
                    "max": len(buckets) - 1,
                    "values": list(range(len(buckets) - 1))
                },
                "y_axis": {
                    "title": "Number of Merchants",
                    "min": 0,
                    "max": bucket_counts['count'].max(),
                    "values": bucket_counts['count'].tolist()
                },
                "type": "bar",
                "timeframe": "N/A",
                "additional_info": {
                    "annotations": [],
                    "trend_lines": []
                }
            }
        }
        
        print(json.dumps(graph_specs, indent=2))
        
    except pd.errors.EmptyDataError:
        print(json.dumps({
            "error": "Data file is empty",
            "status": "Failed due to empty data"
        }))
    except FileNotFoundError as e:
        print(json.dumps({
            "error": f"File not found: {str(e)}",
            "status": "Failed due to missing file"
        }))
    except ValueError as e:
        print(json.dumps({
            "error": f"Value error: {str(e)}",
            "status": "Failed due to invalid data"
        }))
    except Exception as e:
        print(json.dumps({
            "error": f"Something went wrong: {str(e)}",
            "status": "Failed due to unexpected error"
        }))

if __name__ == "__main__":
    generate_graph_specs()