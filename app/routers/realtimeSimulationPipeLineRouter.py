from fastapi import APIRouter, Depends, HTTPException, Path, status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from uuid import UUID
from datetime import datetime
from typing import List, Dict, Any
from ..database import get_db
from ..models import models
from ..schemas.request_models import (
    TransactionCreate
)
from .redFlagGenerationRouter import update_transaction_metrics
from decimal import Decimal

router = APIRouter(
    prefix="",
    tags=["Pipe line"]
)

@router.post("/newTxn")
def new_txn(txn: TransactionCreate):
    try:
        print(txn)
        db = next(get_db())
        new_transaction = models.transactions(
            id=UUID(txn.transaction_id),
            merchant_id=UUID(txn.merchant_id),
            transaction_type=txn.transaction_type,
            merchant_type=txn.merchant_type,
            city=txn.city,
            payment_channel=txn.payment_channel,
            country_code=txn.country_code,
            amount=txn.amount,
            merchant_name=txn.merchant_name,
            risk_score=txn.risk_score,
            risk_description=txn.risk_description,
            product_name=txn.product_name,
            status=txn.status,
            timestamp=txn.timestamp,
            dispute_date=txn.dispute_date,
            complain_date=txn.complain_date,
            created_at=txn.created_at,
            is_fraud_transaction=txn.is_fraud_transaction,
            cx_id=txn.cx_id,
            cx_ip=txn.cx_ip,
            cx_device_id=txn.cx_device_id,
            cx_card_number=txn.cx_card_number,
            cx_city=txn.cx_city,
            cx_pii_linkage_score=txn.cx_pii_linkage_score,
            is_cardholder_name_match=txn.is_cardholder_name_match,
            is_chargeback=txn.is_chargeback,
            is_cx_international=txn.is_cx_international,
            txn_status=txn.txn_status,
            is_cx_risky=txn.is_cx_risky,
            invoice_amount=txn.invoice_amount,
            is_cancelled=txn.is_cancelled,
            txn_currency=txn.txn_currency,
            has_cx_complaint=txn.has_cx_complaint,
        )
        db.add(new_transaction)
        db.commit()
        update_transaction_metrics(new_transaction.id)
        return {"message": "Transaction created successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
