from fastapi import APIRouter, Depends, HTTPException, Path, status, BackgroundTasks, File, UploadFile, Form
from ..routers.ares.RAG.ocr_pdf import process_pdf
from transformers import AutoTokenizer, AutoModel
from sklearn.metrics.pairwise import cosine_similarity
import torch
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from uuid import UUID, uuid4
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from ..database import get_db
from ..models import models
from ..schemas.request_models import (
    EmailCommunicationListCreate,
    EmailCommunicationCreate,
    EmailAttachmentCreate,
    message
)
import logging
from groq import Groq
import os
from .apiProtection import get_current_user
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
import imaplib
import email
from email.header import decode_header
import email.utils
from fastapi.security import OAuth2Password<PERSON>earer
import ssl
import asyncio
from pathlib import Path
import aiofiles
import mimetypes
from fastapi.responses import FileResponse
import socket
import re
import json
from sentence_transformers import SentenceTransformer
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import PyMuPDFLoader, TextLoader
from pydantic import BaseModel
import numpy as np
from functools import lru_cache
from sqlalchemy import or_, and_
from sqlalchemy import func

# Configure logging
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

# Email configuration
EMAIL_CONFIG = {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "imap_server": "imap.gmail.com",
    "username": os.getenv("EMAIL_USERNAME"),
    "password": os.getenv("EMAIL_PASSWORD"),
    "attachment_path": "email_attachments"
}


# Create attachments directory if it doesn't exist
Path(EMAIL_CONFIG["attachment_path"]).mkdir(parents=True, exist_ok=True)

router = APIRouter(
    prefix="",
    tags=["Email Communication"]
)

class EmailService:
    def __init__(self):
        self.smtp_server = EMAIL_CONFIG["smtp_server"]
        self.smtp_port = EMAIL_CONFIG["smtp_port"]
        self.username = EMAIL_CONFIG["username"]
        self.password = EMAIL_CONFIG["password"]
        self.attachment_path = EMAIL_CONFIG["attachment_path"]

    def create_smtp_connection(self):
        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls(context=ssl.create_default_context())
            server.login(self.username, self.password)
            return server
        except Exception as e:
            # logger.error(f"SMTP connection error: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to connect to email server")

    def create_imap_connection(self):
        try:
            # logger.info(f"Attempting to connect to IMAP server: {EMAIL_CONFIG['imap_server']}")
            mail = imaplib.IMAP4_SSL(EMAIL_CONFIG["imap_server"])
            # logger.info("IMAP SSL connection established")
            
            if not self.username or not self.password:
                # logger.error("Missing email credentials")
                raise ValueError("Email username or password not configured")
            
            mail.login(self.username, self.password)
            # logger.info("Successfully logged into IMAP server")
            return mail
        except imaplib.IMAP4.error as e:
            # logger.error(f"IMAP authentication error: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to authenticate with email server")
        except socket.gaierror as e:
            # logger.error(f"DNS resolution error: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to resolve email server address")
        except Exception as e:
            # logger.error(f"IMAP connection error: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to connect to email server")

    async def create_email_message(
        self, 
        to_email: str, 
        subject: str, 
        content: str,
        attachments: List[UploadFile] = None,
        in_reply_to: Optional[str] = None, 
        references: Optional[List[str]] = None
    ) -> MIMEMultipart:
        message = MIMEMultipart()
        message_id = email.utils.make_msgid()
        message["From"] = self.username
        message["To"] = to_email
        message["Subject"] = subject
        message["Message-ID"] = message_id
        message["Date"] = email.utils.formatdate()

        if in_reply_to:
            message["In-Reply-To"] = in_reply_to
        if references:
            message["References"] = ", ".join(references)

        # Add text content
        message.attach(MIMEText(content, "plain"))

        # Handle attachments
        if attachments:
            for attachment in attachments:
                file_content = await attachment.read()
                part = MIMEApplication(file_content, Name=attachment.filename)
                part['Content-Disposition'] = f'attachment; filename="{attachment.filename}"'
                message.attach(part)

        return message, message_id

email_service = EmailService()

@router.post("/send-without-attachments")
async def send_email(
    to_email: str,
    subject: str,
    content: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Create and send email
        message, message_id = await email_service.create_email_message(
            to_email=to_email,
            subject=subject,
            content=content,
            attachments=None
        )
        server = email_service.create_smtp_connection()
        server.send_message(message)
        server.quit()

        # Store email in database
        db_email = models.email_communication(
            id=uuid4(),
            message_id=message_id,
            thread_id=uuid4(),
            sender=EMAIL_CONFIG["username"],
            receiver=to_email,
            subject=subject,
            content=content,
            attachments=None,
            timestamp=datetime.now(),
            created_at=datetime.now()
        )
        db.add(db_email)
        db.commit()

        return {
            "message": "Email sent successfully",
            "email_id": str(db_email.id),
            "message_id": message_id,
            "thread_id": str(db_email.thread_id)
        }

    except Exception as e:
        # logger.error(f"Error sending email: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/send-with-attachments")
async def send_email(
    to_email: str,
    subject: str,
    content: str,
    attachments: List[UploadFile] = None,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Create and send email
        message, message_id = await email_service.create_email_message(
            to_email=to_email,
            subject=subject,
            content=content,
            attachments=attachments if isinstance(attachments, list) else None
        )
        
        server = email_service.create_smtp_connection()
        server.send_message(message)
        server.quit()

        # Save attachments
        saved_attachments = []
        if isinstance(attachments, list):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            for attachment in attachments:
                # logger.info(f"Processing attachment: {attachment.filename}")
                timestamped_filename = f"{timestamp}_{attachment.filename}"
                file_path = os.path.join(EMAIL_CONFIG["attachment_path"], timestamped_filename)
                
                # Reset file pointer before reading
                attachment.file.seek(0)
                
                async with aiofiles.open(file_path, 'wb') as f:
                    attachment_content = await attachment.read()
                    if not attachment_content:
                        # logger.error(f"Attachment {timestamped_filename} is empty.")
                        pass
                    await f.write(attachment_content)
                saved_attachments.append(file_path)

       
        saved_attachments_ids = []
         # Store email in database
        db_email = models.email_communication(
            id=uuid4(),
            message_id=message_id,
            thread_id=uuid4(),
            sender=EMAIL_CONFIG["username"],
            receiver=to_email,
            subject=subject,
            content=content,
            attachments=None,
            timestamp=datetime.now(),
            created_at=datetime.now()
        )
        # Add the email record to the database without committing
        db.add(db_email) 
        db.flush()  # This assigns an ID without committing

        # Store documents in database
        if isinstance(attachments, list):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            for attachment in attachments:
                timestamped_filename = f"{timestamp}_{attachment.filename}"
                doc = models.documents_from_email(
                    document_id=uuid4(),
                    email_id=db_email.id,
                    filename=timestamped_filename,
                    path=os.path.join(EMAIL_CONFIG["attachment_path"], timestamped_filename),
                    size=len(await attachment.read()),
                    content_type=attachment.content_type,
                    download_url=f"/api/v1/email-communication/attachment/{timestamped_filename}"
                )
                db.add(doc)
                saved_attachments_ids.append(doc.filename)

        # Update the email record with the attachment IDs
        db_email.attachments = saved_attachments_ids if saved_attachments_ids else None
        
        # Commit all changes
        db.commit()

        return {
            "message": "Email sent successfully",
            "email_id": str(db_email.id),
            "message_id": message_id,
            "thread_id": str(db_email.thread_id)
        }

    except Exception as e:
        # logger.error(f"Error sending email: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/reply-without-attachments/{email_id}")
async def reply_to_email(
    email_id: UUID,
    content: str,
    subject: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Get original email
        original_email = db.query(models.email_communication).filter(
            models.email_communication.id == email_id
        ).first()
        
        if not original_email:
            raise HTTPException(status_code=404, detail="Original email not found")

        # Determine recipient
        to_email = original_email.sender if original_email.sender != EMAIL_CONFIG["username"] else original_email.receiver
        
        # Fix subject line construction
        subject = f"Re: {original_email.subject} - {subject}" if not original_email.subject.startswith("Re:") else f"{original_email.subject} - {subject}"
        
        # Create and send reply
        message, new_message_id = await email_service.create_email_message(
            to_email=to_email,
            subject=subject,
            content=content,
            in_reply_to=original_email.message_id,
            references=[original_email.message_id]
        )

        server = email_service.create_smtp_connection()
        server.send_message(message)
        server.quit()

        # Store reply in database
        db_email = models.email_communication(
            id=uuid4(),
            message_id=new_message_id,
            thread_id=original_email.thread_id,
            parent_message_id=original_email.message_id,
            sender=EMAIL_CONFIG["username"],
            receiver=to_email,
            subject=subject,
            content=content,
            attachments=None,
            timestamp=datetime.now(),
            created_at=datetime.now()
        )
        db.add(db_email)
        db.commit()

        return {
            "message": "Reply sent successfully",
            "email_id": str(db_email.id),
            "message_id": new_message_id
        }

    except Exception as e:
        logger.error(f"Error sending reply: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/reply-with-attachments/{email_id}")
async def reply_to_email(
    email_id: UUID,
    content: str,
    subject: str,
    attachments: List[UploadFile] = None,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Get original email
        original_email = db.query(models.email_communication).filter(
            models.email_communication.id == email_id
        ).first()
        
        if not original_email:
            raise HTTPException(status_code=404, detail="Original email not found")

        # Determine recipient
        to_email = original_email.sender if original_email.sender != EMAIL_CONFIG["username"] else original_email.receiver
        
        # Fix subject line construction - remove reference to undefined reply_content
        subject = f"Re: {original_email.subject} - {subject}" if not original_email.subject.startswith("Re:") else f"{original_email.subject} - {subject}"
        
        # Create and send reply
        message, new_message_id = await email_service.create_email_message(
            to_email=to_email,
            subject=subject,
            content=content,
            attachments=attachments if isinstance(attachments, list) else None,  # No need for conditional here since None is handled in create_email_message
            in_reply_to=original_email.message_id,
            references=[original_email.message_id]
        )

        server = email_service.create_smtp_connection()
        server.send_message(message)
        server.quit()

        saved_attachments = []
        if isinstance(attachments, list):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            for attachment in attachments:
                # logger.info(f"Processing attachment: {attachment.filename}")
                timestamped_filename = f"{timestamp}_{attachment.filename}"
                file_path = os.path.join(EMAIL_CONFIG["attachment_path"], timestamped_filename)
                
                # Reset file pointer before reading
                attachment.file.seek(0)
                
                async with aiofiles.open(file_path, 'wb') as f:
                    attachment_content = await attachment.read()
                    if not attachment_content:
                        logger.error(f"Attachment {timestamped_filename} is empty.")
                    await f.write(attachment_content)
                saved_attachments.append(file_path)

        # Store reply in database
        
        saved_attachments_ids = []
        db_email = models.email_communication(
            id=uuid4(),
            message_id=new_message_id,
            thread_id=original_email.thread_id,
            parent_message_id=original_email.message_id,
            sender=EMAIL_CONFIG["username"],
            receiver=to_email,
            subject=subject,
            content=content,
            attachments=saved_attachments_ids if saved_attachments_ids else None,
            timestamp=datetime.now(),
            created_at=datetime.now()
        )
        # Store documents in database
        if isinstance(attachments, list):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            for attachment in attachments:
                timestamped_filename = f"{timestamp}_{attachment.filename}"
                doc = models.documents_from_email(
                    document_id=uuid4(),
                    email_id=db_email.id,
                    filename=timestamped_filename,
                    path=os.path.join(EMAIL_CONFIG["attachment_path"], timestamped_filename),
                    size=len(await attachment.read()),
                    content_type=attachment.content_type,
                    download_url=f"/api/v1/email-communication/attachment/{timestamped_filename}"
                )
                db.add(doc)
                saved_attachments_ids.append(doc.document_id)
            db.commit()

        
        db.add(db_email)
        db.commit()

        return {
            "message": "Reply sent successfully",
            "email_id": str(db_email.id),
            "message_id": new_message_id
        }

    except Exception as e:
        logger.error(f"Error sending reply: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# @router.get("/conversation/{email_address}")
# async def get_email_conversation(
#     email_address: str,
#     limit: int = 50,
#     offset: int = 0,
#     db: Session = Depends(get_db),
#     current_user: models.User = Depends(get_current_user)
# ):
#     try:
#         # Get all emails between the current user and specified email address
#         emails = db.query(models.email_communication).filter(
#             ((models.email_communication.sender == email_address) & 
#              (models.email_communication.receiver == EMAIL_CONFIG["username"])) |
#             ((models.email_communication.sender == EMAIL_CONFIG["username"]) & 
#              (models.email_communication.receiver == email_address))
#         ).order_by(
#             models.email_communication.timestamp.desc()
#         ).offset(offset).limit(limit).all()

#         return {
#             "total": len(emails),
#             "emails": emails
#         }

#     except Exception as e:
#         logger.error(f"Error fetching conversation: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

# @router.get("/search")
# async def search_emails(
#     query: str,
#     db: Session = Depends(get_db),
#     current_user: models.User = Depends(get_current_user)
# ):
#     try:
#         # Search in subject and content
#         emails = db.query(models.email_communication).filter(
#             (models.email_communication.subject.ilike(f"%{query}%")) |
#             (models.email_communication.content.ilike(f"%{query}%"))
#         ).order_by(models.email_communication.timestamp.desc()).all()

#         return emails

#     except Exception as e:
#         logger.error(f"Error searching emails: {str(e)}")
#         raise HTTPException(status_code=500, detail=str(e))

@router.get("/emails/{merchant_email}")
async def get_emails(
    merchant_email: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    # Get all emails for current user
    emails = db.query(models.email_communication).filter(
        (
            # (models.email_communication.sender == os.getenv("EMAIL_USERNAME")) & 
            (models.email_communication.receiver == merchant_email)
        ) | 
        (
            # (models.email_communication.receiver == os.getenv("EMAIL_USERNAME")) & 
            (models.email_communication.sender == merchant_email)
         )
    ).order_by(models.email_communication.thread_id).all()

    # Group emails by thread_id
    email_threads = {}
    for email in emails:
        if email.thread_id not in email_threads:
            email_threads[email.thread_id] = []
        email_threads[email.thread_id].append(email)

    # Sort emails within each thread by timestamp
    for thread in email_threads.values():
        thread.sort(key=lambda x: x.timestamp)

    # Get latest timestamp for each thread for sorting
    thread_latest_times = {
        thread_id: max(emails, key=lambda x: x.timestamp).timestamp
        for thread_id, emails in email_threads.items()
    }

    # Sort threads by latest timestamp in descending order
    sorted_threads = sorted(
        email_threads.items(),
        key=lambda x: thread_latest_times[x[0]],
        reverse=True
    )

    return {
        "threads": [
            {
                "thread_id": thread_id,
                "emails": emails
            }
            for thread_id, emails in sorted_threads
        ]
    }

@router.get("/attachment/{filename:path}")
async def get_attachment(
    filename: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get an email attachment by filename.
    The filename should include the relative path (e.g., 'pdfs/20240103_123456_document.pdf')
    """
    try:
        # Construct full file path
        file_path = os.path.join(EMAIL_CONFIG["attachment_path"], filename)
        file_path = Path(file_path)
        
        # Security check: Ensure the file is within the attachments directory
        if not file_path.resolve().is_relative_to(Path(EMAIL_CONFIG["attachment_path"]).resolve()):
            raise HTTPException(
                status_code=403,
                detail="Access to this file is forbidden"
            )

        # Check if file exists
        if not file_path.exists():
            raise HTTPException(
                status_code=404,
                detail="Attachment not found"
            )

        # Determine content type
        content_type, _ = mimetypes.guess_type(str(file_path))
        
        # Return the file
        return FileResponse(
            path=file_path,
            media_type=content_type,
            filename=os.path.basename(filename)  # Original filename for download
        )

    except Exception as e:
        logger.error(f"Error serving attachment: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error retrieving attachment"
        )

@router.get("/email/{email_id}/attachments", include_in_schema=False)
async def get_email_attachments(
    email_id: UUID,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Get all attachments for a specific email
    """
    try:
        # Get email from database
        email = db.query(models.email_communication).filter(
            models.email_communication.id == email_id
        ).first()

        if not email:
            raise HTTPException(
                status_code=404,
                detail="Email not found"
            )

        if not email.attachments:
            return {"attachments": []}

        # Prepare attachment information
        attachment_info = []
        for attachment_path in email.attachments:
            full_path = Path(EMAIL_CONFIG["attachment_path"]) / attachment_path
            if full_path.exists():
                attachment_info.append({
                    "filename": os.path.basename(attachment_path),
                    "path": attachment_path,
                    "size": full_path.stat().st_size,
                    "content_type": mimetypes.guess_type(str(full_path))[0],
                    "download_url": f"/api/v1/email-communication/attachment/{attachment_path}"
                })

        return {"attachments": attachment_info}

    except Exception as e:
        logger.error(f"Error getting email attachments: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error retrieving attachments"
        )

# Helper functions (unchanged)
def decode_email_header(header):
    if not header:
        return ""
    decoded_header, encoding = decode_header(header)[0]
    if isinstance(decoded_header, bytes):
        return decoded_header.decode(encoding if encoding else "utf-8")
    return decoded_header

def extract_email_content(msg):
    if msg.is_multipart():
        content = ""
        for part in msg.walk():
            if part.get_content_type() == "text/plain":
                try:
                    # Try different encodings
                    payload = part.get_payload(decode=True)
                    encodings = ['utf-8', 'latin1', 'iso-8859-1', 'cp1252']
                    
                    for encoding in encodings:
                        try:
                            content += payload.decode(encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                    
                    if not content:  # If no encoding worked
                        content += payload.decode('utf-8', errors='replace')
                        
                except Exception as e:
                    logger.error(f"Error decoding email content: {str(e)}")
                    content += "[Content Decoding Error]"
        return content
    else:
        try:
            payload = msg.get_payload(decode=True)
            encodings = ['utf-8', 'latin1', 'iso-8859-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    return payload.decode(encoding)
                except UnicodeDecodeError:
                    continue
                    
            # If no encoding worked, use replace mode
            return payload.decode('utf-8', errors='replace')
            
        except Exception as e:
            logger.error(f"Error decoding non-multipart email: {str(e)}")
            return "[Content Decoding Error]"

def parse_email_date(date_str):
    try:
        return datetime.fromtimestamp(email.utils.mktime_tz(email.utils.parsedate_tz(date_str)))
    except Exception:
        return datetime.now()

def extract_email_name(text: str) -> str:
    """
    Extract text between angle brackets from a string.
    
    Args:
        text (str): Input string containing text between angle brackets
        
    Returns:
        str: Text between angle brackets, or empty string if no brackets found
    """
    # Find text between angle brackets using regex
    match = re.search(r'<([^>]+)>', text)
    
    # Return matched text or empty string if no match
    return match.group(1) if match else ""

# # Start background task for periodic email fetching



async def periodic_email_fetch(db: Session):
    """Background task to fetch emails every minute"""
    while True:
        try:
            await fetch_and_store_emails(db)
            await asyncio.sleep(60)  # Sleep for 1 minute
        except Exception as e:
            logger.error(f"Error in periodic email fetch: {str(e)}")
            await asyncio.sleep(30)  # On error, retry after 30 seconds

def percentage_name_match(name1: str, name2: str) -> float:
    """
    Calculate similarity between two merchant names using BERT embeddings and cosine similarity.
    Returns percentage match between 0-100.
    """
    try:
        # Load BERT model and tokenizer
        tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
        model = AutoModel.from_pretrained('bert-base-uncased')

        # Preprocess names
        name1 = str(name1).lower().strip() if name1 else ""
        name2 = str(name2).lower().strip() if name2 else ""

        if not name1 or not name2:
            return 0.0

        # Tokenize names
        inputs1 = tokenizer(name1, return_tensors='pt', padding=True, truncation=True)
        inputs2 = tokenizer(name2, return_tensors='pt', padding=True, truncation=True)

        # Get embeddings
        with torch.no_grad():
            outputs1 = model(**inputs1)
            outputs2 = model(**inputs2)

        # Use CLS token embeddings
        embedding1 = outputs1.last_hidden_state[:, 0, :].numpy()
        embedding2 = outputs2.last_hidden_state[:, 0, :].numpy()

        # Calculate cosine similarity
        similarity = cosine_similarity(embedding1, embedding2)[0][0]

        # Convert to percentage (0-100)
        percentage = float(similarity * 100)
        # print(f"[DEBUG] Name match percentage: {percentage}")
        return percentage

    except Exception as e:
        logger.error(f"Error in name matching: {str(e)}")
        return 0.0
async def fetch_and_store_emails(db: Session):
    """Fetch new emails and store them in the database"""
    # print("[DEBUG] Starting fetch_and_store_emails function")
    mail = email_service.create_imap_connection()
    # print("[DEBUG] Created IMAP connection")
    mail.select("inbox")
    # print("[DEBUG] Selected inbox")
    
    try:
        # print("[DEBUG] Searching for emails")
        _, messages = mail.search(None, "ALL")
        email_ids = messages[0].split()
        # print(f"[DEBUG] Found {len(email_ids)} total emails")
        
        # Get only the last 5 email IDs
        latest_email_ids = email_ids[-5:] if len(email_ids) > 5 else email_ids
        # print(f"[DEBUG] Processing {len(latest_email_ids)} latest emails")
        
        # Create PDF storage directory if it doesn't exist
        pdf_storage_path = os.path.join(EMAIL_CONFIG["attachment_path"], "pdfs")
        Path(pdf_storage_path).mkdir(parents=True, exist_ok=True)
        # print(f"[DEBUG] Created PDF storage path: {pdf_storage_path}")
        
        for email_id in latest_email_ids:
            try:
                # print(f"[DEBUG] Processing email ID: {email_id}")
                _, msg_data = mail.fetch(email_id, "(RFC822)")
                email_body = msg_data[0][1]
                email_message = email.message_from_bytes(email_body)
                # print(f"[DEBUG] Fetched email with subject: {email_message['Subject']}")
                
                # Check if email already exists in database
                if db.query(models.email_communication).filter(models.email_communication.message_id == email_message["Message-ID"]).first():
                    # print("[DEBUG] Email already exists in database, skipping")
                    continue
                    
                # Extract attachments and save them
                attachments = []
                # print("[DEBUG] Processing attachments")
                if email_message.is_multipart():
                    for part in email_message.walk():
                        if part.get_content_maintype() == 'multipart':
                            continue
                        if part.get('Content-Disposition') is None:
                            continue
                            
                        filename = part.get_filename()
                        if filename:
                            # print(f"[DEBUG] Processing attachment: {filename}")
                            # Decode filename if needed
                            if isinstance(filename, bytes):
                                filename = filename.decode('utf-8', errors='replace')
                                # print(f"[DEBUG] Decoded filename: {filename}")
                            
                            # Check if it's a PDF
                            if filename.lower().endswith('.pdf'):
                                # print("[DEBUG] Processing PDF attachment")
                                # Create a unique filename to avoid conflicts
                                unique_filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{filename}"
                                filepath = os.path.join(pdf_storage_path, unique_filename)
                                # print(f"[DEBUG] Saving PDF to: {filepath}")
                                
                                # Save the PDF file
                                with open(filepath, 'wb') as f:
                                    pdf_content = part.get_payload(decode=True)
                                    f.write(pdf_content)
                                # print("[DEBUG] PDF saved successfully")
                                
                                # Store relative path
                                relative_path = os.path.join("pdfs", unique_filename)
                                attachments.append(relative_path)
                                # print(f"[DEBUG] Added PDF path to attachments: {relative_path}")
                            else:
                                # print("[DEBUG] Processing non-PDF attachment")
                                # Handle non-PDF attachments
                                filepath = os.path.join(EMAIL_CONFIG["attachment_path"], filename)
                                with open(filepath, 'wb') as f:
                                    f.write(part.get_payload(decode=True))
                                attachments.append(filepath)
                                # print(f"[DEBUG] Added non-PDF path to attachments: {filepath}")

                # print("[DEBUG] Creating email record")
                # Store email in database
                thread_id = email_message.get("References", str(uuid4()))
                if isinstance(thread_id, str) and thread_id.strip() == "":
                    thread_id = str(uuid4())
                # print(f"[DEBUG] Using thread ID: {thread_id}")
                    
                # Create new email object with all fields
                
                # print("[DEBUG] Created email object")

                # Store attachments in documents_from_email table
                saved_attachments_ids = []
                for attachment_path in attachments:
                    file_path = Path(EMAIL_CONFIG["attachment_path"]) / attachment_path
                    if file_path.exists():
                        content_type, _ = mimetypes.guess_type(str(file_path))
                        doc = models.documents_from_email(
                            document_id=uuid4(),
                            email_id=new_email.id,
                            filename=os.path.basename(attachment_path),
                            path=attachment_path,
                            size=file_path.stat().st_size,
                            content_type=content_type,
                            download_url=f"/api/v1/email-communication/attachment/{attachment_path}",
                            created_at=datetime.now()
                        )
                        saved_attachments_ids.append(doc.document_id)
                        db.add(doc)
                        # print(f"[DEBUG] Added document record for {attachment_path}")
                
                new_email = models.email_communication(
                    id=uuid4(),
                    message_id=email_message["Message-ID"], 
                    thread_id=thread_id,
                    sender=extract_email_name(email_message["From"]),
                    receiver=email_message["To"],
                    subject=decode_email_header(email_message["Subject"]),
                    content=extract_email_content(email_message),
                    timestamp=parse_email_date(email_message["Date"]),
                    attachments=saved_attachments_ids if saved_attachments_ids else None, 
                    created_at=datetime.now()
                )

                # print("[DEBUG] Starting Groq AI analysis")
                # Analyze email with Groq AI
                client = Groq(api_key=os.environ.get("GROQ_API_KEY"))
                prompt = f"""Please analyze this email and extract complaint information in the following JSON format:
                {{
                    "merchant_id": "string or null",
                    "merchant_name": "string or null", 
                    "transaction_id": "string or null",
                    "complained_by": "customer or LEA or null",
                    "complaint_description": "string(this is the complaint description of 30-40 words or less) or null"
                }}
                Email content: {new_email.content}"""

                try:
                    # print("[DEBUG] Sending request to Groq API ")
                    chat_completion = client.chat.completions.create(
                        messages=[
                            {
                                "role": "system",
                                "content": "You are a helpful assistant that extracts complaint information from emails and returns it in JSON format only, without any additional text or formatting."
                            },
                            {
                                "role": "user", 
                                "content": prompt
                            }
                        ],
                        model="llama-3.3-70b-versatile",
                        temperature=0.1  # Lower temperature for more consistent JSON output
                    )
                    # print("[DEBUG] Received response from Groq API")

                    # Extract just the JSON part from the response
                    response_text = chat_completion.choices[0].message.content.strip()
                    # Remove any markdown formatting or additional text
                    if "```json" in response_text:
                        response_text = response_text.split("```json")[1].split("```")[0].strip()
                    elif "```" in response_text:
                        response_text = response_text.split("```")[1].strip()
                    
                    # Parse and validate JSON response
                    # print("[DEBUG] Parsing AI response")
                    complaint_info = json.loads(response_text)
                    # print(f"[DEBUG] Parsed complaint info: {complaint_info}")

                    # Validate that at least one identifier field exists and has a non-null value
                    identifier_fields = ["merchant_id", "merchant_name", "transaction_id"]
                    if not any(complaint_info.get(field) for field in identifier_fields):
                        raise ValueError("At least one of merchant_id, merchant_name, or transaction_id must have a value")

                    if any(complaint_info.values()):

                        # print("[DEBUG] Found valid complaint information")
                        # Search for merchant in database
                        merchant_query = db.query(models.Merchant)
                        merchant_id = None
                        transaction_query = None

                        # print("[DEBUG] Searching for merchant by name similarity")
                        # Try to find merchant by name similarity
                        
                        # print("[DEBUG] Searching for merchant by ID/name/transaction")
                        #  Try to find merchant by ID or transaction
                        if complaint_info["merchant_id"]:
                            try:
                                merchant_query = merchant_query.filter(models.Merchant.id == UUID(complaint_info["merchant_id"]))
                                merchant = merchant_query.first()
                                if merchant:
                                    merchant_id = merchant.id
                                    # print(f"[DEBUG] Found merchant by ID: {merchant_id}")
                            except ValueError as e:
                                logger.warning(f"Invalid merchant_id format: {complaint_info['merchant_id']}")
                                
                        if complaint_info["transaction_id"]:
                            try:
                                # Try to convert to UUID if it's in UUID format
                                if len(complaint_info["transaction_id"]) == 36:  # UUID length
                                    transaction_query = db.query(models.transactions).filter(
                                        models.transactions.id == UUID(complaint_info["transaction_id"])
                                    ).first()
                            except ValueError as e:
                                logger.warning(f"Invalid transaction_id format: {complaint_info['transaction_id']}")

                        # Initialize variables to track highest match
                        highest_match = 0
                        best_matching_merchant = None
                        
                        # Check all merchants for best match
                        for merchant in merchant_query:
                            # Get match percentages for both names
                            trade_name_match = percentage_name_match(merchant.trade_name, complaint_info["merchant_name"])
                            legal_name_match = percentage_name_match(merchant.legal_name, complaint_info["merchant_name"])
                            
                            # Get the higher of the two percentages
                            current_match = max(trade_name_match, legal_name_match)
                            
                            # Update if this is the highest match so far
                            if current_match > highest_match:
                                highest_match = current_match
                                best_matching_merchant = merchant
                        
                        # If highest match is above 90%, use this merchant
                        if highest_match > 90 and best_matching_merchant:
                            merchant_id = best_matching_merchant.id
                            # print(f"[DEBUG] Found merchant by name similarity: {merchant_id}")
                            # print(f"[DEBUG] Trade name: {best_matching_merchant.trade_name} ({trade_name_match}%)")
                            # print(f"[DEBUG] Legal name: {best_matching_merchant.legal_name} ({legal_name_match}%)")
                            # print(f"[DEBUG] Match percentage: {highest_match}%")

                        # Create investigation case
                        investigation_id = str(uuid4())
                        # print(f"[DEBUG] Creating investigation with ID: {investigation_id}")
                        
                        if merchant_id:
                            # print(f"[DEBUG] Merchant ID: {merchant_id}")
                            # print("[DEBUG] Creating case with merchant info")
                            merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
                            
                            # Get next case number
                            latest_case = db.query(models.investigations).order_by(
                                models.investigations.case_number.desc()
                            ).first()
                            next_case_number = str(int(latest_case.case_number) + 1) if latest_case and latest_case.case_number else "1"
                            # print(f"[DEBUG] Using case number: {next_case_number}")

                            new_case = models.investigations(
                                investigation_id=investigation_id,
                                case_number=next_case_number,
                                title="Email Complaint",
                                description=complaint_info["complaint_description"],
                                status="OPEN", 
                                priority="Medium", 
                                assignee_Name="Unassigned",
                                assignee_Email="",
                                merchant_id=merchant_id,
                                merchant_name=merchant.trade_name,
                                last_updated=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                sla_deadline=(datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S"),
                                initiating_email_id=new_email.id,
                                created_by=complaint_info["complained_by"]
                            )
                        else:
                            # print("[DEBUG] Creating case  without merchant info")
                            new_case = models.investigations(
                                investigation_id=investigation_id,
                                case_number="",
                                title="Email Complaint",
                                description=complaint_info["complaint_description"],
                                status="OPEN",
                                priority="Medium",
                                assignee_Name="Unassigned", 
                                assignee_Email="",
                                merchant_id=None,
                                merchant_name="",
                                last_updated=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                sla_deadline=(datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S"),
                                initiating_email_id=new_email.id,
                                created_by=complaint_info["complained_by"]
                            )
                        # print(new_case)
                        db.add(new_case)
                        db.flush()
                        # print("[DEBUG] Added case to database")

                        # print("[DEBUG] Creating case event")
                        # Create case event
                        case_event = models.caseEvents(
                            case_event_id=str(uuid4()),
                            investigation_id=investigation_id,
                            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            type=complaint_info["complained_by"],
                            description=f"Complaint email received from {new_email.sender}",
                            user=new_email.sender,
                            content=complaint_info["complaint_description"]
                        )

                        db.add(case_event)
                        db.commit()
                        # print("[DEBUG] Added case event and committed transaction")

                except (json.JSONDecodeError, ValueError, AttributeError) as e:
                    pass
                    # print(f"[DEBUG] Error parsing AI response: {str(e)}")
                    # logger.error(f"Error parsing AI response: {str(e)}")
                    # logger.error(f"Raw AI response: {chat_completion.choices[0].message.content if chat_completion and chat_completion.choices else 'No response'}")

                # print("[DEBUG] Storing email in database")
                # Store email in database
                db.add(new_email)
                db.commit()
                # print("[DEBUG] Email stored successfully")
                
            except Exception as e:
                # print(f"[DEBUG] Error processing email {email_id}: {str(e)}")
                # logger.error(f"Error processing email {email_id}: {str(e)}")
                continue
        
        # print("[DEBUG] Successfully processed all emails")
        # logger.info("Successfully stored latest emails with attachments")
        
    except Exception as e:
        # print(f"[DEBUG] Error in main try block: {str(e)}")
        db.rollback()
        # logger.error(f"Error fetching and storing emails: {str(e)}")
    finally:
        # print("[DEBUG] Logging out of email")
        mail.logout()

class RAGQueryRequest(BaseModel):
    email: str
    prompt: str
    thread_id: Optional[str] = None

class PromptSuggestionRequest(BaseModel):
    email: str
    thread_id: Optional[str] = None

def get_documents_by_email(db: Session, email: str, current_user_email: str) -> List[str]:
    # Get all emails related to this email address
    # Query the database
    # logger.info(f"Getting documents for email: {email}")
    emails = db.query(models.email_communication).filter(
        or_(
            and_(
                models.email_communication.sender == email,
                # models.email_communication.receiver == current_user.email
            ),
            and_(
                # models.email_communication.sender == current_user.email,
                models.email_communication.receiver == email
            )
        )
    ).order_by(models.email_communication.timestamp.desc()).all()
    
    document_paths = []
    for email_obj in emails:
        if email_obj.attachments:
            document_paths.extend(email_obj.attachments)
    
    if not document_paths:
        # logger.warning(f"No documents found for email: {email}")
        pass
        
    return document_paths

def output_file_exists(file_path: str) -> bool:
    """Check if a file exists at the given path"""
    return os.path.exists(file_path)

def process_documents_for_rag(document_paths: List[str]) -> List[str]:
    """Process documents and return text chunks"""
    text_documents = []
    base_path, ocr_path = setup_ocr_directories()
    
    for doc_path in document_paths:
        # Check cache first
        cached_content = document_cache.get(doc_path)
        if cached_content:
            text_documents.extend(cached_content)
            continue
            
        # Process document if not in cache
        full_path = base_path / doc_path
        
        if not full_path.exists():
            # logger.error(f"Document not found: {full_path}")
            continue
            
        try:
            processed_content = []
            if doc_path.lower().endswith('.pdf'):
                # Try direct PDF extraction first
                try:
                    loader = PyMuPDFLoader(str(full_path))
                    docs = loader.load()
                    if any(doc.page_content.strip() for doc in docs):
                        processed_content.extend(docs)
                except Exception as e:
                    # logger.info(f"Direct PDF extraction failed, attempting OCR: {str(e)}")
                    pass
                    
                    # Fallback to OCR if needed
                    ocr_filename = f"{Path(doc_path).stem}_text.pdf"
                    ocr_output = ocr_path / ocr_filename
                    
                    if not ocr_output.exists():
                        try:
                            if process_pdf(str(full_path), str(ocr_output)):
                                full_path = ocr_output
                            else:
                                # logger.error(f"OCR processing failed for: {doc_path}")
                                pass
                        except RuntimeError as e:
                            # logger.error(f"OCR error: {str(e)}")
                            continue
                    else:
                        full_path = ocr_output
                    
                    loader = PyMuPDFLoader(str(full_path))
                    docs = loader.load()
                    processed_content.extend(docs)
                    
            elif doc_path.lower().endswith('.txt'):
                loader = TextLoader(str(full_path), encoding='UTF-8')
                docs = loader.load()
                processed_content.extend(docs)
                
            if processed_content:
                document_cache.set(doc_path, processed_content)
                text_documents.extend(processed_content)
                
        except Exception as e:
            # logger.error(f"Error processing document {doc_path}: {str(e)}")
            pass

    if not text_documents:
        raise HTTPException(
            status_code=500,
            detail="No documents could be processed successfully"
        )

    # Split documents
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=100)
    doc_chunks = text_splitter.split_documents(text_documents)
    return [doc.page_content for doc in doc_chunks]

@router.post("/rag-query")
async def query_email_documents(
    request: RAGQueryRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Get email context first
        email_context = ""
        if request.thread_id:
            # Get emails from the same thread
            thread_emails = db.query(models.email_communication).filter(
                models.email_communication.thread_id == request.thread_id
            ).order_by(models.email_communication.timestamp.desc()).all()
            
            if thread_emails:
                email_context = "\n\n".join([
                    f"From: {email.sender}\n"
                    f"To: {email.receiver}\n"
                    f"Subject: {email.subject}\n"
                    f"Date: {email.timestamp}\n"
                    f"Content:\n{email.content}\n"
                    f"---"
                    for email in thread_emails
                ])

        # Get and process documents - Pass current_user.email instead of current_user
        document_paths = get_documents_by_email(db, request.email, current_user.email)
        if not document_paths:
            raise HTTPException(
                status_code=404,
                detail="No documents found for this email"
            )
        
        try:
            docs = process_documents_for_rag(document_paths)
            if not docs:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to process documents"
                )
        except Exception as e:
            # logger.error(f"Error processing documents: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error processing documents: {str(e)}"
            )
        
        # Initialize RAG service
        rag_service = RAGService()
        rag_service.add_documents(docs)
        
        # Search for relevant documents
        search_results = rag_service.search(request.prompt, top_k=5)
        
        # Format results for LLM with email context
        formatted_results = ""
        if email_context:
            formatted_results += f"Email Thread Context:\n{email_context}\n\nRelevant Documents:\n"
        
        for result in search_results:
            formatted_results += f"- {result['text']} (Score: {result['score']:.3f})\n"
            
        # Enhance prompt with context awareness
        enhanced_prompt = f"""Given this email thread and document context:

{formatted_results}

Please answer the following question while considering both the email conversation context and the relevant documents:
{request.prompt}

Provide a response that:
1. Addresses the specific question
2. References relevant parts of the email thread if applicable
3. Incorporates insights from the documents
4. Maintains consistency with the overall conversation context"""

        # Get LLM response with enhanced prompt
        answer = get_llm_answer(enhanced_prompt, formatted_results)
        
        return {
            "answer": answer,
            "has_email_context": bool(email_context),
            "document_count": len(docs)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        # logger.error(f"Error in RAG query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/suggest-prompts") 
async def suggest_prompts(
    request: PromptSuggestionRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Get email thread context if thread_id is provided
        email_context = ""
        thread_emails = []
        if request.thread_id:
            thread_emails = db.query(models.email_communication).filter(
                models.email_communication.thread_id == request.thread_id
            ).order_by(models.email_communication.timestamp.desc()).all()
            
            if thread_emails:
                email_context = "\n\n".join([
                    f"From: {email.sender}\n"
                    f"To: {email.receiver}\n"
                    f"Subject: {email.subject}\n"
                    f"Date: {email.timestamp}\n"
                    f"Content:\n{email.content}\n"
                    f"---"
                    for email in thread_emails
                ])

        # Get document paths - but don't raise exception if none found
        document_paths = get_documents_by_email(db, request.email, current_user.email)
        docs = []
        doc_summary = ""
        
        if document_paths:
            try:
                docs = process_documents_for_rag(document_paths)
                doc_summary = "\n".join(docs[:3])
            except Exception as e:
                # logger.warning(f"Error processing documents: {str(e)}")
                pass
                # Continue without document context
        
        # Use same LLM config as before
        api_key = os.getenv("GROQ_API_KEY")
        if not api_key:
            raise ValueError("GROQ_API_KEY not found in environment variables")
            
        client = Groq(api_key=api_key)
        
        # Modified prompt that works with or without documents
        context_prompt = f"""Based on the following information:

Email Thread Context:
{email_context if email_context else "No email thread context available"}

{"Document Excerpts:" + doc_summary if doc_summary else "No documents available for analysis"}

Generate 8 different investigation prompts that would be relevant for analyzing these materials.
Focus primarily on the email communications{" and documents" if doc_summary else ""}.
Each prompt should be 3-4 words only and focus on:
- Communication patterns
- Risk indicators
- Compliance issues
- Financial irregularities
{"- Document discrepancies" if doc_summary else "- Email content analysis"}
- Merchant behaviors
- Transaction anomalies
- Potential fraud indicators
- Suspicious activities

Return only the prompts, one per line, without any additional text or numbering."""
        
        chat_completion = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert fraud investigator who specializes in generating focused investigation prompts based on email communications and related documents."
                },
                {
                    "role": "user",
                    "content": context_prompt
                }
            ],
            model="llama-3.3-70b-versatile",
            temperature=0.7,
            max_tokens=1024,
            top_p=1,
            stream=False
        )
        
        suggested_prompts = chat_completion.choices[0].message.content.strip().split("\n")
        
        return {
            "prompts": suggested_prompts,
            "has_email_context": bool(email_context),
            "email_count": len(thread_emails) if request.thread_id else 0,
            "document_count": len(docs),
            "has_documents": bool(doc_summary)
        }
        
    except Exception as e:
        # logger.error(f"Error generating prompt suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

class EmailSummaryResponse(BaseModel):
    email_summary: str
    document_insights: str
    key_topics: List[str]
    communication_pattern: str
    total_emails: int
    total_documents: int
    last_communication: Optional[datetime]


@router.get("/email-summary/{email}")
async def get_email_summary(
    email: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Add logging for debugging
        # logger.info(f"Fetching email summary for: {email}")
        # logger.info(f"Current user email: {current_user.email}")
        
        # Query the database
        emails = db.query(models.email_communication).filter(
            or_(
                and_(
                    models.email_communication.sender == email,
                    # models.email_communication.receiver == current_user.email
                ),
                and_(
                    # models.email_communication.sender == current_user.email,
                    models.email_communication.receiver == email
                )
            )
        ).order_by(models.email_communication.timestamp.desc()).all()
        
        # logger.info(f"Found {len(emails)} emails for {email}")

        # Handle no emails case gracefully
        if not emails:
            return {
                "email_summary": "No email communications found.",
                "document_insights": "No documents available.",
                "key_topics": [],
                "communication_pattern": "No communication history.",
                "total_emails": 0,
                "total_documents": 0,
                "last_communication": None
            }

        # Process emails if found
        email_texts = [f"From: {e.sender}\nTo: {e.receiver}\nSubject: {e.subject}\nContent: {e.content}" 
                      for e in emails]
        
        # Create summary prompt
        # Note: Limit to last 5 emails for context
        summary_prompt = f"""Analyze these email communications and provide insights:

Email History:
{'\n---\n'.join(email_texts[:5])}

Please provide:
1. Overall communication summary
2. Key topics discussed
3. Communication patterns
4. Important action items or follow-ups
"""

        # Get insights from Groq
        client = Groq(api_key=os.getenv("GROQ_API_KEY"))
        completion = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "You are an email analysis expert focusing on communication patterns and insights."
                },
                {
                    "role": "user",
                    "content": summary_prompt
                }
            ],
            model="llama-3.3-70b-versatile",
            temperature=0.5,
            max_tokens=1500
        )

        # Extract document information
        total_documents = sum(1 for e in emails if e.attachments)
        
        return {
            "email_summary": completion.choices[0].message.content,
            "document_insights": f"Total {total_documents} documents exchanged",
            "key_topics": extract_key_topics(emails),
            "communication_pattern": analyze_communication_pattern(emails),
            "total_emails": len(emails),
            "total_documents": total_documents,
            "last_communication": emails[0].timestamp if emails else None
        }

    except Exception as e:
        # logger.error(f"Error in email summary: {str(e)}", exc_info=True)
        if isinstance(e, HTTPException):
            raise
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

def extract_communication_pattern(emails):
    """Helper function to analyze communication patterns"""
    if not emails:
        return "No communications found"
        
    # Calculate average response time
    response_times = []
    for i in range(1, len(emails)):
        time_diff = emails[i-1].timestamp - emails[i].timestamp
        response_times.append(time_diff.total_seconds() / 3600)  # Convert to hours
    
    avg_response_time = sum(response_times) / len(response_times) if response_times else 0
    
    # Determine frequency pattern
    total_days = (emails[0].timestamp - emails[-1].timestamp).days
    frequency = len(emails) / total_days if total_days > 0 else len(emails)
    
    return f"Average response time: {avg_response_time:.1f} hours, Frequency: {frequency:.1f} emails per day"

class RAGService:
    def __init__(self):
        self.model = SentenceTransformer('all-mpnet-base-v2')
        self.device = 'cuda:0' if torch.cuda.is_available() else 'cpu'
        self.model = self.model.to(self.device)
        self.document_embeddings = []
        self.documents = []

    def add_documents(self, docs):
        """Add documents and their embeddings to the service"""
        if not docs:
            # logger.warning("No documents provided to add_documents")
            return
            
        embeddings = self.model.encode(docs, convert_to_tensor=True)
        self.document_embeddings.extend(embeddings.cpu().numpy())
        self.documents.extend(docs)
        
    def search(self, query, top_k=5):
        """Search for most similar documents"""
        if not self.documents or not self.document_embeddings:
            # logger.warning("No documents available for search")
            return []
            
        # Encode query and convert to numpy for compatibility
        query_embedding = self.model.encode([query], convert_to_tensor=True)[0]
        query_embedding = query_embedding.cpu().numpy()
        
        # Calculate cosine similarities
        similarities = cosine_similarity(
            query_embedding.reshape(1, -1),
            self.document_embeddings
        )[0]
        
        # Get top k results
        top_indices = similarities.argsort()[-top_k:][::-1]
        
        # Format results
        results = []
        for idx in top_indices:
            results.append({
                'text': self.documents[idx],
                'score': float(similarities[idx])
            })
            
        return results

# @router.on_event("startup")
# async def start_periodic_tasks():
#     asyncio.create_task(periodic_email_fetch(next(get_db())))

def get_llm_answer(prompt: str, context: str) -> str:
    """Get answer from LLM using Groq"""
    client = Groq(api_key=os.getenv("GROQ_API_KEY"))
    
    full_prompt = f"""Based on this context:
    {context}
    
    Answer this question: {prompt}"""
    
    completion = client.chat.completions.create(
        messages=[{"role": "user", "content": full_prompt}],
        model="llama-3.3-70b-versatile",
        temperature=0.7
    )
    
    return completion.choices[0].message.content

def setup_ocr_directories():
    """Create required directories for OCR processing"""
    base_path = Path(EMAIL_CONFIG["attachment_path"])
    ocr_path = base_path / "ocr"
    
    # Create directories if they don't exist
    for path in [base_path, ocr_path]:
        path.mkdir(parents=True, exist_ok=True)
    
    return base_path, ocr_path

@router.get("/email-documents/{email_id}")
async def get_email_documents(
    email_id: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Query documents associated with the email
        documents = db.query(models.documents_from_email).filter(
            models.documents_from_email.email_id == email_id
        ).all()
        
        if not documents:
            raise HTTPException(
                status_code=404,
                detail="No documents found for this email"
            )
            
        return {
            "documents": [
                {
                    "document_id": str(doc.document_id),
                    "filename": doc.filename,
                    "content_type": doc.content_type,
                    "size": doc.size,
                    "download_url": doc.download_url,
                    "created_at": doc.created_at
                } for doc in documents
            ]
        }
        
    except Exception as e:
        # logger.error(f"Error fetching email documents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/email-attachments/{merchant_email}")
async def get_email_attachments(
    merchant_email: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    #find all the email with either receiver or sender as the email

    emails = db.query(models.email_communication).filter(
        (models.email_communication.receiver == merchant_email) | (models.email_communication.sender == merchant_email)
    ).all()

    #get all the documents from the emails
    documents = db.query(models.documents_from_email).filter(
        models.documents_from_email.email_id.in_(email.id for email in emails)
    ).all()

    return {"documents": documents}



@lru_cache(maxsize=1)
def get_sentence_transformer():
    return SentenceTransformer('all-mpnet-base-v2')




@lru_cache(maxsize=1)
def get_reranker():
    return CrossEncoderRerankFunction(
        model_name="cross-encoder/ms-marco-MiniLM-L-6-v2",
        device="cuda:0",
        batch_size=32
    )

class DocumentCache:
    def __init__(self):
        self.cache = {}
        self.max_size = 1000  # Adjust based on your memory constraints
        
    def get(self, doc_path):
        return self.cache.get(doc_path)
        
    def set(self, doc_path, content):
        if len(self.cache) >= self.max_size:
            # Remove oldest item
            self.cache.pop(next(iter(self.cache)))
        self.cache[doc_path] = content


document_cache = DocumentCache()

class DocumentRequest(BaseModel):
    document_id: UUID

class DocumentPromptRequest(BaseModel):
    document_id: UUID
    prompt: str

@router.post("/document-summary")
async def get_document_summary(
    request: DocumentRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Get document
        doc = db.query(models.documents_from_email).filter(
            models.documents_from_email.document_id == request.document_id
        ).first()
        
        if not doc:
            raise HTTPException(
                status_code=404,
                detail="Document not found"
            )
            
        try:
            document_texts = process_documents_for_rag([doc.filename])
        except Exception as e:
            # logger.error(f"Error processing document {request.document_id}: {str(e)}")
            pass
            raise HTTPException(
                status_code=500,
                detail="Error processing document"
            )

        # Create summary prompt
        summary_prompt = f"""Analyze this document and provide a comprehensive summary:

Document Content:
{' '.join(document_texts[:3])}

Provide a structured analysis covering:
1. Document Overview
   - Type and purpose of document
   - Key dates and deadlines
   - Parties involved

2. Key Information
   - Critical data points
   - Financial information
   - Legal requirements

3. Risk Analysis
   - Compliance concerns
   - Missing information
   - Unusual patterns

4. Required Actions
   - Immediate steps needed
   - Follow-up requirements
   - Verification needs

Be specific and highlight any concerning elements."""

        # Get summary from Groq
        client = Groq(api_key=os.getenv("GROQ_API_KEY"))
        completion = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "You are a document analysis expert focusing on risk assessment and compliance verification."
                },
                {
                    "role": "user",
                    "content": summary_prompt
                }
            ],
            model="llama-3.3-70b-versatile",
            temperature=0.5,
            max_tokens=1500
        )

        return {
            "summary": completion.choices[0].message.content
        }

    except Exception as e:
        # logger.error(f"Error generating document summary: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/document-query")
async def query_documents(
    request: DocumentPromptRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Get document
        doc = db.query(models.documents_from_email).filter(
            models.documents_from_email.document_id == request.document_id
        ).first()
        
        if not doc:
            raise HTTPException(
                status_code=404,
                detail="Document not found"
            )
            
        try:
            document_texts = process_documents_for_rag([doc.filename])
        except Exception as e:
            # logger.error(f"Error processing document {request.document_id}: {str(e)}")
            pass
            raise HTTPException(
                status_code=500,
                detail="Error processing document"
            )

        # Initialize RAG service
        rag_service = RAGService()
        rag_service.add_documents(document_texts)
        
        # Search for relevant sections
        search_results = rag_service.search(request.prompt, top_k=5)
        
        # Format results for LLM
        formatted_results = "Relevant Document Sections:\n"
        for result in search_results:
            formatted_results += f"- {result['text']} (Score: {result['score']:.3f})\n"
            
        # Enhance prompt with context awareness
        enhanced_prompt = f"""Given these document excerpts:

{formatted_results}

Please answer the following question while considering the relevant sections:
{request.prompt}

Provide a response that:
1. Addresses the specific question
2. References relevant parts of the document
3. Maintains factual accuracy
4. Highlights any important context"""

        # Get LLM response with enhanced prompt
        answer = get_llm_answer(enhanced_prompt, formatted_results)
        
        return {
            "answer": answer
        }

    except Exception as e:
        # logger.error(f"Error querying document: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/suggest-document-prompts")
async def suggest_document_prompts(
    request: DocumentRequest,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Get document
        doc = db.query(models.documents_from_email).filter(
            models.documents_from_email.document_id == request.document_id
        ).first()
        
        if not doc:
            raise HTTPException(
                status_code=404,
                detail="Document not found"
            )
            
        try:
            document_texts = process_documents_for_rag([doc.filename])
        except Exception as e:
            # logger.error(f"Error processing document {request.document_id}: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Error processing document"
            )

        # Create prompt for suggestion generation
        suggestion_prompt = f"""Based on this document content:

{' '.join(document_texts[:3])}

Generate 4 focused investigation prompts that would be relevant for analyzing this material.
Each prompt should be 3-4 words only and focus on:
- Document content analysis
- Risk identification
- Compliance verification
- Pattern detection

Return only the prompts, one per line, without any additional text or numbering."""

        # Get suggestions from Groq
        client = Groq(api_key=os.getenv("GROQ_API_KEY"))
        completion = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert document analyst who specializes in generating focused investigation prompts."
                },
                {
                    "role": "user",
                    "content": suggestion_prompt
                }
            ],
            model="llama-3.3-70b-versatile",
            temperature=0.7,
            max_tokens=1024
        )
        
        suggested_prompts = completion.choices[0].message.content.strip().split("\n")
        
        return {
            "prompts": suggested_prompts[:4]  # Ensure we only return 4 prompts
        }

    except Exception as e:
        # logger.error(f"Error generating prompt suggestions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def extract_key_topics(emails):
    """Extract key topics from email subjects and content"""
    topics = []
    for email in emails:
        # Add basic topic extraction logic
        topics.extend(email.subject.split())
    return list(set(topics))[:5]  # Return top 5 unique topics

def analyze_communication_pattern(emails):
    """Analyze communication patterns from email history"""
    if not emails:
        return "No communication history"
    
    # Calculate basic metrics
    total_emails = len(emails)
    date_range = (emails[0].timestamp - emails[-1].timestamp).days
    
    if date_range == 0:
        return "All communications occurred today"
    
    frequency = total_emails / date_range if date_range > 0 else total_emails
    return f"Average {frequency:.1f} emails per day over {date_range} days"

@router.get("/email-stats")
async def get_email_stats(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user),
    skip: int = 0,
    limit: int = 100
):
    try:
        # Get total counts
        total_emails = db.query(models.email_communication).count()
        total_attachments = db.query(models.documents_from_email).count()
        
        # Get latest email
        latest_email = db.query(models.email_communication)\
            .order_by(models.email_communication.timestamp.desc())\
            .first()
            
        # Get storage usage
        attachment_sizes = db.query(func.sum(models.documents_from_email.size))\
            .scalar() or 0
            
        # Get all emails with pagination
        emails = db.query(models.email_communication)\
            .order_by(models.email_communication.timestamp.desc())\
            .offset(skip)\
            .limit(limit)\
            .all()
            
        # Format email list with relevant information
        email_list = []
        for email in emails:
            # Get attachments for this email
            attachments = db.query(models.documents_from_email)\
                .filter(models.documents_from_email.email_id == email.id)\
                .all()
                
            email_list.append({
                "id": str(email.id),
                "message_id": email.message_id,
                "thread_id": str(email.thread_id),
                "sender": email.sender,
                "receiver": email.receiver,
                "subject": email.subject,
                "timestamp": email.timestamp,
                "has_attachments": bool(attachments),
                "attachment_count": len(attachments),
                "attachments": [
                    {
                        "filename": att.filename,
                        "size": att.size,
                        "content_type": att.content_type,
                        "download_url": att.download_url
                    } for att in attachments
                ] if attachments else []
            })
            
        return {
            "total_emails": total_emails,
            "total_attachments": total_attachments,
            "latest_email_time": latest_email.timestamp if latest_email else None,
            "storage_usage_bytes": attachment_sizes,
            "last_fetch_time": datetime.now(),
            "emails": email_list,
            "pagination": {
                "skip": skip,
                "limit": limit,
                "total": total_emails
            }
        }
    except Exception as e:
        # logger.error(f"Error getting email stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))