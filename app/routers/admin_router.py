from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.database import get_db
from init_db import init_database
from add_admin_user import add_admin_user
from add_llm_red_flag import add_llm_red_flags
# from add_rules import add_rules
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/reset-database")
async def reset_database(
    db: Session = Depends(get_db)
):
    try:
        init_database(reset_db=True)
        return {"message": "Database reset successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/add-admin")
async def add_admin_endpoint():
    try:
        add_admin_user()
        return {"message": "Admin user added successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/add-llm-flags")
async def add_llm_flags_endpoint():
    try:
        add_llm_red_flags()
        return {"message": "LLM red flags added successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# @router.post("/add-rules")
# async def add_rules_endpoint():
#     try:
#         add_rules()
#         return {"message": "Rules added successfully"}
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

@router.post("/initialize-all")
async def initialize_all():
    """Initialize everything in the correct order, matching start_pankaj.sh"""
    try:
        init_database(reset_db=True)
        add_admin_user()
        add_llm_red_flags()
        # add_rules()
        return {"message": "Database initialized successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 