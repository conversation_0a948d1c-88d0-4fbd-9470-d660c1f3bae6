from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from ..database import get_db
from ..models.models import customer_red_flags, rules_store, customer
from pydantic import BaseModel
import uuid
import logging
import pandas as pd
from sqlalchemy import select, join

logger = logging.getLogger(__name__)
router = APIRouter()

class CustomerRedFlagCreate(BaseModel):
    customer_id: uuid.UUID
    rule_code: str
    description: Optional[str] = None
    severity: Optional[str] = None
    metric_values: Optional[dict] = None
    metric_data_timestamp: Optional[datetime] = None
    notes: Optional[str] = None
    category: Optional[str] = None

class CustomerRedFlagResponse(CustomerRedFlagCreate):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    rule_name: Optional[str] = None
    rule_description: Optional[str] = None
    rule_type: Optional[str] = None
    rule_severity: Optional[str] = None
    rule_fraud_type: Optional[str] = None
    rule: Optional[dict] = None

    class Config:
        from_attributes = True

def validate_rule_code(db: Session, rule_code: str) -> bool:
    rule = db.query(rules_store).filter(rules_store.code == rule_code).first()
    if not rule:
        logger.warning(f"Rule code {rule_code} not found in rules_store")
    return rule is not None

def create_red_flag(db: Session, red_flag_data: CustomerRedFlagCreate) -> customer_red_flags:
    validate_rule_code(db, red_flag_data.rule_code)
    
    db_red_flag = customer_red_flags(
        customer_id=red_flag_data.customer_id,
        rule_code=red_flag_data.rule_code,
        description=red_flag_data.description,
        severity=red_flag_data.severity,
        metric_values=red_flag_data.metric_values,
        metric_data_timestamp=red_flag_data.metric_data_timestamp,
        notes=red_flag_data.notes,
        category=red_flag_data.category
    )
    db.add(db_red_flag)
    db.commit()
    db.refresh(db_red_flag)
    return db_red_flag

@router.post("/", response_model=CustomerRedFlagResponse)
def create_customer_red_flag(red_flag: CustomerRedFlagCreate, db: Session = Depends(get_db)):
    try:
        return create_red_flag(db, red_flag)
    except Exception as e:
        logger.error(f"Error creating red flag: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

def get_customer_red_flags_internal(customer_id: uuid.UUID, db: Session, rule_type: str = None) -> List[dict]:
    query = db.query(
        customer_red_flags,
        rules_store.name.label('rule_name'),
        rules_store.description.label('rule_description'),
        rules_store.type.label('rule_type'),
        rules_store.severity.label('rule_severity'),
        rules_store.fraud_type.label('rule_fraud_type'),
        rules_store.rule.label('rule')
    ).outerjoin(
        rules_store,
        customer_red_flags.rule_code == rules_store.code
    ).filter(
        customer_red_flags.customer_id == customer_id
    )
    
    if rule_type:
        query = query.filter(
            (customer_red_flags.category == rule_type) | (rules_store.type == rule_type)
        )
    
    red_flags = query.all()
    
    if not red_flags:
        return []
        
    response = []
    for flag, rule_name, rule_desc, rule_type, rule_sev, fraud_type, rule in red_flags:
        response.append({
            "id": flag.id,
            "customer_id": flag.customer_id,
            "rule_code": flag.rule_code,
            "description": flag.description,
            "severity": flag.severity,
            "metric_values": flag.metric_values,
            "created_at": flag.created_at,
            "updated_at": flag.updated_at,
            "metric_data_timestamp": flag.metric_data_timestamp,
            "notes": flag.notes,
            "category": flag.category,
            "rule_name": rule_name,
            "rule_description": rule_desc,
            "rule_type": flag.category if flag.category is not None else rule_type,
            "rule_severity": rule_sev,
            "rule_fraud_type": fraud_type,
            "rule": rule
        })
    
    return response

@router.get("/customer/{customer_id}", response_model=List[CustomerRedFlagResponse])
def get_customer_red_flags(
    customer_id: uuid.UUID, 
    rule_type: Optional[str] = Query(None, description="Filter by rule type"), 
    db: Session = Depends(get_db)
):
    return get_customer_red_flags_internal(customer_id, db, rule_type)

@router.get("/rule/{rule_code}", response_model=List[CustomerRedFlagResponse])
def get_red_flags_by_rule(rule_code: str, db: Session = Depends(get_db)):
    validate_rule_code(db, rule_code)
    
    red_flags = db.query(customer_red_flags).filter(customer_red_flags.rule_code == rule_code).all()
    if not red_flags:
        return []
    return red_flags

@router.put("/{red_flag_id}", response_model=CustomerRedFlagResponse)
def update_red_flag(red_flag_id: uuid.UUID, red_flag: CustomerRedFlagCreate, db: Session = Depends(get_db)):
    validate_rule_code(db, red_flag.rule_code)
    
    db_red_flag = db.query(customer_red_flags).filter(customer_red_flags.id == red_flag_id).first()
    if not db_red_flag:
        raise HTTPException(status_code=404, detail="Red flag not found")
    
    for key, value in red_flag.dict().items():
        setattr(db_red_flag, key, value)
    
    db.commit()
    db.refresh(db_red_flag)
    return db_red_flag

@router.delete("/{red_flag_id}")
def delete_red_flag(red_flag_id: uuid.UUID, db: Session = Depends(get_db)):
    db_red_flag = db.query(customer_red_flags).filter(customer_red_flags.id == red_flag_id).first()
    if not db_red_flag:
        raise HTTPException(status_code=404, detail="Red flag not found")
    
    db.delete(db_red_flag)
    db.commit()
    return {"message": "Red flag deleted successfully"} 