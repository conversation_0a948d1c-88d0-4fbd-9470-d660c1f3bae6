from fastapi import APIRouter, Depends, HTTPException, Path, status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from uuid import UUID, uuid4
from datetime import datetime
from typing import List, Dict, Any
from ..database import get_db
from ..models import models
from ..schemas.request_models import (
    InvestigationListCreate,
    CaseEventListCreate,
    CaseEventMetaDataListCreate,
    NoteCaseEvent, 
    CaseRelation,
    InvestigatorListCreate,
    InvestigationUpdate,
)
from decimal import Decimal
from urllib.parse import unquote
import pandas as pd
import requests
import logging
from .apiProtection import get_current_user

# Initialize the FastAPI router for case management endpoints
router = APIRouter(
    prefix="",
    tags=["Case Management"]
)

@router.get("/{investigation_id}/case-events")
async def get_merchant_investigation_case_events(
    investigation_id: UUID = Path(..., description="The UUID of the investigation"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    """
    Retrieve all case events associated with a specific investigation.
    
    Args:
        investigation_id (UUID): Unique identifier for the investigation.
        db (Session): Database session dependency.
        current_user (models.User): Current authenticated user.

    Returns:
        JSON response containing a list of case events.
    """
    try:
        # Query the investigation by ID
        investigation = db.query(models.investigations).filter(
            models.investigations.investigation_id == str(investigation_id)
        ).first()
        
        # Raise an exception if the investigation is not found
        if not investigation:
            raise HTTPException(status_code=404, detail="Investigation not found")

        # Retrieve all case events related to the investigation
        case_events = db.query(models.caseEvents).filter(models.caseEvents.investigation_id == str(investigation_id)).all()
        data = []
        
        # Iterate over each case event to construct the response
        for case_event in case_events:
            temp = {
                "case_event_id": case_event.case_event_id,
                "description": case_event.description,
                "timestamp": case_event.timestamp,
                "user": case_event.user,
                "content": case_event.content,
                "type": case_event.type,
                "meta_data": []
            }
            # Retrieve metadata for each case event
            meta_data = db.query(models.caseEventMetaData).filter(models.caseEventMetaData.case_event_id == str(case_event.case_event_id)).all()
            temp["meta_data"] = meta_data
            data.append(temp)
        
        # Return the list of case events
        return {    
            "case_events": data
        }
    except Exception as e:
        # Rollback the transaction in case of an error
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/{investigation_id}/investigation-details")
async def get_merchant_investigation_details(
    investigation_id: UUID = Path(..., description="The UUID of the investigation"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    investigation = db.query(models.investigations).filter(models.investigations.investigation_id == str(investigation_id)).first()
    
    if not investigation:
        raise HTTPException(status_code=404, detail="Investigation not found")
    
    data = {
        "investigation_id": investigation.investigation_id,
        "case_number": investigation.case_number,
        "title": investigation.title,
        "description": investigation.description,
        "status": investigation.status,
        "merchant_email": db.query(models.contacts).filter(models.contacts.merchant_id == investigation.merchant_id).first().email,
        "priority": investigation.priority,
        "assignee_Name": investigation.assignee_Name,
        "assignee_Email": investigation.assignee_Email,
        "merchant_name": investigation.merchant_name,
        "last_updated": investigation.last_updated,
        "sla_deadline": investigation.sla_deadline,
        "created_at": investigation.created_at,
        "created_by": investigation.created_by
    }
    
    return { "investigation": data }
    
@router.get("/{investigation_id}/case-events")
async def get_merchant_investigation_notes(
    investigation_id: UUID = Path(..., description="The UUID of the investigation"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:

        investigation = db.query(models.investigations).filter(
            models.investigations.investigation_id == str(investigation_id)
        ).first()
        if not investigation:
            raise HTTPException(status_code=404, detail="Investigation not found")

        case_events = db.query(models.caseEvents).filter(models.caseEvents.investigation_id == str(investigation_id)).all()
        case_event_meta_data = []
        for case_event in case_events:
            meta_data = db.query(models.caseEventMetaData).filter(models.caseEventMetaData.case_event_id == str(case_event.case_event_id)).all()
            case_event_meta_data.append(meta_data)

        return {
            "case_events": case_events,
            "case_event_meta_data": case_event_meta_data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/investigations")
async def create_merchant_investigation(
    investigation: InvestigationListCreate,
    db: Session = Depends(get_db)
):
    try:

        db.query(models.investigations)\
            .delete()
        
        db.flush()  

        # Create new investigation
        for investigation in investigation.investigations:
            db_investigation = models.investigations(
                merchant_id=investigation.merchant_id,
                investigation_id=investigation.investigation_id,
                title=investigation.title,
                description=investigation.description,
                status=investigation.status,
                priority=investigation.priority,
                assignee_Name=investigation.assignee_Name,
                assignee_Email=investigation.assignee_Email,
                merchant_name=investigation.merchant_name,
                case_number=investigation.case_number,
                last_updated=investigation.last_updated,
                sla_deadline=investigation.sla_deadline,
                created_at=datetime.now(),
                created_by="ADMIN"
            )
            db.add(db_investigation)
        db.commit()
        db.refresh(db_investigation)

        # Return the newly created investigation in the same format as GET endpoint
        return {
            "investigations": [
                {
                    "status": "success"
                }
            ]
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{merchant_id}/investigations")
async def get_merchant_investigations(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")

    investigations = db.query(models.investigations).filter(models.investigations.merchant_id == merchant_id).order_by(models.investigations.created_at.desc()).all()
    data = []
    for investigation in investigations:
        data.append({
            "investigation_id": investigation.investigation_id,
            "case_number": investigation.case_number,
            "title": investigation.description,
            "description": investigation.status,
            "merchant_email": db.query(models.contacts).filter(models.contacts.merchant_id == investigation.merchant_id).first().email,
            "status": investigation.status,
            "priority": investigation.priority,
            "assignee_Name": investigation.assignee_Name,
            "assignee_Email": investigation.assignee_Email,
            "merchant_name": investigation.merchant_name,
            "last_updated": investigation.last_updated,
            "sla_deadline": investigation.sla_deadline,
            "created_at": investigation.created_at,
            "created_by": investigation.created_by
        })
    return { "investigations": data }

@router.get("/investigations")
async def get_merchant_investigations(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    investigations = db.query(models.investigations).order_by(models.investigations.created_at.desc()).all()
    data = []
    for investigation in investigations:
        data.append({
            "investigation_id": investigation.investigation_id,
            "case_number": investigation.case_number,
            "title": investigation.title,
            "description": investigation.description,
            "status": investigation.status,
            "merchant_email": db.query(models.contacts).filter(models.contacts.merchant_id == investigation.merchant_id).first().email,
            "priority": investigation.priority,
            "assignee_Name": investigation.assignee_Name,
            "assignee_Email": investigation.assignee_Email,
            "merchant_name": investigation.merchant_name,
            "last_updated": investigation.last_updated,
            "sla_deadline": investigation.sla_deadline,
            "created_at": investigation.created_at,
            "created_by": investigation.created_by
        })
    return { "investigations": data }



@router.post("/{investigation_id}/add-case-note")
async def create_merchant_investigation_case_events(
    investigation_id: UUID,  # Non-default argument
    description: NoteCaseEvent,  # Non-default argument
    db: Session = Depends(get_db), # Default argument
    current_user: models.User = Depends(get_current_user)
):
    # Check if the investigation exists
    investigation = db.query(models.investigations).filter(
        models.investigations.investigation_id == str(investigation_id)
    ).first()
    if not investigation:
        raise HTTPException(status_code=404, detail="Investigation not found")
    
    # Create a new case event
    case_event = models.caseEvents(
        case_event_id=str(uuid4()),
        description=description.description,
        timestamp=description.timestamp,
        user=description.user,
        content=description.title,
        type="note",
        investigation_id=str(investigation_id),
    )
    
    # Add and commit the new case event to the database
    db.add(case_event)
    db.commit()
    db.refresh(case_event)
    
    return { "message": "Case event note created successfully 💀💀💀💀" }


#create post api for updating a investigation case event which will only update the data for the investigatior ,






@router.get("/{merchant_id}/{investigation_id}/related-cases")
async def get_merchant_investigation_related_cases(
    merchant_id: UUID,
    investigation_id: UUID,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    investigation = db.query(models.investigations).filter(
        models.investigations.merchant_id == merchant_id,
        models.investigations.investigation_id == str(investigation_id)
    ).first()
    if not investigation:
        raise HTTPException(status_code=404, detail="Investigation not found")
    related_cases = db.query(models.related_cases).filter(models.related_cases.case_id == str(investigation.case_number)).all()
    return { "related_cases": related_cases }

@router.post("/{merchant_id}/related-cases")
async def create_merchant_investigation_related_cases(
    merchant_id: UUID,
    case_relation: CaseRelation,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Check if merchant exists
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        # Create new related case
        related_case = models.related_cases(
            case_number=case_relation.case_number,
            related_case_number=case_relation.related_case_number,
            relationship_type=case_relation.relationship_type,
            created_at=datetime.now()
        )
        
        db.add(related_case)
        db.commit()
        db.refresh(related_case)

        return {
            "status": "success",
            "message": "Related case created successfully",
            "data": {
                "case_number": related_case.case_number,
                "related_case_number": related_case.related_case_number,
                "relationship_type": related_case.relationship_type
            }
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{investigator_email}/investigator-details")
async def get_investigator_investigation_details(
    investigator_email: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    # Explicitly decode the email
    decoded_email = unquote(investigator_email)
    print(decoded_email)
    # Query the database using the decoded email
    investigator = db.query(models.investigators).filter_by(email=decoded_email).first()
    return { "investigator": investigator }

@router.post("/investigators")
async def create_investigator(
    investigator: InvestigatorListCreate,
    db: Session = Depends(get_db)
):  
    for investigator in investigator.investigators:
        db_investigator = models.investigators(
            name=investigator.name,
            email=investigator.email,
            current_caseload=investigator.current_caseload,
            expertise=investigator.expertise,
            SLA_adherence_percentage=investigator.SLA_adherence_percentage
        )
        db.add(db_investigator)
    db.commit()
    db.refresh(db_investigator)
    return { "message": "Investigator created successfully" }

@router.get("/investigators")
async def get_merchant_investigators(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    investigators = db.query(models.investigators).all()
    return { "investigators": investigators }

@router.get("/assigned_cases/{investigator_email}")
async def get_assigned_cases(
    investigator_email: str,
    db: Session = Depends(get_db)
):
    cases = db.query(models.investigations).filter(models.investigations.assignee_Email == investigator_email).all()
    investigator = db.query(models.investigators).filter(models.investigators.email == investigator_email).first()
    investigator.current_caseload += 1
    db.commit()
    db.refresh(investigator)
    return { "cases": cases }

@router.get("/investigationIDs")
async def get_investigations_ids(
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    # send investigation id and the case number
    investigations = db.query(models.investigations).all()
    data = []
    for investigation in investigations:
        data.append({
            "investigation_id": investigation.investigation_id,
            "case_number": investigation.case_number
        })
    return { "data": data }

def related_cases_request():    
    try:
        # Read CSV files with error handling
        data_related_cases = pd.read_csv("investigation/related_cases.csv")
    except FileNotFoundError as e:
        logging.error(f"Required CSV file not found: {e}")
        return False

    success = True
    
    # Convert DataFrame to list of dictionaries with proper structure
    for _, row in data_related_cases.iterrows():
        # The API endpoint expects merchant_id in the URL path and case relation in the body
        merchant_id = str(row["merchant_id"])
        case_relation = {
            "case_number": str(row["case_number"]),
            "related_case_number": str(row["related_case_number"]),
            "relationship_type": str(row["relationship_type"])
        }
        
        url_endpoint = f"http://0.0.0.0:8000/api/v1/case-management/{merchant_id}/related-cases"      
        
        try:
            response = requests.post(url_endpoint, json=case_relation)
            response.raise_for_status()  # Raise an exception for bad status codes
            print(f"Successfully created related case for merchant {merchant_id}")
            print(f"Response Status: {response.status_code}")
            print(f"Response Body: {response.json()}")
        except requests.exceptions.RequestException as e:
            logging.error(f"Request failed for merchant {merchant_id}, case {case_relation['case_number']}: {e}")
            success = False
            continue
    
    return success

@router.post("/{investigation_id}/update-investigation")
async def update_investigation_investigator(
    investigation_id: UUID,
    investigation_update: InvestigationUpdate,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        db_investigation = db.query(models.investigations).filter(
            models.investigations.investigation_id == str(investigation_id)
        ).first()
        
        if not db_investigation:
            raise HTTPException(status_code=404, detail="Investigation not found")

        # Update the fields from the input model
        db_investigation.assignee_Name = investigation_update.assignee_Name
        db_investigation.assignee_Email = investigation_update.assignee_Email
        db_investigation.status = investigation_update.status
        db_investigation.last_updated = datetime.now()

        db.commit()
        db.refresh(db_investigation)
        
        return {"message": "Investigation updated successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
