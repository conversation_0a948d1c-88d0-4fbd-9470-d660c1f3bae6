from fastapi import APIRouter, Depends, HTTPException, Path, status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from uuid import UUID
from datetime import datetime
from typing import List, Dict, Any
from ..database import get_db
from ..models import models
from ..schemas.request_models import (
    TransactionListCreate,
    PaymentChannelListCreate,
    PayoutListCreate,
    CommunicationListCreate,
    TimelineEventListCreate,
    RiskAssessmentCreate,
    FlagListCreate,
    MerchantProfileUpdate,
    MerchantComplianceUpdate,
    MerchantFinancialsUpdate,
    MerchantLinkageUpdate,
    MerchantDigitalFootprintUpdate,
    KeyMetricsCreate,
    InvestigationNoteListCreate,
    InvestigationListCreate,
    RulesCreate,
    CommonConnectionsListCreate,
    NetworkOverviewListCreate,
    LinkedEntitiesListCreate,
    KeyMetricsListCreate,
    RecentMentionsListCreate,
    WebsiteMetricsListCreate,
    ReviewSourcesListCreate,
    SocialPresenceListCreate,
    DocumentsUploadedListCreate,
    DevicesUsedListCreate,
    CaseEventListCreate,
    CaseEventMetaDataListCreate,
    TemporalMetricsListCreate,
)
from decimal import Decimal

router = APIRouter(
    prefix="",
    tags=["Merchant Operations"]
)

@router.post("/update_rules")
async def update_rules(
    rules: RulesCreate,
    db: Session = Depends(get_db)
):
    try:
        # Get or create rules
        db_rules = db.query(models.Rules).first()
        if not db_rules:
            db_rules = models.Rules()
            db.add(db_rules)
        
        # Update rules with new values
        for field, value in rules.dict().items():
            setattr(db_rules, f"max_{field}", value)
        
        db.flush()

        # Get all merchants
        merchants = db.query(models.Merchant).all()
        
        # Update merchant flags based on rules
        for merchant in merchants:
            # Get merchant's transactions
            # check in the global transactions table
            transactions = db.query(models.transactions).filter(
                models.transactions.merchant_id == merchant.id
            ).all()
            
            if transactions:
                # Compare with rules and set flags
                merchant.avg_cx_pii_score_is_high = merchant.avg_cx_pii_score > db_rules.max_cx_pii_score
                merchant.txn_amt_avg_is_high = merchant.txn_amt_avg > db_rules.max_txn_amt_avg
                merchant.cancelled_txn_cnt_pct_is_high = merchant.cancelled_txn_cnt_pct > db_rules.max_cancelled_txn_cnt_pct
                merchant.card_num_density_is_high = merchant.card_num_density > db_rules.max_card_num_density
                merchant.curr_diversity_score_is_high = merchant.curr_diversity_score > db_rules.max_curr_diversity_score
                merchant.customer_density_is_high = merchant.customer_density > db_rules.max_customer_density
                merchant.cx_complaint_txn_pct_is_high = merchant.cx_complaint_txn_pct > db_rules.max_cx_complaint_txn_pct
                merchant.day_cos_is_high = merchant.day_cos > db_rules.max_day_cos
                merchant.day_sin_is_high = merchant.day_sin > db_rules.max_day_sin
                merchant.device_id_density_is_high = merchant.device_id_density > db_rules.max_device_id_density
                merchant.failed_txn_cnt_pct_is_high = merchant.failed_txn_cnt_pct > db_rules.max_failed_txn_cnt_pct
                merchant.hour_cos_is_high = merchant.hour_cos > db_rules.max_hour_cos
                merchant.hour_sin_is_high = merchant.hour_sin > db_rules.max_hour_sin
                merchant.hrs_since_last_transaction_is_high = merchant.hrs_since_last_transaction > db_rules.max_hrs_since_last_transaction
                merchant.interntational_txn_cnt_pct_is_high = merchant.interntational_txn_cnt_pct > db_rules.max_interntational_txn_cnt_pct
                merchant.invoice_and_txn_amt_diff_pct_is_high = merchant.invoice_and_txn_amt_diff_pct > db_rules.max_invoice_and_txn_amt_diff_pct
                merchant.ip_density_is_high = merchant.ip_density > db_rules.max_ip_density
                merchant.late_night_txn_amt_avg_is_high = merchant.late_night_txn_amt_avg > db_rules.max_late_night_txn_amt_avg
                merchant.late_night_txn_cnt_is_high = merchant.late_night_txn_cnt > db_rules.max_late_night_txn_cnt
                merchant.month_cos_is_high = merchant.month_cos > db_rules.max_month_cos
                merchant.month_sin_is_high = merchant.month_sin > db_rules.max_month_sin
                merchant.num_distinct_currency_used_is_high = merchant.num_distinct_currency_used > db_rules.max_num_distinct_currency_used
                merchant.chargeback_txn_cnt_pct_is_high = merchant.chargeback_txn_cnt_pct > db_rules.max_chargeback_txn_cnt_pct
                merchant.name_mismatch_txn_cnt_pct_is_high = merchant.name_mismatch_txn_cnt_pct > db_rules.max_name_mismatch_txn_cnt_pct
                merchant.risky_cx_txn_cnt_pct_is_high = merchant.risky_cx_txn_cnt_pct > db_rules.max_risky_cx_txn_cnt_pct
                merchant.round_txn_cnt_pct_is_high = merchant.round_txn_cnt_pct > db_rules.max_round_txn_cnt_pct
                merchant.txn_cnt_is_high = merchant.txn_cnt > db_rules.max_txn_cnt
                merchant.txn_amt_sum_is_high = merchant.txn_amt_sum > db_rules.max_txn_amt_sum
                merchant.velocity_transaction_is_high = merchant.velocity_transaction > db_rules.max_velocity_transaction

        db.commit()
        return {"message": "Rules updated successfully", "rules": db_rules}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/add_merchant_id/{merchant_id}")
async def add_merchant_id(
    merchant_id: UUID = Path(..., description="The UUID to be assigned as merchant ID"),
    db: Session = Depends(get_db)
) -> dict:
    """
    Basic endpoint to add a merchant ID to the database.
    Returns 200 OK if merchant already exists or is successfully added.
    """
    try:
        # Check if merchant already exists
        existing_merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if existing_merchant:
            return {"message": "Merchant ID already exists"}

        # Create new merchant with just the ID
        db_merchant = models.Merchant(
            id=merchant_id,
            created_at=datetime.now()
        )
        db.add(db_merchant)
        db.commit()
        
        return {"message": "Merchant ID added successfully"}

    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add merchant ID"
        )
    
@router.post("/transactions")
async def create_merchant_transaction(
    transaction: TransactionListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.transactions).delete()
        db.flush()

        # Create new transaction
        for transaction in transaction.transactions:
            db_transaction = models.transactions(
                id=transaction.transaction_id,
                merchant_id=UUID(transaction.merchant_id),
                transaction_type=transaction.transaction_type,
                merchant_type=transaction.merchant_type,
                city=transaction.city,
                payment_channel=transaction.payment_channel,
                country_code=transaction.country_code,
                amount=transaction.amount,
                merchant_name=transaction.merchant_name,
                risk_score=transaction.risk_score,
                risk_description=transaction.risk_description,
                product_name=transaction.product_name,
                dispute_date=transaction.dispute_date,
                complain_date=transaction.complain_date,
                timestamp=transaction.timestamp,
                status=transaction.status,
                created_at=datetime.now(),
                is_fraud_transaction=transaction.is_fraud_transaction,
                cx_id=UUID(transaction.cx_id) if transaction.cx_id else None,
                cx_ip=transaction.cx_ip,
                cx_device_id=transaction.cx_device_id,
                cx_card_number=transaction.cx_card_number,
                cx_city=transaction.cx_city,
                cx_pii_linkage_score=transaction.cx_pii_linkage_score,
                is_cardholder_name_match=transaction.is_cardholder_name_match,
                is_chargeback=transaction.is_chargeback,
                is_cx_international=transaction.is_cx_international,
                txn_status=transaction.txn_status,
                is_cx_risky=transaction.is_cx_risky,
                invoice_amount=transaction.invoice_amount,
                is_cancelled=transaction.is_cancelled,
                txn_currency=transaction.txn_currency,
                has_cx_complaint=transaction.has_cx_complaint
            )
            db.add(db_transaction)
        db.commit()
        return {"message": "Transactions created successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/payment-channels")
async def create_payment_channel(
    channels: PaymentChannelListCreate,
    db: Session = Depends(get_db)
):
    try:
        # Delete existing payment channels for this merchant
        db.query(models.payment_channels).delete()
        db.flush()

        # Create new payment channels
        for channel in channels.channels:
            # Convert added_on to datetim
            db_channel = models.payment_channels(
                merchant_id=channel.merchant_id,
                id=channel.id,
                type=channel.type,
                name=channel.name,
                status=channel.status,
                added_on=channel.added_on,
                created_at=datetime.now(),  # Ensure this is a dictionary
            )
            db.add(db_channel)

        db.commit()
        return {"message": "Payment channels created successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/payouts")
async def create_payout(
    payout: PayoutListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.payouts).delete()
        db.flush()

        for payout in payout.payouts:
            db_payout = models.payouts(
                merchant_id=payout.merchant_id,
                id=payout.payout_id,
                amount=int(payout.amount),
                status=payout.status,
                bank_account=payout.bank_account,
                timestamp=payout.timestamp,
                utr=payout.utr,
                created_at=datetime.now()
            )
            db.add(db_payout)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/communications")
async def create_communication(
    communication: CommunicationListCreate,
    db: Session = Depends(get_db)
):
    try:

        # Delete existing communications for this merchant
        db.query(models.communications)\
            .delete()
        
        db.flush()

        # Create new communication
        for communication in communication.communications:
            db_communication = models.communications(
                sender_id = communication.sender_id,
                receiver_id = communication.receiver_id,
                type = communication.type,
                subject = communication.subject,
                content = communication.content,
                timestamp = communication.timestamp,
                created_at=datetime.now(),
            )
            db.add(db_communication)
        db.commit()
        db.refresh(db_communication)
        return {"id": str(db_communication.id)}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/timeline")
async def create_timeline_event(
    event: TimelineEventListCreate,
    db: Session = Depends(get_db)
):
    try:
        # Delete existing timeline events for this merchant
        db.query(models.timeline_events).delete()
        db.flush()

        # Create new timeline events
        for event_data in event.events:  # Access the 'events' attribute
            db_event = models.timeline_events(
                merchant_id=event_data.merchant_id,
                time=event_data.time,
                event=event_data.event,
                type=event_data.type
            )
            db.add(db_event)
        db.commit()
        return {"message": "Timeline events created successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{merchant_id}/risk-assessment")
async def create_risk_assessment(
    merchant_id: UUID,
    assessment: RiskAssessmentCreate,
    db: Session = Depends(get_db)
):
    print(assessment.dict())
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        # Delete all the risk indicators, risk assessments, risk categories for this merchant
        db.query(models.risk_indicators).filter(models.risk_indicators.merchant_id == merchant_id).delete()
        db.query(models.risk_assessments).filter(models.risk_assessments.merchant_id == merchant_id).delete()
        db.query(models.risk_categories).filter(models.risk_categories.merchant_id == merchant_id).delete()
        db.flush()

        db_risk_assessment = models.risk_assessments(
            merchant_id=merchant_id,
            percentile=str(assessment.percentile),
            percentile_business_category=str(assessment.percentile_business_category),
            risk_level=(assessment.risk_level),
            description=(assessment.description)
        )
        db.add(db_risk_assessment)
        db.flush() 


        for category in assessment.categories:
            db_risk_category = models.risk_categories(
                merchant_id=merchant_id,  
                category=category.title,
                score=category.score,
                description=category.description
            )
            db.add(db_risk_category)
            db.flush()  

            for indicator in category.indicators:
                db_risk_indicator = models.risk_indicators(
                    merchant_id=merchant_id,
                    category=category.title,
                    indicator_label=indicator.label,
                    indicator_value=indicator.value,
                    severity=indicator.severity
                )
                db.add(db_risk_indicator)

        db.commit()
        return {"id": str(db_risk_assessment.id)}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/common-connections")
async def create_common_connections(
    common_connections: CommonConnectionsListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.common_connections).delete()
        db.flush()

        for connection in common_connections.connections:
            db_connection = models.common_connections(
                merchant_id=connection.merchant_id,
                connection_type=connection.connection_type,
                connection_value=str(connection.connection_value),
                sender_id=connection.sender_id,
                receiver_id=connection.receiver_id,
                shared_with=connection.shared_with,
                created_at=datetime.now()
            )
            db.add(db_connection)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/recent-mentions")
async def create_recent_mentions(   
    recent_mentions: RecentMentionsListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.recent_mentions).delete()
        db.flush()

        for mention in recent_mentions.recent_mentions:
            db_recent_mention = models.recent_mentions(
                merchant_id=mention.merchant_id,
                source=mention.source,
                title=mention.title,
                date=mention.date,
                sentiment=mention.sentiment,    
                snippet=mention.snippet,
                created_at=datetime.now()
            )
            db.add(db_recent_mention)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/website-metrics")
async def create_website_metrics(
    website_metrics: WebsiteMetricsListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.website_metrics).delete()
        db.flush()

        for mention in website_metrics.website_metrics:
            # Convert the list of SecurityStatus objects to a list of dictionaries
            security_data = [security.dict() for security in mention.security]

            db_recent_mention = models.website_metrics(
                merchant_id=mention.merchant_id,
                domain=mention.domain,
                age=mention.age,
                monthly_traffic=mention.monthly_traffic,
                traffic_trend_value=mention.traffic_trend_value,
                traffic_trend_positive=mention.traffic_trend_positive,
                trust_score=mention.trust_score,
                security=security_data,  # Assign the converted data
                last_updated=mention.last_updated,
                created_at=datetime.now()
            )
            db.add(db_recent_mention)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/review-sources")
async def create_review_sources(
    review_sources: ReviewSourcesListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.review_sources).delete()
        db.flush()

        for source in review_sources.review_sources:
            db_recent_mention = models.review_sources(
                merchant_id=source.merchant_id,
                platform=source.platform,
                rating=source.rating,
                total_reviews=source.total_reviews,
                sentiment=source.sentiment,
                created_at=datetime.now()
            )
            db.add(db_recent_mention)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/social-presence")
async def create_social_presence(
    social_presence: SocialPresenceListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.social_presence).delete()
        db.flush()

        for source in social_presence.social_presence:
            db_recent_mention = models.social_presence(
                merchant_id=source.merchant_id,
                platform=source.platform,
                icon_type=source.icon_type,
                metrics=source.metrics,
                followers=source.followers,
                posts_monthly=source.posts_monthly,
                engagement_rate=source.engagement_rate,
                employee_count=source.employee_count,
                verified=source.verified,
                created_at=datetime.now()
            )
            db.add(db_recent_mention)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/network-overview")
async def create_network_overview(
    network_overview: NetworkOverviewListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.network_overview).delete()
        db.flush()

        for overview in network_overview.network_overviews:
            db_network_overview = models.network_overview(
                merchant_id=overview.merchant_id,
                total_connections=overview.total_connections,
                high_risk_connections=overview.high_risk_connections,
                network_risk_score=overview.network_risk_score
            )
            db.add(db_network_overview)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/linked-entities")
async def create_linked_entities(
    linked_entities: LinkedEntitiesListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.relationships).delete()
        db.flush()

        for entity in linked_entities.linked_entities:
            db_entity = models.relationships(
                merchant_id=entity.merchant_id,
                related_entity_name=entity.related_entity_name,
                relationship_type=entity.relationship_type,
                registration_number=entity.registration_number,
                location=entity.location,
                connection_count=entity.connection_count,
                risk_level=entity.risk_level,
            )
            db.add(db_entity)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/flags")
async def create_merchant_flag(
    flag: FlagListCreate,
    db: Session = Depends(get_db)
):
    try:
        # Delete existing flags for this merchant
        db.query(models.flags).delete()
        
        db.flush()

        # Create new flags
        for flag_data in flag.flags:  # flag.flags is a list of FlagCreate objects
            db_flag = models.flags(
                merchant_id=flag_data.merchant_id,
                flag_type=flag_data.flag_type,  # Use dot notation
                severity=flag_data.severity,      # Use dot notation
                text=flag_data.text,              # Use dot notation
                importance=flag_data.importance,
                timestamp=flag_data.timestamp or datetime.now()  # Use provided timestamp or current time
            )
            db.add(db_flag)

        db.commit()
        return {"message": "Flags created successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
@router.post("/{merchant_id}/profile")
async def update_merchant_profile(
    merchant_id: UUID,
    profile: MerchantProfileUpdate,
    db: Session = Depends(get_db)
):
    try:
        # Delete the basic info of the merchant if it exists
        existing_info = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if existing_info:
            db.delete(existing_info)
            db.flush()

        # Add the new basic info
        new_info = models.Merchant(
            id=merchant_id,
            legal_name=profile.basicInfo.legalName,
            trade_name=profile.basicInfo.tradeName,
            business_type=profile.basicInfo.businessType,
            num_directors=profile.basicInfo.num_directors,
            domain=profile.basicInfo.domain,
            incorporation_date=profile.basicInfo.incorporationDate,
            industry=profile.basicInfo.industry,
            description=profile.basicInfo.description,
            mca_description=profile.basicInfo.mcaDescription,
            business_category=profile.basicInfo.businessCategory,
            business_subcategory=profile.basicInfo.businessSubcategory,
            business_model=profile.basicInfo.businessModel,
            onboarding_date=profile.basicInfo.onboardingDate,
            onboarding_platform=str(profile.basicInfo.onboardingPlatform),
            kyc_verification_status=profile.basicInfo.kycVerificationStatus,
            kyc_verification_date=profile.basicInfo.kycVerificationDate,
            ip_geolocation=profile.basicInfo.ip_geolocation,
            rule_1 = False,
            rule_2 = False
        )
        db.add(new_info)
        db.commit()

        # Update contact information
        if profile.contact:
            contact = db.query(models.contacts).filter(models.contacts.merchant_id == merchant_id).first()
            if not contact:
                contact = models.contacts(merchant_id=merchant_id)
                db.add(contact)
                db.flush()

            contact_data = profile.contact.dict(exclude_unset=True)
            for key, value in contact_data.items():
                if value is not None:
                    setattr(contact, key, value)

        # Update online presence
        if profile.onlinePresence:
            db.query(models.online_presence).filter(models.online_presence.merchant_id == merchant_id).delete()
            for platform, url in profile.onlinePresence.items():
                if url:
                    new_presence = models.online_presence(
                        merchant_id=merchant_id,
                        platform=platform,
                        url=url
                    )
                    db.add(new_presence)

        # Update compliance information
        if profile.compliance:
            db.query(models.compliance_docs).filter(models.compliance_docs.merchant_id == merchant_id).delete()
            db.flush()
            if profile.compliance.businessDocs:
                for doc_type, details in profile.compliance.businessDocs.items():
                    new_doc = models.compliance_docs(
                        merchant_id=merchant_id,
                        document_type=details.get('type'),
                        document_number=details.get('document_number'),
                        status=details.get('status'),
                        last_updated=details.get('last_updated')
                    )
                    db.add(new_doc)  # Move this line inside the loop

            if profile.compliance.kycStatus:
                db.query(models.kyc_statuses).filter(models.kyc_statuses.merchant_id == merchant_id).delete()
                for kyc_type, status in profile.compliance.kycStatus.items():
                    new_kyc = models.kyc_statuses(
                        merchant_id=merchant_id,
                        kyc_type=kyc_type,
                        status=status
                    )
                    db.add(new_kyc)
            
        if profile.compliance.documents:
            db.query(models.compliance_docs).filter(models.compliance_docs.merchant_id == merchant_id).delete()
            for document in profile.compliance.documents:
                new_doc = models.compliance_docs(
                    merchant_id=merchant_id,
                    document_type=document.get('type'),
                    document_number=document.get('number'),
                    status=document.get('status'),
                    last_updated=document.get('lastUpdated')
                )
                db.add(new_doc)

        if profile.financial:
            db.query(models.financial_metrics).filter(models.financial_metrics.merchant_id == merchant_id).delete()
            new_metrics = models.financial_metrics(
                merchant_id = merchant_id,
                monthlyVolume = profile.financial.processingMetrics.monthlyVolume,
                averageTicketSize = profile.financial.processingMetrics.averageTicketSize,
                successRate = profile.financial.processingMetrics.successRate,
                refundRate = profile.financial.processingMetrics.refundRate,
                chargebackRate = profile.financial.processingMetrics.chargebackRate,
                disputeRate = profile.financial.processingMetrics.disputeRate,
                accountNumber = profile.financial.bankDetails.accountNumber,
                ifsc = profile.financial.bankDetails.ifsc,
                bankName = profile.financial.bankDetails.bankName,
                accountType = profile.financial.bankDetails.accountType,
                verificationStatus = profile.financial.bankDetails.verificationStatus
            )
            db.add(new_metrics)
        db.commit()
        return {"message": "Profile updated successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{merchant_id}/linkages")
async def create_merchant_linkage(
    merchant_id: UUID,
    linkage: MerchantLinkageUpdate,
    db: Session = Depends(get_db)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        # Delete existing records to maintain consistency
        db.query(models.network_overview)\
            .filter(models.network_overview.merchant_id == merchant_id)\
            .delete()
    
        db.query(models.relationships)\
            .filter(models.relationships.merchant_id == merchant_id)\
            .delete()
            
        db.query(models.common_connections)\
            .filter(models.common_connections.merchant_id == merchant_id)\
            .delete()
            
        db.flush()

        # Create network overview
        overview = models.network_overview(
            merchant_id=merchant_id,
            total_connections=linkage.network_overview.total_connections,
            high_risk_connections=linkage.network_overview.high_risk_connections,
            network_risk_score=linkage.network_overview.network_risk_score,
            created_at=datetime.now()
        )
        db.add(overview)
        db.flush()

        # Create linked entities
        for entity in linkage.linked_entities:
            db_entity = models.relationship(
                merchant_id=merchant_id,
                related_entity_name=entity.related_entity_name,
                relationship_type=entity.relationship_type,
                registration_number=entity.registration_number,
                location=entity.location,
                connection_count=entity.connection_count,
                risk_level=entity.risk_level,
                details=entity.details,
                created_at=datetime.now()
            )
            db.add(db_entity)

        # Create common connections
        for connection in linkage.common_connections:
            db_connection = models.common_connections(
                merchant_id=merchant_id,
                connection_type=connection.connection_type,
                connection_value=connection.connection_value,
                shared_with=connection.shared_with,
                created_at=datetime.now()
            )
            db.add(db_connection)

        db.commit()
        return {"message": "Linkage data created successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/{merchant_id}/digital-footprint")
async def create_merchant_digital_footprint(
    merchant_id: UUID,
    footprint: MerchantDigitalFootprintUpdate,
    db: Session = Depends(get_db)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        # Delete existing records to maintain consistency
        db.query(models.website_metrics)\
            .filter(models.website_metrics.merchant_id == merchant_id)\
            .delete()
            
        db.query(models.review_sources)\
            .filter(models.review_sources.merchant_id == merchant_id)\
            .delete()
            
        db.query(models.social_presence)\
            .filter(models.social_presence.merchant_id == merchant_id)\
            .delete()
            
        db.query(models.recent_mentions)\
            .filter(models.recent_mentions.merchant_id == merchant_id)\
            .delete()
            
        db.flush()

        # Create website metrics
        website_metrics = models.website_metrics(
            merchant_id=merchant_id,
            domain=footprint.website_metrics.domain,
            age=footprint.website_metrics.age,
            monthly_traffic=footprint.website_metrics.monthly_traffic,
            traffic_trend_value=footprint.website_metrics.traffic_trend_value,
            traffic_trend_positive=footprint.website_metrics.traffic_trend_positive,
            trust_score=footprint.website_metrics.trust_score,
            security_status=footprint.website_metrics.security_status,
            last_updated=footprint.website_metrics.last_updated,
            created_at=datetime.now()
        )
        db.add(website_metrics)

        # Create review sources
        for source in footprint.review_sources:
            review_source = models.review_sources(
                merchant_id=merchant_id,
                platform=source.platform,
                rating=source.rating,
                total_reviews=source.total_reviews,
                sentiment=source.sentiment,
                created_at=datetime.now()
            )
            db.add(review_source)

        # Create social presence
        for presence in footprint.social_presence:
            social = models.social_presence(
                merchant_id=merchant_id,
                platform=presence.platform,
                metrics=presence.metrics,
                created_at=datetime.now()
            )
            db.add(social)

        # Create recent mentions
        for mention in footprint.recent_mentions:
            recent_mention = models.recent_mentions(
                merchant_id=merchant_id,
                source=mention.source,
                title=mention.title,
                date=mention.date,
                sentiment=mention.sentiment,
                snippet=mention.snippet,
                created_at=datetime.now()
            )
            db.add(recent_mention)

        db.commit()

        # Return the newly created data in the same format as the GET endpoint
        return {
            "websiteMetrics": {
                "domain": website_metrics.domain,
                "age": website_metrics.age,
                "traffic": {
                    "monthly": website_metrics.monthly_traffic,
                    "trend": {
                        "value": website_metrics.traffic_trend_value,
                        "isPositive": website_metrics.traffic_trend_positive
                    }
                },
                "trustScore": website_metrics.trust_score,
                "securityStatus": website_metrics.security_status,
                "lastUpdated": website_metrics.last_updated
            },
            "reviewSources": [
                {
                    "platform": source.platform,
                    "rating": source.rating,
                    "totalReviews": source.total_reviews,
                    "sentiment": source.sentiment
                }
                for source in footprint.review_sources
            ],
            "socialPresence": [
                {
                    "platform": presence.platform,
                    "metrics": presence.metrics
                }
                for presence in footprint.social_presence
            ],
            "recentMentions": [
                {
                    "source": mention.source,
                    "title": mention.title,
                    "date": mention.date.strftime("%Y-%m-%d"),
                    "sentiment": mention.sentiment,
                    "snippet": mention.snippet
                }
                for mention in footprint.recent_mentions
            ]
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/key-metrics")
async def create_merchant_key_metrics(
    metrics: KeyMetricsListCreate,
    db: Session = Depends(get_db)
):
    try:
       
        # Delete existing metrics if any
        db.query(models.key_metrics)\
            .delete()
        
        db.flush()

        # Create new key metrics
        for metric in metrics.key_metrics:
            db_metrics = models.key_metrics(
                merchant_id=metric.merchant_id,
                date_of_onboarding=metric.date_of_onboarding,
                average_daily_transactions=metric.average_daily_transactions,
                average_payout_size=metric.average_payout_size,
                total_amount=metric.total_amount,
                total_count = metric.total_count,
                business_type = metric.business_type,
                business_category = metric.business_category,
                total_num_investigations = metric.total_num_investigations,
                merchant_legalName = metric.merchant_legalName,
                chargeback_percentage = metric.chargeback_percentage,
                current_balance_in_ledger = metric.current_balance_in_ledger,
                account_status = metric.account_status,
                integration_types = metric.integration_types,
                no_of_unique_customers = metric.no_of_unique_customers,
                created_at=datetime.now()
            )
            db.add(db_metrics)
        db.commit()
        db.refresh(db_metrics)

        # Return the newly created data in the same format as the GET endpoint
        return {
            "date_of_onboarding": db_metrics.date_of_onboarding,
            "average_daily_transactions": db_metrics.average_daily_transactions,
            "average_payout_size": db_metrics.average_payout_size,
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/investigation-notes")
async def create_merchant_investigation_note(
    note: InvestigationNoteListCreate,
    db: Session = Depends(get_db)
):
    try:
        # Delete existing investigation notes for this merchant
        db.query(models.investigation_notes)\
            .delete()
        
        db.flush()

        # Create new investigation note
        print(note)
        for note in note.notes:
            db_note = models.investigation_notes(
                merchant_id=note.merchant_id,
                investigation_id=note.investigation_id,
                title=note.title,
                description=note.description,
                timestamp=note.timestamp,
                created_at=datetime.now()
            )
            db.add(db_note)
        db.commit()
        db.refresh(db_note)

        # Return the newly created note in the same format as the GET endpoint
        return {
            "notes": [
                {
                    "id": str(db_note.id),
                    "title": db_note.title,
                    "description": db_note.description,
                    "timestamp": db_note.timestamp,
                    "created_at": db_note.created_at.isoformat() if db_note.created_at else None,
                    "created_by": None
                }
            ]
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    
@router.post("/temporal-metrics")
async def create_merchant_temporal_metrics(
    temporal_metrics: TemporalMetricsListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.merchant_temporal_metrics)\
            .delete()
        db.flush()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    
    return {
        "message": "Temporal metrics created successfully"
    }

    
@router.post("/documents-uploaded")
async def create_merchant_documents_uploaded(
    document: DocumentsUploadedListCreate,
    db: Session = Depends(get_db)
):
    # Delete existing all documents uploaded 
    db.query(models.documents_uploaded)\
        .delete()
    db.flush()

    for document in document.documents_uploaded:
        db_document = models.documents_uploaded(
            merchant_id=document.merchant_id,
            document_type=document.document_type,
            is_forged=document.is_forged,
            document_number=document.document_number,
            status=document.status,
            date_of_upload=document.date_of_upload,
            created_at=datetime.now()
        )   
        db.add(db_document)
    db.commit()
    db.refresh(db_document)

    return {
        "message": "Documents uploaded successfully"
    }

@router.post("/devices-used")
async def create_merchant_devices_used(
    device: DevicesUsedListCreate,
    db: Session = Depends(get_db)
):
    # Delete existing all devices used 
    db.query(models.devices_used)\
        .delete()
    db.flush()

    for device in device.devices_used:
        db_device = models.devices_used(
            merchant_id=device.merchant_id,
            device_id=device.device_id,
            ip_address=device.ip_address,
            date_of_addition=device.date_of_addition,
            created_at=datetime.now()
        )
        db.add(db_device)
    db.commit()
    db.refresh(db_device)

    return {
        "message": "Devices used successfully"
    }

@router.post("/case-event-meta-data")
async def create_merchant_case_event_meta_data(
    meta_data: CaseEventMetaDataListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.caseEventMetaData)\
            .delete()
        db.flush()

        for meta_data in meta_data.meta_data:
            db_meta_data = models.caseEventMetaData(
                case_event_id=meta_data.case_event_id,
                channel=meta_data.channel,
                oldStatus=meta_data.oldStatus,
                newStatus=meta_data.newStatus,
                documentType=meta_data.documentType,
                communicationType=meta_data.communicationType,
                created_at=datetime.now()
            )
            db.add(db_meta_data)
        db.commit()
        db.refresh(db_meta_data)

        return {
            "message": "Case event meta data created successfully"
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/case-events")
async def create_merchant_case_events(
    event: CaseEventListCreate,
    db: Session = Depends(get_db)
):
    try:
        db.query(models.caseEvents)\
            .delete()
        db.flush()
        
        for event in event.events:
            db_event = models.caseEvents(
                case_event_id=event.case_event_id,
                investigation_id=event.investigation_id,
                timestamp=event.timestamp,
                type=event.type,
                description=event.description,
                user=event.user,
                content=event.content,
                created_at=datetime.now()
            )
            db.add(db_event)
        db.commit()
        db.refresh(db_event)

        return {
            "message": "Case events created successfully"
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
