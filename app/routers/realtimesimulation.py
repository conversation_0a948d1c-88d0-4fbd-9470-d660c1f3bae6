from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from datetime import datetime
from typing import Optional

from app.database import get_db
from app.schemas.request_models import TransactionCreate
from app.models import models
from app.routers.redFlagGenerationRouter import new_transaction_added

router = APIRouter()

@router.post("/new-transaction")
def create_transaction(transaction: TransactionCreate, db: Session = Depends(get_db)):
    try:
        # Create new transaction object
        new_transaction = models.transactions(
            id=UUID(transaction.transaction_id),
            merchant_id=UUID(transaction.merchant_id), 
            transaction_type=transaction.transaction_type,
            merchant_type=transaction.merchant_type,
            city=transaction.city,
            payment_channel=transaction.payment_channel,
            country_code=transaction.country_code,
            amount=transaction.amount,
            merchant_name=transaction.merchant_name,
            risk_score=transaction.risk_score,
            risk_description=transaction.risk_description,
            product_name=transaction.product_name,
            status=transaction.status,
            timestamp=transaction.timestamp,
            dispute_date=transaction.dispute_date,
            complain_date=transaction.complain_date,
            created_at=transaction.created_at or datetime.now(),
            is_fraud_transaction=transaction.is_fraud_transaction,
            cx_id=transaction.cx_id,
            cx_ip=transaction.cx_ip,
            cx_device_id=transaction.cx_device_id,
            cx_card_number=transaction.cx_card_number,
            cx_city=transaction.cx_city,
            cx_pii_linkage_score=transaction.cx_pii_linkage_score,
            is_cardholder_name_match=transaction.is_cardholder_name_match,
            is_chargeback=transaction.is_chargeback,
            is_cx_international=transaction.is_cx_international,
            txn_status=transaction.txn_status,
            is_cx_risky=transaction.is_cx_risky,
            invoice_amount=transaction.invoice_amount,
            is_cancelled=transaction.is_cancelled,
            txn_currency=transaction.txn_currency,
            has_cx_complaint=transaction.has_cx_complaint
        )
        
        # Add and commit the transaction
        db.add(new_transaction)
        db.commit()
        db.refresh(new_transaction)
        
        # Call the red flag generation function
        new_transaction_added(new_transaction.id)
        
        return {"message": "Transaction created successfully", "transaction_id": new_transaction.id}
    
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    
