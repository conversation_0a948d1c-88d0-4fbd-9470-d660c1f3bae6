from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Path, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID, uuid4
from datetime import datetime, date
from ..database import get_db
from ..models import models, rule_repo
from ..schemas import request_models
from ..schemas.response_models import TransactionResponse, TransactionListResponse, PaymentChannelListResponse, PayoutListResponse, CommunicationListResponse, TimelineEventResponse, TimelineEventListResponse, InvestigationResponse, InvestigationListResponse, InvestigationNoteListResponse, KeyMetricsResponse
from collections import defaultdict
from .ares.model.llm_transaction_analysis import run_this_please
from .ares.feature_generation.incremental_feature_gen import main
from .apiProtection import get_current_user
import pandas as pd
import numpy as np
import os
import json
from .ares.investigation_gpt.gpt_with_context import use_it
from .ares.model.summarizzer import summer_is_mine
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from neo4j import GraphDatabase, exceptions
import time
from transformers import AutoTokenizer, AutoModel
import torch
from sklearn.metrics.pairwise import cosine_similarity
from groq import Groq
import asyncio

router = APIRouter()

scheduler = BackgroundScheduler()

# def calculate_percentiles():
#     db = next(get_db())  # Get the database session from the generator
#     try:
#         merchants = db.query(models.Merchant).all()
#         sum = 0
#         sum_category = defaultdict(int)
#         for merchant in merchants:
#             flags = db.query(models.flags).filter(models.flags.merchant_id == merchant.id).order_by(models.flags.timestamp.desc()).all()
#             if len(flags) == 0:
#                 continue
#             score = 0
#             for flag in flags:
#                 if flag.severity == "critical":
#                     score += 50
#                 elif flag.severity == "high":
#                     score += 20 
#                 elif flag.severity == "medium":
#                     score += 10
#                 elif flag.severity == "low":
#                     score += 5
#             sum += score
#             sum_category[merchant.business_category] += score
#             existing_entry = db.query(models.risk_score_for_percentile).filter(models.risk_score_for_percentile.merchant_id == merchant.id).one_or_none()
#             if existing_entry is None:
#                 new_entry = models.risk_score_for_percentile(
#                     merchant_id=merchant.id,
#                     risk_score=score
#                 )
#                 db.add(new_entry)
#             else:
#                 db.query(models.risk_score_for_percentile).filter(models.risk_score_for_percentile.merchant_id == merchant.id).update({models.risk_score_for_percentile.risk_score: score})
#             db.commit()
        
#         risk_scores = db.query(models.risk_score_for_percentile)
#         for risk_score in risk_scores:
#             merchant_id = risk_score.merchant_id
#             score = risk_score.risk_score
#             score_category = risk_score.risk_score_category if risk_score.risk_score_category is not None else 0  # Default to 0 if None
#             total_cnt = 1
#             cnt = 0
#             total_category_cnt = 1
#             category_cnt = 0
#             for risk_scorek in risk_scores:
#                 if risk_scorek.merchant_id == merchant_id:
#                     continue
#                 if risk_scorek.risk_score <= score:
#                     cnt += 1
#                 if db.query(models.Merchant).filter(models.Merchant.id == risk_scorek.merchant_id).first().business_category == db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first().business_category:
#                     total_category_cnt += 1
#                     if risk_scorek.risk_score <= score_category:
#                         category_cnt += 1
#             if total_cnt != 0:
#                 percentile = cnt / total_cnt
#             else:
#                 percentile = 0
#             if total_category_cnt != 0:
#                 percentile_category = category_cnt / total_category_cnt
#             else:
#                 percentile_category = 0
#             existing_entry = db.query(models.risk_assessments).filter(models.risk_assessments.merchant_id == merchant_id).one_or_none()
#             if existing_entry is None:
#                 new_entry = models.risk_assessments(
#                     merchant_id=merchant_id,
#                     percentile=percentile,
#                     percentile_business_category=percentile_category
#                 )
#                 db.add(new_entry)
#             else:
#                 db.query(models.risk_assessments).filter(models.risk_assessments.merchant_id == merchant_id).update({models.risk_assessments.percentile: percentile, models.risk_assessments.percentile_business_category: percentile_category})
#             db.commit()
#     finally:
#         db.close()  # Ensure the session is closed after use






scheduler.start()

class ConnectionManager:
    def __init__(self):
        # Dictionary to store user_id -> WebSocket connection mapping
        self.active_connections: dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, user_id: str):
        """Accept a new WebSocket connection for a specific user."""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        print(f"User {user_id} connected")

    def disconnect(self, user_id: str):
        """Remove a user's WebSocket connection when they disconnect."""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
            print(f"User {user_id} disconnected")

    async def send_message(self, user_id: str, message: str):
        """Send a message to a specific user."""
        websocket = self.active_connections.get(user_id)
        if websocket:
            await websocket.send_text(message)

    async def broadcast(self, message: str):
        """Send a message to all active users."""
        for websocket in self.active_connections.values():
            await websocket.send_text(message)

manager = ConnectionManager()

@router.get("/active-chatIDs", include_in_schema=False)
async def get_active_chat_ids(db: Session = Depends(get_db),current_user: models.User = Depends(get_current_user)):
    active_chats = db.query(models.active_chats).order_by(models.active_chats.last_chated.desc()).all()
    return [{"id": str(chat.chat_id), "title": chat.title, "lastchated": chat.last_chated.isoformat()} for chat in active_chats]

@router.get("/active-chat-history/{chat_id}", include_in_schema=False)
async def get_active_chat_history(chat_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    #check if the chat id exists in the active_chats table
    active_chat = db.query(models.active_chats).filter(models.active_chats.chat_id == chat_id).first()
    if not active_chat:
        raise HTTPException(status_code=404, detail="Chat not found")
    
    #get the chat history from the file .areas/data/context_{chat_id}.json
    # if the file does not exist, return "no chat history"
    context_file = f".ares/data/context_frontend_{chat_id}.json"
    if not os.path.exists(context_file):
        return {"chat_history": []}
    with open(context_file, "r") as file:
        chat_history = json.load(file)
    return {"chat_history": chat_history}

@router.get("/newChatID", include_in_schema=False)
async def create_chat(current_user: models.User = Depends(get_current_user)):
    db = next(get_db())
    chat_id = str(uuid4())
    new_chat = models.active_chats(chat_id=chat_id, title="New Chat", last_chated=datetime.now())
    db.add(new_chat)
    db.commit()
    db.close()
    return {"chat_id": chat_id}



def update_context_frontend(context_file, user_message, llm_response):
    """Update the context with the user's message and the LLM's response."""
    with open(context_file, "r") as file:
        context = json.load(file)
    if len(context["history"]) >= 10:
        context["history"] = context["history"][-9:]
    context["history"].append({"user": user_message, "llm": llm_response})
    with open(context_file, "w") as file:
        json.dump(context, file, indent=4)

@router.post("/chat/{chat_id}", include_in_schema=False)
async def chat_endpoint(chat_id: UUID, prompt: str, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    active_chat = db.query(models.active_chats).filter(models.active_chats.chat_id == chat_id).first()
    if not active_chat:
        raise HTTPException(status_code=404, detail="Chat not found")
    merchants = db.query(models.Merchant).all()
    merchants_data = [{
        'mer_id': m.id,
        'mer_onboarding_timestamp': m.onboarding_date,
        'mer_incorporation_date': m.incorporation_date,
        'mer_first_txn_date': m.first_txn_date,
        'mer_lst_txn_date': m.last_txn_date,
        'mer_industry': m.industry,
        'mer_industry_mca': m.mca_description,
        'mer_fraud_flag': bool(m.fraud_flag) if m.fraud_flag is not None else False,
        'mer_avg_txn_size': float(m.avg_txn_size) if m.avg_txn_size else 0.0,
        'mer_total_txn': int(m.total_txn) if m.total_txn else 0,
        'mer_total_txn_fy': int(m.total_txn_fy) if m.total_txn_fy else 0,
        'mer_gst_risk_flag': m.gst_risk_flag,
        'mer_mca_fillings_risk_flag': m.mca_fillings_risk_flag,
        'mer_directors_risk_flag': m.directors_risk_flag,
        'mer_num_employees': m.num_employees,
        'mer_epfo_reg_status': m.epfo_reg_status,
        'mer_is_sanctioned': m.is_sanctioned,
        'mer_is_online_business': m.is_online_business,
        'mer_online_presence_flag': m.online_presence_flag,
        'mer_tax_irregularity_flag': m.tax_irregularity_flag,
        'mer_is_pan_compatible': m.is_pan_compatible,
        'mer_is_address_compatible': m.is_address_compatible,
        'mer_prior_fraud_investigation_flag': m.prior_fraud_investigation_flag,
        'mer_is_MCA_submission_taken': m.is_MCA_submission_taken,
        'mer_udyam_cert_flag': m.udyam_cert_flag
    } for m in merchants]

    print(merchants_data)
    # Get transactions and convert to dict first
    transactions_data = []
    transactions = db.query(models.transactions).all()
    transactions_data = [{ 
        'txn_id': t.id,
        'mer_id': t.merchant_id,
        'business_type': t.merchant_type or '',
        'txn_timestamp': t.timestamp.isoformat() if t.timestamp else None,
        'txn_amount': float(t.amount) if t.amount else 0.0,
        'is_fraud_transaction': bool(t.is_fraud_transaction) if t.is_fraud_transaction is not None else False,
        'cx_id': str(t.cx_id) if t.cx_id else '',
        'cx_ip': t.cx_ip or '',
        'cx_device_id': t.cx_device_id or '',
        'cx_card_number': t.cx_card_number or '',
        'cx_pii_linkage_score': int(t.cx_pii_linkage_score) if t.cx_pii_linkage_score else 0,
        'is_cardholder_name_match': bool(t.is_cardholder_name_match) if t.is_cardholder_name_match is not None else True,
        'is_chargeback': bool(t.is_chargeback) if t.is_chargeback is not None else False,
        'is_cx_international': bool(t.is_cx_international) if t.is_cx_international is not None else False,
        'txn_status': bool(t.status == 'completed') if t.status else False,
        'is_cx_risky': bool(t.is_cx_risky) if t.is_cx_risky is not None else False,
        'invoice_amount': float(t.invoice_amount) if t.invoice_amount else 0.0,
        'is_cancelled': bool(t.is_cancelled) if t.is_cancelled is not None else False,
        'txn_currency': t.txn_currency or 'INR',
        'has_cx_complaint': bool(t.has_cx_complaint) if t.has_cx_complaint is not None else False,
    } for t in transactions]

    investigations = db.query(models.investigations).all()
    investigations_data = [{
        'investigation_id': i.investigation_id,
        'case_number': i.case_number,
        'investigation_status': i.status,
        'title': i.title,
        'description': i.description,
        'priority': i.priority,
        'assignee_Name': i.assignee_Name,
        'assignee_Email': i.assignee_Email,
        'mer_id': i.merchant_id,
        'merchant_name': i.merchant_name,
        'last_updated': i.last_updated,
        'sla_deadline': i.sla_deadline,
    } for i in investigations]
    print(investigations_data)
    print(transactions_data)
    # Create DataFrames from the dictionaries
    investigations_df = pd.DataFrame(investigations_data)
    transactions_df = pd.DataFrame(transactions_data)
    merchants_df = pd.DataFrame(merchants_data)
    print(transactions_df)   
    print(merchants_df)
    
    # Create the directory if it doesn't exist
    os.makedirs('.ares/data', exist_ok=True)
    context_file = f".ares/data/context_frontend_{chat_id}.json"
    if not os.path.exists(context_file):
        with open(context_file, "w") as file:
            json.dump({"history": []}, file)
    
    # Save to csv 
    investigations_df.to_csv('.ares/data/investigations.csv', index=False)
    transactions_df.to_csv('.ares/data/transactions.csv', index=False)
    merchants_df.to_csv('.ares/data/merchants.csv', index=False)
    
    # Verify the data was saved correctly
    saved_df = pd.read_csv('.ares/data/transactions.csv')

    try:
        if(prompt == "hi"):
            update_context_frontend(context_file, prompt, "default response " + str(datetime.now()))
            return {"response": "default response " + str(datetime.now()) }
        response = use_it(chat_id, prompt)
        if response is None:
            response = "No response generated"
        if active_chat.title == "New Chat":
            client = Groq(
                api_key=os.environ.get("GROQ_API_KEY3"),
            )
            chat_completion = client.chat.completions.create(
                messages=[
                    {
                        "role": "system",
                        "content": "You are a title generator. Respond with only the title text, no other words or punctuation. Keep titles to 5 words or less."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                model="llama3-8b-8192",
            )
            active_chat.title = chat_completion.choices[0].message.content
            db.commit()
        active_chat.last_chated = datetime.now()
        db.commit()
        return {"response": str(response)}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@router.get("/merchantIDs")
async def get_merchant_ids(db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    merchants = db.query(models.Merchant).all()
    #please return all list of merchant ids
    #send the merchant id in the response and lebalname for each merchant
    return [{"id": str(merchant.id), "legalName": merchant.legal_name} for merchant in merchants]

@router.get("/rules")
async def get_rules(db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    rules = db.query(models.Rules).first()
    return {
        "max_cx_pii_score": rules.max_cx_pii_score,
        "max_txn_amt_avg": rules.max_txn_amt_avg,
        "max_cancelled_txn_cnt_pct": rules.max_cancelled_txn_cnt_pct,
        "max_card_num_density": rules.max_card_num_density,
        "max_curr_diversity_score": rules.max_curr_diversity_score,
        "max_customer_density": rules.max_customer_density,
        "max_cx_complaint_txn_pct": rules.max_cx_complaint_txn_pct,
        "max_day_cos": rules.max_day_cos,
        "max_day_sin": rules.max_day_sin,
        "max_device_id_density": rules.max_device_id_density,
        "max_failed_txn_cnt_pct": rules.max_failed_txn_cnt_pct,
        "max_hour_cos": rules.max_hour_cos,
        "max_hour_sin": rules.max_hour_sin,
        "max_hrs_since_last_transaction": rules.max_hrs_since_last_transaction,
        "max_interntational_txn_cnt_pct": rules.max_interntational_txn_cnt_pct,
        "max_invoice_and_txn_amt_diff_pct": rules.max_invoice_and_txn_amt_diff_pct,
        "max_ip_density": rules.max_ip_density,
        "max_late_night_txn_amt_avg": rules.max_late_night_txn_amt_avg,
        "max_late_night_txn_cnt": rules.max_late_night_txn_cnt,
        "max_month_cos": rules.max_month_cos,
        "max_month_sin": rules.max_month_sin,
        "max_num_distinct_currency_used": rules.max_num_distinct_currency_used,
        "max_chargeback_txn_cnt_pct": rules.max_chargeback_txn_cnt_pct,
        "max_name_mismatch_txn_cnt_pct": rules.max_name_mismatch_txn_cnt_pct,
        "max_risky_cx_txn_cnt_pct": rules.max_risky_cx_txn_cnt_pct,
        "max_round_txn_cnt_pct": rules.max_round_txn_cnt_pct,
        "max_txn_cnt": rules.max_txn_cnt,
        "max_txn_amt_sum": rules.max_txn_amt_sum,
        "max_velocity_transaction": rules.max_velocity_transaction
    }





@router.post("/", response_model=request_models.Response)
async def create_merchant(merchant: request_models.MerchantCreate, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    # Create merchant
    db_merchant = models.Merchant(**merchant.dict(exclude={'contacts', 'banking'}))
    db.add(db_merchant)
    db.flush()

    # Create contacts
    for contact in merchant.contacts:
        db_contact = models.MerchantContact(**contact.dict(), merchant_id=db_merchant.id)
        db.add(db_contact)

    # Create banking
    db_banking = models.MerchantBanking(**merchant.banking.dict(), merchant_id=db_merchant.id)
    db.add(db_banking)

    db.commit()
    return {
        "status": "success",
        "message": "Merchant created successfully",
        "data": {"merchant_id": str(db_merchant.id)}
    }

# search merchant by merchant id
@router.get("/search/{merchant_id}")
async def search_merchant(merchant_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    return {"success"}

# this is the merchant profile page
@router.get("/{merchant_id}")
async def get_merchant_profile(merchant_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        # Get the latest contact
        contact = db.query(models.contacts).filter(models.contacts.merchant_id == merchant_id).order_by(models.contacts.created_at.desc()).all()
        
        # Get online presence
        online_presence = db.query(models.online_presence).filter(models.online_presence.merchant_id == merchant_id).order_by(models.online_presence.created_at.desc()).all()

        # Get compliance documents
        compliance_docs = db.query(models.compliance_docs).filter(models.compliance_docs.merchant_id == merchant_id).order_by(models.compliance_docs.created_at.desc()).all()

        # Get latest processing metrics
        financial_metrics = db.query(models.financial_metrics).filter(models.financial_metrics.merchant_id == merchant_id).order_by(models.financial_metrics.created_at.desc()).all()


        return {
            "basicInfo": {
                "legalName": merchant.legal_name,
                "tradeName": merchant.trade_name,
                "domain": merchant.domain,
                "businessType": merchant.business_type,
                "incorporationDate": merchant.incorporation_date.isoformat(),
                "industry": merchant.industry,
                "description": merchant.description,
                "mcaDescription": merchant.mca_description,
                "businessCategory": merchant.business_category,
                "businessSubcategory": merchant.business_subcategory,
                "businessModel": merchant.business_model,
                "onboardingDate": merchant.onboarding_date.isoformat(),
                "onboardingPlatform": merchant.onboarding_platform,
                "kycVerificationStatus": merchant.kyc_verification_status,
                "kycVerificationDate": merchant.kyc_verification_date.isoformat() if merchant.kyc_verification_date else None,
            },
            "contacts": contact,
            "onlinePresence": online_presence,
            "documents": compliance_docs,
            "financial":  financial_metrics
        }
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid merchant ID format")
    

@router.get("/{merchant_id}/summary")
async def get_merchant_summary(merchant_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    #check if the merchant exist 
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    summary = merchant.summery  
    return {"summary": summary}
    
@router.get("/{merchant_id}/compliance")
async def get_merchant_compliance(merchant_id: UUID, db: Session = Depends(get_db), current_user: models.User = Depends(get_current_user)):
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    
    compliance_docs = db.query(models.compliance_docs).filter(models.compliance_docs.merchant_id == merchant_id).order_by(models.compliance_docs.created_at.desc()).all()

    return { "compliance_docs": compliance_docs,
             "notes": "this data is already present in the merchant profile api" }

# @router.get("/{merchant_id}/financials")
# async def get_merchant_financials(merchant_id: UUID, db: Session = Depends(get_db)):
#     merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
#     if not merchant:
#         raise HTTPException(status_code=404, detail="Merchant not found")
    
#     banking = db.query(models.banking).filter(models.banking.merchant_id == merchant_id).order_by(models.banking.created_at.desc()).all()
    
#     return {
#         "settlementDetails": {
#             "bankAccount": {
#                 "accountNumber": banking.account_number if banking else None,
#                 "ifsc": banking.ifsc if banking else None,
#                 "bankName": banking.bank_name if banking else None,
#                 "accountType": banking.account_type if banking else None
#             },
#             "settlementCycle": banking.settlement_cycle if banking else None,
#             "rollingReserve": str(banking.rolling_reserve_percentage) if banking else None,
#             "currentBalance": str(banking.current_balance) if banking else None
#         },
#         "revenueMetrics": {
#             "monthlyTrend": [
#                 {
#                     "month": trend.month.isoformat(),
#                     "revenue": str(trend.revenue)
#                 }
#                 for trend in merchant.revenue_trends
#             ],
#             "peakHours": merchant.revenue_trends[0].peak_hours if merchant.revenue_trends else None,
#             "seasonalPeaks": merchant.revenue_trends[0].seasonal_peaks if merchant.revenue_trends else None
#         },
#         "riskMetrics": {
#             "overallScore": merchant.risk_metrics[0].overall_score if merchant.risk_metrics else None,
#             "riskIndicators": [
#                 {
#                     "type": metric.indicator_type,
#                     "frequency": metric.frequency,
#                     "severity": metric.severity,
#                     "status": metric.status
#                 }
#                 for metric in merchant.risk_metrics
#             ]
#         }
#     }

@router.get("/{merchant_id}/transactions")
async def get_merchant_transactions(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        transactions = db.query(models.transactions).filter(models.transactions.merchant_id == merchant_id).order_by(models.transactions.created_at.desc()).all()
        return { "transactions": transactions }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{merchant_id}/payment-channels")
async def get_merchant_payment_channels(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")

    payment_channels = db.query(models.payment_channels).filter(models.payment_channels.merchant_id == merchant_id).order_by(models.payment_channels.created_at.desc()).all()

    return { "payment_channels": payment_channels }


@router.get("/{merchant_id}/payouts")
async def get_merchant_payouts(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")
        payouts = db.query(models.payouts).filter(models.payouts.merchant_id == merchant_id).all()
        return { "payouts": payouts }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{merchant_id}/communications")
async def get_merchant_communications(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        communications1 = db.query(models.communications).filter(models.communications.sender_id == merchant_id).all()
        communications2 = db.query(models.communications).filter(models.communications.receiver_id == merchant_id).all()
        communications = communications1 + communications2

        return {"communications": communications}  # Return as a dictionary with a key
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{merchant_id}/timeline", response_model=TimelineEventListResponse)
async def get_merchant_timeline(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        events = db.query(models.timeline_events).filter(models.timeline_events.merchant_id == merchant_id).order_by(models.timeline_events.time.desc()).all()

        return { "events": events }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/{merchant_id}/documents-uploaded")
async def get_merchant_documents_uploaded(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    documents_uploaded = db.query(models.documents_uploaded).filter(models.documents_uploaded.merchant_id == merchant_id).order_by(models.documents_uploaded.created_at.desc()).all()
    # Get merchant email from contacts
    merchant_contact = db.query(models.contacts).filter(models.contacts.merchant_id == merchant_id).first()
    if not merchant_contact:
        raise HTTPException(status_code=404, detail="Merchant contact not found")
    merchant_email = merchant_contact.email

    # Get all email communications for this merchant
    email_communications = db.query(models.email_communication).filter(
        # Using OR condition with | operator for sender/receiver
        ((models.email_communication.receiver == merchant_email) |
         (models.email_communication.sender == merchant_email))
    ).order_by(models.email_communication.created_at.desc()).all()

    # Get all documents from emails for this merchant
    documents_from_emails = db.query(models.documents_from_email).filter(
        models.documents_from_email.email_id.in_([email.id for email in email_communications])
    ).order_by(models.documents_from_email.created_at.desc()).all()

    return {
        "documents_uploaded": documents_uploaded,
        "documents_from_email": documents_from_emails,
    }

@router.get("/{merchant_id}/devices-used")
async def get_merchant_devices_used(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    devices_used = db.query(models.devices_used).filter(models.devices_used.merchant_id == merchant_id).order_by(models.devices_used.created_at.desc()).all()
    return { "devices_used": devices_used }

@router.get("/{merchant_id}/risk-assessment")
async def get_merchant_risk_assessment(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Check if merchant exists
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        risk_assessment = db.query(models.risk_assessments).filter(models.risk_assessments.merchant_id == merchant_id).first()
        risk_categories = db.query(models.risk_categories).filter(models.risk_categories.merchant_id == merchant_id).all()
        risk_indicators = db.query(models.risk_indicators).filter(models.risk_indicators.merchant_id == merchant_id).all()
        
        # Get monitoring data for overall distribution

        data = {
            "risk_score": risk_assessment.risk_score,
            "overall": {
                "percentile": risk_assessment.percentile,
                "max_score": risk_assessment.risk_score_max,
                "quartiles": {
                    "q0": 0,
                    "q1": risk_assessment.risk_score_025_rank,
                    "q2": risk_assessment.risk_score_050_rank,
                    "q3": risk_assessment.risk_score_075_rank,
                }
            },
            "business_category": {
                "percentile": risk_assessment.percentile_business_category,
                "max_score": risk_assessment.risk_score_max_business_category,
                "quartiles": {
                    "q0": 0,
                    "q1": risk_assessment.risk_score_business_category_025_rank,
                    "q2": risk_assessment.risk_score_business_category_050_rank,
                    "q3": risk_assessment.risk_score_business_category_075_rank,
                }
            },
            "risk_level": risk_assessment.risk_level,
            "description": risk_assessment.description,
            "categories": []
        }
        for category in risk_categories:
            category_data = {
                "title": category.category,
                "score": category.score,
                "description": category.description,
                "indicators": []
            }
            for indicator in risk_indicators:
                if indicator.category == category.category:
                    indicator_data = {
                        "label": indicator.indicator_label,
                        "value": indicator.indicator_value,
                        "severity": indicator.severity
                    }
                    category_data["indicators"].append(indicator_data)
            data["categories"].append(category_data)
        return {"data": data}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail="An error occurred while retrieving risk assessment : " + str(e)
        )

@router.get("/{merchant_id}/generate-flags-and-store-in-db")
async def get_merchant_flags(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")
        
        # Get transactions and convert to dict first
        
        
        # Get merchants and convert to dict first
        merchants = db.query(models.Merchant).all()
        merchants_data = [{
            'mer_id': m.id,
            'mer_onboarding_timestamp': m.onboarding_date,
            'mer_incorporation_date': m.incorporation_date,
            'mer_first_txn_date': m.first_txn_date,
            'mer_lst_txn_date': m.last_txn_date,
            'mer_industry': m.industry,
            'mer_business_industry': m.industry,
            'mer_industry_mca': m.mca_description,
            'mer_fraud_flag': bool(m.fraud_flag) if m.fraud_flag is not None else False,
            'mer_avg_txn_size': float(m.avg_txn_size) if m.avg_txn_size else 0.0,
            'mer_total_txn': int(m.total_txn) if m.total_txn else 0,
            'mer_total_txn_fy': int(m.total_txn_fy) if m.total_txn_fy else 0,
            'mer_gst_risk_flag': m.gst_risk_flag,
            'mer_mca_fillings_risk_flag': m.mca_fillings_risk_flag,
            'mer_directors_risk_flag': m.directors_risk_flag,
            'mer_num_employees': m.num_employees,
            'mer_epfo_reg_status': m.epfo_reg_status,
            'mer_is_sanctioned': m.is_sanctioned,
            'mer_is_online_business': m.is_online_business,
            'mer_online_presence_flag': m.online_presence_flag,
            'mer_tax_irregularity_flag': m.tax_irregularity_flag,
            'mer_is_pan_compatible': m.is_pan_compatible,
            'mer_is_address_compatible': m.is_address_compatible,
            'mer_prior_fraud_investigation_flag': m.prior_fraud_investigation_flag,
            'mer_is_MCA_submission_taken': m.is_MCA_submission_taken,
            'mer_udyam_cert_flag': m.udyam_cert_flag
        } for m in merchants]

        print(merchants_data)
        # Get transactions and convert to dict first
        transactions_data = []
        transactions = db.query(models.transactions).all()
        transactions_data = [{ 
            'txn_id': t.id,
            'mer_id': t.merchant_id,
            'business_type': t.merchant_type or '',
            'mer_business_industry': t.merchant_type or '',
            'txn_timestamp': t.timestamp.isoformat() if t.timestamp else None,
            'txn_amount': float(t.amount) if t.amount else 0.0,
            'is_fraud_transaction': bool(t.is_fraud_transaction) if t.is_fraud_transaction is not None else False,
            'cx_id': str(t.cx_id) if t.cx_id else '',
            'cx_ip': t.cx_ip or '',
            'cx_device_id': t.cx_device_id or '',
            'cx_card_number': t.cx_card_number or '',
            'cx_pii_linkage_score': int(t.cx_pii_linkage_score) if t.cx_pii_linkage_score else 0,
            'is_cardholder_name_match': bool(t.is_cardholder_name_match) if t.is_cardholder_name_match is not None else True,
            'is_chargeback': bool(t.is_chargeback) if t.is_chargeback is not None else False,
            'is_cx_international': bool(t.is_cx_international) if t.is_cx_international is not None else False,
            'txn_status': bool(t.status == 'completed') if t.status else False,
            'is_cx_risky': bool(t.is_cx_risky) if t.is_cx_risky is not None else False,
            'invoice_amount': float(t.invoice_amount) if t.invoice_amount else 0.0,
            'is_cancelled': bool(t.is_cancelled) if t.is_cancelled is not None else False,
            'txn_currency': t.txn_currency or 'INR',
            'has_cx_complaint': bool(t.has_cx_complaint) if t.has_cx_complaint is not None else False,
        } for t in transactions]

        print(transactions_data)
        # Create DataFrames from the dictionaries
        transactions_df = pd.DataFrame(transactions_data)
        merchants_df = pd.DataFrame(merchants_data)
        print(transactions_df)   
        print(merchants_df)
        
        # # Create the directory if it doesn't exist
        os.makedirs('.ares/data', exist_ok=True)
        
        # # Save to csv 
        transactions_df.to_csv('.ares/data/transactions.csv', index=False)
        merchants_df.to_csv('.ares/data/merchants.csv', index=False)
        print("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++=")
        # # Verify the data was saved correctly
        saved_df = pd.read_csv('.ares/data/transactions.csv')
        if saved_df.empty:
            print("Warning: Saved transactions DataFrame is empty ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
        else:
            print("Data saved successfully")
        main()
        print("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++=")
        data_flags = run_this_please(merchant_id)
        print(data_flags)
        for d in data_flags:
            print(d)
        print("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++=")
        print(data_flags)
        db.query(models.flags).filter(models.flags.merchant_id == merchant_id).filter(models.flags.flag_type == 'transactions risk for ML').delete()
        data = []
        LLM_rules = db.query(rule_repo.Rules).filter(rule_repo.Rules.LLM_bool == True).all()
        for s in data_flags:
            i = 0
            for rule in LLM_rules:
                if rule.LLM_string_sample == s.metric:
                    i = 1
                    break
            if(i): 
                continue
            data.append({
                "flag_type": "transactions ML",
                "severity": "high",
                "text": s.description,
                "importance": s.importance,
                "timestamp": datetime.now().isoformat()
            })
        flags = db.query(models.flags).filter(models.flags.merchant_id == merchant_id).all()
        for flag in flags:
            data.append({
                "flag_type": flag.flag_type,
                "severity": flag.severity,
                "text": flag.text,
                "importance": flag.importance,
                "timestamp": flag.timestamp.isoformat()
            })
        for s in data_flags:
            i = 0
            for rule in LLM_rules:
                if rule.LLM_string_sample == s.metric:
                    i = 1
                    break
            if(i): 
                continue
            db.add(models.flags(
                merchant_id=merchant_id,
                flag_type="transactions ML",
                severity="high",
                text=s.description,
                importance=s.importance,
                timestamp=datetime.now()
            ))
        db.commit()
        return { "flags": data }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/{merchant_id}/flags")
async def get_merchant_flags(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    #check if merchant exists
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    
    #get flags
    flags = db.query(models.flags).filter(models.flags.merchant_id == merchant_id).all()
    return { "flags": flags }

@router.get("/{merchant_id}/key-metrics")
async def get_merchant_key_metrics(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        metrics = db.query(models.key_metrics).filter(models.key_metrics.merchant_id == merchant_id).all()

        return { "key_metrics": metrics }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/{merchant_id}/linkages")
async def get_merchant_linkage(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        # Get network overview
        overview = db.query(models.network_overview).filter(models.network_overview.merchant_id == merchant_id).first()

        # Get first degree linked entities
        first_degree_entities = db.query(models.related).filter(models.related.merchant_id == merchant_id).all()
        first_degree_ids = [entity.related_entity_id for entity in first_degree_entities]

        # Get second degree linked entities and paths
        second_degree_entities = []
        second_degree_ids = []
        second_degree_paths = {}  # Store paths from main merchant to second degree nodes
        for first_id in first_degree_ids:
            second_links = db.query(models.related).filter(
                models.related.merchant_id == first_id,
                models.related.related_entity_id.notin_(first_degree_ids + [merchant_id])
            ).all()
            for link in second_links:
                second_degree_entities.append(link)
                second_degree_ids.append(link.related_entity_id)
                # Store path: main -> first degree -> second degree
                second_degree_paths[str(link.related_entity_id)] = {
                    "path": [merchant_id, first_id, link.related_entity_id],
                    "relationship_types": [
                        next(e.relationship_type for e in first_degree_entities if e.related_entity_id == first_id),
                        link.relationship_type
                    ]
                }

        # Get third degree linked entities and paths
        third_degree_entities = []
        third_degree_ids = []
        third_degree_paths = {}  # Store paths from main merchant to third degree nodes
        for second_id in second_degree_ids:
            third_links = db.query(models.related).filter(
                models.related.merchant_id == second_id,
                models.related.related_entity_id.notin_(first_degree_ids + second_degree_ids + [merchant_id])
            ).all()
            for link in third_links:
                third_degree_entities.append(link)
                third_degree_ids.append(link.related_entity_id)
                # Get path to second degree node
                second_path = second_degree_paths[str(second_id)]
                # Extend path to third degree node
                third_degree_paths[str(link.related_entity_id)] = {
                    "path": second_path["path"] + [link.related_entity_id],
                    "relationship_types": second_path["relationship_types"] + [link.relationship_type]
                }

        # Build adjacency list representation
        adjacency_list = {}
        
        # Add first degree connections
        for entity in first_degree_entities:
            if str(merchant_id) not in adjacency_list:
                adjacency_list[str(merchant_id)] = []
            adjacency_list[str(merchant_id)].append({
                "node": str(entity.related_entity_id),
                "relationship_type": entity.relationship_type
            })

        # Add second degree connections
        for entity in second_degree_entities:
            source_id = str(entity.merchant_id)
            if source_id not in adjacency_list:
                adjacency_list[source_id] = []
            adjacency_list[source_id].append({
                "node": str(entity.related_entity_id),
                "relationship_type": entity.relationship_type
            })

        # Add third degree connections
        for entity in third_degree_entities:
            source_id = str(entity.merchant_id)
            if source_id not in adjacency_list:
                adjacency_list[source_id] = []
            adjacency_list[source_id].append({
                "node": str(entity.related_entity_id),
                "relationship_type": entity.relationship_type
            })

        # Get common connections
        connections_dict = {
            "phone": {},
            "mobile": {},
            "email": {},
            "address": {}
        }

        for entity in first_degree_entities:
            contact_details = db.query(models.contacts).filter(models.contacts.merchant_id == entity.related_entity_id).first()
            if not contact_details:
                continue

            related_merchant = db.query(models.Merchant).filter(models.Merchant.id == entity.related_entity_id).first()
            merchant_name = related_merchant.legal_name if related_merchant else "Unknown"

            if(entity.relationship_type == "SAME_PHONE" and contact_details.phone):
                if contact_details.phone not in connections_dict["phone"]:
                    connections_dict["phone"][contact_details.phone] = []
                connections_dict["phone"][contact_details.phone].append(merchant_name)

            if(entity.relationship_type == "SAME_MOBILE" and contact_details.mobile):
                if contact_details.mobile not in connections_dict["mobile"]:
                    connections_dict["mobile"][contact_details.mobile] = []
                connections_dict["mobile"][contact_details.mobile].append(merchant_name)

            if(entity.relationship_type == "SAME_EMAIL" and contact_details.email):
                if contact_details.email not in connections_dict["email"]:
                    connections_dict["email"][contact_details.email] = []
                connections_dict["email"][contact_details.email].append(merchant_name)

            if(entity.relationship_type == "SAME_ADDRESS" and contact_details.registeredAddress):
                if contact_details.registeredAddress not in connections_dict["address"]:
                    connections_dict["address"][contact_details.registeredAddress] = []
                connections_dict["address"][contact_details.registeredAddress].append(merchant_name)

        connections = []
        for conn_type, values in connections_dict.items():
            for value, shared_with in values.items():
                connections.append({
                    "connection_type": conn_type,
                    "connection_value": value,
                    "shared_with": shared_with
                })

        return {
            "networkOverview": overview,
            "firstDegreeCommunity": {
                "entities": first_degree_entities,
                "edges": adjacency_list.get(str(merchant_id), [])
            },
            "secondDegreeCommunity": {
                "entities": second_degree_entities,
                "edges": [adjacency_list.get(str(id), []) for id in first_degree_ids],
                "paths": second_degree_paths
            },
            "thirdDegreeCommunity": {
                "entities": third_degree_entities,
                "edges": [adjacency_list.get(str(id), []) for id in second_degree_ids],
                "paths": third_degree_paths
            },
            "commonConnections": connections,
            "adjacencyList": adjacency_list
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@router.get("/{merchant_id}/one-degree-community") 
async def get_merchant_one_degree_community(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    include_phone_connections: bool = True,
    include_email_connections: bool = True,
    include_address_connections: bool = True,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        # Get first degree entities and their contact details
        first_degree_entities = db.query(models.related).filter(models.related.merchant_id == merchant_id).all()
        first_degree_ids = [entity.related_entity_id for entity in first_degree_entities]

        # Initialize nodes dict and counter for sequential IDs
        nodes = {}
        next_node_id = 1

        # Helper function to get next sequential ID
        def get_next_id():
            nonlocal next_node_id
            current_id = next_node_id
            next_node_id += 1
            return str(current_id)

        # Add main merchant node
        merchant_contact = db.query(models.contacts).filter(models.contacts.merchant_id == merchant_id).first()
        main_merchant_node_id = get_next_id()
        nodes[main_merchant_node_id] = {
            "node_id": main_merchant_node_id,
            "node_type": "merchant",
            "node_value": merchant.legal_name,
            "list_connected_node_ids": []
        }

        # Helper function to add/update connection nodes
        def add_connection_node(node_type, node_value, merchant_node_id):
            # Check if this value already exists as a node
            existing_node = next((node_id for node_id, node in nodes.items() 
                                if node["node_type"] == node_type and node["node_value"] == node_value), None)
            
            if existing_node is None:
                new_node_id = get_next_id()
                nodes[new_node_id] = {
                    "node_id": new_node_id,
                    "node_type": node_type,
                    "node_value": node_value,
                    "list_connected_node_ids": [merchant_node_id]
                }
                nodes[merchant_node_id]["list_connected_node_ids"].append(new_node_id)
            else:
                if merchant_node_id not in nodes[existing_node]["list_connected_node_ids"]:
                    nodes[existing_node]["list_connected_node_ids"].append(merchant_node_id)
                if existing_node not in nodes[merchant_node_id]["list_connected_node_ids"]:
                    nodes[merchant_node_id]["list_connected_node_ids"].append(existing_node)

        # Process main merchant's contact details
        if merchant_contact:
            if merchant_contact.phone and include_phone_connections:
                add_connection_node("phone", merchant_contact.phone, main_merchant_node_id)
            
            if merchant_contact.email and include_email_connections:
                add_connection_node("email", merchant_contact.email, main_merchant_node_id)
                
            if merchant_contact.registeredAddress and include_address_connections:
                add_connection_node("address", merchant_contact.registeredAddress, main_merchant_node_id)

            if merchant_contact.operatingAddress and include_address_connections:
                add_connection_node("address", merchant_contact.operatingAddress, main_merchant_node_id)

        # Process first degree entities
        for entity in first_degree_entities:
            related_merchant = db.query(models.Merchant).filter(models.Merchant.id == entity.related_entity_id).first()
            related_contact = db.query(models.contacts).filter(models.contacts.merchant_id == entity.related_entity_id).first()
            
            # Add related merchant node
            related_merchant_node_id = get_next_id()
            nodes[related_merchant_node_id] = {
                "node_id": related_merchant_node_id,
                "node_type": "merchant",
                "node_value": related_merchant.legal_name,
                "list_connected_node_ids": []
            }

            if related_contact:
                # Process phone connections
                if related_contact.phone and include_phone_connections:
                    add_connection_node("phone", related_contact.phone, related_merchant_node_id)

                # Process email connections  
                if related_contact.email and include_email_connections:
                    add_connection_node("email", related_contact.email, related_merchant_node_id)

                # Process address connections
                if related_contact.registeredAddress and include_address_connections:
                    add_connection_node("address", related_contact.registeredAddress, related_merchant_node_id)

                if related_contact.operatingAddress and include_address_connections:
                    add_connection_node("address", related_contact.operatingAddress, related_merchant_node_id)

        return {
            "nodes": list(nodes.values())
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/{merchant_id}/two-degree-community")
async def get_merchant_two_degree_community(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    include_phone_connections: bool = True,
    include_email_connections: bool = True,
    include_address_connections: bool = True,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try: 
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")  
    
        #get first degree entities
        first_degree_entities = db.query(models.related).filter(models.related.merchant_id == merchant_id).all()
        first_degree_ids = list(set([entity.related_entity_id for entity in first_degree_entities]))

        #get second degree entities
        second_degree_entities = db.query(models.related).filter(models.related.merchant_id.in_(first_degree_ids)).all()
        second_degree_ids = list(set(entity.related_entity_id for entity in second_degree_entities))

        all_ids = list(set(first_degree_ids + second_degree_ids))
        all_entites = first_degree_entities + second_degree_entities

        nodes = {}
        next_node_id = 1 

        def get_next_id():
            nonlocal next_node_id
            current_id = next_node_id
            next_node_id += 1
            return str(current_id)

        merchant_contact = db.query(models.contacts).filter(models.contacts.merchant_id == merchant_id).first()
        main_merchant_node_id = get_next_id()
        nodes[main_merchant_node_id] = {
            "node_id": main_merchant_node_id,
            "node_type": "merchant",
            "node_value": merchant.legal_name,
            "list_connected_node_ids": []
        }

        def add_connection_node(node_type, node_value, merchant_node_id):
            existing_node = next((node_id for node_id, node in nodes.items() 
                                if node["node_type"] == node_type and node["node_value"] == node_value), None)
            if existing_node is None:
                new_node_id = get_next_id()
                nodes[new_node_id] = {
                    "node_id": new_node_id,
                    "node_type": node_type,
                    "node_value": node_value,
                    "list_connected_node_ids": [merchant_node_id]
                }
                nodes[merchant_node_id]["list_connected_node_ids"].append(new_node_id)
            else:
                if merchant_node_id not in nodes[existing_node]["list_connected_node_ids"]:
                    nodes[existing_node]["list_connected_node_ids"].append(merchant_node_id)
                if existing_node not in nodes[merchant_node_id]["list_connected_node_ids"]:
                    nodes[merchant_node_id]["list_connected_node_ids"].append(existing_node)    
        
        # Process main merchant's contact details
        if merchant_contact:
            if merchant_contact.phone and include_phone_connections:
                add_connection_node("phone", merchant_contact.phone, main_merchant_node_id)
            
            if merchant_contact.email and include_email_connections:
                add_connection_node("email", merchant_contact.email, main_merchant_node_id)
                
            if merchant_contact.registeredAddress and include_address_connections:
                add_connection_node("address", merchant_contact.registeredAddress, main_merchant_node_id)

            if merchant_contact.operatingAddress and include_address_connections:
                add_connection_node("address", merchant_contact.operatingAddress, main_merchant_node_id)

        for entity in all_entites:
            related_merchant = db.query(models.Merchant).filter(models.Merchant.id == entity.related_entity_id).first()
            related_contact = db.query(models.contacts).filter(models.contacts.merchant_id == entity.related_entity_id).first()

            related_merchant_node_id = get_next_id()
            nodes[related_merchant_node_id] = {
                "node_id": related_merchant_node_id,
                "node_type": "merchant",
                "node_value": related_merchant.legal_name,
                "list_connected_node_ids": []
            }

            if related_contact:
                if related_contact.phone and include_phone_connections:
                    add_connection_node("phone", related_contact.phone, related_merchant_node_id)
                
                if related_contact.email and include_email_connections:
                    add_connection_node("email", related_contact.email, related_merchant_node_id)
                
                if related_contact.registeredAddress and include_address_connections:
                    add_connection_node("address", related_contact.registeredAddress, related_merchant_node_id)

                if related_contact.operatingAddress and include_address_connections:
                    add_connection_node("address", related_contact.operatingAddress, related_merchant_node_id)

        return {
            "nodes": list(nodes.values())
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/{merchant_id}/three-degree-community")
async def get_merchant_three_degree_community(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    include_phone_connections: bool = True,
    include_email_connections: bool = True,
    include_address_connections: bool = True,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try: 
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")
        
        #get first degree entities
        first_degree_entities = db.query(models.related).filter(models.related.merchant_id == merchant_id).all()
        first_degree_ids = list(set([entity.related_entity_id for entity in first_degree_entities]))

        #get second degree entities
        second_degree_entities = db.query(models.related).filter(models.related.merchant_id.in_(first_degree_ids)).all()
        second_degree_ids = list(set(entity.related_entity_id for entity in second_degree_entities))

        #get third degree entities
        third_degree_entities = db.query(models.related).filter(models.related.merchant_id.in_(second_degree_ids)).all()
        third_degree_ids = list(set(entity.related_entity_id for entity in third_degree_entities))

        all_ids = list(set(first_degree_ids + second_degree_ids + third_degree_ids))
        all_entites = first_degree_entities + second_degree_entities + third_degree_entities

        nodes = {}
        next_node_id = 1

        def get_next_id():
            nonlocal next_node_id
            current_id = next_node_id
            next_node_id += 1
            return str(current_id)
        
        merchant_contact = db.query(models.contacts).filter(models.contacts.merchant_id == merchant_id).first()
        main_merchant_node_id = get_next_id()
        nodes[main_merchant_node_id] = {
            "node_id": main_merchant_node_id,
            "node_type": "merchant",
            "node_value": merchant.legal_name,
            "list_connected_node_ids": []
        }

        # Helper function to add/update connection nodes
        def add_connection_node(node_type, node_value, merchant_node_id):
            # Check if this value already exists as a node
            existing_node = next((node_id for node_id, node in nodes.items() 
                                if node["node_type"] == node_type and node["node_value"] == node_value), None)
            
            if existing_node is None:
                new_node_id = get_next_id()
                nodes[new_node_id] = {
                    "node_id": new_node_id,
                    "node_type": node_type,
                    "node_value": node_value,
                    "list_connected_node_ids": [merchant_node_id]
                }
                nodes[merchant_node_id]["list_connected_node_ids"].append(new_node_id)
            else:
                if merchant_node_id not in nodes[existing_node]["list_connected_node_ids"]:
                    nodes[existing_node]["list_connected_node_ids"].append(merchant_node_id)
                if existing_node not in nodes[merchant_node_id]["list_connected_node_ids"]:
                    nodes[merchant_node_id]["list_connected_node_ids"].append(existing_node)

        # Process main merchant's contact details
        if merchant_contact:
            if merchant_contact.phone and include_phone_connections:
                add_connection_node("phone", merchant_contact.phone, main_merchant_node_id)
            
            if merchant_contact.email and include_email_connections:
                add_connection_node("email", merchant_contact.email, main_merchant_node_id)
                
            if merchant_contact.registeredAddress and include_address_connections:
                add_connection_node("address", merchant_contact.registeredAddress, main_merchant_node_id)

            if merchant_contact.operatingAddress and include_address_connections:
                add_connection_node("address", merchant_contact.operatingAddress, main_merchant_node_id)

        # Process first degree entities
        for entity in first_degree_entities:
            related_merchant = db.query(models.Merchant).filter(models.Merchant.id == entity.related_entity_id).first()
            related_contact = db.query(models.contacts).filter(models.contacts.merchant_id == entity.related_entity_id).first()
            
            # Add related merchant node
            related_merchant_node_id = get_next_id()
            nodes[related_merchant_node_id] = {
                "node_id": related_merchant_node_id,
                "node_type": "merchant",
                "node_value": related_merchant.legal_name,
                "list_connected_node_ids": []
            }

            if related_contact:
                # Process phone connections
                if related_contact.phone and include_phone_connections:
                    add_connection_node("phone", related_contact.phone, related_merchant_node_id)

                # Process email connections  
                if related_contact.email and include_email_connections:
                    add_connection_node("email", related_contact.email, related_merchant_node_id)

                # Process address connections
                if related_contact.registeredAddress and include_address_connections:
                    add_connection_node("address", related_contact.registeredAddress, related_merchant_node_id)

                if related_contact.operatingAddress and include_address_connections:
                    add_connection_node("address", related_contact.operatingAddress, related_merchant_node_id)

        return {
            "nodes": list(nodes.values())
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{merchant_id}/time-series-merchant-domain-graph")
async def get_merchant_time_series_merchant_domain_graph(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")

    response_time_series = db.query(models.response_time_series).filter(models.response_time_series.merchant_id == merchant_id).all()
    data = []
    for r in response_time_series:
        data.append({
            "status_code": r.status_code,
            "response_time_ms": r.response_time_ms,
            "is_active": r.is_active,
            "timestamp": r.timestamp,
            "error": r.error
        })
    return { "response_time_series": data }

@router.get("/{merchant_id}/digital-informationV2")
async def get_merchant_digital_information_v2(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    
    merchant_id = str(merchant_id)

    # find the latest digital information based on timestamp
    digital_info = db.query(models.AllDFPDataTable).filter(models.AllDFPDataTable.merchant_id == merchant_id).order_by(models.AllDFPDataTable.timestamp.desc()).first()

    # get website monitoring data - latest 1000 entries
    time_series = db.query(models.response_time_series).filter(models.response_time_series.merchant_id == merchant_id).order_by(models.response_time_series.timestamp.desc()).limit(1000).all()
    if not digital_info:
        raise HTTPException(status_code=404, detail="Digital information not found")

    return {
        "success": True,
        "message": "Digital information retrieved successfully",
        "data": {
            "digital_information": digital_info.data,
            "website_monitoring": [
                {
                    "timestamp": ts.timestamp,
                    "status_code": ts.status_code,
                    "response_time_ms": ts.response_time_ms,
                    "is_active": ts.is_active,
                    "error": ts.error
                } for ts in time_series
            ]
        }
    }

# api to give digital footprint
@router.get("/{merchant_id}/digital-footprintv2")
async def get_merchant_digital_footprint_v2(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
    # current_user: models.User = Depends(get_current_user)
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")
        
        merchant_id = str(merchant.id)

        digital_footprint = db.query(models.DigitalFootPrint).filter(models.DigitalFootPrint.merchant_id == merchant_id).order_by(models.DigitalFootPrint.timestamp.desc()).first()
        if not digital_footprint:
            raise HTTPException(status_code=404, detail="Digital footprint not found")
        
        # Get website monitoring data - latest 1000 entries
        time_series = db.query(models.response_time_series).filter(models.response_time_series.merchant_id == merchant_id).order_by(models.response_time_series.timestamp.desc()).limit(1000).all()
        if not time_series:
            raise HTTPException(status_code=404, detail="Website monitoring data not found")
        return {
            "success": True,
            "message": "Digital footprint retrieved successfully",
            "data": {
                "digital_footprint": digital_footprint.data,
                "website_monitoring": [
                    {
                        "timestamp": ts.timestamp,
                        "status_code": ts.status_code,
                        "response_time_ms": ts.response_time_ms,
                        "is_active": ts.is_active,
                        "error": ts.error
                    } for ts in time_series
                ]
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@router.get("/{merchant_id}/digital-information")
async def get_merchant_digital_information(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
        db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")

    digital_info = db.query(models.digital_information).filter(models.digital_information.merchant_id == merchant_id).first()
    time_series = db.query(models.response_time_series).filter(models.response_time_series.merchant_id == merchant_id).order_by(models.response_time_series.timestamp.desc()).limit(1000).all()
    web_keywords = db.query(models.web_keywords).filter(models.web_keywords.merchant_id == merchant_id).all()
    web_contacts = db.query(models.web_contacts).filter(models.web_contacts.merchant_id == merchant_id).all()
    reviews_merchant = db.query(models.reviews_merchant).filter(models.reviews_merchant.merchant_id == merchant_id).all()
    individual_reviews = db.query(models.individual_reviews).filter(models.individual_reviews.merchant_id == merchant_id).all()
    products_and_services = db.query(models.products_and_services).filter(models.products_and_services.merchant_id == merchant_id).all()
    products_and_services_details = db.query(models.products_and_services_details).filter(models.products_and_services_details.merchant_id == merchant_id).all()
    news_fraud_scam_illegal_risk = db.query(models.news_fraud_scam_illegal_risk).filter(models.news_fraud_scam_illegal_risk.merchant_id == merchant_id).all()
    news_incidents = db.query(models.news_incidents).filter(models.news_incidents.merchant_id == merchant_id).all()
    
    data = {
        "domain_information": {
            "domain": digital_info.domain if digital_info else None,
            "server_details": {
                "ip_address": digital_info.server_ip_address if digital_info else None,
                "country": digital_info.server_ip_country if digital_info else None,
                "owner": digital_info.server_ip_owner if digital_info else None
            },
            "domain_details": {
                "age": digital_info.domain_age if digital_info else None,
                "registrar": digital_info.registrar if digital_info else None,
                "owner_email": digital_info.owner_email if digital_info else None,
                "admin_contact": digital_info.admin_contact if digital_info else None,
                "privacy_protection": digital_info.privacy_protection if digital_info else None
            },
            "business_model": digital_info.business_model if digital_info else None
        },
        "website_monitoring": [
            {
                "timestamp": ts.timestamp,
                "status_code": ts.status_code,
                "response_time_ms": ts.response_time_ms,
                "is_active": ts.is_active,
                "error": ts.error
            } for ts in time_series
        ],
        "website_content": {
            "keywords": [
                {
                    "keyword": kw.keyword,
                    "frequency": kw.frequency
                } for kw in web_keywords
            ],
            "contact_information": [
                {
                    "type": contact.contact_type,
                    "value": contact.contact_value
                } for contact in web_contacts
            ]
        },
        "reviews": {
            "summary": [
                {
                    "sentiment": review.sentiment_status,
                    "summary": review.reviews_summary
                } for review in reviews_merchant
            ],
            "detailed_reviews": [
                {
                    "source": review.source_website,
                    "title": review.review_title,
                    "summary": review.review_summary,
                    "content": review.review_content,
                    "url": review.url
                } for review in individual_reviews
            ]
        },
        "products_and_services": {
            "overview": [
                {
                    "description": ps.products_and_services_description_summary
                } for ps in products_and_services
            ],
            "details": [
                {
                    "name": psd.product_and_service,
                    "summary": psd.summary,
                    "price": psd.price,
                    "industry_and_location": psd.industry_and_location
                } for psd in products_and_services_details
            ]
        },
        "risk_and_news": {
            "risk_summary": [
                {
                    "summary": risk.news_fraud_scam_illegal_risk_summary,
                    "sentiment": risk.news_sentiment
                } for risk in news_fraud_scam_illegal_risk
            ],
            "incidents": [
                {
                    "title": incident.incident_title,
                    "summary": incident.summary,
                    "content": incident.content,
                    "time": incident.time_of_upload,
                    "link": incident.link
                } for incident in news_incidents
            ]
        }
    }
    return { "digital_information": data }


@router.get("/{merchant_id}/digital-informationV2")
async def get_merchant_digital_information_v2(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    db: Session = Depends(get_db),
):
    try:
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")

        digital_info = db.query(models.AllDFPDataTable).filter(models.AllDFPDataTable.merchant_id == merchant_id).first()
        if not digital_info:
            raise HTTPException(status_code=404, detail="raw digital information not found")

        return { "digital_information": digital_info }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))