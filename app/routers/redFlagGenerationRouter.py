from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Path, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID, uuid4
from datetime import datetime, date
from ..database import get_db
from ..models import models, rule_repo
from ..schemas import request_models
from ..schemas.response_models import TransactionResponse, TransactionListResponse, PaymentChannelListResponse, PayoutListResponse, CommunicationListResponse, TimelineEventResponse, TimelineEventListResponse, InvestigationResponse, InvestigationListResponse, InvestigationNoteListResponse, KeyMetricsResponse
from collections import defaultdict
from .ares.model.llm_transaction_analysis import run_this_please
from .ares.feature_generation.incremental_feature_gen import main
import pandas as pd
import numpy as np
import os
import json
from .ares.investigation_gpt.gpt_with_context import use_it
from .ares.model.summarizzer import summer_is_mine
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from neo4j import GraphDatabase, exceptions
import time
from transformers import AutoTokenizer, AutoModel
import torch
from sklearn.metrics.pairwise import cosine_similarity
from groq import Groq
import asyncio
import logging
# Initialize logger
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="",
    tags=["Red Flag"]
)

scheduler = BackgroundScheduler()
def calculate_ML_red_flags(merchant_id):
    db = next(get_db())
    try:
        merchants = db.query(models.Merchant).all()

        merchants = db.query(models.Merchant).all()
        merchants_data = [{
            'mer_id': m.id,
            'mer_onboarding_timestamp': m.onboarding_date,
            'mer_incorporation_date': m.incorporation_date,
            'mer_first_txn_date': m.first_txn_date,
            'mer_lst_txn_date': m.last_txn_date,
            'mer_industry': m.industry,
            'mer_business_industry': m.industry,
            'mer_industry_mca': m.mca_description,
            'mer_fraud_flag': bool(m.fraud_flag) if m.fraud_flag is not None else False,
            'mer_avg_txn_size': float(m.avg_txn_size) if m.avg_txn_size else 0.0,
            'mer_total_txn': int(m.total_txn) if m.total_txn else 0,
            'mer_total_txn_fy': int(m.total_txn_fy) if m.total_txn_fy else 0,
            'mer_gst_risk_flag': m.gst_risk_flag,
            'mer_mca_fillings_risk_flag': m.mca_fillings_risk_flag,
            'mer_directors_risk_flag': m.directors_risk_flag,
            'mer_num_employees': m.num_employees,
            'mer_epfo_reg_status': m.epfo_reg_status,
            'mer_is_sanctioned': m.is_sanctioned,
            'mer_is_online_business': m.is_online_business,
            'mer_online_presence_flag': m.online_presence_flag,
            'mer_tax_irregularity_flag': m.tax_irregularity_flag,
            'mer_is_pan_compatible': m.is_pan_compatible,
            'mer_is_address_compatible': m.is_address_compatible,
            'mer_prior_fraud_investigation_flag': m.prior_fraud_investigation_flag,
            'mer_is_MCA_submission_taken': m.is_MCA_submission_taken,
            'mer_udyam_cert_flag': m.udyam_cert_flag
        } for m in merchants]

        print(merchants_data)
        
        # Get transactions and convert to dict first
        transactions_data = []
        transactions = db.query(models.transactions).all()
        transactions_data = [{ 
            'txn_id': t.id,
            'mer_id': t.merchant_id,
            'business_type': t.merchant_type or '',
            'mer_business_industry': t.merchant_type or '',
            'txn_timestamp': t.timestamp.isoformat() if t.timestamp else None,
            'txn_amount': float(t.amount) if t.amount else 0.0,
            'is_fraud_transaction': bool(t.is_fraud_transaction) if t.is_fraud_transaction is not None else False,
            'cx_id': str(t.cx_id) if t.cx_id else '',
            'cx_ip': t.cx_ip or '',
            'cx_device_id': t.cx_device_id or '',
            'cx_card_number': t.cx_card_number or '',
            'cx_pii_linkage_score': int(t.cx_pii_linkage_score) if t.cx_pii_linkage_score else 0,
            'is_cardholder_name_match': bool(t.is_cardholder_name_match) if t.is_cardholder_name_match is not None else True,
            'is_chargeback': bool(t.is_chargeback) if t.is_chargeback is not None else False,
            'is_cx_international': bool(t.is_cx_international) if t.is_cx_international is not None else False,
            'txn_status': bool(t.status == 'completed') if t.status else False,
            'is_cx_risky': bool(t.is_cx_risky) if t.is_cx_risky is not None else False,
            'invoice_amount': float(t.invoice_amount) if t.invoice_amount else 0.0,
            'is_cancelled': bool(t.is_cancelled) if t.is_cancelled is not None else False,
            'txn_currency': t.txn_currency or 'INR',
            'has_cx_complaint': bool(t.has_cx_complaint) if t.has_cx_complaint is not None else False,
        } for t in transactions]

        print(transactions_data)
        
        # Create DataFrames from the dictionaries
        transactions_df = pd.DataFrame(transactions_data)
        merchants_df = pd.DataFrame(merchants_data)
        print(transactions_df)   
        print(merchants_df)
        
        # Create the directory if it doesn't exist
        os.makedirs('.ares/data', exist_ok=True)
        
        # Save to csv 
        transactions_df.to_csv('.ares/data/transactions.csv', index=False)
        merchants_df.to_csv('.ares/data/merchants.csv', index=False)
        print("chayan3")
        print("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++=")
        
        # Verify the data was saved correctly
        saved_df = pd.read_csv('.ares/data/transactions.csv')
        if saved_df.empty:
            print("Warning: Saved transactions DataFrame is empty ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
        else:
            print("Data saved successfully")
        
        main()
        print("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++=")
        
        print(f"Processing merchant with ID: {merchant_id}")
        data_flags = run_this_please(merchant_id)
        print(f"Data flags for merchant {merchant_id}: {data_flags}")
        
        db.query(models.flags).filter(models.flags.merchant_id == merchant_id).filter(models.flags.flag_type == 'transactions risk for ML').delete()
        print(f"Deleted existing flags for merchant {merchant_id} with type 'transactions risk for ML'")
        
        data = []
        LLM_rules = db.query(models.LLM_metrics).all()
        print(f"Retrieved LLM rules: {LLM_rules}")
        
        for s in data_flags:
            for rule in LLM_rules:
                if rule.metric_name == s['metric']:
                    if rule.active_status == True:
                        data.append({
                            "flag_type": "transactions ML",
                            "severity": rule.severity,
                            "text": s['description'],
                            "importance": s['importance'],
                            "timestamp": datetime.now().isoformat()
                        })
                        print(f"Appended new flag for merchant {merchant_id}: {data[-1]}")
        
        flags = db.query(models.flags).filter(models.flags.merchant_id == merchant_id).all()
        print(f"Existing flags for merchant {merchant_id}: {flags}")
        
        for flag in flags:
            data.append({
                "flag_type": flag.flag_type,
                "severity": flag.severity,
                "text": flag.text,
                "importance": flag.importance,
                "timestamp": flag.timestamp.isoformat()
            })
            print(f"Appended existing flag for merchant {merchant_id}: {data[-1]}")
        
        for s in data_flags:
            for rule in LLM_rules:
                if rule.metric_name == s['metric'] and rule.active_status == True:
                    print(f"Rule {rule.metric_name} is active and matches the metric active_status {rule.active_status}")
                    new_flag = models.flags(
                        merchant_id=merchant_id,
                        flag_type="transactions ML",
                        severity=rule.severity,
                        text=s['description'],
                        importance=s['importance'],
                        timestamp=datetime.now()
                    )
                    db.add(new_flag)
                    print(f"Added new flag to database for merchant {merchant_id}: {new_flag}")
    
        db.commit()
        print("Database commit successful")
        return { "flags": data }
    except Exception as e:
        print(f"Exception occurred: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
def update_metrics_for_merchant(merchant_id):
    try:
        db = next(get_db())
        merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
        if not merchant:
            print(f"Merchant with id {merchant_id} not found")
            return
            
        transactions = db.query(models.transactions).filter(models.transactions.merchant_id == merchant_id).all()
        if transactions and len(transactions) > 0:
            # IP Density calculation
            cx_ip_counts = defaultdict(int)
            for txn in transactions:
                if txn.cx_ip:
                    cx_ip_counts[txn.cx_ip] += 1
            print(f"IP counts: {cx_ip_counts}")
            
            if cx_ip_counts:
                max_cx_ip = max(cx_ip_counts, key=cx_ip_counts.get)
                ip_density = cx_ip_counts[max_cx_ip] / len(transactions)
                print(f"Calculated IP density: {ip_density}")

                # Delete existing IP density record
                db.query(models.merchant_metric_value_numeric).filter(
                    models.merchant_metric_value_numeric.merchant_id == merchant_id,
                    models.merchant_metric_value_numeric.metric_type == "ip_density"
                ).delete()
                
                # Add new IP density record
                new_ip_density = models.merchant_metric_value_numeric(
                    merchant_id=merchant_id,
                    metric_type="ip_density", 
                    metric_value=str(ip_density)
                )
                db.add(new_ip_density)
                db.commit()
                print(f"Added new IP density record: {new_ip_density}")

            # Cities calculation
            matched_cities = set()
            for txn in transactions:
                if txn.cx_city:
                    matched_cities.add(txn.cx_city)
            
            if matched_cities:
                # Merge cities record
                db.query(models.merchant_metric_value_string).filter(
                    models.merchant_metric_value_string.merchant_id == merchant_id,
                    models.merchant_metric_value_string.metric_type == "cx_city"
                ).delete()
                db.add(models.merchant_metric_value_string(
                    merchant_id=merchant_id,
                    metric_type="cx_city",
                    metric_value=";".join(matched_cities)
                ))
                print(f"Merged cities record: {matched_cities}")
                db.commit()

            # Failed Transaction Percentage
            failed_txns = sum(txn.amount for txn in transactions if txn.status == "failed" and txn.amount is not None)
            total_txns = sum(txn.amount for txn in transactions if txn.amount is not None)
            if total_txns > 0:
                fail_pct = failed_txns / total_txns
                print(f"Failed transaction percentage: {fail_pct}")
                db.query(models.merchant_metric_value_numeric).filter(
                    models.merchant_metric_value_numeric.merchant_id == merchant_id,
                    models.merchant_metric_value_numeric.metric_type == "pct_failed_txn_amt"
                ).delete()
                db.add(models.merchant_metric_value_numeric(
                    merchant_id=merchant_id,
                    metric_type="pct_failed_txn_amt",
                    metric_value=str(fail_pct)
                ))
                db.commit()

            # Document Forgery Check
            merchant_docs = db.query(models.documents_uploaded).filter(
                models.documents_uploaded.merchant_id == merchant_id
            ).all()
            
            if merchant_docs:
                has_forged_docs = any(doc.is_forged for doc in merchant_docs if doc.is_forged is not None)
                db.query(models.merchant_metric_value_boolean).filter(
                    models.merchant_metric_value_boolean.merchant_id == merchant_id,
                    models.merchant_metric_value_boolean.metric_type == "is_forged_doc"
                ).delete()
                db.add(models.merchant_metric_value_boolean(
                    merchant_id=merchant_id,
                    metric_type="is_forged_doc",
                    metric_value=True if has_forged_docs else False
                ))
                db.commit()
                print(f"Document forgery status: {has_forged_docs}")

            # Network metrics
            network_overview = db.query(models.network_overview).filter(
                models.network_overview.merchant_id == merchant_id
            ).first()

            if network_overview:
                if network_overview.total_connections is not None:
                    db.query(models.merchant_metric_value_numeric).filter(
                        models.merchant_metric_value_numeric.merchant_id == merchant_id,
                        models.merchant_metric_value_numeric.metric_type == "number_connected_entities"
                    ).delete()
                    db.add(models.merchant_metric_value_numeric(
                        merchant_id=merchant_id,
                        metric_type="number_connected_entities",
                        metric_value=str(network_overview.total_connections)
                    ))
                    db.commit()
                if network_overview.directors_count is not None:
                    db.query(models.merchant_metric_value_numeric).filter(
                        models.merchant_metric_value_numeric.merchant_id == merchant_id,
                        models.merchant_metric_value_numeric.metric_type == "directors_count"
                    ).delete()
                    db.add(models.merchant_metric_value_numeric(
                        merchant_id=merchant_id,
                        metric_type="directors_count",
                        metric_value=str(network_overview.directors_count)
                    ))
                    db.commit()

            # Risk score
            risk_assessment = db.query(models.risk_assessments).filter(
                models.risk_assessments.merchant_id == merchant_id
            ).first()
            if risk_assessment and risk_assessment.risk_score is not None:
                db.query(models.merchant_metric_value_numeric).filter(
                    models.merchant_metric_value_numeric.merchant_id == merchant_id,
                    models.merchant_metric_value_numeric.metric_type == "mer_risk_score"
                ).delete()
                db.add(models.merchant_metric_value_numeric(
                    merchant_id=merchant_id,
                    metric_type="mer_risk_score", 
                    metric_value=str(risk_assessment.risk_score)
                ))
                db.commit()
                print(f"Risk score: {risk_assessment.risk_score}")

            # Transaction success rate
            successful_txns = sum(1 for txn in transactions if txn.status == "completed")
            if len(transactions) > 0:
                successful_txn_pct = successful_txns / len(transactions)
                print(f"Transaction success rate: {successful_txn_pct}")
                
                db.query(models.merchant_metric_value_numeric).filter(
                    models.merchant_metric_value_numeric.merchant_id == merchant_id,
                    models.merchant_metric_value_numeric.metric_type == "successful_txn_pct"
                ).delete()
                db.add(models.merchant_metric_value_numeric(
                    merchant_id=merchant_id,
                    metric_type="successful_txn_pct",
                    metric_value=str(successful_txn_pct)
                ))
                db.commit()

            # Merchant IP geolocation
            if merchant.ip_geolocation:
                db.query(models.merchant_metric_value_string).filter(
                    models.merchant_metric_value_string.merchant_id == merchant_id,
                    models.merchant_metric_value_string.metric_type == "mer_ip_geolocation"
                ).delete()
                db.add(models.merchant_metric_value_string(
                    merchant_id=merchant_id,
                    metric_type="mer_ip_geolocation",
                    metric_value=str(merchant.ip_geolocation)
                ))
                db.commit()

            # Print all metric values for verification
            merchant_metrics = db.query(models.merchant_metric_value_numeric).filter(
                models.merchant_metric_value_numeric.merchant_id == merchant_id
            ).all()
            print("\nCurrent merchant metrics:")
            for metric in merchant_metrics:
                print(f"Metric Type: {metric.metric_type}")
                print(f"Metric Value: {metric.metric_value}")
                print("-" * 50)

            # Commit all changes
            try:
                db.commit()
                print("Successfully committed all metric updates to database")
            except Exception as commit_error:
                print(f"Error committing changes: {commit_error}")
                db.rollback()
                raise

    except Exception as e:
        print(f"Error updating metrics for merchant {merchant_id}: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()
        
def metrics_calculation():
    db = next(get_db())
    merchants = db.query(models.Merchant).all()
    merchant_ids = [merchant.id for merchant in merchants]
    for merchant_id in merchant_ids:
        update_metrics_for_merchant(merchant_id)




def update_transaction_metrics(transaction_id):
    db = next(get_db())
    transaction = db.query(models.transactions).filter(models.transactions.id == transaction_id).first()
    merchant_id = transaction.merchant_id
    transactions = db.query(models.transactions).filter(models.transactions.merchant_id == merchant_id).all()
    metrics_dict = {metric.metric_name: metric for metric in db.query(models.Metrics).all()}
    if transactions:
        cx_ip_counts = defaultdict(int)
        for txn in transactions:
            cx_ip_counts[txn.cx_ip] += 1
        print(cx_ip_counts)
        max_cx_ip = max(cx_ip_counts, key=cx_ip_counts.get)
        ip_density = cx_ip_counts[max_cx_ip] / len(transactions)
        print(ip_density)
        
        db.merge(models.merchant_metric_value_numeric(
            merchant_id=merchant_id,
            metric_type="ip_density",
            metric_value=str(ip_density)
        ))
        print("true ip density")

        matched_cities = set()
        for txn in transactions:
            matched_cities.add(txn.cx_city)
        
        db.merge(models.merchant_metric_value_string(
            merchant_id=merchant_id,
            metric_type="cx_city",
            metric_value=";".join(matched_cities)
        ))
        print(";".join(matched_cities))   

        # Failed Transaction Percentage
        failed_txns = sum(txn.amount for txn in transactions if txn.status == "failed")
        fail_pct = failed_txns / sum(txn.amount for txn in transactions)
        print(fail_pct)
            
        db.merge(models.merchant_metric_value_numeric(
            merchant_id=merchant_id,
            metric_type="pct_failed_txn_amt",
            metric_value=str(fail_pct)
        ))
        transaction.fail_pct = fail_pct
        transaction.cx_city = ";".join(matched_cities)
        transaction.ip_density = ip_density
        db.commit()


def eval_number(metric_value, metric_operation, metric_value_for_rule):
    if metric_operation == ">":
        return metric_value > metric_value_for_rule
    elif metric_operation == "<":
        return metric_value < metric_value_for_rule
    elif metric_operation == "=":
        return metric_value == metric_value_for_rule
    elif metric_operation == "!=":
        return metric_value != metric_value_for_rule
    elif metric_operation == ">=":
        return metric_value >= metric_value_for_rule
    elif metric_operation == "<=":
        return metric_value <= metric_value_for_rule
    return False

def eval_is_in(metric_value, metric_operation, metric_value_for_rule):
    metric_value_for_rule = metric_value_for_rule.split(";")
    metric_value = metric_value.split(";")
    print("*"*100)
    print(metric_value)
    print(metric_value_for_rule)
    print("*"*100)
    if metric_operation == "is in":
        return any(item in metric_value_for_rule for item in metric_value)
    return False

def eval_is_equal(metric_value, metric_operation, metric_value_for_rule):
    if metric_operation == "=":
        return metric_value == metric_value_for_rule
    return False

def update_rule_based_for_merchant(merchant_id):
    if not merchant_id:
        print("No merchant_id provided")
        return
        
    db = next(get_db())
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if not merchant:
        print(f"No merchant found for id {merchant_id}")
        return
        
    transactions = db.query(models.transactions).filter(models.transactions.merchant_id == merchant_id).all()
    if not transactions:
        print(f"No transactions found for merchant {merchant_id}")
        return
        
    # Convert metrics values list to dictionary for easier lookup
    metrics_values_dict = {}
    metrics_values_numeric = db.query(models.merchant_metric_value_numeric).filter(
        models.merchant_metric_value_numeric.merchant_id == merchant_id
    ).all()
    metrics_values_string = db.query(models.merchant_metric_value_string).filter(
        models.merchant_metric_value_string.merchant_id == merchant_id
    ).all()
    metrics_values_boolean = db.query(models.merchant_metric_value_boolean).filter(
        models.merchant_metric_value_boolean.merchant_id == merchant_id
    ).all()

    print("\nFetching metric values for merchant:", merchant_id)
    for metric in metrics_values_numeric:
        if metric.metric_type and metric.metric_value is not None:
            metrics_values_dict[metric.metric_type] = metric.metric_value
            print(f"  {metric.metric_type}: {metric.metric_value}")
            
    for metric in metrics_values_string:
        if metric.metric_type and metric.metric_value is not None:
            metrics_values_dict[metric.metric_type] = metric.metric_value
            print(f"  {metric.metric_type}: {metric.metric_value}")
            
    for metric in metrics_values_boolean:
        if metric.metric_type and metric.metric_value is not None:
            metrics_values_dict[metric.metric_type] = metric.metric_value
            print(f"  {metric.metric_type}: {metric.metric_value}")

    # Check that all required metrics exist and have non-null values
    print("*"*100)
    required_metrics = ["ip_density", "cx_city", "pct_failed_txn_amt", "is_forged_doc", "successful_txn_pct", "mer_risk_score"]
    
    # Check if all required metrics exist
    for metric in required_metrics:
        if metric not in metrics_values_dict:
            print(f"Missing required metric: {metric}")
            return
            
    rule1 = db.query(models.Rules).filter(models.Rules.rule_code == "TA101").first()
    if not rule1:
        print("Rule TA101 not found")
        return
        
    print("*"*100)
    print(rule1.rule_status)
    print("*"*100)
    
    if rule1.rule_status == True:
        print("Fetching metrics for rule TA101...")
        metrics_numeric = db.query(models.Metrics_numeric).filter(models.Metrics_numeric.rule_code == "TA101").all()
        metrics_string = db.query(models.Metrics_string).filter(models.Metrics_string.rule_code == "TA101").all()
        metrics_boolean = db.query(models.Metrics_boolean).filter(models.Metrics_boolean.rule_code == "TA101").all()
        print(f"Found {len(metrics_numeric)} metrics for rule TA101")
        
        metrics_dict_rule_numeric = {}
        for metric in metrics_numeric:
            if metric.metric_name:
                print(f"Adding metric {metric.metric_name} to dictionary")
                metrics_dict_rule_numeric[metric.metric_name] = metric

        metrics_dict_rule_string = {}
        for metric in metrics_string:
            if metric.metric_name:
                print(f"Adding metric {metric.metric_name} to dictionary")
                metrics_dict_rule_string[metric.metric_name] = metric

        metrics_dict_rule_boolean = {}
        for metric in metrics_boolean:
            if metric.metric_name:
                print(f"Adding metric {metric.metric_name} to dictionary")
                metrics_dict_rule_boolean[metric.metric_name] = metric

        print("Final metrics dictionary:", metrics_dict_rule_numeric)
        print("Debugging ip_density evaluation:")
        print(f"metrics_values_dict['ip_density']: {metrics_values_dict['ip_density']} (type: {type(metrics_values_dict['ip_density'])})")
        print(f"metrics_dict_rule['ip_density'].metric_operation: {metrics_dict_rule_numeric['ip_density'].metric_operation} (type: {type(metrics_dict_rule_numeric['ip_density'].metric_operation)})")
        print(f"metrics_dict_rule['ip_density'].metric_value: {metrics_dict_rule_numeric['ip_density'].metric_value} (type: {type(metrics_dict_rule_numeric['ip_density'].metric_value)})")
        
        try:
            ip_density_value = float(metrics_values_dict["ip_density"])
            ip_density_rule_value = float(metrics_dict_rule_numeric["ip_density"].metric_value)
            
            print(f"Converted ip_density_value: {ip_density_value} (type: {type(ip_density_value)})")
            print(f"Converted ip_density_rule_value: {ip_density_rule_value} (type: {type(ip_density_rule_value)})")
            
            if all(x is not None for x in [
                metrics_dict_rule_numeric["ip_density"].metric_operation,
                metrics_dict_rule_string["cx_city"].metric_operation,
                metrics_dict_rule_string["cx_city"].metric_value,
                metrics_dict_rule_numeric["pct_failed_txn_amt"].metric_operation,
                metrics_dict_rule_boolean["is_forged_doc"].metric_operation,
                metrics_dict_rule_numeric["successful_txn_pct"].metric_operation,
                metrics_dict_rule_numeric["mer_risk_score"].metric_operation
            ]):
                ip_density_bool = eval_number(ip_density_value,
                                            metrics_dict_rule_numeric["ip_density"].metric_operation,
                                            ip_density_rule_value)
                
                cx_city_bool = eval_is_in(metrics_values_dict["cx_city"],
                                         metrics_dict_rule_string["cx_city"].metric_operation,
                                         metrics_dict_rule_string["cx_city"].metric_value)
                
                pct_failed_txn_amt_bool = eval_number(float(metrics_values_dict["pct_failed_txn_amt"]),
                                                    metrics_dict_rule_numeric["pct_failed_txn_amt"].metric_operation,
                                                    float(metrics_dict_rule_numeric["pct_failed_txn_amt"].metric_value))
                
                is_forged_doc_bool = eval_is_equal(metrics_values_dict["is_forged_doc"],
                                                 metrics_dict_rule_boolean["is_forged_doc"].metric_operation,
                                                 metrics_dict_rule_boolean["is_forged_doc"].metric_value)
                
                successful_txn_pct_bool = eval_number(float(metrics_values_dict["successful_txn_pct"]),
                                                   metrics_dict_rule_numeric["successful_txn_pct"].metric_operation,
                                                   float(metrics_dict_rule_numeric["successful_txn_pct"].metric_value))
                
                mer_risk_score_bool = eval_number(float(metrics_values_dict["mer_risk_score"]),
                                               metrics_dict_rule_numeric["mer_risk_score"].metric_operation,
                                               float(metrics_dict_rule_numeric["mer_risk_score"].metric_value))
                
                print("*"*100)
                print(ip_density_bool, cx_city_bool, pct_failed_txn_amt_bool, is_forged_doc_bool, successful_txn_pct_bool, mer_risk_score_bool)
                print("*"*100)
                
                if (ip_density_bool and cx_city_bool) and \
                   ((pct_failed_txn_amt_bool or is_forged_doc_bool) and \
                    (successful_txn_pct_bool and mer_risk_score_bool)):
                    merchant.rule_1 = True
                    db.commit()
        except (ValueError, AttributeError) as e:
            print(f"Error processing metrics: {str(e)}")
            return
def update_rule_based_red_flags(merchant_id):
    if merchant_id is None:
        print("No merchant_id provided")
        return
        
    db = next(get_db())
    merchant = db.query(models.Merchant).filter(models.Merchant.id == merchant_id).first()
    if merchant is None:
        print(f"No merchant found for id {merchant_id}")
        return
        
    rule1 = db.query(models.Rules).filter(models.Rules.rule_code == "TA101").first()
    if rule1 is None:
        print("Rule TA101 not found")
        return
        
    print("*"*100)
    print(merchant.rule_1)
    print(rule1.rule_status) 
    print("*"*100)
    if rule1.rule_description is not None:
        db.query(models.flags).filter_by(merchant_id=merchant_id, text=rule1.rule_description).delete()
    if (merchant.rule_1 is not None and rule1.rule_status is not None and
        merchant.rule_1 == True and rule1.rule_status == True and
        rule1.rule_type is not None and rule1.rule_severity is not None and 
        rule1.rule_description is not None):
            
        print("flag added")
        
        db.add(models.flags(
            merchant_id=merchant_id,
            flag_type=rule1.rule_type,
            severity=rule1.rule_severity,
            importance=1.0,
            text=rule1.rule_description,
            timestamp=datetime.now(),
            created_at=datetime.now()
        ))
        print("flag added")
        
    db.commit()

def new_transaction_added(transaction_id):
    db = next(get_db())
    transaction = db.query(models.transactions).filter(models.transactions.id == transaction_id).first()
    update_transaction_metrics(transaction_id)
    update_rule_based_for_merchant(transaction.merchant_id)
    update_rule_based_red_flags(transaction.merchant_id)
    calculate_ML_red_flags(transaction.merchant_id)


def red_flag_merchant(merchant_id):
    update_metrics_for_merchant(merchant_id)
    update_rule_based_for_merchant(merchant_id)
    update_rule_based_red_flags(merchant_id)
    calculate_ML_red_flags(merchant_id)


def generate_red_flag():
    db = next(get_db())
    merchants = db.query(models.Merchant).all()
    merchant_ids = [merchant.id for merchant in merchants]
    for merchant_id in merchant_ids:
        red_flag_merchant(merchant_id)


scheduler.add_job(generate_red_flag, IntervalTrigger(minutes=int(os.getenv("calculate_red_flags_interval"))))
scheduler.start()

