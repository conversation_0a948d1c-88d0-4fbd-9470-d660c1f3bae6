from fastapi import APIRouter, Depends, HTTPException, Path, status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from uuid import UUID, uuid4
from datetime import datetime
from typing import List, Dict, Any
from ..database import get_db
from ..models import models
from ..schemas.request_models import (
    ReportGenListCreate,
    ReportGenCreate,
)
from decimal import Decimal
from urllib.parse import unquote
import pandas as pd
import requests
import logging
from .apiProtection import get_current_user
import os
from groq import Groq

router = APIRouter(
    prefix="",
    tags=["Report Generation"]
)


@router.get("/added-new-report")
async def add_new_report(
    investigator_email: str,
    report_title: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    report_id = uuid4()
    db.add(models.report_gen(report_id=report_id,
                             investigator_email=investigator_email,
                             report_title=report_title,
                             last_updated=datetime.now(),
                             ))
    db.commit()
    return {"message": "Report added successfully", "report_id": report_id, "report_title": report_title}


@router.get("/{report_id}/get-report")
async def get_report(
    report_id: UUID,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    report_gen = db.query(models.report_gen).filter(models.report_gen.report_id == report_id).first()   
    reports = db.query(models.entity_report_gen).filter(models.entity_report_gen.report_id == report_id).all()
    data = {
        "report_title": report_gen.report_title,
        "investigator_email": report_gen.investigator_email,
        "last_updated": report_gen.last_updated,
        "created_at": report_gen.created_at,
        "components": []
    }
    for report in reports:
        data["components"].append({
            "frontend_component_id": report.frontend_component_id,
            "component_type": report.component_type,
            "data": report.data,
            "uploaded_at" : report.created_at
        })
    return {
        "response": data
    }


@router.get("/{investigator_email}/get-report-ids")
async def get_report_ids(
    investigator_email: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    data = db.query(models.report_gen).filter(models.report_gen.investigator_email == investigator_email).all()
    return [{"id": str(report.report_id), "report_title": report.report_title, "last_updated": report.last_updated.isoformat()} for report in data]


@router.post("/{report_id}/update-report-components")
async def update_report_components(
    report_gen: ReportGenListCreate,
    report_id: UUID,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):  
    report_gen_data = db.query(models.report_gen).filter(models.report_gen.report_id == report_id).first()
    if report_gen_data is None:
        return {"message": "No report to update"}
    db.query(models.entity_report_gen).filter(models.entity_report_gen.report_id == report_id).delete()
    for report in report_gen.report_gen:
        db.add(models.entity_report_gen(
            component_type=report.component_type,
            frontend_component_id = report.frontend_component_id,   
            data = report.data,
            report_id=report_id,
        ))
    report_gen = db.query(models.report_gen).filter(models.report_gen.report_id == report_id).first()
    report_gen.last_updated = datetime.now()
    db.commit()
    return {"message": "All Report entries added successfully", "report_id": report_id}


@router.get("/{report_id}/update-report-title")
async def update_report_title(
    report_id: UUID,
    report_title: str,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    report_gen = db.query(models.report_gen).filter(models.report_gen.report_id == report_id).first()
    report_gen.report_title = report_title
    db.commit()
    return {"message": "Report title updated successfully", "report_id": report_id, "report_title": report_title}

@router.get("/{report_id}/report-gen-summary")
async def get_report_gen_summary(
    report_id: UUID,
    db: Session = Depends(get_db),
    current_user: models.User = Depends(get_current_user)
):
    try:
        # Get report details from database
        report_gen = db.query(models.report_gen).filter(models.report_gen.report_id == report_id).first()
        if not report_gen:
            raise HTTPException(status_code=404, detail="Report not found")
            
        reports = db.query(models.entity_report_gen).filter(models.entity_report_gen.report_id == report_id).all()
        
        # Prepare data for summarization
        report_data = {
            "title": report_gen.report_title,
            "components": []
        }
        
        for report in reports:
            report_data["components"].append({
                "type": report.component_type,
                "data": report.data
            })

        # Initialize Groq client
        client = Groq(
            api_key=os.environ.get("GROQ_API_KEY"),
        )
        
        # Create system and user prompts
        system_prompt = """You are a professional report summarizer specializing in creating concise, clear summaries.
        Your task is to create a 30-40 word summary that captures the key points of the report.
        Focus on:
        - Main findings and insights
        - Key data points
        - Critical patterns or trends
        - Important conclusions
        
        Keep the tone professional and objective.
        Avoid technical jargon unless essential."""

        user_prompt = f"""Please summarize this report:
        Title: {report_data['title']}
        Content: {str(report_data['components'])}
        
        Provide a 30-40 word summary that captures the essential information."""
        
        # Make API call to Groq
        chat_completion = client.chat.completions.create(
            messages=[
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ],
            model="llama-3.3-70b-versatile",
            temperature=0.7,
            max_tokens=100
        )
        
        summary = chat_completion.choices[0].message.content
        
        return {
            "message": "Report summary generated successfully",
            "report_id": report_id,
            "report_title": report_gen.report_title,
            "summary": summary
        }
        
    except Exception as e:
        logging.error(f"Error generating summary: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error occurred while generating summary"
        )