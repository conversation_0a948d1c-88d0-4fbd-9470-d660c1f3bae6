from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import logging
from ..database import get_db
from ..engines.red_flag_engine import RedFlagEngine
from uuid import UUID

logger = logging.getLogger(__name__)
router = APIRouter(
    prefix="/red-flags",
    tags=["red-flags"]
)

@router.post("/process")
def process_red_flags(merchant_ids: str = None, rules: str = None, db: Session = Depends(get_db)):
    """Process red flags for specified merchants and rules"""
    try:
        logger.info("Starting red flag processing")
        
        # Parse merchant IDs if provided
        merchant_id_list = [UUID(id.strip()) for id in merchant_ids.split(',')] if merchant_ids else None
        
        # Parse rules if provided
        rule_list = [rule.strip() for rule in rules.split(',')] if rules else None
        
        engine = RedFlagEngine(db)
        engine.process_rules(merchant_id_list, rule_list)
        
        logger.info("Red flag processing completed successfully")
        return {"message": "Red flags processed successfully"}
    except ValueError as e:
        logger.error(f"Invalid merchant ID format: {str(e)}")
        raise HTTPException(status_code=400, detail="Invalid merchant ID format")
    except Exception as e:
        logger.error(f"Error processing red flags: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 