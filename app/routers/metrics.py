from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List
from decimal import Decimal
from ..database import get_db
from ..models.models import MetricStore, merchant_metrics, transaction_metrics, customer_metrics
import json
from ..engines.metric_calculator import serialize_to_json
from datetime import datetime, date
from uuid import UUID

router = APIRouter()

@router.post("/calculate-metrics")
async def calculate_metrics(merchant_ids: str = None, metric_codes: str = None, db: Session = Depends(get_db)):
    try:
        print("Starting metric calculation process")
        
        # Parse merchant IDs if provided
        merchant_id_list = [UUID(id.strip()) for id in merchant_ids.split(',')] if merchant_ids else None
        
        # Parse metric codes if provided
        metric_code_list = [code.strip() for code in metric_codes.split(',')] if metric_codes else None

        # Get approved and active metrics, filtered by metric codes if provided
        query = db.query(MetricStore).filter(
            MetricStore.status == 'approved',
            MetricStore.is_active == True
        )
        
        if metric_code_list:
            query = query.filter(MetricStore.metric_code.in_(metric_code_list))
        
        metrics = query.all()

        print(f"Found {len(metrics)} approved and active metrics to calculate")

        for metric in metrics:
            try:
                print(f"Calculating metric: {metric.metric_code}")

                # Validate query before execution
                if "SELECT" in metric.query.upper() and "FROM" in metric.query.upper():
                    # Modify query to filter by merchant IDs if provided
                    query = metric.query
                    if merchant_id_list:
                        merchant_ids_str = ','.join([f"'{str(id)}'" for id in merchant_id_list])
                        if "WHERE" in query.upper():
                            query = query.replace("WHERE", f"WHERE merchant_id IN ({merchant_ids_str}) AND")
                        else:
                            query = f"{query} WHERE merchant_id IN ({merchant_ids_str})"
                    
                    # Execute the query
                    result = db.execute(text(query))

                    # Get the metric table name
                    metric_table = metric.metric_table
                    print(f"Storing results in {metric_table} table")

                    # Store results based on metric table
                    if metric_table == 'merchant_metrics':
                        for row in result:
                            # Serialize the metric value to JSON, handling Decimal values
                            metric_value_json = serialize_to_json(row.metric_value)
                            # Get year and financials_date from query result if available
                            year = getattr(row, 'year', None)
                            financials_date = getattr(row, 'financials_date', None)
                            
                            # Convert year to date if financials_date is not provided
                            if financials_date is None and year is not None:
                                financials_date = date(year, 3, 31)  # Set to March 31st of the year
                            
                            new_metric = merchant_metrics(
                                merchant_id=row.merchant_id,
                                metric_type=metric.metric_code,
                                metric_value=metric_value_json,
                                financials_date=financials_date,
                                year=year
                            )
                            db.add(new_metric)

                    elif metric_table == 'transaction_metrics':
                        for row in result:
                            # Serialize the metric value to JSON, handling Decimal values
                            metric_value_json = serialize_to_json(row.metric_value)
                            # Get year and financials_date from query result if available
                            year = getattr(row, 'year', None)
                            financials_date = getattr(row, 'financials_date', None)
                            
                            # Convert year to date if financials_date is not provided
                            if financials_date is None and year is not None:
                                financials_date = date(year, 3, 31)  # Set to March 31st of the year
                            
                            new_metric = transaction_metrics(
                                transaction_id=row.transaction_id,
                                metric_type=metric.metric_code,
                                metric_value=metric_value_json,
                                financials_date=financials_date,
                                year=year
                            )
                            db.add(new_metric)

                    elif metric_table == 'customer_metrics':
                        for row in result:
                            # Serialize the metric value to JSON, handling Decimal values
                            metric_value_json = serialize_to_json(row.metric_value)
                            # Get year and financials_date from query result if available
                            year = getattr(row, 'year', None)
                            financials_date = getattr(row, 'financials_date', None)
                            
                            # Convert year to date if financials_date is not provided
                            if financials_date is None and year is not None:
                                financials_date = date(year, 3, 31)  # Set to March 31st of the year
                            
                            new_metric = customer_metrics(
                                customer_id=row.customer_id,
                                metric_type=metric.metric_code,
                                metric_value=metric_value_json,
                                financials_date=financials_date,
                                year=year
                            )
                            db.add(new_metric)

                    db.commit()
                    print(f"Successfully calculated and stored metric: {metric.metric_code}")
                else:
                    print(f"Invalid query format for metric: {metric.metric_code}")
                    continue

            except Exception as metric_error:
                print(f"Error calculating metric {metric.metric_code}: {str(metric_error)}")
                db.rollback()
                continue

        print("Completed metric calculation process")
        return {"message": "Metrics calculated and stored successfully"}

    except Exception as e:
        print(f"Error in metric calculation process: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))