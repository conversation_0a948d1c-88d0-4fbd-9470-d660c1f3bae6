# Import required FastAPI components for routing, dependency injection and error handling
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
# Import database connection utilities
from ..database import get_db
# Import database models and request/response schemas
from ..models import models
from ..schemas import request_models
# Import authentication and security utilities
from ..utils.auth import get_password_hash, verify_password
from ..utils.jwt import create_access_token
# Import datetime utilities for token expiration and OTP management
from datetime import timedel<PERSON>, datetime
# Import OTP generation and email utilities
from ..utils.otp import generate_otp
from ..utils.email_utils import send_email

# Configure JWT access token expiration time (in minutes)
ACCESS_TOKEN_EXPIRE_MINUTES = 400  # Token expires after 400 minutes

# Initialize FastAPI router for authentication endpoints
router = APIRouter()


@router.post("/send-otp")
def send_otp(email: str, db: Session = Depends(get_db)):
    """
    Generate and send a One-Time Password (OTP) to the provided email address.
    
    Args:
        email (str): The recipient's email address where the OTP will be sent.
        db (Session): Database session dependency for database operations.
        
    Returns:
        dict: A message confirming that OTP was sent successfully.
        
    Raises:
        HTTPException (500): If there is an error in sending the email or database operation.
        
    Process:
        1. Generates a new random OTP code using the generate_otp utility.
        2. Sets expiration time to 5 minutes from current time.
        3. Checks database for existing OTP record for the email.
        4. If exists, updates the existing record with new OTP and expiration.
        5. If not exists, creates new OTP record in database.
        6. Sends the OTP to user's email using email utility.
    """
    # Generate new OTP and set expiration time
    otp = generate_otp()
    expires_at = datetime.now() + timedelta(minutes=5)
    
    # Check for existing OTP record and update or create new one
    existing_otp = db.query(models.Otp).filter(models.Otp.email == email).first()
    if existing_otp:
        # Update existing OTP record with new code and expiration
        existing_otp.otp = otp
        existing_otp.expires_at = expires_at
    else:
        # Create new OTP record
        db_otp = models.Otp(email=email, otp=otp, expires_at=expires_at)
        db.add(db_otp)
    db.commit()
    
    # Send OTP via email
    send_email(email, "Your OTP Code", f"Your OTP code is {otp}")
    return {"message": "OTP sent to your email"} 

@router.post("/register/{otp}", response_model=request_models.UserResponse)
def register_user(user: request_models.UserCreate, otp: str, db: Session = Depends(get_db)):
    """
    Register a new user with email verification via OTP.
    
    Args:
        user (UserCreate): Pydantic model containing user registration data.
        otp (str): One-Time Password received via email for verification.
        db (Session): Database session dependency for database operations.
        
    Returns:
        UserResponse: Pydantic model containing created user data.
        
    Raises:
        HTTPException (400): When OTP is invalid or expired.
        HTTPException (400): When email or username is already registered.
        
    Process:
        1. Validates provided OTP against stored OTP in database.
        2. Verifies OTP hasn't expired.
        3. Checks if email or username is already registered.
        4. Hashes the provided password securely.
        5. Creates new user record in database.
        6. Commits transaction and refreshes user object.
    """
    # Verify OTP validity
    stored_otp_record = db.query(models.Otp).filter(models.Otp.email == user.email).first()
    if not stored_otp_record or stored_otp_record.otp != otp:
        raise HTTPException(status_code=400, detail="Invalid or expired OTP")
    
    # Check OTP expiration
    if stored_otp_record.expires_at < datetime.now():
        raise HTTPException(status_code=400, detail="OTP expired")
    
    # Prevent duplicate email registration
    if db.query(models.User).filter(models.User.email == user.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Prevent duplicate username registration
    if db.query(models.User).filter(models.User.username == user.username).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    # Create new user with hashed password
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        email=user.email,
        username=user.username,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

@router.post("/login")
def login_user(user: request_models.UserAuthenticate, db: Session = Depends(get_db)):
    """
    Authenticate user and generate JWT access token.
    
    Args:
        user (UserAuthenticate): Pydantic model containing login credentials.
        db (Session): Database session dependency for database operations.
        
    Returns:
        dict: Authentication response containing access token and token type.
        
    Raises:
        HTTPException (401): When credentials are invalid.
        
    Process:
        1. Queries database for user with provided email.
        2. Verifies provided password against stored hash.
        3. Generates JWT access token with configured expiration.
        4. Returns token with bearer type.
    """
    # Verify user exists and password is correct
    db_user = db.query(models.User).filter(models.User.email == user.email).first()
    if not db_user or not verify_password(user.password, db_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )
    
    # Generate JWT access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": db_user.email},  # Using email as JWT subject
        expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.post("/forgot-password")
def forgot_password(email: str, db: Session = Depends(get_db)):
    """
    Initiate password reset process by sending OTP.
    
    Args:
        email (str): User's registered email address.
        db (Session): Database session dependency for database operations.
        
    Returns:
        dict: Confirmation message of OTP delivery.
        
    Raises:
        HTTPException (404): When user email not found.
        
    Process:
        1. Verifies user exists in database.
        2. Generates new OTP for password reset.
        3. Sets OTP expiration time.
        4. Stores or updates OTP in database.
        5. Sends OTP to user's email.
    """
    # Verify user exists
    user = db.query(models.User).filter(models.User.email == email).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Generate new OTP and set expiration
    otp = generate_otp()
    expires_at = datetime.now() + timedelta(minutes=5)

    # Update or create OTP record
    existing_otp = db.query(models.Otp).filter(models.Otp.email == email).first()
    if existing_otp:
        existing_otp.otp = otp
        existing_otp.expires_at = expires_at
    else:
        db_otp = models.Otp(email=email, otp=otp, expires_at=expires_at)
        db.add(db_otp)
    
    db.commit()

    # Send password reset OTP via email
    send_email(email, "Password Reset OTP", f"Your OTP for password reset is {otp}")
    return {"message": "OTP sent to your email"}

@router.post("/reset-password")
def reset_password(email: str, otp: str, new_password: str, confirm_new_password: str, db: Session = Depends(get_db)):
    """
    Complete password reset process with OTP verification.
    
    Args:
        email (str): User's registered email address.
        otp (str): One-Time Password received via email.
        new_password (str): New password to be set.
        confirm_new_password (str): Confirmation of new password.
        db (Session): Database session dependency for database operations.
        
    Returns:
        dict: Success message confirming password reset.
        
    Raises:
        HTTPException (400): For invalid/expired OTP.
        HTTPException (400): When passwords don't match.
        HTTPException (404): When user not found.
        
    Process:
        1. Validates provided OTP against stored OTP.
        2. Checks if OTP has expired.
        3. Verifies new password matches confirmation.
        4. Updates user's password with new hashed password.
    """
    # Verify OTP validity
    stored_otp_record = db.query(models.Otp).filter(models.Otp.email == email).first()
    if not stored_otp_record or stored_otp_record.otp != otp:
        raise HTTPException(status_code=400, detail="Invalid or expired OTP")

    # Check OTP expiration
    if stored_otp_record.expires_at < datetime.now():
        raise HTTPException(status_code=400, detail="OTP expired")

    # Verify password confirmation matches
    if new_password != confirm_new_password:
        raise HTTPException(status_code=400, detail="New password and confirm new password do not match")

    # Update user's password
    user = db.query(models.User).filter(models.User.email == email).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user.hashed_password = get_password_hash(new_password)
    db.commit()

    return {"message": "Password reset successfully"}

@router.get("/test")
def test_auth(db: Session = Depends(get_db)):
    """
    Test endpoint to verify authentication system functionality.
    
    Args:
        db (Session): Database session dependency for database operations.
        
    Returns:
        dict: Contains list of all users in system.
        
    Security Warning:
        This endpoint should be:
        - Disabled in production environment.
        - Protected with appropriate authentication.
        - Limited to administrative access only.
    """
    db_user = db.query(models.User).all()
    return {"message": db_user}