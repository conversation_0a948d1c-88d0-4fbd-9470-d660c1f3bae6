from .merchantRelatedApis import router as merchant_router
from .datafeedingRouter import router as merchant_post_router
from .caseManagementRouter import router as case_management_router
from .reportGenerationApis import router as report_gen_router
from .emailCommunicationRouter import router as email_communication_router
from .jobSchedulingRouter import router as job_sch_router
from .authRouter import router as auth_router
from .ruleRepoApis import router as rule_repo_router
from .percentileSegmentsRouter import router as monitoring_router
from .redFlagGenerationRouter import router as red_flag_router
from .ruleRepoApis import router as rule_repo_router
from .realtimeSimulationPipeLineRouter import router as pipe_line_router
from .investigationGPTRouter import router as chat_router
from .creditInsolvancy import router as creditInsolvancy_router
from .investigationGPTRouter2 import router as chat_router2
from .customer_red_flags import router as customer_red_flags
from .dashBoardApis import router as dashboard_router
from . import strategy_backtesting