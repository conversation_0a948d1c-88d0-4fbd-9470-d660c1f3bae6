from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import inspect, text
from typing import Optional, Dict, Any, List, Tuple
from ..database import get_db, engine
from ..models.models import (
    MetricStore, customer, transactions, 
    merchant_metrics, transaction_metrics, customer_metrics,
    probe_merchant, probe_financials, probe_msme_supplier_payment_delays,
    probe_authorized_signatories, probe_related_party_transactions_company,
    probe_related_party_transactions_llp, probe_related_party_transactions_individual,
    probe_related_party_transactions_others
)
from ..static.red_flags_engine_prompts import get_metric_generation_prompt, get_source_query_prompt
from groq import Groq
import os
from datetime import datetime
import uuid
import re
import json

router = APIRouter()

def determine_metric_value_type(query: str) -> str:
    """
    Analyze the SQL query to determine the expected return type of the metric value.
    Returns one of: 'numeric', 'boolean', 'string', 'json', 'timestamp'
    """
    # Convert query to lowercase for easier pattern matching
    query_lower = query.lower()
    
    # Check for aggregation functions
    if any(agg in query_lower for agg in ['count(', 'sum(', 'avg(', 'min(', 'max(']):
        return 'numeric'
    
    # Check for boolean operations
    if any(bool_op in query_lower for bool_op in ['case when', 'exists', 'not exists', 'is true', 'is false']):
        return 'boolean'
    
    # Check for date/time operations
    if any(time_op in query_lower for time_op in ['date_trunc', 'extract', 'date_part', 'interval']):
        return 'timestamp'
    
    # Check for JSON operations
    if any(json_op in query_lower for json_op in ['jsonb_', 'json_', '->', '->>']):
        return 'json'
    
    # Check the SELECT clause for type hints
    select_match = re.search(r'select\s+(.*?)\s+from', query_lower)
    if select_match:
        select_clause = select_match.group(1)
        
        # Check for explicit type casts
        if '::numeric' in select_clause or '::int' in select_clause or '::float' in select_clause:
            return 'numeric'
        if '::boolean' in select_clause:
            return 'boolean'
        if '::text' in select_clause or '::varchar' in select_clause:
            return 'string'
        if '::json' in select_clause or '::jsonb' in select_clause:
            return 'json'
        if '::timestamp' in select_clause or '::date' in select_clause:
            return 'timestamp'
    
    # Default to string if type cannot be determined
    return 'string'

def get_table_schema(model_class) -> List[str]:
    """
    Get table schema information from SQLAlchemy model
    Returns a list of column names and their types
    """
    inspector = inspect(model_class)
    columns = []
    
    for column in inspector.columns:
        # Get column type and convert to string
        col_type = str(column.type)
        # Remove length/precision information for cleaner output
        if '(' in col_type:
            col_type = col_type.split('(')[0]
        columns.append(f"{column.name} ({col_type})")
    
    return columns

def get_metrics_tables_schema() -> Dict[str, List[str]]:
    """
    Get schema information for all metrics tables
    """
    return {
        'merchant_metrics': get_table_schema(merchant_metrics),
        'transaction_metrics': get_table_schema(transaction_metrics),
        'customer_metrics': get_table_schema(customer_metrics)
    }

def analyze_query(query: str, db: Session) -> Dict[str, Any]:
    """
    Analyze a PostgreSQL query using EXPLAIN ANALYZE and return performance metrics
    """
    try:
        # First, wrap the query in EXPLAIN ANALYZE
        explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
        
        # Execute the explain query
        result = db.execute(text(explain_query)).scalar()
        
        if not result:
            return None
            
        # Parse the JSON result
        plan_data = json.loads(result)[0]
        
        # Extract relevant metrics with detailed explanations
        analysis = {
            # The type of operation being performed (e.g., Seq Scan, Index Scan, Aggregate, Hash Join)
            # This indicates how PostgreSQL plans to execute the query
            "Query Analysis Type": plan_data['Plan']['Node Type'],
            
            # The number of rows PostgreSQL estimates will be processed
            # This is based on table statistics and helps in query planning
            "Estimated Rows": plan_data['Plan']['Plan Rows'],
            
            # The actual number of rows processed during execution
            # Comparing this with Estimated Rows helps identify statistics accuracy
            "Actual Rows": plan_data['Plan'].get('Actual Rows'),
            
            # Total time taken to execute the query in milliseconds
            # Includes both planning and execution time
            "Execution Time (ms)": plan_data['Execution Time'],
            
            # Time taken to create the execution plan in milliseconds
            # This is the time spent analyzing the query and choosing the best plan
            "Planning Time (ms)": plan_data['Planning Time'],
            
            # Buffer usage statistics showing how the query interacts with PostgreSQL's shared buffer cache
            "Buffers": {
                # Number of blocks read from disk
                # Higher values indicate more I/O operations
                "Shared Read Blocks": plan_data['Plan'].get('Shared Read Blocks', 0),
                
                # Number of blocks found in the buffer cache
                # Higher values indicate better cache utilization
                "Shared Hit Blocks": plan_data['Plan'].get('Shared Hit Blocks', 0),
                
                # Number of blocks written to disk
                # Relevant for write operations
                "Shared Written Blocks": plan_data['Plan'].get('Shared Written Blocks', 0)
            },
            
            # Cost metrics used by the query planner
            "Cost": {
                # Initial cost before the first row is returned
                # Important for queries that need quick first results
                "Startup Cost": plan_data['Plan'].get('Startup Cost'),
                
                # Total cost of executing the entire query
                # Used by planner to choose between different execution strategies
                "Total Cost": plan_data['Plan'].get('Total Cost')
            },
            
            # Amount of memory allocated for the query execution
            # Important for identifying memory-intensive operations
            "Memory Usage": plan_data['Plan'].get('Memory Usage'),
            
            # Number of parallel workers planned for query execution
            # Higher values indicate better parallel processing capability
            "Parallel Workers": plan_data['Plan'].get('Workers Planned', 0),
            
            # Number of sub-plans in the execution plan
            # Higher values might indicate complex query structure
            "Subplans": len(plan_data['Plan'].get('Plans', [])),
            
            # List of indexes used in the query execution
            # Helps in understanding index utilization and potential optimization opportunities
            "Index Usage": [node['Index Name'] for node in plan_data['Plan'].get('Plans', []) 
                          if node.get('Node Type') == 'Index Scan']
        }
        
        return analysis
    except Exception as e:
        print(f"Error analyzing query: {str(e)}")
        return None

def extract_sql_query(response_text: str) -> str:
    """
    Extract only the SQL query from the LLM response, removing any explanatory text.
    """
    # Remove any text before the first SQL statement
    if "```sql" in response_text:
        response_text = response_text.split("```sql")[1]
    elif "```" in response_text:
        response_text = response_text.split("```")[1]
    
    # Remove any text after the last SQL statement
    if "```" in response_text:
        response_text = response_text.split("```")[0]
    
    # Clean up the query
    query = response_text.strip()
    
    # Remove any remaining explanatory text
    if "Here is the generated PostgreSQL query:" in query:
        query = query.split("Here is the generated PostgreSQL query:")[1]
    if "This query" in query:
        query = query.split("This query")[0]
    
    return query.strip()

def generate_metric_query(description: str, existing_metrics: list) -> Tuple[str, str, str]:
    """
    Generate SQL query for metric calculation using Groq
    Returns tuple of (query, value_type, prompt)
    """
    client = Groq(api_key=os.environ.get("GROQ_API_KEY"))
    
    # Get schema information dynamically
    customer_columns = get_table_schema(customer)
    transaction_columns = get_table_schema(transactions)
    metrics_tables = get_metrics_tables_schema()
    probe_merchant_columns = get_table_schema(probe_merchant)
    probe_financials_columns = get_table_schema(probe_financials)
    probe_msme_columns = get_table_schema(probe_msme_supplier_payment_delays)
    probe_directors_columns = get_table_schema(probe_authorized_signatories)
    probe_rpt_company_columns = get_table_schema(probe_related_party_transactions_company)
    probe_rpt_llp_columns = get_table_schema(probe_related_party_transactions_llp)
    probe_rpt_individual_columns = get_table_schema(probe_related_party_transactions_individual)
    probe_rpt_others_columns = get_table_schema(probe_related_party_transactions_others)
    
    # Get prompt from static file
    prompt = get_metric_generation_prompt(
        description=description,
        customer_columns=customer_columns,
        transaction_columns=transaction_columns,
        metrics_tables=metrics_tables,
        probe_merchant_columns=probe_merchant_columns,
        probe_financials_columns=probe_financials_columns,
        probe_msme_columns=probe_msme_columns,
        probe_directors_columns=probe_directors_columns,
        probe_rpt_company_columns=probe_rpt_company_columns,
        probe_rpt_llp_columns=probe_rpt_llp_columns,
        probe_rpt_individual_columns=probe_rpt_individual_columns,
        probe_rpt_others_columns=probe_rpt_others_columns,
        existing_metrics=existing_metrics
    )

    try:
        response = client.chat.completions.create(
            messages=[{"role": "user", "content": prompt}],
            model="llama3-70b-8192",
            temperature=0.1,
            max_tokens=1000
        )
        
        raw_query = response.choices[0].message.content.strip()
        print(f"Generated query: {raw_query}")
        query = extract_sql_query(raw_query)
        print(f"Generated query: {query}")
        
        if query.lower() == "query_outbound":
            return None, None, None
            
        # Determine the value type from the query
        value_type = determine_metric_value_type(query)
        return query, value_type, prompt
    except Exception as e:
        print(f"Error generating metric query: {str(e)}")
        return None, None, None

def generate_source_query(metric_query: str) -> str:
    """
    Generate source data query using Groq
    """
    client = Groq(api_key=os.environ.get("GROQ_API_KEY"))
    
    # Get schema information dynamically
    customer_columns = get_table_schema(customer)
    transaction_columns = get_table_schema(transactions)
    metrics_tables = get_metrics_tables_schema()
    probe_merchant_columns = get_table_schema(probe_merchant)
    probe_financials_columns = get_table_schema(probe_financials)
    probe_msme_columns = get_table_schema(probe_msme_supplier_payment_delays)
    probe_directors_columns = get_table_schema(probe_authorized_signatories)
    probe_rpt_company_columns = get_table_schema(probe_related_party_transactions_company)
    probe_rpt_llp_columns = get_table_schema(probe_related_party_transactions_llp)
    probe_rpt_individual_columns = get_table_schema(probe_related_party_transactions_individual)
    probe_rpt_others_columns = get_table_schema(probe_related_party_transactions_others)
    
    # Get prompt from static file
    prompt = get_source_query_prompt(
        metric_query=metric_query,
        customer_columns=customer_columns,
        transaction_columns=transaction_columns,
        metrics_tables=metrics_tables,
        probe_merchant_columns=probe_merchant_columns,
        probe_financials_columns=probe_financials_columns,
        probe_msme_columns=probe_msme_columns,
        probe_directors_columns=probe_directors_columns,
        probe_rpt_company_columns=probe_rpt_company_columns,
        probe_rpt_llp_columns=probe_rpt_llp_columns,
        probe_rpt_individual_columns=probe_rpt_individual_columns,
        probe_rpt_others_columns=probe_rpt_others_columns
    )

    try:
        response = client.chat.completions.create(
            messages=[{"role": "user", "content": prompt}],
            model="llama3-70b-8192",
            temperature=0.1,
            max_tokens=1000
        )
        
        raw_query = response.choices[0].message.content.strip()
        query = extract_sql_query(raw_query)
        print(f"Generated source query: {query}")
        
        if query.lower() == "query_outbound":
            return None
        return query
    except Exception as e:
        print(f"Error generating source query: {str(e)}")
        return None

@router.post("/metrics/generate")
async def generate_metric(
    name: str,
    description: str,
    frequency: str,
    tags: List[str],
    metric_code: str,
    db: Session = Depends(get_db)
):
    """
    Generate a new metric based on name and description
    """
    try:
        # Get existing metrics for reference
        existing_metrics = db.query(MetricStore).all()
        existing_metrics_list = [
            {
                "name": m.name,
                "description": m.description,
                "query": m.query,
                "metric_table": m.metric_table,
                "metric_value_type": m.metric_value_type
            } for m in existing_metrics
        ]

        # Generate metric query and determine value type
        metric_query, value_type, prompt = generate_metric_query(description, existing_metrics_list)
        if not metric_query:
            return {"status": "failed", "message": "Could not generate metric query"}

        # Generate source query
        source_query = generate_source_query(metric_query)
        if not source_query:
            return {"status": "failed", "message": "Could not generate source query"}

        # Analyze the generated query
        # query_analysis = analyze_query(metric_query, db)

        # Create new metric in store
        new_metric = MetricStore(
            name=name,
            description=description,
            status='pending',
            query=metric_query,
            source_fetching_query=source_query,
            metric_value_type=value_type,
            prompt=prompt,
            # query_analysis=query_analysis,  # Store the query analysis
            frequency=frequency,
            tags=tags,
            metric_code=metric_code,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        db.add(new_metric)
        db.commit()
        db.refresh(new_metric)

        return {
            "status": "success",
            "message": "Metric generated successfully",
            "metric_id": str(new_metric.id),
            "metric_value_type": value_type,
            # "query_analysis": query_analysis
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e)) 