import json
import uuid
import logging
from datetime import datetime
from decimal import Decimal

from sqlalchemy.orm import Session
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy import Column, String, DateTime
from sqlalchemy.ext.declarative import declarative_base

# --- Setup logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- JSON utility that handles Decimal ---
class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super().default(obj)

def serialize_to_json(data):
    """Serialize Python object to JSON string, handling Decimal values."""
    return json.dumps(data, cls=DecimalEncoder)

# --- SQLAlchemy model ---
Base = declarative_base()

class merchant_metrics(Base):
    __tablename__ = "merchant_metrics"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    merchant_id = Column(String, nullable=False)
    metric_type = Column(String, nullable=False)
    metric_value = Column(JSONB, nullable=False)
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

# --- MetricCalculator class ---
class MetricCalculator:
    def __init__(self, db: Session):
        self.db = db

    def store_metric(self, merchant_id: str, metric_type: str, metric_value: any):
        """Store a metric value in the merchant_metrics table"""
        try:
            # Convert to JSON (Decimal-safe)
            metric_value_json = serialize_to_json(metric_value)

            # Create and add new metric entry
            metric = merchant_metrics(
                merchant_id=str(merchant_id),
                metric_type=metric_type,
                metric_value=metric_value_json,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )

            self.db.add(metric)
            self.db.commit()

        except Exception as e:
            logger.error(f"Error storing metric {metric_type} for merchant {merchant_id}: {str(e)}")
            self.db.rollback()
            raise
