from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Dict, Any
from ..models.models import rules_store, merchant_red_flags
from ..schemas.rule_schema import RuleGroup, RuleCondition, is_metric_table
import json
from datetime import datetime
from decimal import Decimal
from uuid import UUID

class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super(DecimalEncoder, self).default(obj)

class RedFlagEngine:
    def __init__(self, db: Session):
        self.db = db

    def get_active_rules(self) -> List[rules_store]:
        """Fetch all active rules from the database"""
        return self.db.query(rules_store).filter(
            rules_store.is_active == True,
            rules_store.status == True,
            rules_store.is_deleted == False
        ).all()

    def extract_fields_from_rule(self, rule: RuleGroup) -> List[str]:
        """Extract all fields mentioned in the rule conditions"""
        fields = set()
        
        def extract_from_condition(condition: RuleCondition):
            if is_metric_table(condition.table):
                fields.add(condition.condition)
            else:
                fields.add(condition.condition.split()[0])

        def process_group(group: RuleGroup):
            if group.and_conditions:
                for condition in group.and_conditions:
                    if isinstance(condition, RuleCondition):
                        extract_from_condition(condition)
                    else:
                        process_group(condition)
            if group.or_conditions:
                for condition in group.or_conditions:
                    if isinstance(condition, RuleCondition):
                        extract_from_condition(condition)
                    else:
                        process_group(condition)

        process_group(rule)
        return list(fields)

    def build_select_query(self, table: str, fields: List[str]) -> str:
        """Build SQL query to fetch required data"""
        if is_metric_table(table):
            return f"""
                WITH latest_metrics AS (
                    SELECT DISTINCT ON (merchant_id, metric_type)
                        merchant_id,
                        metric_type,
                        metric_value,
                        year
                    FROM {table}
                    WHERE metric_type IN ({','.join([f"'{field}'" for field in fields])})
                    ORDER BY merchant_id, metric_type, year DESC
                )
                SELECT merchant_id, metric_type, metric_value
                FROM latest_metrics
            """
        else:
            return f"""
                SELECT DISTINCT merchant_id, {', '.join(fields)}
                FROM {table}
            """

    def evaluate_condition(self, condition: RuleCondition, data: Dict[str, Any]) -> bool:
        """Evaluate if a condition is met for given data"""
        if is_metric_table(condition.table):
            value = data.get(condition.condition)
            # Handle JSONB metric value
            if isinstance(value, dict):
                value = value.get('value', value)
        else:
            field = condition.condition.split()[0]
            value = data.get(field)

        if value is None:
            return False

        operator = condition.operator
        expected_value = condition.value

        # Convert values to float for numeric comparisons
        if operator in ['>', '<', '>=', '<=', '==', '!=']:
            try:
                # Handle string values that might be numeric
                if isinstance(value, str):
                    value = value.strip('"')  # Remove quotes if present
                value = float(value)
                expected_value = float(expected_value)
            except (ValueError, TypeError):
                print(f"Type conversion failed for value: {value}, expected_value: {expected_value}")
                return False

        if operator == '>':
            return value > expected_value
        elif operator == '<':
            return value < expected_value
        elif operator == '>=':
            return value >= expected_value
        elif operator == '<=':
            return value <= expected_value
        elif operator == '==':
            return value == expected_value
        elif operator == '!=':
            return value != expected_value
        elif operator == 'in':
            return value in expected_value
        elif operator == 'not in':
            return value not in expected_value
        return False

    def evaluate_rule(self, rule: RuleGroup, data: Dict[str, Any]) -> bool:
        """Evaluate if a rule is met for given data"""
        if rule.and_conditions:
            return all(
                self.evaluate_condition(condition, data) if isinstance(condition, RuleCondition)
                else self.evaluate_rule(condition, data)
                for condition in rule.and_conditions
            )
        elif rule.or_conditions:
            return any(
                self.evaluate_condition(condition, data) if isinstance(condition, RuleCondition)
                else self.evaluate_rule(condition, data)
                for condition in rule.or_conditions
            )
        return False

    def create_red_flag(self, merchant_id: str, rule: rules_store, metric_values: Dict[str, Any]):
        """Create a red flag entry in the database"""
        # Convert Decimal values to float before storing
        metric_values_json = json.loads(json.dumps(metric_values, cls=DecimalEncoder))
        
        red_flag = merchant_red_flags(
            merchant_id=merchant_id,
            rule_code=rule.code,
            description=rule.description,
            severity=rule.severity,
            metric_values=metric_values_json,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        self.db.add(red_flag)
        self.db.commit()

    def process_rules(self, merchant_ids: List[UUID] = None, rule_codes: List[str] = None):
        """Main method to process rules and create red flags for specified merchants and rule codes"""
        print("Starting red flag engine process")
        query = self.db.query(rules_store).filter(
            rules_store.is_active == True,
            rules_store.status == True,
            rules_store.is_deleted == False
        )
        
        if rule_codes:
            query = query.filter(rules_store.code.in_(rule_codes))
            
        rules = query.all()
        print(f"Found {len(rules)} active rules to process")
        
        for rule in rules:
            try:
                print(f"\nProcessing rule: {rule.code} - {rule.name}")
                
                # # Skip rules that don't match specified metrics
                # if rule_codes:
                #     rule_fields = self.extract_fields_from_rule(RuleGroup(**rule.rule))
                #     if not any(field in rule_codes for field in rule_fields):
                #         print(f"Skipping rule {rule.code} - no matching metrics")
                #         continue
                
                # print(f"Rule data: {rule.rule}")
                
                # Validate rule structure
                if not isinstance(rule.rule, dict):
                    print(f"Invalid rule format for {rule.code}: rule must be a dictionary")
                    continue
                
                # Handle direct condition
                if all(key in rule.rule for key in ['table', 'condition', 'operator', 'value']):
                    print("Processing direct condition")
                    condition = RuleCondition(**rule.rule)
                    fields = [condition.condition]
                    tables = {condition.table}
                # Handle grouped conditions
                elif 'and' in rule.rule or 'or' in rule.rule:
                    print("Processing grouped conditions")
                    rule_group = RuleGroup(**rule.rule)
                    fields = self.extract_fields_from_rule(rule_group)
                    tables = set()
                    def extract_tables(group: RuleGroup):
                        if group.and_conditions:
                            for condition in group.and_conditions:
                                if isinstance(condition, RuleCondition):
                                    tables.add(condition.table)
                                else:
                                    extract_tables(condition)
                        if group.or_conditions:
                            for condition in group.or_conditions:
                                if isinstance(condition, RuleCondition):
                                    tables.add(condition.table)
                                else:
                                    extract_tables(condition)
                    extract_tables(rule_group)
                else:
                    print(f"Invalid rule format for {rule.code}: must be a direct condition or contain 'and'/'or' operator")
                    continue
                
                print(f"Extracted fields: {fields}")
                print(f"Tables to process: {tables}")
                
                # Process each table
                for table in tables:
                    print(f"\nProcessing table: {table}")
                    query = self.build_select_query(table, fields)
                    
                    # Add merchant filter if merchant_ids provided
                    if merchant_ids:
                        merchant_ids_str = ','.join([f"'{str(id)}'" for id in merchant_ids])
                        if "WHERE" in query.upper():
                            query = query.replace("WHERE", f"WHERE merchant_id IN ({merchant_ids_str}) AND")
                        else:
                            query = f"{query} WHERE merchant_id IN ({merchant_ids_str})"
                    
                    print(f"Generated query: {query}")
                    results = self.db.execute(text(query)).fetchall()
                    print(f"Found {len(results)} records")
                    
                    # Group results by merchant_id
                    merchant_data = {}
                    for row in results:
                        merchant_id = row.merchant_id
                        if merchant_id not in merchant_data:
                            merchant_data[merchant_id] = {}
                        
                        if is_metric_table(table):
                            metric_type = row.metric_type
                            metric_value = row.metric_value
                            merchant_data[merchant_id][metric_type] = metric_value
                        else:
                            for field in fields:
                                merchant_data[merchant_id][field] = getattr(row, field)
                    
                    print(f"Grouped data for {len(merchant_data)} merchants")
                    
                    # Evaluate rules for each merchant
                    red_flags_created = 0
                    for merchant_id, data in merchant_data.items():
                        if 'and' in rule.rule or 'or' in rule.rule:
                            is_triggered = self.evaluate_rule(rule_group, data)
                        else:
                            is_triggered = self.evaluate_condition(condition, data)
                            
                        if is_triggered:
                            print(f"Rule triggered for merchant {merchant_id}")
                            self.create_red_flag(merchant_id, rule, data)
                            red_flags_created += 1
                    
                    print(f"Created {red_flags_created} red flags for table {table}")
                
                print(f"Completed processing rule: {rule.code}")
                
            except Exception as e:
                print(f"Error processing rule {rule.code}: {str(e)}")
                continue
        
        print("\nRed flag engine process completed") 