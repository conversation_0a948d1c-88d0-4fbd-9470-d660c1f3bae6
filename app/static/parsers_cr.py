from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel
from typing import List

# 17. Audiot Concerns
audit_concerns_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "audit_concerns": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "audit_concern": {"type": "string"},
                    "summary": {"type": "string"},
                    "title": {"type": "string"},
                    "year": {"type": "string"},
                }
            }
        }
    }
}
)

# 18. Insights from the Financial Report
financial_report_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "insights_and_flags": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "insight": {"type": "string"},
                    "tag": {"type": "string"},
                    "filename": {"type": "string"},
                    "bare_text": {"type": "string"},
                    "page_number": {"type": "string"},
                    "year": {"type": "string"},
                }
            }
        }
    }
}
)

about_the_company_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "company_name": {"type": "string"},
        "about_the_company": {"type": "string"},
        "source_urls": {
            "type": "array",
            "items": {
                "type": "string"
            }
        }
    }
}
)

about_the_industry_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "about_the_industry": {"type": "string"},
        "is_industry_risky": {"type": "string"},
        "justification": {"type": "string"},
    }
}
)

external_red_flags_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "red_flag": {"type": "string"},
        "severity": {"type": "string"},
        }
    }
)

unearned_revenue_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "unearned_revenue": {"type": "string"},
    }
}
)