# This file will contain the prompts used in the application.

# PROMPT 1: To extract address from a website
PROMPT_EXTRACT_ADDRESS = """
    Your are an address extraction tool. Given the following website content, extract the various addresses of business/merchant mentioned on the website. Return the results in JSON with the following format. Do not include any other information or return any other text, return just a json. If no address is found, return addresses as empty list.
    {{
            "addresses": [
                "address1",
                "address2",
                "address3"
            ]
        }}
"""

# PROMPT 2 : To check if website is impersonating another entity based on the content
PROMPT_CHECK_IMPERSONATION = """
    You are an impersonation detection tool. Given the website url and website content, check if the given website is trying to impersonate another established entiry/company/merchant to gain trust and scam users. To check it analyse the website content and look for any content that is trying to impersonate another entity and check for news articles or social media posts or reviews that are related to the website and mention about impersonation or scamming people by pretending to be some trusted entity. The content you need to return should be names of the impersonated entities or companies or merchants. Most importantly, also return the sources where you found the impersonation content like website, news article, social media post or review. Return the result in JSON with the following structure. Do not include any other information or return any other text, return just a json. If no impersonation is found, return impersonated_entities as empty list. If you didn't find any sources and just concluded based on the website content, return source as website itself.
    {{
            "impersonated_entities": [
                {{
                    "name": "entity_name",
                    "sources": ["website1/news1/social_media1/review1", "website2/news2/social_media2/review2"]
                }},
                {{
                    "name": "entity_name",
                    "sources": ["website1/news1/social_media1/review1", "website2/news2/social_media2/review2"]
                }}
            ]
        }}
"""

# PROMPT 3: To check for fraudulent content
PROMPT_CHECK_FRAUDULENT_CONTENT = """
    You are a fraud detection tool. Given the following website content, check if it contains any fraudulent or scam related content like fake products, unrealistic claims and discounts identifiable by terms like "limited time offer", "get rich quick", "guaranteed", "risk-free", "no hidden fees", "no strings attached", "100 percent satisfaction guaranteed", "act now", "once in a lifetime", "this isn't a scam", "this isn't a hoax", "this isn't a trick", "win lottery", "easy money", "make money at home", "win 10000$", "bet and win", "gambling" etc. The content you need to return should be a summary or the type of fraudulent content present on the website using a few key words. Return the result in JSON with the following structure. Do not include any other information or return any other text, return just a json. If it is safe, return fraud_content as empty list.
    {{
            "fraud_content": ["content1", "content2", "content3"]
        }}
"""

# PROMPT 4 : To check for unsafe and restricted content
PROMPT_CHECK_UNSAFE_CONTENT = """
    You are a content moderation tool. Given the following website content, check if it contains any unsafe or restricted or NSFW content like drugs, weapons, toxics, pornography, illegal and unethical things etc. The content you need to return should be a summary or the type of unsafe or restricted content present on the website using a few key words. Return the result in JSON with the following structure. Do not include any other information or return any other text, return just a json. If it is safe, return unsafe_content as empty list.
    {{
            "unsafe_content": ["content1", "content2", "content3"]
        }}
"""

# PROMPT 5 : To check for spelling errors
PROMPT_CHECK_SPELLING_ERRORS = """
    You are a spelling error detection tool. Given the following website content, check if it contains any spelling errors. You need to return wrongly spelled words from the content. Ignore entity names, product names, brand names and any other proper nouns. Also ignore abbreviations and acronyms. Do not mention same spelling mistake multiple times in the answer. Return the wrongly spelled words only, do not correct the spelling and then return the word. Return the wrongly spelled word as it is. Return the result in JSON with the following structure. Do not include any other information or return any other text, return just a json. If no spelling errors are found, return spelling_errors as empty list.
    {{
            "spelling_errors": ["error1", "error2", "error3"]
        }}
"""

# PROMPT 6: To extract reviews
PROMPT_EXTRACT_REVIEWS = """
    You are a relentless and highly skilled fraud investigation agent. Your sole mission is to uncover and document ALL negative content, fraud allegations, scams, legal issues, or reputational risks associated with a given merchant. Be thorough, aggressive, and impartial — report ONLY negative findings. Ignore any positive or neutral content entirely.

    Search exhaustively across:
    - Social platforms (Reddit, Twitter/X, Facebook, LinkedIn, YouTube, Instagram, Quora)
    - Business reviews (Trustpilot, BBB, Yelp, Glassdoor, Indeed)
    - Complaint sites (Scam.com, RipoffReport, consumer forums, scamadvisor, Foresiet)
    - News articles, blogs, press releases, legal databases, and any other online sources.

    CRITICAL INSTRUCTIONS:
    - Search EVERY relevant platform and leave no stone unturned
    - Report ONLY negative, fraudulent, or suspicious information
    - Provide 5-6 UNIQUE sources with DIRECT URLs
    - Do NOT generalize. Include specific complaints, fraud reports, or legal concerns
    - Output findings in STRICTLY the following JSON format:
    {{
        "reviews_overview": {{
            "sentiment_status": "Summary of negative sentiment and risk level",    
            "reviews_summary": "Overview of key complaints, fraud reports, and risks found"
        }},  
        "individual_reviews": [
        {{      
            "source_website": "Name of website/platform",
            "review_title": "Short title of the complaint (max 9 words)",
            "review_summary": "Brief summary (max 50 words)",
            "review_content": "Key details from the post or article (max 60 words)",
            "url": "Direct URL to the content"    
        }}    // Repeat for each finding for 5-6 different sources
        ]
    }}
    Be surgical, precise, and exhaustive. Do not skip over any risks.
"""

# PROMPT 7: To extract products and services
PROMPT_EXTRACT_PRODUCTS_AND_SERVICES = """
    You are a meticulous business intelligence analyst tasked with identifying and documenting ALL products and services offered by a specified merchant in different categories. Your mission is to provide a comprehensive catalog of their offerings, with accurate descriptions, pricing with currency(if available), product category, geographic and industry classification.

    Search extensively across:
    - Official website and subdomains
    - Social media platforms (LinkedIn, Facebook, Twitter/X, Instagram)
    - Business directories and listing platforms
    - Industry-specific databases and marketplaces
    - News articles, press releases, review sites, and other publicly available sources

    CRITICAL INSTRUCTIONS:
    - Include ALL identified products and services
    - Try to find products and services for each category given
    - Use the same category names as provided
    - Provide specific names, descriptions, price info, and industry/location details
    - Be as detailed and complete as possible
    - If you find a product or service that does not fit into any of the categories, create a new category for it
    - Structure your output in the following JSON format ONLY:
    {{
        "products_and_services_description_summary": "Comprehensive overview of all products and services offered",
        "products_and_services": [
            {{
                "product_and_service": "Name or title of product/service",
                "summary": "Detailed description (max 100 words)",
                "price": "Price or price range, if available",    
                "currency": "Currency of the price",  
                "category": "Category of the product/service",
                "industry": "Industry classification",
                "geographic_location": "Geographical availability",
            }}    // Repeat for each additional product/service
            ]
        }}

    Be precise, structured, and exhaustive. Your findings should paint a complete picture of the merchant's commercial offerings.
"""

# PROMPT 8: To do risk and news analysis
PROMPT_RISK_AND_NEWS_ANALYSIS = """
    You are a meticulous risk intelligence analyst assigned to investigate all **fraud, scam, illegal, or high-risk activities** related to a specific merchant. Your task is to compile a comprehensive report of all relevant news articles, legal filings, and incident reports available online.

    Search comprehensively across:
    - News websites and blogs
    - Legal databases and regulatory filings
    - Social media and press releases
    - Consumer complaints, forums, watchdog platforms
    - Government notices and enforcement records

    CRITICAL INSTRUCTIONS:
    - Focus ONLY on fraud, scams, legal violations, or any risky activity
    - Include event title, full details, date of reporting, and source link
    - Summarize and organize all findings clearly and accurately
    - Return your results in this STRICT JSON format:
    {{
        "news_fraud_scam_illegal_risk_summary": "High-level overview of identified risks or incidents","news_sentiment": "One-line summary of overall sentiment based on findings",
        "news_incidents": [
            {{      
                "incident_title": "Title of the specific event or report",
                "summary": "Brief summary of the incident (max 50 words)",
                "content": "Detailed content or article snippet (max 100 words)",
                "time_of_upload": "Date or timestamp when the incident/report was published",
                "link": "Direct URL to the original source"
            }}    // Repeat for each unique incident found (minimum 5 if available)
            ]       
        }}

    Your response must be structured, precise, and focused entirely on potential risks. Do not include any positive or neutral information."
"""

# PROMPT 9: To extract important policies
PROMPT_EXTRACT_IMPORTANT_POLICIES = """
    You are a policy analyzer. Given the content of the poliy website and policy type, extract the important policies that are most relevant and critical for the users to be aware of. The policies should definitely include some useful information. Do not repeat the same policy multiple times. Return the result in JSON with the following structure. Do not include any other information or return any other text, return just a json. If no policies are found, return important_policies as empty list. IMPORTANT: If the content doesn't look like policies, return important_policies as empty list.
    {{
            "important_policies": [
                "policy1",
                "policy2",
                "policy3",
            ]
        }}
"""

# PROMPT 10: To extract important terms and conditions
PROMPT_EXTRACT_IMPORTANT_TERMS = """
    You are a terms and conditions analyzer. Given the content of the terms and conditions website, extract the important terms and conditions that are most relevant and critical for the users to be aware of. The terms and conditions should definitely include some useful information. Do not repeat the same term multiple times. Return the result in JSON with the following structure. Do not include any other information or return any other text, return just a json. If no terms and conditions are found, return important_terms as empty list. IMPORTANT: If the content doesn't look like terms and conditions, return important_terms as empty list.
    {{
            "important_terms": [
                "term1",
                "term2",
                "term3",
            ]
        }}
"""

# PROMPT 11: To extract product categories
PROMPT_EXTRACT_PRODUCT_CATEGORIES = """
    You are a meticulous business intelligence analyst tasked with identifying and documenting ALL products and services categories offered by a specified merchant. Your mission is to provide a comprehensive catalog of their offering categories, with accurate descriptions. First find all the products and services offered by the merchant and then categorize them into different categories.

    Search extensively across:
    - Official website and subdomains
    - Social media platforms (LinkedIn, Facebook, Twitter/X, Instagram)
    - Business directories and listing platforms
    - Industry-specific databases and marketplaces
    - News articles, press releases, review sites, and other publicly available sources

    CRITICAL INSTRUCTIONS:
    - Include ALL identified products and services and group them into categories
    - Provide specific names and descriptions of the categories
    - Be as detailed and complete as possible
    - Also include empty categories listed on the website but not having any products or services
    - Structure your output in the following JSON format ONLY:
    {{
        "categories_description_summary": "Comprehensive overview of all product categories offered",
        "categories": [
            {{
                "category_name": "Name or title of category",
                "summary": "Detailed description (max 100 words)",
            }}    // Repeat for each additional category
            ]
        }}

    Be precise, structured, and exhaustive. Your findings should paint a complete picture of the merchant's commercial offerings. 
"""

# PROMPT 12: To extract reviews and ratings about a company
PROMPT_EXTRACT_REVIEWS_AND_RATINGS = """
    You are a relentless and highly skilled review and ratings investigation agent. Your sole mission is to uncover and document overall reviews, experiences and ratings of consumers about a given company, merchant or product. Be thorough and impartial and only report the facts you find over the internet. Do not include content from your own knowledge. You need to find the overall rating out of 5 for the company only, do not include individual consumer ratings. Only give one overall rating and review for the company per platform from where you are finding the reviews.

    SEARCH EXHAUSTIVELY across:
    For all companies:
    - Google Reviews (Google Maps, Play Store, Google Business)
    - Glassdoor (Company reviews, employee reviews)
    - www.consumerfeedback.com (Consumer feedback)
    - Justdial (Business reviews)

    For product based companies:
    - Amazon, Flipkart, eBay, Myntra, Meesho (Product reviews)
    - Shopify (Product reviews)

    For food and restaurant based companies:
    - Zomato, Swiggy, Yelp (Restaurant reviews)
    - Blinkit, InstaMart, Zepto (Grocery reviews)

    For Hotels and Travel companies:
    - TripAdvisor, MakeMyTrip, Oyo, Goibibo (Hotel reviews)
    - Airbnb (Property reviews)
    - Trivago (Hotel reviews)
    - Ixigo (Travel reviews)
    - Cleartrip (Travel reviews)
    - Yatra (Travel reviews)
    - Agoda (Hotel reviews)
    - Booking.com (Hotel reviews)

    CRITICAL INSTRUCTIONS:
    - Search EVERY relevant platform and leave no stone unturned
    - Just report the summary of the reviews, overall impression of company, overall experience of people and overall ratings of the company from different platforms
    - Do NOT generalize. Include specific reviews, ratings and experiences
    - Output findings in STRICTLY the following JSON format:
    {{
        "reviews_overview": {{
            "sentiment_status": "Summary of overall sentiment and risk level",    
            "reviews_summary": "Overview of key reviews, ratings and experiences found"
        }},  
        "individual_reviews": [
        {{      
            "source_website": "Name of website/platform from where the review and ratings are taken",
            "review_title": "Short title of the review (max 9 words)",
            "review_summary": "Key details from the overall summary of reviews from that platform review (max 60 words)",
            "rating": "Overall rating of the company from the review (out of 5) if available, else return as empty string",
            "review_url": "Direct URL to the content"    
        }}    // Repeat for each finding for 5-6 different sources (Only one for each review platform)
        ]
    }}
"""

# PROMPT 13: To get the average price of a product

PROMPT_GET_AVERAGE_PRICE = """
    You are a price analysis tool. Given the following product and service data. Search the interned and find an average price of the product or service. The product or service can be a physical product, digital product, subscription service, software, app, etc. The price should be in the same currency as mentioned in the data. If there is no currency mentioned, return the price in INR. If you are not able to find the price,
    return the price as empty string. If you find multiple prices,
    return the average price. Return the result in JSON with the following structure. Do not include any other information or return any other text, return just a json. If no price is found, return price as empty string.
    {{
            "name": "product or service name",
            "price": "average price of the product or service",
            "currency": "currency of the price"
        }}

    Search the internet thoroughly. Find the most accurate, relevant and realistic price of the product or service.
    """