from langchain_core.output_parsers import JsonOutputParser

# PARSER 1: Parser for extracting keywords
address_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "addresses": {
            "type": "array",
            "items": {"type": "string"}
        },
    },
})


# PARSER 2: Parser for extracting summary
summary_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "summary": {"type": "string"},
    },
})


# PARSER 3: Parser for checking impersonation
impersonation_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "impersonated_entities": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "sources": {
                        "type": "array",
                        "items": {"type": "string"}
                    }
                }
            }
        },
    },
})


# PARSER 3: Parser for checking fraudulent content
fraudulent_content_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "fraud_content": {
            "type": "array",
            "items": {"type": "string"}
        },
    },
})


# PARSER 4: Parser for checking unsafe content
unsafe_content_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "unsafe_content": {
            "type": "array",
            "items": {"type": "string"}
        },
    },
})


# PARSER 5: Parser for checking relevance with description
spelling_errors_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "spelling_errors": {
            "type": "array",
            "items": {"type": "string"}
        },
    },
})


# PARSER 6: Parser for extracting reviews
reviews_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "reviews_overview": {
            "type": "object",
            "properties": {
                "sentiment_status": {"type": "string"},
                "reviews_summary": {"type": "string"},
            }
        },
        "individual_reviews": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "source_website": {"type": "string"},
                    "review_title": {"type": "string"},
                    "review_content": {"type": "string"},
                    "review_summary": {"type": "string"},
                    "review_url": {"type": "string"},
                }
            }
        },
    },
})


# PARSER 7: Parser for extracting products and services
products_and_services_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "products_and_services_description_summary": {"type": "string"},
        "products_and_services": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "product_name": {"type": "string"},
                    "summary": {"type": "string"},
                    "price": {"type": "string"},
                    "currency": {"type": "string"},
                    "category": {"type": "string"},
                    "industry": {"type": "string"},
                    "geographic_location": {"type": "string"},
                }
            }
        },
    },
})


# PARSER 8: Parser for extracting risk and news analysis
risk_and_news_analysis_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "news_fraud_scam_illegal_risk_summary": {"type": "string"},
        "news_sentiment": {"type": "string"},
        "news_incidents": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "incident_title": {"type": "string"},
                    "content": {"type": "string"},
                    "summary": {"type": "string"},
                    "time_of_upload": {"type": "string"},
                    "link": {"type": "string"},
                }
            }
        },
    },
})


# PARSER 9: Parser for extracting policies
policies_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "important_policies": {
            "type": "array",
            "items": {
                "type": "string"
            }
        }
    },
})


# PARSER 10: Parser for extracting terms and conditions
terms_and_conditions_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "important_terms": {
            "type": "array",
            "items": {
                "type": "string"
            }
        },
    },
})


# PARSER 11: Parser for extracting products and services categories
products_and_services_categories_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "categories": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "category_name": {"type": "string"},
                    "summary": {"type": "string"},
                }
            }
        },
    },
})

# PARSER 12: Parser for reviews and ratings extraction
reviews_and_ratings_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "reviews_overview": {
            "type": "object",
            "properties": {
                "sentiment_status": {"type": "string"},
                "reviews_summary": {"type": "string"},
            }
        },
        "individual_reviews": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "source_website": {"type": "string"},
                    "review_title": {"type": "string"},
                    "review_summary": {"type": "string"},
                    "rating": {"type": "string"},
                    "review_url": {"type": "string"},
                }
            }
        },
    },
})

# PARSER 12: Parser to parse average price
average_price_parser = JsonOutputParser(pydantic_object={
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "price": {"type": "string"},
        "currency": {"type": "string"},
    },
})