# -------------- Prompts for deep research on Insolvancy risk of spicejet -------------------
PROMPT_DEEP_RESEARCH = """
You are a financial intelligence analyst with deep expertise in risk modeling, in the given industry/industries  operations, and corporate financial distress indicators. Your goal is to gather and analyze the most recent and relevant external data signals that could indicate credit or insolvency risk for the given company.

Instructions:
Using up-to-date web search, regulatory databases, news platforms, and financial intelligence sources:

Collect data for each of the following external indicators of financial distress (grouped by category):


### **Legal, Regulatory, and Compliance Issues**

- Ongoing or past lawsuits or arbitration involving the company.
- Class-action lawsuits or mass consumer litigation.
- Investigations or raids by regulatory agencies (e.g., SEC, CCI, ED, IRS).
- Regulatory fines, sanctions, or license suspensions.
- Non-compliance with environmental, safety, or tax obligations.
- Repeated legal disputes with suppliers, customers, or employees.
- Unpaid Statuatory dues or penalties.


### **Executive and Workforce Developments**

- Resignation or termination of key executives (CEO, CFO, board members).
- Frequent leadership turnover or governance instability.
- Widespread layoffs, furloughs, or hiring freezes.
- Delays in employee salary payments or benefits.
- Union strikes, labor disputes, or workplace unrest.
- Declining employee satisfaction or poor employer reviews (e.g., Glassdoor).


### **Operational Disruptions**

- Recurring supply chain issues or production halts.
- Critical vendor disputes or reliance on a financially unstable supplier.
- Product recalls, safety violations, or service quality issues.
- Cyberattacks, data breaches, or IT infrastructure failures.
- Decline in delivery/service consistency or customer satisfaction.


### **Sentiment, Brand, and Reputation Signals**

- Surge in negative news coverage or social media backlash.
- Viral campaigns or hashtags targeting the company (e.g., #Boycott[Brand]).
- Negative customer reviews, complaints, or brand trust metrics.
- Whistleblower claims or leaked internal documents.
- Negative sentiment from influencers, analysts, or industry reports.


### **Financial Warning Signs**

- Missed debt payments or loan covenant breaches.
- Delayed or skipped financial filings or earnings reports.
- Auditor red flags (e.g., going-concern warnings, disclaimer opinions, ).
- Significant year-over-year revenue or profit decline.
- High debt-to-equity or negative working capital.
- Rating agency downgrades or rising bond yields.
- Credit rating Downgrades or negative outlooks from agencies (e.g., CARE, S&P, Moody's, Fitch).
- One time income reliance (e.g., asset sales, litigation wins) for profit.


### **Industry and Macroeconomic Pressures**

- Unfavorable regulatory changes affecting the business model.
- Rising input or commodity costs affecting margins.
- Industry peers consolidating or exiting due to stress.
- FX volatility or interest rate hikes impacting debt costs.
- Exposure to geopolitical risks, tariffs, or sanctions.
- Impact of rising fuel prices and forex rates.


### **Insights from Financial Disclosures**

- Reliance on one-time income (e.g., asset sales, litigation wins) for profit.
- Sudden changes in accounting methods or disclosures.
- Unusually low effective tax rate or deferred liabilities buildup.
- Gaps between segment performance or sharp business line exits.
- Management commentary lacking clarity or becoming overly vague/optimistic.
- Qualified rating/issues highlighted by auditor
- Delayed financial filings.

**IMPORTANT INSTRUTION FOR THE RESPONSE**:
- Try to include as many relevant issues as possible inside each category.
- Only return the json if you find any relevant issues. Do not return any other text.
- You need to return a the response in the following json format:

{{
    "legal_regulatory_compliance":[
    {{
        "legal_issue": "description of the legal issue in detail",
        "title": "title of the legal issue",
        "summary": "summary of the legal issue",
        "date": "date of the legal issue if available in (DD-MM-YYYY) format",
        "source_urls": ["url 1", "url 2", ...],
    }}, / add more legal issues as needed
    ]
    "executive_workforce_developments":[
    {{
        "executive_issue": "description of the executive issue in detail",
        "title": "title of the executive issue",
        "summary": "summary of the executive issue",
        "date": "date of the executive issue if available in (DD-MM-YYYY) format",
        "source_urls": ["url 1", "url 3", ...],
    }}, /add more executive issues as needed
    ],
    "operational_disruptions":[
    {{
        "operational_issue": "description of the operational issue in detail",
        "title": "title of the operational issue",
        "summary": "summary of the operational issue",
        "date": "date of the operational issue if available in (DD-MM-YYYY) format",
        "source_urls": ["url 1", "url 3", ...],
    }}, /add more operational issues as needed
    ],
    "sentiment_brand_reputation":[
    {{
        "sentiment_issue": "description of the sentiment issue in detail",
        "title": "title of the sentiment issue",
        "summary": "summary of the sentiment issue",
        "date": "date of the sentiment issue if available in (DD-MM-YYYY) format",
        "source_urls": ["url 1", "url 3", ...],
    }}, /add more sentiment issues as needed
    ],
    "financial_warning_signs":[
    {{
        "financial_issue": "description of the financial issue in detail",
        "title": "title of the financial issue",
        "summary": "summary of the financial issue",
        "date": "date of the financial issue if available in (DD-MM-YYYY) format",
        "source_urls": ["url 1", "url 3", ...],
    }}, /add more financial issues as needed
    ],
    "industry_macroeconomic":[
    {{
        "industry_issue": "description of the industry issue in detail",
        "title": "title of the industry issue",
        "summary": "summary of the industry issue",
        "date": "date of the industry issue if available in (DD-MM-YYYY) format",
        "source_urls": ["url 1", "url 3", ...],
    }}, /add more industry issues as needed
    ],
    "financial_disclosures":[
    {{
        "financial_issue": "description of the financial issue in detail",
        "title": "title of the financial issue",
        "summary": "summary of the financial issue",
        "date": "date of the financial issue if available in (DD-MM-YYYY) format",
        "source_urls": ["url 1", "url 3", ...],
    }}, /add more financial issues as needed
    ],
]
}}
"""

# Prompt for finding red flags/ reasons for why the audit report qualified
PROMPT_AUDIT_QUALIFIED = """
You are a financial analyst with expertise in auditing and risk assessment. You will be provided with a qualified audit report for some company. Your task is to identify the audit concers (red flags or reasons or problems like insolvency risk) that led to the qualification of the audit report. Also try to find things mentioned in the audit report that are reason for concern and also find out the things that signify insolvency risk for the company. Pay close attention to the audit report by auditors only. 

**IMPORTANT**:
- Only return the json if you find any relevant red flags or reasons. Do not return any other text.
- Return the response in the following JSON format:
{{
    "audit_concerns": [
        {{
            "audit_concern": "mention the concern mentioned in the report in detail",
            "summary": "summary of the concern",
            "title": "short title of the concern",
            "year": "this concern is deduced from audit report of which year (YYYY format)",
        }}, / add more concerns to the list as needed
    ]
}}
- If you don't find any relevant concerns, return the "audit_concerns" key with an empty list.
- Do not include any other text or explanation outside the JSON format.
- Ensure that the JSON is well-structured and valid.
"""

PROMPT_FINANCIAL_REPORT_INSIGHT = """
You are a forensic financial analyst reviewing the health of a company based on retrieved context from its annual report. Below is a list of queries with their corresponding context excerpts from the report. Each query targets a specific financial or operational factor that may signal insolvency risk. Moreover **you will also be provided with the name of the company as many companies are subsidiaries of other companies but they publish only single annual report for all the subsidaries**. So you need to pay attention to if the company name is mentioned in the context or not. If you are not sure that the context is related to the company or not, then you can skip that context. Also make sure you do not include same insight multiple times. If the multiple contexts or queries seem very similar, then try to combine them into a single insight. Do not make multiple insights for the same thing by paraphrasing it in different ways.

Your task is to:

1. Carefully read the context associated with each query.
2. Identify and summarize **any financial, operational, legal, or strategic insight** that could indicate the company is experiencing or approaching financial distress or insolvency.
3. For each insight you extract, determine whether it should be tagged as a **"redflag"** (if it strongly suggests insolvency risk) or just an **"insight"** (if it's informative but not conclusively a red flag).
4. Make sure your insights are specific and evidence-backed. Do not hallucinate or infer beyond what the context clearly supports.
5. Provide the **exact sentence or passage from the context** that led to the insight (as `bare_text`).
6. Add the **filename and page number** associated with each context (if available) to trace it back to the source.
7. If there are multiple insights in a single context, extract each one separately.
8. Do not include insights from other companies or unrelated contexts.
9. If the context is related to some other subsidiary or company, then you can skip that context and do not include it in the response.

**CRITICAL**: (Company Specific Special Case)
- In case of Air India Express company, only include the insights that are coming from context related to the Air India Express company (the company name should be mentioned in the context). Do not include the insights that are coming from context related to Air India (the parent company) or any other subsidiary of Air India.

**IMPORTANT:**
- Do not include an insight multiple times. If the multiple contexts or queries seem very similar, then try to combine them into a single insight.
- Only return the json if you find any relevant red flags or reasons. Do not return any other text.
- Make sure every insight has all the required keys, i.e, insight, tag, filename, bare_text, page_number, and year.
- Return the response in the following JSON format:
{{
  "insights_and_flags": [
    {{
      "insight": "summary of insight",
      "tag": "redflag or insight",
      "filename": "name of the file from which the insights are coming from",
      "bare_text": "the exact text from the context from where you are getting this insight from",
      "page_number": "the page number from which the insights are coming from (very important)",
      "year": "the year of the report from which the insights are coming from (very important)",
    }}
  ]
}}
- If you don't find any relevant insights or flags, return the "insights_and_flags" key with an empty list.
- Do not include any other text or explanation outside the JSON format.
- Ensure that the JSON is well-structured and valid.
"""

PROMPT_INFORMATION_ABOUT_COMPANY = """
You are a smart internet explorer with expertise in company research. You will be provided with a company name and your task is to generate an about the company in detail.

**IMPORTANT**:
- Only return the json if you find any relevant information. Do not return any other text.
- Return the response in the following JSON format:
{{
    "company_name": "name of the company",
    "about_the_company": "write about the company in detail in one paragraph",
    "source_urls": ["url 1", "url 2", ...]
}}
- Do not include any other text or explanation outside the JSON format.
- Ensure that the JSON is well-structured and valid.
"""

PROMPT_INFORMATION_ABOUT_INDUSTY = """
You are a smart internet explorer with expertise in Indian industry research. You will be provided with the name of an Indian industry (e.g., Indian textile industry, Indian pharmaceutical industry, etc.). Your task is to generate a 3 to 4 line summary of this Indian industry. Also try to find out if the industry is risky or not on the basis of other current and past companies in the industry

**IMPORTANT**:
- Only return the json if you find any relevant information. Do not return any other text.
- Return the response in the following JSON format:
{{
    "about_the_industry": "write about the industry in detail in one paragraph",
    "is_industry_risky": "yes or no",
    "justification": "write the justification for the above answer in one paragraph",
}}
- Do not include any other text or explanation outside the JSON format.
- Ensure that the JSON is well-structured and valid.
"""

PROMPT_TO_CHECK_IF_INSIGHT_IS_A_RED_FLAG = """
You are a financial analyst with expertise in risk assessment and credit insolvency predition. You are trying to determine if a given company will go bankrupt (Insolvency risk) or not. You will be provided with a an insight from either external resources like news articles, blogs, or social media posts or from sources like annual reports, audit reports, or financial disclosures. Your task is to determine if the given insight is a red flag (indicating a high risk of insolvency) or not. Give a reasoning for you answer.

**IMPORTANT**:
- Only return a boolean value (true or false) for the red flag and a reasoning for your answer.
- Return the response in the following JSON format:
{{
    "is_red_flag": true or false,
    "reasoning": "write the reasoning for your answer in one paragraph"
}}
- Do not include any other text or explanation outside the JSON format.
- Ensure that the JSON is well-structured and valid.
"""

PROMPT_TO_SUMMARIZE_RED_FLAGS = """
You are a financial analyst with expertise in risk assessment and credit insolvency prediction. You are trying to determine if a given company will go bankrupt (Insolvency risk) or not. You will be provided with a red flag (indicating a high risk of insolvency) for a company. It may be from external resources like news articles, blogs, or social media posts or from sources like annual reports, audit reports, or financial disclosures. Your task is to summarize the red flag in a single line while retaining the key information. Also give a severity label to the red flag depending on how serious the red flag is. The severity labels can be "severe", "high", "medium", or "low"(Only give one label to a red flag). Make sure to include the figures or numbers mentioned in the red flag in the summary. If the red flag is from an external source, then make sure to include the source name in the summary.

**IMPORTANT**:
- If there are figures or numbers mentioned in the red flag, then make sure to include them in the summary.
- If the red flag is from an external source, then make sure to include the source name in the summary.
- Only return the json if you find any relevant red flags or reasons. Do not return any other text.
- Only give one label to a red flag.
- I observed that you are biased towards giving "high" and "medium" severity labels. Please give unbiased severity and downplay the severity of the red flag if it is not that serious.
- Return the response in the following JSON format:
{{
    "red_flag": "write the red flag in one line concisely",
    "severity": "severe or high or medium or low"
}}
- Do not include any other text or explanation outside the JSON format.
- Ensure that the JSON is well-structured and valid.
"""

PROMPT_UNEARNED_REVENUE = """
You are a financial analyst with expertise in analyzing annual reports and financial disclosures of various companies. You will be provided with a list of queries related to unearned revenue and the context fetched from the annual report of a company. Your task is to identify if the company mentioned in the context is the same as the company name provided to make sure you do not provide the unearned revenue of some subsidiary or other company. If the company name is not mentioned in the context, then you can skip that context. Also make sure you do not include same unearned revenue multiple times. The unearned revenue should be returned in numbers only. Do not write in denominations like crores, lakhs, etc. Just return the numbers only.

**IMPORTANT**:
- If the unearned revenue is not mentioned in the context, then you can skip that context.
- If no unearned revenue is found, then return the unearned revenue as None.
- Only return the json if you find any relevant unearned revenue. Do not return any other text.
- Return the response in the following JSON format:
{{
    "unearned_revenue": "unearned revenue in numbers only or None",
}}
- Do not include any other text or explanation outside the JSON format.
- Ensure that the JSON is well-structured and valid.
"""