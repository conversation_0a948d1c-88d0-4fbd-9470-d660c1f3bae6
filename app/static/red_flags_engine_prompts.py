def get_metric_generation_prompt(description: str, customer_columns: list, transaction_columns: list, metrics_tables: dict, 
    probe_merchant_columns: list, probe_financials_columns: list, probe_msme_columns: list, 
    probe_directors_columns: list, probe_rpt_company_columns: list, probe_rpt_llp_columns: list,
    probe_rpt_individual_columns: list, probe_rpt_others_columns: list, existing_metrics: list) -> str:
    """
    Generate the prompt for metric query generation
    """
    return f"""
    Generate a PostgreSQL SELECT query to calculate a metric with the following description:
    {description}

    Available tables and their fields:

    1. Source Tables:
       customer table:
       {chr(10).join('       - ' + col for col in customer_columns)}

       transactions table:
       {chr(10).join('       - ' + col for col in transaction_columns)}

       probe_merchant table:
       {chr(10).join('       - ' + col for col in probe_merchant_columns)}

       probe_financials table:
       {chr(10).join('       - ' + col for col in probe_financials_columns)}

       probe_msme_supplier_payment_delays table:
       {chr(10).join('       - ' + col for col in probe_msme_columns)}

       probe_authorized_signatories table as directors:
       {chr(10).join('       - ' + col for col in probe_directors_columns)}

       probe_related_party_transactions_company table:
       {chr(10).join('       - ' + col for col in probe_rpt_company_columns)}

       probe_related_party_transactions_llp table:
       {chr(10).join('       - ' + col for col in probe_rpt_llp_columns)}

       probe_related_party_transactions_individual table:
       {chr(10).join('       - ' + col for col in probe_rpt_individual_columns)}

       probe_related_party_transactions_others table:
       {chr(10).join('       - ' + col for col in probe_rpt_others_columns)}

    2. Metrics Storage Tables:
       merchant_metrics table:
       {chr(10).join('       - ' + col for col in metrics_tables['merchant_metrics'])}

       transaction_metrics table:
       {chr(10).join('       - ' + col for col in metrics_tables['transaction_metrics'])}

       customer_metrics table:
       {chr(10).join('       - ' + col for col in metrics_tables['customer_metrics'])}

    Important Notes:
    1. Generate a SELECT query that calculates the metric for all merchants
    2. The query should return merchant_id and the calculated metric value
    3. Include appropriate JOINs between source tables if needed
    4. The query should return a single value per merchant of a specific type (numeric, boolean, string, json, or timestamp)
    5. The metric_value_type field in metric_store table indicates the type of value being stored
    6. When returning values:
       - For numeric values: Return the numeric value directly
       - For boolean values: Return the boolean value directly
       - For string values: Return the string value directly
       - For timestamp values: Return the timestamp value directly
       - For complex JSON values: Return the JSON value directly
    7. The query should be compatible with the metric_value_type that will be determined
    8. Only SELECT query should be generated, no other text should be returned
    9. The query should calculate values for all merchants in the system

    Requirements:
    1. Use appropriate JOINs if needed
    2. Return merchant_id and calculated metric value
    3. Return only the SELECT query
    4. If the metric cannot be resolved, respond with "query_outbound"
    5. Ensure the query returns a value that can be properly stored
    6. Use appropriate type casting to ensure compatibility with the metric_value_type
    7. Only SELECT query should be generated, no other text should be returned
    8. Calculate values for all merchants

    Existing metrics for reference:
    {existing_metrics}
    """

def get_source_query_prompt(metric_query: str, customer_columns: list, transaction_columns: list, metrics_tables: dict,
    probe_merchant_columns: list, probe_financials_columns: list, probe_msme_columns: list,
    probe_directors_columns: list, probe_rpt_company_columns: list, probe_rpt_llp_columns: list,
    probe_rpt_individual_columns: list, probe_rpt_others_columns: list) -> str:
    """
    Generate the prompt for source query generation
    """
    return f"""
    Generate a PostgreSQL SELECT query to fetch the raw data needed to calculate this metric:
    {metric_query}

    Available tables and their fields:

    1. Source Tables:
       customer table:
       {chr(10).join('       - ' + col for col in customer_columns)}

       transactions table:
       {chr(10).join('       - ' + col for col in transaction_columns)}

       probe_merchant table:
       {chr(10).join('       - ' + col for col in probe_merchant_columns)}

       probe_financials table:
       {chr(10).join('       - ' + col for col in probe_financials_columns)}

       probe_msme_supplier_payment_delays table:
       {chr(10).join('       - ' + col for col in probe_msme_columns)}

       probe_authorized_signatories table:
       {chr(10).join('       - ' + col for col in probe_directors_columns)}

       probe_related_party_transactions_company table:
       {chr(10).join('       - ' + col for col in probe_rpt_company_columns)}

       probe_related_party_transactions_llp table:
       {chr(10).join('       - ' + col for col in probe_rpt_llp_columns)}

       probe_related_party_transactions_individual table:
       {chr(10).join('       - ' + col for col in probe_rpt_individual_columns)}

       probe_related_party_transactions_others table:
       {chr(10).join('       - ' + col for col in probe_rpt_others_columns)}

    2. Metrics Storage Tables:
       merchant_metrics table:
       {chr(10).join('       - ' + col for col in metrics_tables['merchant_metrics'])}

       transaction_metrics table:
       {chr(10).join('       - ' + col for col in metrics_tables['transaction_metrics'])}

       customer_metrics table:
       {chr(10).join('       - ' + col for col in metrics_tables['customer_metrics'])}

    Important Notes:
    1. Generate a SELECT query that fetches raw data for all merchants
    2. Include all necessary fields that will be used in the metric calculation
    3. Use appropriate JOINs between tables if needed
    4. The query should be optimized for performance
    5. Include any necessary WHERE clauses for filtering
    6. Consider using appropriate indexes on the joined columns
    7. Only SELECT query should be generated, no other text should be returned
    8. The query should fetch data for all merchants in the system

    Requirements:
    1. Query should fetch the underlying data needed for the metric calculation
    2. Include all necessary fields and JOINs
    3. Return only the SELECT query
    4. If the source query cannot be resolved, respond with "query_outbound"
    5. Only SELECT query should be generated, no other text should be returned
    6. Fetch data for all merchants
    """ 