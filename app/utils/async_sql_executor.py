"""
Async SQL execution utilities for the application.
This module provides functions to execute SQL queries using async SQLAlchemy sessions.
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import logging
import asyncpg
from typing import Dict, Any, Optional, Union, List

logger = logging.getLogger(__name__)


async def run_sql_query_async(db_session: AsyncSession, query: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Executes a SQL query using an async SQLAlchemy session and returns the results.

    Args:
        db_session (AsyncSession): The async SQLAlchemy database session.
        query (str): The SQL query string to execute.
        params (dict, optional): A dictionary of parameters to pass to the query. Defaults to None.

    Returns:
        dict: A dictionary containing status, message, and results.
              Results is a list of dictionaries representing the query results.
    """
    try:
        # Create a text object for the query
        sql_query = text(query)
        
        # Execute the query with or without parameters
        if params:
            result = await db_session.execute(sql_query, params)
        else:
            result = await db_session.execute(sql_query)
        
        # Fetch all results and convert to list of dictionaries
        rows = result.fetchall()
        
        # Convert rows to list of dictionaries
        if rows:
            # Get column names from the result
            columns = result.keys()
            results = [dict(zip(columns, row)) for row in rows]
        else:
            results = []
        
        logger.debug(f"Query executed successfully: '{query[:100]}...'")
        return {
            "status": "success",
            "message": "Query executed successfully.",
            "results": results
        }

    except Exception as e:
        logger.error(f"SQL query error: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "results": []
        }


async def run_sql_query_async_legacy(db_uri: str, query: str, params: Optional[tuple] = None) -> Dict[str, Any]:
    """
    Legacy function for backward compatibility.
    Connects to a PostgreSQL database asynchronously using a DB URI,
    executes a given SQL query, and returns the results.
    
    This function is deprecated. Use run_sql_query_async with AsyncSession instead.

    Args:
        db_uri (str): The database connection URI (e.g., "postgresql://user:password@host:port/database").
        query (str): The SQL query string to execute.
        params (tuple, optional): A tuple of parameters to pass to the query. Defaults to None.

    Returns:
        dict: A dictionary containing status, message, and results.
    """
    conn = None
    try:
        conn = await asyncpg.connect(db_uri)
        logger.debug(f"Connected to PostgreSQL database using URI")

        if params:
            results = await conn.fetch(query, *params)
        else:
            results = await conn.fetch(query)

        results = [dict(record) for record in results]
        logger.debug(f"Query executed successfully")
        return {
            "status": "success",
            "message": "Query executed successfully.",
            "results": results
        }

    except asyncpg.exceptions.PostgresError as e:
        logger.error(f"PostgreSQL error: {e}")
        return {
            "status": "error",
            "message": str(e),
            "results": []
        }
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        return {
            "status": "error",
            "message": str(e),
            "results": []
        }
    finally:
        if conn:
            await conn.close()
            logger.debug("Database connection closed.")


async def execute_multiple_queries(
    db_session: AsyncSession, 
    queries: List[str], 
    params_list: Optional[List[Dict[str, Any]]] = None
) -> List[Dict[str, Any]]:
    """
    Execute multiple SQL queries in sequence using the same session.
    
    Args:
        db_session (AsyncSession): The async SQLAlchemy database session.
        queries (List[str]): List of SQL query strings to execute.
        params_list (List[Dict], optional): List of parameter dictionaries for each query.
        
    Returns:
        List[Dict]: List of results for each query.
    """
    results = []
    
    if params_list is None:
        params_list = [None] * len(queries)
    
    if len(queries) != len(params_list):
        raise ValueError("Number of queries must match number of parameter sets")
    
    for i, (query, params) in enumerate(zip(queries, params_list)):
        try:
            result = await run_sql_query_async(db_session, query, params)
            results.append(result)
            
            # If any query fails, stop execution
            if result["status"] == "error":
                logger.error(f"Query {i+1} failed: {result['message']}")
                break
                
        except Exception as e:
            logger.error(f"Error executing query {i+1}: {str(e)}")
            results.append({
                "status": "error",
                "message": str(e),
                "results": []
            })
            break
    
    return results


async def execute_query_with_transaction(
    db_session: AsyncSession,
    query: str,
    params: Optional[Dict[str, Any]] = None,
    commit: bool = True
) -> Dict[str, Any]:
    """
    Execute a SQL query within a transaction.
    
    Args:
        db_session (AsyncSession): The async SQLAlchemy database session.
        query (str): The SQL query string to execute.
        params (dict, optional): Parameters for the query.
        commit (bool): Whether to commit the transaction. Defaults to True.
        
    Returns:
        dict: Query execution result.
    """
    try:
        result = await run_sql_query_async(db_session, query, params)
        
        if result["status"] == "success" and commit:
            await db_session.commit()
            logger.debug("Transaction committed successfully")
        elif result["status"] == "error":
            await db_session.rollback()
            logger.error("Transaction rolled back due to error")
            
        return result
        
    except Exception as e:
        await db_session.rollback()
        logger.error(f"Transaction failed and rolled back: {str(e)}")
        return {
            "status": "error",
            "message": str(e),
            "results": []
        }


def validate_sql_query(query: str) -> Dict[str, Any]:
    """
    Basic SQL query validation.
    
    Args:
        query (str): SQL query to validate.
        
    Returns:
        dict: Validation result with is_valid flag and any issues.
    """
    validation_result = {
        "is_valid": True,
        "issues": [],
        "query_type": None
    }
    
    # Basic validation
    query_lower = query.lower().strip()
    
    if not query_lower:
        validation_result["is_valid"] = False
        validation_result["issues"].append("Query is empty")
        return validation_result
    
    # Determine query type
    if query_lower.startswith('select'):
        validation_result["query_type"] = "SELECT"
    elif query_lower.startswith('insert'):
        validation_result["query_type"] = "INSERT"
    elif query_lower.startswith('update'):
        validation_result["query_type"] = "UPDATE"
    elif query_lower.startswith('delete'):
        validation_result["query_type"] = "DELETE"
    elif query_lower.startswith('with'):
        validation_result["query_type"] = "CTE"
    else:
        validation_result["query_type"] = "OTHER"
    
    # Check for potentially dangerous operations
    dangerous_keywords = ['drop', 'truncate', 'alter', 'create', 'grant', 'revoke']
    for keyword in dangerous_keywords:
        if keyword in query_lower:
            validation_result["issues"].append(f"Contains potentially dangerous keyword: {keyword}")
    
    # Check for basic SQL injection patterns
    injection_patterns = ["';", "--", "/*", "*/", "xp_", "sp_"]
    for pattern in injection_patterns:
        if pattern in query_lower:
            validation_result["issues"].append(f"Contains potential SQL injection pattern: {pattern}")
    
    if validation_result["issues"]:
        validation_result["is_valid"] = False
    
    return validation_result
