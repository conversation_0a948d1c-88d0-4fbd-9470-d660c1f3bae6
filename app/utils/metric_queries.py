from sqlalchemy import text
import pandas as pd
from ..database import engine
from typing import Optional, Dict, Any, List

def query_numeric_metrics(table_name: str, metric_name: str, min_value: float = None, max_value: float = None) -> pd.DataFrame:
    """
    Query numeric metrics from specified table
    
    Args:
        table_name: Name of the metrics table (e.g., 'merchant_metrics', 'transaction_metrics')
        metric_name: Name of the metric to query
        min_value: Optional minimum value filter
        max_value: Optional maximum value filter
    
    Returns:
        DataFrame containing the query results
    """
    query = f"""
        SELECT * FROM {table_name}
        WHERE metric_type = :metric_name
        {"AND (metric_value)::numeric >= :min_val" if min_value is not None else ""}
        {"AND (metric_value)::numeric <= :max_val" if max_value is not None else ""}
    """
    with engine.connect() as conn:
        result = conn.execute(
            text(query),
            {"metric_name": metric_name, "min_val": min_value, "max_val": max_value}
        )
        return pd.DataFrame(result.fetchall(), columns=result.keys())

def query_string_metrics(table_name: str, metric_name: str, value: str) -> pd.DataFrame:
    """
    Query string metrics from specified table
    
    Args:
        table_name: Name of the metrics table
        metric_name: Name of the metric to query
        value: String value to match
    
    Returns:
        DataFrame containing the query results
    """
    with engine.connect() as conn:
        result = conn.execute(
            text(f"""
                SELECT * FROM {table_name}
                WHERE metric_type = :metric_name
                AND metric_value @> :json_value
            """),
            {"metric_name": metric_name, "json_value": f'"{value}"'}
        )
        return pd.DataFrame(result.fetchall(), columns=result.keys())

def query_boolean_metrics(table_name: str, metric_name: str, expected_value: bool) -> pd.DataFrame:
    """
    Query boolean metrics from specified table
    
    Args:
        table_name: Name of the metrics table
        metric_name: Name of the metric to query
        expected_value: Boolean value to match
    
    Returns:
        DataFrame containing the query results
    """
    with engine.connect() as conn:
        result = conn.execute(
            text(f"""
                SELECT * FROM {table_name}
                WHERE metric_type = :metric_name
                AND metric_value @> :json_value
            """),
            {"metric_name": metric_name, "json_value": 'true' if expected_value else 'false'}
        )
        return pd.DataFrame(result.fetchall(), columns=result.keys())

def query_timestamp_metrics(table_name: str, metric_name: str, after: str = None, before: str = None) -> pd.DataFrame:
    """
    Query timestamp metrics from specified table
    
    Args:
        table_name: Name of the metrics table
        metric_name: Name of the metric to query
        after: Optional start timestamp
        before: Optional end timestamp
    
    Returns:
        DataFrame containing the query results
    """
    query = f"""
        SELECT * FROM {table_name}
        WHERE metric_type = :metric_name
        {"AND (metric_value)::timestamp >= :after" if after else ""}
        {"AND (metric_value)::timestamp <= :before" if before else ""}
    """
    with engine.connect() as conn:
        result = conn.execute(
            text(query),
            {"metric_name": metric_name, "after": after, "before": before}
        )
        return pd.DataFrame(result.fetchall(), columns=result.keys())

def query_all_metrics(table_name: str, entity_id: str) -> Dict[str, Any]:
    """
    Query all metrics for a specific entity from specified table
    
    Args:
        table_name: Name of the metrics table
        entity_id: ID of the entity (merchant_id, customer_id, etc.)
    
    Returns:
        Dictionary with metric_type as keys and metric_value as values
    """
    id_column = f"{table_name.split('_')[0]}_id"  # e.g., merchant_id, customer_id, transaction_id
    with engine.connect() as conn:
        result = conn.execute(
            text(f"""
                SELECT jsonb_object_agg(metric_type, metric_value) AS metrics
                FROM {table_name}
                WHERE {id_column} = :entity_id
            """),
            {"entity_id": entity_id}
        ).fetchone()
        return result.metrics if result else {}

def get_metric_value(table_name: str, entity_id: str, metric_name: str) -> Any:
    """
    Get a specific metric value for an entity
    
    Args:
        table_name: Name of the metrics table
        entity_id: ID of the entity
        metric_name: Name of the metric to get
    
    Returns:
        The metric value or None if not found
    """
    id_column = f"{table_name.split('_')[0]}_id"
    with engine.connect() as conn:
        result = conn.execute(
            text(f"""
                SELECT metric_value
                FROM {table_name}
                WHERE {id_column} = :entity_id
                AND metric_type = :metric_name
            """),
            {"entity_id": entity_id, "metric_name": metric_name}
        ).fetchone()
        return result.metric_value if result else None 