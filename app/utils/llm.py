import os
import time
from typing import Dict, List, Tuple
from langchain_groq import <PERSON>tGroq
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser

# Initialize Groq LLM
# llm = ChatGroq(
#     model_name="llama3-8b-8192",
#     temperature=0.7,
# )

MAX_RETRIES = 3
RETRY_DELAY = 5

llm = ChatOpenAI(
    model="gpt-4.1-mini",
    temperature=0.7,
    max_tokens=None,
)

perp_llm = ChatOpenAI(
    model="sonar-pro",
    base_url="https://api.perplexity.ai",
    max_tokens=None,
    api_key=os.environ.get("PERPLEXITY_API_KEY"),
)

perp_llm_reasoning = ChatOpenAI(
    model="sonar-reasoning",
    base_url="https://api.perplexity.ai",
    max_tokens=None,
    api_key=os.environ.get("PERPLEXITY_API_KEY"),
    temperature=0.7,
)

llm_reasoning = ChatOpenAI(
    model="o4-mini",
    max_tokens=None,
)

def create_and_run_pipeline_without_parser(system_prompt: str, user_inp: str) -> Dict:
    """
    Create a pipeline with a system prompt and no parser.
    """
    # Create a simple prompt
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("user", "{input}")
    ])

    # Create the chain that guarantees JSON output
    chain = prompt | llm_reasoning

    # Run the chain with the user input
    result = chain.invoke({"input": user_inp})
    return result

def create_and_run_pipeline(system_prompt: str, parser: JsonOutputParser, user_inp: str) -> Dict:
    """
    Create a pipeline with a system prompt and a parser.
    """
    # Create a simple prompt
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("user", "{input}")
    ])

    # Create the chain that guarantees JSON output
    chain = prompt | llm | parser

    tries = 0
    while tries < MAX_RETRIES:
        try:
            # Run the chain with the user input
            result = chain.invoke({"input": user_inp})
            return result
        except Exception as e:
            print(f"Error: {e}")
            tries += 1
            if tries < MAX_RETRIES:
                print(f"Retrying in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)
            else:
                raise e
    # If we reach here, it means all retries failed
    print("Max retries exceeded. Returning None.")
    return None


def create_and_run_pipeline_with_history(system_prompt: str, parser: JsonOutputParser, user_inp: str, history: List[Tuple]) -> Dict:
    """
    Create a pipeline with a system prompt and a parser.
    """
    # Create a simple prompt
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        *history,
        ("user", "{input}")
    ])

    # Create the chain that guarantees JSON output
    chain = prompt | llm | parser

    tries = 0
    while tries < MAX_RETRIES:
        try:
            # Run the chain with the user input
            result = chain.invoke({"input": user_inp})
            return result
        except Exception as e:
            print(f"Error: {e}")
            tries += 1
            if tries < MAX_RETRIES:
                print(f"Retrying in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)
            else:
                raise e
    # If we reach here, it means all retries failed
    print("Max retries exceeded. Returning None.")
    return None


def create_and_run_pipeline_with_history(system_prompt: str, parser: JsonOutputParser, user_inp: str, history: List[Tuple]) -> Dict:
    """
    Create a pipeline with a system prompt and a parser.
    """
    # Create a simple prompt
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        *history,
        ("user", "{input}")
    ])

    # Create the chain that guarantees JSON output
    chain = prompt | llm | parser

    # Run the chain with the user input
    result = chain.invoke({"input": user_inp})
    return result

def create_and_run_perp_pipeline(system_prompt: str, parser: JsonOutputParser, user_inp: str) -> Dict:
    """
    Create a pipeline with a system prompt and a parser.
    """
    # Create a simple prompt
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("user", "{input}")
    ])
    # Create the chain that guarantees JSON output
    chain = prompt | perp_llm | parser
    # Run the chain with the user input
    tries = 0
    while tries < MAX_RETRIES:
        try:
            # Run the chain with the user input
            result = chain.invoke({"input": user_inp})
            return result
        except Exception as e:
            print(f"Error: {e}")
            tries += 1
            if tries < MAX_RETRIES:
                print(f"Retrying in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)
            else:
                raise e
    # If we reach here, it means all retries failed
    print("Max retries exceeded. Returning None.")
    return None

def create_and_run_perp_pipeline_without_system(parser: JsonOutputParser, user_inp: str) -> Dict:
    """
    Create a pipeline with a system prompt and a parser.
    """
    # Create a simple prompt
    prompt = ChatPromptTemplate.from_messages([
        ("user", "{input}")
    ])
    # Create the chain that guarantees JSON output
    chain = prompt | perp_llm | parser
    # Run the chain with the user input
    result = chain.invoke({"input": user_inp})
    return result