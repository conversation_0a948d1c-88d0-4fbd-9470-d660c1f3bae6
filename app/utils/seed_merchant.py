from datetime import datetime, date
from decimal import Decimal
from ..database import SessionLocal
from ..models import models
import csv
from pathlib import Path
import logging
import json
import pandas as pd
import uuid
import random

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

def seed_merchant_data():
    db = SessionLocal()
    try:
        # Create Merchant
        merchant = models.Merchant(
            legal_name="TechServe",
            trade_name="TechServe",
            business_type="Service",
            incorporation_date=date(2024, 1, 1),
            industry="Technology",
            description="TechServe is a technology company that provides services to businesses.",
            mca_description="TechServe is a technology company that provides services to businesses.",
            business_category="Technology",
            business_subcategory="Software Development",
            business_model="SaaS",
            onboarding_date=date(2024, 1, 1),
            onboarding_platform="Online",
            kyc_verification_status="Verified",
            kyc_verification_date=date(2024, 1, 1),
            first_txn_date=date(2024, 1, 1),
            last_txn_date=date(2024, 1, 1),
            fraud_flag=False,
            avg_txn_size=1000,
            total_txn=1000,
            total_txn_fy=1000,
            gst_risk_flag=False,
            mca_fillings_risk_flag=False,
            directors_risk_flag=False,
            num_employees=100,
            epfo_reg_status=True,
            is_sanctioned=True,
            is_online_business=True,
            online_presence_flag=True,
            tax_irregularity_flag=False,
            is_pan_compatible=True,
            is_address_compatible=True,
            prior_fraud_investigation_flag=False,
            is_MCA_submission_taken=True,
            is_regular_tax=True,
            multiple_merchant_pan=True,
            udyam_cert_missing=False,
            udyam_cert_flag=False,
            status="active",
            # Transaction metrics
            avg_cx_pii_score=85,
            txn_amt_avg=100000,
            cancelled_txn_cnt_pct=10,
            card_num_density=10,
            curr_diversity_score=10,
            customer_density=10,
            cx_complaint_txn_pct=10,
            day_cos=10,
            day_sin=10,
            device_id_density=10,
            failed_txn_cnt_pct=10,
            hour_cos=10,
            hour_sin=10,
            hrs_since_last_transaction=10,
            interntational_txn_cnt_pct=10,
            invoice_and_txn_amt_diff_pct=10,
            ip_density=10,
            late_night_txn_amt_avg=10,
            late_night_txn_cnt=10,
            month_cos=10,
            month_sin=10,
            num_distinct_currency_used=10,
            chargeback_txn_cnt_pct=10,
            name_mismatch_txn_cnt_pct=10,
            risky_cx_txn_cnt_pct=10,
            round_txn_cnt_pct=10,
            txn_cnt=10,
            txn_amt_sum=10,
            velocity_transaction=10,
            # High flags
            avg_cx_pii_score_is_high=False,
            txn_amt_avg_is_high=False,
            cancelled_txn_cnt_pct_is_high=False,
            card_num_density_is_high=False,
            curr_diversity_score_is_high=False,
            customer_density_is_high=False,
            cx_complaint_txn_pct_is_high=False,
            day_cos_is_high=False,
            day_sin_is_high=False,
            device_id_density_is_high=False,
            failed_txn_cnt_pct_is_high=False,
            hour_cos_is_high=False,
            hour_sin_is_high=False,
            hrs_since_last_transaction_is_high=False,
            interntational_txn_cnt_pct_is_high=False,
            invoice_and_txn_amt_diff_pct_is_high=False,
            ip_density_is_high=False,
            late_night_txn_amt_avg_is_high=False,
            late_night_txn_cnt_is_high=False,
            month_cos_is_high=False,
            month_sin_is_high=False,
            num_distinct_currency_used_is_high=False,
            chargeback_txn_cnt_pct_is_high=False,
            name_mismatch_txn_cnt_pct_is_high=False,
            risky_cx_txn_cnt_pct_is_high=False,
            round_txn_cnt_pct_is_high=False,
            txn_cnt_is_high=False,
            txn_amt_sum_is_high=False,
            velocity_transaction_is_high=False
        )
        
        
        db.add(merchant)
        db.commit()
        return merchant.id
        
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close() 


def seed_rules(merchant_id, db):
    rules = models.Rules()
    rules.max_cx_pii_score = 85
    rules.max_txn_amt_avg = 100000
    rules.max_cancelled_txn_cnt_pct = 10
    rules.max_card_num_density = 10
    rules.max_curr_diversity_score = 10
    rules.max_customer_density = 10
    rules.max_cx_complaint_txn_pct = 10
    rules.max_day_cos = 10
    rules.max_day_sin = 10
    rules.max_device_id_density = 10
    rules.max_failed_txn_cnt_pct = 10
    rules.max_hour_cos = 10
    rules.max_hour_sin = 10
    rules.max_hrs_since_last_transaction = 10
    rules.max_interntational_txn_cnt_pct = 10
    rules.max_invoice_and_txn_amt_diff_pct = 10
    rules.max_ip_density = 10
    rules.max_late_night_txn_amt_avg = 10
    rules.max_late_night_txn_cnt = 10
    rules.max_month_cos = 10
    rules.max_month_sin = 10
    rules.max_num_distinct_currency_used = 10
    rules.max_chargeback_txn_cnt_pct = 10
    rules.max_name_mismatch_txn_cnt_pct = 10
    rules.max_risky_cx_txn_cnt_pct = 10
    rules.max_round_txn_cnt_pct = 10
    rules.max_txn_cnt = 10
    rules.max_txn_amt_sum = 10
    rules.  max_velocity_transaction = 10
    db.add(rules)
    db.commit()

def seed_transaction_data(merchant_id, db):
    transactions = [
        {
            "transaction_type": "Card Payment",
            "merchant_type": "Electronics Store",
            "city": "Mumbai",
            "country_code": "IN",
            "amount": Decimal("59999.00"),
            "transaction_id": "TXN123456789",
            "merchant_name": "Digital World Electronics",
            "risk_score": 85,
            "risk_description": "High value transaction from new merchant",
            "product_name": "Laptop",
            "dispute_date": datetime(2024, 12, 5, 10, 0),
            "complain_date": datetime(2024, 12, 6, 15, 0),
            "timestamp": datetime(2024, 12, 1, 13, 59),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "UPI Payment",
            "merchant_type": "Entertainment",
            "city": "Bangalore",
            "country_code": "IN",
            "product_name": "Premium Subscription",
            "amount": Decimal("1299.00"),
            "transaction_id": "UPI789012345",
            "merchant_name": "StreamFlix India",
            "risk_score": 30,
            "dispute_date": None,
            "complain_date": None,
            "timestamp": datetime(2024, 12, 1, 12, 15),
            "status": "pending",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "Card Payment",
            "merchant_type": "Retail",
            "city": "Kolkata",
            "country_code": "IN",
            "product_name": "Fashion Apparel",
            "amount": Decimal("15999.00"),
            "transaction_id": "TXN789012345",
            "merchant_name": "Fashion Hub",
            "risk_score": 35,
            "dispute_date": datetime(2024, 12, 2, 11, 0),
            "complain_date": datetime(2024, 12, 3, 14, 0),
            "timestamp": datetime(2024, 12, 1, 2, 45),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "International Wire",
            "merchant_type": "Consulting",
            "city": "New York",
            "country_code": "US",
            "amount": Decimal("450000.00"),
            "transaction_id": "WIRE789012345",
            "merchant_name": "Global Consulting Group",
            "risk_score": 82,
            "risk_description": "High-value cross-border service payment",
            "product_name": "Consulting Services",
            "dispute_date": datetime(2024, 12, 4, 9, 0),
            "complain_date": datetime(2024, 12, 5, 10, 0),
            "timestamp": datetime(2024, 12, 1, 2, 0),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "Card Payment",
            "merchant_type": "Automotive",
            "city": "Gurgaon",
            "country_code": "IN",
            "amount": Decimal("35000.00"),
            "transaction_id": "TXN890123456",
            "merchant_name": "AutoCare Services",
            "risk_score": 40,
            "product_name": "Car Repair",
            "dispute_date": datetime(2024, 12, 2, 10, 0),
            "complain_date": datetime(2024, 12, 2, 10, 0),
            "timestamp": datetime(2024, 12, 1, 1, 15),
            "status": "failed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "RTGS Transfer",
            "merchant_type": "Construction",
            "city": "Mumbai",
            "country_code": "IN",
            "amount": Decimal("875000.00"),
            "transaction_id": "RTGS890123456",
            "merchant_name": "BuildTech Solutions",
            "risk_score": 68,
            "risk_description": "Large transfer to new vendor",
            "product_name": "Construction Materials",
            "dispute_date": datetime(2024, 12, 3, 8, 0),
            "complain_date": datetime(2024, 12, 4, 9, 0),
            "timestamp": datetime(2024, 12, 1, 0, 30),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "UPI Payment",
            "merchant_type": "Food & Beverage",
            "city": "Delhi",
            "country_code": "IN",
            "amount": Decimal("2499.00"),
            "transaction_id": "UPI901234567",
            "merchant_name": "Food Express",
            "risk_score": 25,
            "product_name": "Gourmet Meal",
            "dispute_date": None,
            "complain_date": None,
            "timestamp": datetime(2024, 11, 30, 23, 45),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "Card Payment",
            "merchant_type": "Healthcare",
            "city": "Chennai",
            "country_code": "IN",
            "product_name": "Medical Services",
            "amount": Decimal("25000.00"),
            "transaction_id": "TXN901234567",
            "merchant_name": "HealthCare Plus",
            "risk_score": 45,
            "dispute_date": datetime(2024, 12, 2, 10, 0),
            "complain_date": datetime(2024, 12, 3, 11, 0),
            "timestamp": datetime(2024, 11, 30, 22, 30),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "NEFT Transfer",
            "merchant_type": "Education",
            "city": "Pune",
            "country_code": "IN",
            "amount": Decimal("45000.00"),
            "transaction_id": "NEFT012345678",
            "merchant_name": "EduTech Institute",
            "risk_score": 30,
            "product_name": "Online Course",
            "dispute_date": datetime(2024, 12, 2, 10, 0),
            "complain_date": datetime(2024, 12, 2, 10, 0),
            "timestamp": datetime(2024, 11, 30, 21, 15),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "Card Payment",
            "merchant_type": "Travel",
            "city": "Mumbai",
            "country_code": "IN",
            "product_name": "Flight Tickets",
            "amount": Decimal("28500.00"),
            "transaction_id": "TXN012345678",
            "merchant_name": "Travel Solutions",
            "risk_score": 40,
            "dispute_date": datetime(2024, 12, 1, 9, 0),
            "complain_date": datetime(2024, 12, 2, 10, 0),
            "timestamp": datetime(2024, 11, 30, 20, 0),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "UPI Payment",
            "merchant_type": "Retail",
            "city": "Hyderabad",
            "country_code": "IN",
            "amount": Decimal("7999.00"),
            "transaction_id": "UPI123456789",
            "merchant_name": "Retail Mart",
            "risk_score": 20,
            "product_name": "Groceries",
            "dispute_date": datetime(2024, 12, 2, 10, 0),
            "complain_date": datetime(2024, 12, 2, 10, 0),
            "timestamp": datetime(2024, 11, 30, 19, 30),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "Card Payment",
            "merchant_type": "Electronics",
            "city": "Bangalore",
            "country_code": "IN",
            "product_name": "Smartphone",
            "amount": Decimal("49999.00"),
            "transaction_id": "TXN123456780",
            "merchant_name": "Gadget Store",
            "risk_score": 55,
            "dispute_date": datetime(2024, 12, 1, 8, 0),
            "complain_date": datetime(2024, 12, 2, 9, 0),
            "timestamp": datetime(2024, 11, 30, 18, 45),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "International Card",
            "merchant_type": "Software",
            "city": "Singapore",
            "country_code": "SG",
            "product_name": "Software License",
            "amount": Decimal("199999.00"),
            "transaction_id": "INTL123456789",
            "merchant_name": "Global Software Solutions",
            "risk_score": 75,
            "risk_description": "High-value international transaction",
            "dispute_date": datetime(2024, 12, 1, 7, 0),
            "complain_date": datetime(2024, 12, 2, 8, 0),
            "timestamp": datetime(2024, 11, 30, 18, 0),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "UPI Payment",
            "merchant_type": "Services",
            "city": "Delhi",
            "country_code": "IN",
            "amount": Decimal("12500.00"),
            "transaction_id": "UPI234567890",
            "merchant_name": "Service Pro",
            "risk_score": 35,
            "product_name": "Consultation",
            "dispute_date": datetime(2024, 12, 2, 10, 0),
            "complain_date": datetime(2024, 12, 2, 10, 0),
            "timestamp": datetime(2024, 11, 30, 17, 30),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        },
        {
            "transaction_type": "Card Payment",
            "merchant_type": "Hospitality",
            "city": "Goa",
            "country_code": "IN",
            "product_name": "Hotel Booking",
            "amount": Decimal("35000.00"),
            "transaction_id": "TXN234567890",
            "merchant_name": "Beach Resort",
            "risk_score": 45,
            "dispute_date": datetime(2024, 12, 1, 6, 0),
            "complain_date": datetime(2024, 12, 2, 7, 0),
            "timestamp": datetime(2024, 11, 30, 17, 0),
            "status": "completed",
            "is_fraud_transaction": False,
            "cx_id": uuid.uuid4(),
            "cx_ip": "***********",
            "cx_device_id": "device123",
            "cx_card_number": "1234-**************",
            "cx_pii_linkage_score": 85,
            "is_cardholder_name_match": True,
            "is_chargeback": False,
            "is_cx_international": False,
            "txn_status": "completed",
            "is_cx_risky": False,
            "invoice_amount": Decimal("59999.00"),
            "is_cancelled": False,
            "txn_currency": "INR",
            "has_cx_complaint": False
        }
    ]

    for txn_data in transactions:
        transaction = models.MerchantTransaction(
            merchant_id=merchant_id,
            **txn_data
        )
        db.add(transaction)

def seed_payment_channels(merchant_id, db):
    channels = [
        {
            "type": "website",
            "name": "Main Website",
            "status": "active",
            "added_on": datetime(2024, 3, 15, 14, 30),
            "details": {
                "url": "https://example.com",
                "integration": "API"
            }
        },
        {
            "type": "app",
            "name": "Mobile Application",
            "status": "active",
            "added_on": datetime(2024, 3, 14, 10, 0),
            "details": {
                "platform": "Android & iOS",
                "version": "2.1.0"
            }
        },
        {
            "type": "pos",
            "name": "Store POS",
            "status": "inactive",
            "added_on": datetime(2024, 3, 13, 9, 15),
            "details": {
                "location": "Mumbai Branch",
                "terminal": "POS001"
            }
        }
    ]

    for channel_data in channels:
        channel = models.MerchantPaymentChannel(
            merchant_id=merchant_id,
            **channel_data
        )
        db.add(channel) 

def seed_payouts(merchant_id, db):
    payouts = [
        {
            "amount": Decimal("150000.00"),
            "status": "processed",
            "bank_account": "HDFC Bank ****1234",
            "utr": "UTR123456789",
            "timestamp": datetime(2024, 3, 15, 14, 30)
        },
        {
            "amount": Decimal("75000.00"),
            "status": "pending",
            "bank_account": "ICICI Bank ****5678",
            "utr": "UTR987654321",
            "timestamp": datetime(2024, 3, 15, 12, 15)
        },
        {
            "amount": Decimal("200000.00"),
            "status": "failed",
            "bank_account": "SBI Bank ****9012",
            "utr": "UTR456789123",
            "timestamp": datetime(2024, 3, 15, 10, 45)
        }
    ]

    for payout_data in payouts:
        payout = models.MerchantPayout(
            merchant_id=merchant_id,
            **payout_data
        )
        db.add(payout) 

def seed_communications(merchant_id, db):
    communications = [
        {
            "type": "email",
            "subject": "Transaction Alert: High Value Payment",
            "content": "A high value transaction of ₹150,000 was processed on your account.",
            "timestamp": datetime(2024, 3, 15, 14, 30),
            "status": "sent"
        },
        {
            "type": "email",
            "subject": "Welcome to Our Platform",
            "content": "Thank you for joining our platform...",
            "timestamp": datetime(2024, 3, 4, 9, 0),
            "status": "sent"
        },
        {
            "type": "phone",
            "subject": "Support Call",
            "content": "Customer called regarding payment failure issue.",
            "timestamp": datetime(2024, 3, 15, 12, 15),
            "status": "received"
        },
        {
            "type": "chat",
            "subject": "Live Chat Support",
            "content": "Customer inquired about refund status.",
            "timestamp": datetime(2024, 3, 15, 10, 45),
            "status": "missed"
        }
    ]

    for comm_data in communications:
        communication = models.MerchantCommunication(
            merchant_id=merchant_id,
            **comm_data
        )
        db.add(communication) 

def seed_timeline_events(merchant_id, db):
    events = [
        {
            "time": datetime(2024, 3, 3, 23, 30),
            "event": "Transaction: ₹1,50,022 (Mumbai)",
            "type": "transaction"
        },
        {
            "time": datetime(2024, 3, 3, 21, 5),
            "event": "Transaction: ₹40,002 (Mumbai)",
            "type": "transaction"
        },
        {
            "time": datetime(2024, 3, 2, 2, 15),
            "event": "Risk Score Increase: 88 → 92",
            "type": "alert"
        }
    ]

    for event_data in events:
        event = models.MerchantTimelineEvent(
            merchant_id=merchant_id,
            **event_data
        )
        db.add(event) 

def seed_investigations(merchant_id, db):
    # Check if investigations already exist for this merchant
    existing = db.query(models.MerchantInvestigation)\
        .filter(models.MerchantInvestigation.merchant_id == merchant_id)\
        .first()
    
    if existing:
        logger.info("Investigations already exist for this merchant, skipping...")
        return

    # Generate unique IDs
    def generate_unique_inv_id():
        return f"INV-{random.randint(100000, 999999)}"

    investigations = [
        {
            "id": generate_unique_inv_id(),
            "title": "Unusual Transaction Pattern",
            "status": "In Progress",
            "priority": "High",
            "assignee": "John Doe",
            "last_updated": datetime(2024, 3, 15, 10, 30)
        },
        {
            "id": generate_unique_inv_id(),
            "title": "Compliance Documentation",
            "status": "Pending Review",
            "priority": "Medium",
            "assignee": "Jane Smith",
            "last_updated": datetime(2024, 3, 14, 15, 45)
        }
    ]
    
    for inv_data in investigations:
        investigation = models.MerchantInvestigation(
            merchant_id=merchant_id,
            **inv_data
        )
        db.add(investigation) 

def seed_investigation_notes(merchant_id, db):
    notes = [
        {
            "case_number": "123",
            "title": "Unusual Transaction Patterns",
            "description": "Multiple high-value transactions detected during non-business hours. Merchant explanation pending.",
            "timestamp": datetime(2024, 3, 15, 10, 30)
        },
        {
            "case_number": "122",
            "title": "Compliance Review",
            "description": "GST registration verification failed. Business volume exceeds threshold.",
            "timestamp": datetime(2024, 3, 14, 15, 45)
        }
    ]

    for note_data in notes:
        note = models.MerchantInvestigationNote(
            merchant_id=merchant_id,
            **note_data
        )
        db.add(note) 

def seed_risk_assessment(merchant_id, db):
    # Create risk assessment
    risk_assessment = models.MerchantRiskAssessment(
        merchant_id=merchant_id,
        overall_score=82,
        risk_level="High Risk Merchant",
        description="Multiple risk factors identified across transaction patterns and compliance"
    )
    db.add(risk_assessment)
    db.flush()

    # Create risk categories and indicators
    categories = [
        {
            "title": "Transaction Risk",
            "score": 85,
            "icon": "activity",
            "indicators": [
                {"label": "High Value Transaction Rate", "value": "45%", "severity": "high"},
                {"label": "Velocity Check Failures", "value": "12", "severity": "medium"},
                {"label": "Cross-border Transactions", "value": "8%", "severity": "low"}
            ]
        },
        {
            "title": "Compliance Risk",
            "score": 65,
            "icon": "scale",
            "indicators": [
                {"label": "KYC Status", "value": "Incomplete", "severity": "high"},
                {"label": "Document Verification", "value": "Pending", "severity": "medium"},
                {"label": "Regulatory Compliance", "value": "Partial", "severity": "medium"}
            ]
        }
    ]

    for cat_data in categories:
        indicators = cat_data.pop("indicators")
        category = models.MerchantRiskCategory(
            risk_assessment_id=risk_assessment.id,
            **cat_data
        )
        db.add(category)
        db.flush()

        for ind_data in indicators:
            indicator = models.RiskIndicator(
                category_id=category.id,
                **ind_data
            )
            db.add(indicator)

def seed_flags(merchant_id, db):
    flags = [
        {
            "flag_type": "transactions",
            "severity": "critical",
            "text": "Multiple high-value transactions in short duration",
            "timestamp": datetime(2024, 3, 15, 14, 30)
        },
        {
            "flag_type": "compliance",
            "severity": "critical",
            "text": "Missing KYC documentation",
            "timestamp": datetime(2024, 3, 15, 9, 30)
        },
        {
            "flag_type": "network",
            "severity": "high",
            "text": "Connected to flagged merchant",
            "timestamp": datetime(2024, 3, 14, 23, 30)
        },
        {
            "flag_type": "digital-footprint",
            "severity": "critical",
            "text": "Multiple high-value transactions in short duration",
            "timestamp": datetime(2024, 3, 15, 14, 30)
        },
        {
            "flag_type": "verification",
            "severity": "critical",
            "text": "Multiple high-value transactions in short duration",
            "timestamp": datetime(2024, 3, 15, 14, 30)
        }
    ]

    for flag_data in flags:
        flag = models.MerchantFlag(
            merchant_id=merchant_id,
            **flag_data
        )
        db.add(flag) 

def seed_key_metrics(merchant_id, db):
    metrics = models.MerchantKeyMetrics(
        merchant_id=merchant_id,
        days_since_onboarding=245,
        average_daily_transactions=2.5,
        average_payout_size=7.5,
        active_cases=2
    )
    db.add(metrics) 

def seed_linkages(merchant_id, db):
    # Seed network overview
    overview = models.MerchantNetworkOverview(
        merchant_id=merchant_id,
        total_connections=8,
        high_risk_connections=4,
        network_risk_score=65
    )
    db.add(overview)

    # Seed linked entities
    entities = [
        {
            "related_entity_name": "TechServe Solutions",
            "relationship_type": "Parent Company",
            "registration_number": "U72200KA2020PTC123456",
            "location": "Bangalore",
            "connection_count": 5,
            "risk_level": "medium",
            "details": [
                {"label": "Registration", "value": "U72200KA2020PTC123456"},
                {"label": "Location", "value": "Bangalore"},
                {"label": "Common Directors", "value": "2"}
            ]
        },
        {
            "related_entity_name": "Digital Payments Ltd",
            "relationship_type": "Sister Company",
            "registration_number": "U72200KA2021PTC789012",
            "location": "Mumbai",
            "connection_count": 3,
            "risk_level": "low",
            "details": [
                {"label": "Registration", "value": "U72200KA2021PTC789012"},
                {"label": "Location", "value": "Mumbai"},
                {"label": "Common Directors", "value": "1"}
            ]
        }
    ]

    for entity_data in entities:
        entity = models.MerchantRelationship(
            merchant_id=merchant_id,
            **entity_data
        )
        db.add(entity)

    # Seed common connections
    connections = [
        {
            "connection_type": "Phone",
            "connection_value": "+91 98765 43210",
            "shared_with": ["TechServe Solutions", "Digital Payments Ltd"]
        },
        {
            "connection_type": "Email",
            "connection_value": "<EMAIL>",
            "shared_with": ["TechServe Solutions"]
        }
    ]

    for conn_data in connections:
        connection = models.MerchantCommonConnection(
            merchant_id=merchant_id,
            **conn_data
        )
        db.add(connection) 

def seed_digital_footprint(merchant_id, db):
    # Seed website metrics
    website_metrics = models.MerchantWebsiteMetrics(
        merchant_id=merchant_id,
        domain="techserve.com",
        age="3 years",
        monthly_traffic="45K",
        traffic_trend_value="+12% vs last month",
        traffic_trend_positive=True,
        trust_score="85/100",
        security_status="Valid SSL",
        last_updated="2 days ago"
    )
    db.add(website_metrics)

    # Seed review sources
    review_sources = [
        {
            "platform": "Google Reviews",
            "icon_type": "globe",
            "rating": 4.2,
            "total_reviews": 128,
            "sentiment": {"positive": 75, "neutral": 15, "negative": 10}
        },
        {
            "platform": "Play Store",
            "icon_type": "globe",
            "rating": 4.2,
            "total_reviews": 128,
            "sentiment": {"positive": 75, "neutral": 15, "negative": 10}
        }
    ]

    for source_data in review_sources:
        source = models.MerchantReviewSource(
            merchant_id=merchant_id,
            **source_data
        )
        db.add(source)

    # Seed social presence
    social_platforms = [
        {
            "platform": "LinkedIn",
            "icon_type": "linkedin",
            "metrics": [
                {"label": "Followers", "value": "2.5K"},
                {"label": "Posts/Month", "value": "15"},
                {"label": "Engagement Rate", "value": "3.2%"}
            ]
        },
        {
            "platform": "Twitter",
            "icon_type": "twitter",
            "metrics": [
                {"label": "Followers", "value": "2.5K"},
                {"label": "Posts/Month", "value": "15"},
                {"label": "Engagement Rate", "value": "3.2%"}
            ]
        }
    ]

    for platform_data in social_platforms:
        platform = models.MerchantSocialPresence(
            merchant_id=merchant_id,
            **platform_data
        )
        db.add(platform)

    # Seed recent mentions
    mentions = [
        {
            "source": "Tech News Blog",
            "title": "Top 10 Emerging FinTech Solutions",
            "date": date(2024, 3, 10),
            "sentiment": "positive",
            "snippet": "TechServe's innovative approach to digital payments..."
        },
        {
            "source": "Industry Forum",
            "title": "Payment Processing Challenges",
            "date": date(2024, 3, 10),
            "sentiment": "positive",
            "snippet": "Companies like TechServe are addressing integration issues..."
        }
    ]

    for mention_data in mentions:
        mention = models.MerchantRecentMention(
            merchant_id=merchant_id,
            **mention_data
        )
        db.add(mention) 

def create_merchants_csv():
    try:
        current_dir = Path(__file__).resolve().parent
        data_dir = current_dir.parent / "data"
        data_dir.mkdir(exist_ok=True)
        
        csv_path = data_dir / "merchants.csv"
        
        if not csv_path.exists():
            # Sample merchant data
            merchant_data = {
                "merchant_id": "18bf7d8a-03c8-482d-99c3-a66b57cb5c99",
                "timeline_events": json.dumps([]),
                "investigations": json.dumps([
                    {
                        "id": "INV-0012",
                        "title": "Unusual Transaction Pattern",
                        "status": "In Progress",
                        "priority": "High",
                        "assignee": "John Doe",
                        "lastUpdated": datetime.now().isoformat()
                    }
                ]),
                "investigation_notes": json.dumps([]),
                "key_metrics": json.dumps({
                    "daysSinceOnboarding": 30,
                    "averageDailyTransactions": 150,
                    "averagePayoutSize": 25000,
                    "activeCases": 2
                }),
                "risk_score": 75,
                "risk_level": "MEDIUM",
                "risk_factors": json.dumps(["High transaction volume", "Recent suspicious activity"])
            }
            
            df = pd.DataFrame([merchant_data])
            df.to_csv(csv_path, index=False)
            logger.info(f"Created merchants.csv with sample data")
            
    except Exception as e:
        logger.error(f"Error creating merchants.csv: {str(e)}") 