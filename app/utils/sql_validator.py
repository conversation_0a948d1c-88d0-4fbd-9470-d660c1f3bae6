from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
import re
from typing import Dict, Any, Tu<PERSON>, List
from ..database import engine

def _extract_subqueries(query: str) -> List[str]:
    """Extract all subqueries from the main query."""
    subqueries = []
    # Match subqueries in parentheses
    pattern = r'\((?:[^()]+|(?R))*\)'
    matches = re.finditer(pattern, query)
    
    for match in matches:
        subquery = match.group(0)
        # Remove outer parentheses
        subquery = subquery[1:-1].strip()
        # Check if it's a SELECT subquery
        if subquery.lower().startswith('select'):
            subqueries.append(subquery)
            # Recursively find nested subqueries
            subqueries.extend(_extract_subqueries(subquery))
    
    return subqueries

def _check_write_operations(query: str) -> Tuple[bool, str]:
    """Check for write operations in a query."""
    write_operations = ['insert', 'update', 'delete', 'drop', 'alter', 'create', 'truncate']
    query_lower = query.lower()
    
    for op in write_operations:
        # Use word boundaries to avoid matching parts of words
        if re.search(r'\b' + op + r'\b', query_lower):
            return True, f"Write operation '{op}' detected"
    return False, None

def validate_sql_query(query: str) -> Dict[str, Any]:
    """
    Validates and analyzes a SQL query without executing it.
    
    Args:
        query (str): SQL query string to validate
        
    Returns:
        Dict containing validation results, stats and any errors
    """
    result = {
        "is_valid": False,
        "is_read_only": False,
        "has_sql_injection": False,
        "estimated_cost": None,
        "tables_accessed": [],
        "columns_accessed": [],
        "error": None,
        "query_type": None,
        "subquery_validation": {
            "has_write_operations": False,
            "invalid_subqueries": []
        }
    }
    
    try:
        # Convert to lowercase for easier checking
        query_lower = query.lower().strip()
        
        # Check main query for write operations
        has_write, write_error = _check_write_operations(query)
        if has_write:
            result["error"] = write_error
            return result
            
        # Extract and validate all subqueries
        subqueries = _extract_subqueries(query)
        for subquery in subqueries:
            has_write, write_error = _check_write_operations(subquery)
            if has_write:
                result["subquery_validation"]["has_write_operations"] = True
                result["subquery_validation"]["invalid_subqueries"].append({
                    "query": subquery,
                    "error": write_error
                })
        
        if result["subquery_validation"]["has_write_operations"]:
            result["error"] = "Write operations detected in subqueries"
            return result
            
        # Basic SQL injection check
        injection_patterns = [
            r'--.*$',  # SQL comments
            r';.*$',   # Multiple statements
            r'/\*.*\*/', # Multi-line comments
            r'union\s+select', # UNION attacks
            r'exec\s+xp_', # XP commands
            r'exec\s+sp_', # Stored procedures
            r'waitfor\s+delay', # Time-based attacks
            r'benchmark\s*\(', # Benchmark attacks
        ]
        
        for pattern in injection_patterns:
            if re.search(pattern, query_lower, re.IGNORECASE):
                result["has_sql_injection"] = True
                result["error"] = "Potential SQL injection detected"
                return result
        
        # Parse query using SQLAlchemy
        parsed_query = text(query)
        
        # Get query execution plan
        with engine.connect() as conn:
            # Get EXPLAIN ANALYZE output
            explain_query = f"EXPLAIN (FORMAT JSON) {query}"
            explain_result = conn.execute(text(explain_query))
            plan = explain_result.scalar()
            
            # Extract tables and columns from plan
            if plan and isinstance(plan, list):
                for node in plan:
                    if "Relation Name" in node:
                        result["tables_accessed"].append(node["Relation Name"])
                    if "Output" in node:
                        result["columns_accessed"].extend(node["Output"])
                
                # Get estimated cost
                if "Plan" in plan[0]:
                    result["estimated_cost"] = plan[0]["Plan"].get("Total Cost")
        
        # Set query type
        if query_lower.startswith('select'):
            result["query_type"] = "SELECT"
            result["is_read_only"] = True
            result["is_valid"] = True
            
    except SQLAlchemyError as e:
        result["error"] = f"SQL validation error: {str(e)}"
    except Exception as e:
        result["error"] = f"Unexpected error: {str(e)}"
        
    return result 