import PyPDF2
import os
import json
import requests
import time
from typing import List, <PERSON><PERSON>
from langchain_text_splitters import RecursiveCharacterTextSplitter
from pinecone import Pinecone, ServerlessSpec
from datetime import datetime
from uuid import uuid4
from openai import OpenAI

client = OpenAI()

from ..static.queries import queries

# Initialize a Pinecone client with your API key
pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))

# Create a dense index with integrated embedding
index_name = "modus2"
if not pc.has_index(index_name):
    pc.create_index(
        name=index_name,
        dimension=1536,
        metric="cosine",
        spec=ServerlessSpec(
            cloud='aws',
            region='us-east-1'
        )
    )

dense_index = pc.Index(index_name)

VECTOR_SIZE = 1536
CHUNK_SIZE = 1024
CHUNK_OVERLAP = 128
MAX_RETRIES = 3
RETRY_DELAY = 3
BATCH_SIZE = 30

def extract_text_from_pdfs_with_metadata(pdf_paths: List[str]) -> List[str]:
    texts = []
    for path in pdf_paths:
        with open(path, "rb") as file:
            reader = PyPDF2.PdfReader(file)
            for number, page in enumerate(reader.pages):
                if page.extract_text().strip():
                    data = {
                        "text": page.extract_text(),
                        "metadata": {
                            "source": path,
                            "page_number": number + 1,
                            "num_pages": len(reader.pages),
                        },
                    }
                    texts.append(data)
    return texts

def split_text_into_chunks(texts: List[dict]) -> List[dict]:
    splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(
        model_name="gpt-4o-mini", chunk_size=CHUNK_SIZE, chunk_overlap=CHUNK_OVERLAP
    )
    chunks = []
    for text_data in texts:
        text = text_data["text"]
        metadata = text_data["metadata"]
        chunked_texts = splitter.split_text(text)
        for chunk in chunked_texts:
            chunks.append({
                "chunk": chunk,
                "metadata": {
                    "source": metadata["source"],
                    "page_number": metadata["page_number"],
                    "num_pages": metadata["num_pages"],
                },
            })
    return chunks

def get_embedding(text, model="text-embedding-3-small"):
    text = text.replace("\n", " ")
    tries = 0
    while tries < MAX_RETRIES:
        try:
            # Call the OpenAI API to get the embedding
            response = client.embeddings.create(input=[text], model=model)
            return response.data[0].embedding
        except requests.exceptions.RequestException as e:
            print(f"Error getting embedding: {e}")
            tries += 1
            time.sleep(RETRY_DELAY)
    print("Max retries exceeded for embedding generation. Returning None.")
    return None

def upsert_records_to_pinecone(chunks: List[dict], merchant_id: str, year: str) -> None:
    count = 0
    batch = []
    for i, chunk_data in enumerate(chunks):
        _id = f"{merchant_id}_{year}_{str(i)}" 
        text = chunk_data["chunk"]
        embedding = get_embedding(text)
        metadata = chunk_data["metadata"]
        metadata["text"] = text
        metadata["merchant_id"] = merchant_id
        metadata["year"] = year
        metadata["chunk_id"] = str(i)

        data_to_upsert = {
            "id": _id,
            "values": embedding,
            "metadata": metadata,
        }
        batch.append(data_to_upsert)
        if len(batch) >= BATCH_SIZE:
            status = upsert_record_to_pinecone_with_retries(batch, "annual_report", f"Failed to upsert batch no: {i/BATCH_SIZE} in annual report for merchatn_id: {merchant_id} and year: {year}")
            count += status*BATCH_SIZE
            batch = []
            time.sleep(2.0)  # Sleep to avoid rate limits
            print(f"Upserted {count}/{len(chunks)} records to Pinecone index.")
    if batch:
        status = upsert_record_to_pinecone_with_retries(batch, "annual_report", f"Failed to upsert last batch in annual report for merchatn_id: {merchant_id} and year: {year}")
        count += status*len(batch)
        time.sleep(1.0)
        print(f"Upserted {count}/{len(chunks)} records to Pinecone index.")
    # Sleep to avoid rate limits
    print(f"Upserted {count}/{len(chunks)} records to Pinecone index.")
    return count

def upsert_record_to_pinecone_with_retries(vectors: List[dict], namespace: str, log: str) -> None:
    tries = 0
    while tries < MAX_RETRIES:
        try:
            # Upsert the vectors to Pinecone
            dense_index.upsert(namespace=namespace, vectors=vectors)
            return 1
        except requests.exceptions.RequestException as e:
            print(f"Error upserting record: {e}")
            tries += 1
            time.sleep(RETRY_DELAY)
    if tries == MAX_RETRIES:
        print(f"Max retries exceeded for upsert. Skipping record. {log}")
        return 0

def query_pinecone(name_space: str, query: str, filters: dict, top_k: int = 4) -> List[dict]:
    results = dense_index.query(
        namespace=name_space,
        vector=get_embedding(query),
        top_k=top_k,
        filter=filters,
        include_metadata=True,
        include_values=False,
    )
    return results['matches']

def get_context_from_pinecone(merchant_id: str, filters: dict) -> str:
    context = ""
    for query in queries:
        context += f"Query: {query}\n"
        results = query_pinecone("annual_report", query, filters=filters)
        if results:
            for hit in results:
                temp_data = hit['metadata']
                context += f"Retrieved Context: {temp_data['text']}\n"
                file_path = temp_data['source']
                # get file name from file path
                file_name = os.path.basename(file_path)
                context += f"File Name: {file_name}, \n\nFinancial Year in which report was published: {temp_data['year']}, \n\nPage Number: {temp_data['page_number']}\n\n"
        
        context += "\n"
    return context

def insert_pdf_to_pinecone(path_to_pdf: str, year: str, merchant_id: str) -> None:
    print("path_to_pdf: ", path_to_pdf)
    texts = extract_text_from_pdfs_with_metadata([path_to_pdf])
    print(f"Extracted text from {len(texts)} pages.")
    chunks = split_text_into_chunks(texts)
    print(f"Split text into {len(chunks)} chunks.")
    counts = upsert_records_to_pinecone(chunks, merchant_id, year)
    print("PDFs inserted into Pinecone index successfully.")
    return counts
