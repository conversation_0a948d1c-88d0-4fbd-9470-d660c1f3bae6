import requests
import os
import socket
import ssl
import whois
import nltk
import logging
import re
import time
import random

from urllib.parse import urlparse, urljoin
from ipwhois import IPWhois
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from collections import Counter
from bs4 import BeautifulSoup
from typing import Dict, List, Tuple
from langdetect import detect_langs, DetectorFactory

from .llm import create_and_run_pipeline, create_and_run_perp_pipeline

from ..models.dfbmodels import (
    DFPDomain,
    DFPContent,
    DFPReviews,
    DFPReviewsAndRatings,
    DFPProductAndServices,
    DFPProductsAndServicesCategories,
    DFPRiskAndNewsAnalysis,
    DFPURLPolicies,
    DFPContentPolicies,
    DFPPolicies,
    DFPAllPolicies,
    AllDFPData
)
from ..static.placeholders import (
    CONTENT_PLACEHOLDERS
)
from ..static.keywords import (
    RESTRICTED_AND_UNSAFE_CONTENT_KEY<PERSON>ORDS,
    FRAUD_CONTENT_KEYWORDS,
    TNC_KEYWORDS,
    PRIVACY_POLICY_KEYWORDS,
    SHIPPING_POLICY_KEYWORDS,
    RETURN_REFUND_KEYWORDS,
    COOKIES_POLICY_KEYWORDS,
)
from ..static.prompts import (
    PROMPT_CHECK_UNSAFE_CONTENT,
    PROMPT_CHECK_FRAUDULENT_CONTENT,
    PROMPT_EXTRACT_ADDRESS,
    PROMPT_CHECK_IMPERSONATION,
    PROMPT_CHECK_SPELLING_ERRORS,
    PROMPT_EXTRACT_REVIEWS,
    PROMPT_EXTRACT_PRODUCTS_AND_SERVICES,
    PROMPT_RISK_AND_NEWS_ANALYSIS,
    PROMPT_EXTRACT_IMPORTANT_POLICIES,
    PROMPT_EXTRACT_IMPORTANT_TERMS,
    PROMPT_EXTRACT_PRODUCT_CATEGORIES,
    PROMPT_EXTRACT_REVIEWS_AND_RATINGS,
)
from ..static.parsers import (
    unsafe_content_parser,
    fraudulent_content_parser,
    address_parser,
    impersonation_parser,
    spelling_errors_parser,
    reviews_parser,
    products_and_services_parser,
    risk_and_news_analysis_parser,
    policies_parser,
    terms_and_conditions_parser,
    products_and_services_categories_parser,
    reviews_and_ratings_parser,
)

# Set seed for reproducibility
DetectorFactory.seed = 0

logger = logging.getLogger(__name__)

# Define a list of headers (realistic browser behaviors)
HEADERS_LIST = [
    {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept-Language": "en-US,en;q=0.9",
        "Referer": "https://www.google.com/",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Connection": "keep-alive"
    },
    {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15",
        "Accept-Language": "en-US,en;q=0.8",
        "Referer": "https://duckduckgo.com/",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Connection": "keep-alive"
    },
    {
        "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept-Language": "en-GB,en;q=0.7",
        "Referer": "https://www.bing.com/",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
        "Connection": "keep-alive"
    }
    # Add more variations as needed
]

class WebsiteAnalyzerV2:
    def __init__(self):
        pass

    def analyze_website(self, url: str) -> DFPDomain:
        """
        Analyzes a merchant website to extract product/service information,
        categorization, and key words.
        
        Args:
            url (str): The website URL to analyze
            
        Returns:
            Dict: Analysis results containing products/services, categorization, and keywords
        """
        url_info = self._url_info(url)
        domain_info = self._get_domain_info(url)
        ip_info = self._get_ip_info(url)
        certificate_info = self._get_certificate_info(url)
        response = DFPDomain(
            domain=domain_info['domain'],
            server_ip_address=ip_info.get('ip_address', ''),
            server_ip_country=ip_info.get('country',''),
            server_ip_owner=ip_info.get('owner', ''),
            creation_dates=domain_info.get('creation_date', []),
            registrar=domain_info.get('registrar', ''),
            owner_emails=domain_info.get('emails', []),
            admin_contact=domain_info.get('admin_contact', ''),
            privacy_protection=domain_info.get('privacy_protection', ''),
            functional=url_info['is_functional'],
            status_code=url_info['status_code'],
            response_time=url_info['response_time'],
            redirection=url_info['is_redirecting'],
            redirection_url=url_info['final_url'],
            ssl_certificates=certificate_info if 'error' not in certificate_info else None,
        )
        return response
        
    def _get_domain_info(self, domain: str) -> Dict:
        try:
            # Fetch domain WHOIS information
            domain_data = whois.whois(domain)
            creation_date = domain_data.creation_date
            if not creation_date:
                creation_date = []
            elif isinstance(creation_date, list):
                creation_date = [str(date) for date in creation_date]
            else:
                creation_date = [str(creation_date)]

            email_data = domain_data.emails
            if not email_data:
                email_data = []
            elif isinstance(email_data, str):
                email_data = [email_data]
            else:
                email_data = [str(email) for email in email_data]
            return {
                'domain': domain,
                'creation_date': creation_date,
                'registrar': domain_data.registrar if domain_data.registrar else '',
                'emails': email_data,
                'admin_contact': domain_data.name if domain_data.name else '',
                'privacy_protection': bool(domain_data.privacy)
            }
        except Exception as e:
            return {
                'domain': domain,
                'error': str(e)
            }

    def _get_ip_info(self, url: str) -> Dict:
        try:
            # Extract domain from URL
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            if not domain:
                domain = parsed_url.path
            
            # Remove www. if present
            domain = domain.replace('www.', '')
            
            # Get IP address of the domain
            ip_address = socket.gethostbyname(domain)
            
            # Get additional information about the IP
            ip_info = IPWhois(ip_address)
            result = ip_info.lookup_whois()
            
            country = result.get("asn_country_code", "Unknown")
            owner = result.get("asn_description", "Unknown")
            
            return {
                "domain": domain,
                "ip_address": ip_address,
                "country": country,
                "owner": owner
            }
        except Exception as e:
            return {
                "error": str(e)
            }

    def _is_url_functional(self, url: str) -> Tuple[bool, int]:
        """
        Check if a URL is functional (status_code 200)
        """
        try:
            response = requests.get(url)
            is_functional = response.status_code == 200
            return is_functional, response.status_code

        except requests.exceptions.RequestException as e:
            logger.error(f"Error checking URL {url}: {e}")
            return False, 404
        
    def _is_url_redirecting(self, url: str) -> Tuple[bool, str]:
        """
        Function sends a get or post request to an url and check if that url redirects the request
        """
        try:
            response = requests.get(url, allow_redirects=True, timeout=10)

            is_redirecting = len(response.history) > 0
            final_url = response.url  # Always gives final destination

            return is_redirecting, final_url

        except requests.exceptions.RequestException as e:
            logger.error(f"Error: {e}")  # Fixed logger call
            return False, url
        
    def _response_time(self, url: str) -> float:
        """
        Function checks the response time of a url
        """
        try:
            start_time = time.time()
            response = requests.get(url, timeout=10)
            end_time = time.time()
            response_time = end_time - start_time
            return response_time
        except requests.exceptions.RequestException as e:
            logger.error(f"Error: {e}")
            return 0.0  # Return 0.0 if there's an error
        
    def _url_info(self, url: str) -> Dict:
        """
        Function checks if the url is functional and if it redirects to another url
        """
        is_functional, status_code = self._is_url_functional(url)
        is_redirecting, final_url = self._is_url_redirecting(url)
        response_time = self._response_time(url)

        return {
            "is_functional": is_functional,
            "status_code": status_code,
            "is_redirecting": is_redirecting,
            "final_url": final_url,
            "response_time": response_time
        }

    def _get_certificate_info(self, url: str) -> dict:
        """
        Get peer certificates associated with a website
        """
        parsed_url = urlparse(url)
        hostname = parsed_url.hostname
        port = parsed_url.port or 443
        context = ssl.create_default_context()
        try:
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()

                    cert_info = {
                        'Subject': dict(x[0] for x in cert.get('subject', [])),
                        'Issuer': dict(x[0] for x in cert.get('issuer', [])),
                        'Valid From': str(cert.get('notBefore', '')),
                        'Valid Till': str(cert.get('notAfter', '')),
                        'SANs': [x[1] for x in cert.get('subjectAltName', [])],
                    }

                    return cert_info
        except Exception as e:
            logger.error(f"Error getting certificate info: {e}")
            return {
                'error': str(e)
            }

def send_request(url: str):
    """
    Send a GET request to the given URL and return the response.
    """
    try:
        headers = random.choice(HEADERS_LIST)
        response = requests.get(url, headers=headers, timeout=10)
        # response = requests.get(url, timeout=10)
        # payload = { 
        #         'api_key': 'fe6fac0f305e41b622faff99e2808fea',
        #         'url': url
        #     }
        # response = requests.get('https://api.scraperapi.com/', params=payload)
        return response
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching {url}: {e}")
        return None
        

def extract_website_content(url: str) -> Tuple[str, BeautifulSoup]:
        """
        Extract visible text from a webpage and return it along with the BeautifulSoup object
        """
        try:
            # Fetch the website content
            # headers = random.choice(HEADERS_LIST)
            # response = requests.get(url, headers=headers, timeout=10)
            # response.raise_for_status()
            response = send_request(url)
            response.raise_for_status()
            soup = BeautifulSoup(response.text, 'html.parser')
            soup_copy = BeautifulSoup(response.text, 'html.parser')
            # Remove script, style, and noscript tags
            for tag in soup(['script', 'style', 'noscript']):
                tag.decompose()
            # Extract visible text
            text_content = soup.get_text(separator=' ', strip=True)
            # Remove extra spaces by replacing multiple spaces with a single space
            text_content = re.sub(r'\s+', ' ', text_content)
            # If any character(can be anything including space) is repeated more than 3 times, replace it with a single character
            text_content = re.sub(r'(.)\1{4,}', r'\1', text_content)
            return text_content.strip(), soup_copy
        except Exception as e:
            logger.error(f"Error fetching {url}: {e}")
            return '', BeautifulSoup('', 'html.parser')
    
def extract_hyperlinks(url: str, soup: BeautifulSoup) -> List[str]:
    """Extract all hyperlinks from a webpage"""
    try:
        links = []
        for tag in soup.find_all('a', href=True):
            full_url = urljoin(url, tag['href'])
            links.append(full_url)

        return list(set(links))  # Remove duplicates

    except Exception as e:
        logger.error(f"Error extracting hyperlinks: {e}")
        return []
    
def extract_images_links(url: str, soup: BeautifulSoup) -> List[Tuple[str, int]]:
    """Extract all image links from a webpage with count of each image"""
    try:
        if soup is None:
            return []
        images = []
        for tag in soup.find_all('img', src=True):
            full_url = urljoin(url, tag['src'])
            images.append(full_url)

        # Count occurrences of each image link
        image_counts = Counter(images)
        # Return 10 most common images
        return image_counts.most_common(10)  # Return list of tuples (image_link, count)

    except Exception as e:
        logger.error(f"Error extracting image links: {e}")
        return []
    
class ContentAnalyzer:
    def __init__(self):
        # Download required NLTK data
        try:
            # Create nltk_data directory if it doesn't exist
            nltk_data_dir = os.path.expanduser('~/nltk_data')
            if not os.path.exists(nltk_data_dir):
                os.makedirs(nltk_data_dir)
            
            # Download all required NLTK data with explicit paths
            resources = {
                'tokenizers/punkt': 'punkt',
                'corpora/stopwords': 'stopwords',
                'taggers/averaged_perceptron_tagger': 'averaged_perceptron_tagger',
                'chunkers/maxent_ne_chunker': 'maxent_ne_chunker',
                'corpora/words': 'words'
            }
            
            for resource_path, resource_name in resources.items():
                try:
                    nltk.data.find(resource_path)
                except LookupError:
                    logger.info(f"Downloading {resource_name}...")
                    nltk.download(resource_name, quiet=True)
            
            # Initialize stopwords after ensuring downloads
            self.stop_words = set(stopwords.words('english'))
            
        except Exception as e:
            logger.warning(f"Warning: Error initializing NLTK resources: {e}")
            # Provide empty stopwords as fallback
            self.stop_words = set()

    def analyze_website_content(self, url: str) -> DFPContent:
        visible_content, soup = extract_website_content(url)
        hyperlinks = extract_hyperlinks(url, soup)
        placeholders = self._content_placeholder_check(visible_content)
        spelling_errors = self._spelling_errors(visible_content)
        languages = self._detect_language(visible_content)
        default_language = languages[0][0] if languages else 'unknown'
        keywords = self._extract_keywords(visible_content)
        unsafe_content = self._extract_unsafe_content(visible_content)
        fraud_content = self._extract_fraud_content(visible_content)
        impersonation = self._impersonation_check(url, visible_content)
        addresses = self._extract_address(visible_content, hyperlinks)

        social_media = self._extract_social_media_links(hyperlinks)
        contact_info = self._extract_contact_info(soup, visible_content)
        hidden_content = self._hidden_content_check(soup)
        images = extract_images_links(url, soup)

        content = DFPContent(
            content=visible_content,
            placeholders=placeholders,
            spelling_errors=spelling_errors,
            languages=[lang[0] for lang in languages],
            default_language=default_language,
            social_media=social_media,
            contact_info=contact_info,
            keywords=keywords,
            unsafe_content=unsafe_content,
            fraud_content=fraud_content,
            impersonation=impersonation,
            hidden_content=hidden_content,
            addresses=addresses,
            images=images,
        )

        return content

    def _extract_social_media_links(self, hyperlinks: List[str]) -> List[Tuple[str, str]]:
        """Extract social media links from the list of hyperlinks"""
        # Define patterns for social media platforms
        patterns = {
            'facebook': re.compile(r'facebook\.com', re.IGNORECASE),
            'instagram': re.compile(r'instagram\.com', re.IGNORECASE),
            'linkedin': re.compile(r'linkedin\.com', re.IGNORECASE),
            'twitter': re.compile(r'twitter\.com', re.IGNORECASE),
            'youtube': re.compile(r'youtube\.com|youtu\.be', re.IGNORECASE),
            # Add more platforms as needed
        }

        social_media_links = []
        unique_links = set()
        for link in hyperlinks:
            for platform, pattern in patterns.items():
                if pattern.search(link):
                    if link not in unique_links:
                        unique_links.add(link)
                        social_media_links.append((platform, link))
                        break  # Stop checking other platforms for this link
        return social_media_links
    
    def _extract_contact_info(self, soup: BeautifulSoup, visible_text: str) -> List[Tuple[str, str]]:
        """Extract contact information (email and phone) from visible text and HTML attributes"""

        contact_info = []
        unique_contacts = set()

        # Define compiled regex patterns
        phone_pattern = re.compile(r'''
            (?<!\w)                              # Negative lookbehind for word char
            (?:\+|00)?                           # '+' or '00' prefix (optional)
            (?:\d{1,4}[\s\-\.]?)?                # Country/area code
            (?:\(?\d{2,5}\)?[\s\-\.]?)?          # Optional parentheses area code
            (?:\d{2,4}[\s\-\.]?){2,4}            # Main number blocks
            (?:\s?(?:ext|x|extension)\s?\d{1,5})?# Optional extension
            (?!\w)                               # Negative lookahead
        ''', re.VERBOSE)

        email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b')

        # Step 1: Clean soup
        for tag in soup(['script', 'style', 'noscript']):
            tag.decompose()

        # Step 2: Extract from visible text
        for number in phone_pattern.findall(visible_text):
            cleaned = number.strip()
            digits = re.sub(r'\D', '', cleaned)
            if digits and len(digits) >= 7 and cleaned.strip() not in unique_contacts:
                unique_contacts.add(cleaned)
                contact_info.append(('phone', cleaned))

        for email in email_pattern.findall(visible_text):
            if email.strip() not in unique_contacts:
                unique_contacts.add(email)
                contact_info.append(('email', email))

        # Step 3: Extract from hrefs
        for tag in soup.find_all('a', href=True):
            href = tag['href'].strip()
            if href.startswith('mailto:'):
                email = href.split(':', 1)[1].split('?')[0]
                if email_pattern.fullmatch(email) and email.strip() not in unique_contacts:
                    unique_contacts.add(email)
                    contact_info.append(('email', email))
            elif href.startswith('tel:'):
                phone = href.split(':', 1)[1]
                digits = re.sub(r'\D', '', phone)
                if digits and len(digits) >= 7 and phone.strip() not in unique_contacts:
                    unique_contacts.add(phone)
                    contact_info.append(('phone', phone))

        return contact_info
    
    
    def _extract_keywords(self, content: str) -> List[Tuple[str, int]]:
        """Extract and analyze key words from the website"""
        try:
            # Get text content
            text = content.lower()
            # Use a more robust tokenization approach
            try:
                tokens = word_tokenize(text.lower())
            except LookupError:
                # Fallback to basic tokenization if NLTK tokenizer fails
                tokens = text.lower().split()
            
            # Remove stop words and non-alphabetic tokens
            keywords = [word for word in tokens 
                       if word.isalpha() 
                       and word not in self.stop_words 
                       and len(word) > 2]
            
            # Count frequency
            keyword_freq = Counter(keywords)
            
            # Return top 20 keywords with their frequencies
            return keyword_freq.most_common(20)
        except Exception as e:
            logger.error(f"Warning: Error extracting keywords: {e}")
            return []

    def _content_placeholder_check(self, content: str) -> List[Tuple[str, int]]:
        """
        Check for content placeholders in the website content
        """
        content = content.lower()
        placeholders = [(placeholder, content.count(placeholder)) for placeholder in CONTENT_PLACEHOLDERS]
        return [(placeholder, count) for placeholder, count in placeholders if count > 0]
    
    def _spelling_errors(self, content: str) -> List[Tuple[str, int]]:
        """
        Check for spelling errors in the website content.
        Returns a list of misspelled words and their frequency.
        """
        if len(content) == 0:
            return []
        
        resp = create_and_run_pipeline(
            system_prompt=PROMPT_CHECK_SPELLING_ERRORS,
            parser=spelling_errors_parser,
            user_inp=content
        )
        content = content.lower()
        spelling_errors = resp.get('spelling_errors', [])
        spelling_errors = [error.lower() for error in spelling_errors]
        spelling_erros = list(set(spelling_errors))
        # Count occurrences of each spelling error in the content
        spelling_errors_count = [(word, content.count(word)) for word in spelling_erros]
        spelling_errors_count = [(word, count) for word, count in spelling_errors_count if count > 0]
        # Sort by frequency
        spelling_errors_count.sort(key=lambda x: x[1], reverse=True)
        # Return top 20 spelling errors with their frequencies
        return spelling_errors_count[:20]
    
    def _detect_language(self, content: str) -> List[Tuple[str, float]]:
        """
        Detect the top 3 most probable languages in the given content.
        Returns a list of (language_code, confidence_score) tuples.
        """
        try:
            detections = detect_langs(content)
            # Limit to top 3 results
            top_3 = [(lang.lang, round(lang.prob, 4)) for lang in detections[:3]]
            return top_3
        except Exception as e:
            # Log or handle the exception if needed
            return [("unknown", 0.0)]
        
    def _extract_unsafe_content(self, content: str) -> List[str]:
        """
        Check for unsafe content in the website content.
        Returns a list of unsafe keywords found in the content.
        """
        if len(content)==0:
            return []
        content = content.lower()

        # Check for unsafe content based on some keywords
        unsafe_content = [keyword for keyword in RESTRICTED_AND_UNSAFE_CONTENT_KEYWORDS if keyword.lower() in content]

        # Check for unsafe content using AI
        resp = create_and_run_pipeline(
            system_prompt=PROMPT_CHECK_UNSAFE_CONTENT,
            parser=unsafe_content_parser,
            user_inp=content
        )

        unsafe_content_llm = resp.get('unsafe_content', [])
        if unsafe_content_llm:
            # If LLM found any unsafe content, add it to the list
            unsafe_content.extend(unsafe_content_llm)
        # Remove duplicates
        return list(set(unsafe_content))

    def _extract_fraud_content(self, content: str) -> List[str]:
        """
        Check for fraud content in the website content.
        Returns a list of fraud keywords found in the content.
        """
        if len(content) == 0:
            return []
        content = content.lower()

        # Check for fraud content based on some keywords
        fraud_content = [keyword for keyword in FRAUD_CONTENT_KEYWORDS if keyword.lower() in content]

        # Check for fraud content using AI
        resp = create_and_run_pipeline(
            system_prompt=PROMPT_CHECK_FRAUDULENT_CONTENT,
            parser=fraudulent_content_parser,
            user_inp=content
        )
        fraud_content_llm = resp.get('fraud_content', [])
        if fraud_content_llm:
            # If LLM found any fraud content, add it to the list
            fraud_content.extend(fraud_content_llm)
        # Remove duplicates
        return list(set(fraud_content))
    
    def _impersonation_check(self, url: str, content: str) -> List[Dict]:
        """
        Check for impersonation in the website content.
        Returns a list of impersonation keywords found in the content.
        """
        if len(content) == 0:
            return []
        user_input = f"""
        website_url: {url}
        content: {content}
        """
        resp = create_and_run_perp_pipeline(
            system_prompt=PROMPT_CHECK_IMPERSONATION,
            parser=impersonation_parser,
            user_inp=user_input
        )
        return resp.get('impersonated_entities', [])
    
    def _hidden_content_check(self, soup: BeautifulSoup) -> List[str]:
        """
        Check for hidden content in the website, by checking if the text color is the same as the background color
        """
        hidden_content = []
        for tag in soup.find_all(True):
            # Check if the tag has a style attribute
            if 'style' in tag.attrs:
                style = tag['style']
                # Check for color and background-color properties
                if 'color' in style and 'background-color' in style:
                    color = re.search(r'color:\s*([^;]+)', style)
                    background_color = re.search(r'background-color:\s*([^;]+)', style)
                    if color and background_color:
                        text_color = color.group(1).strip()
                        bg_color = background_color.group(1).strip()
                        # Compare colors (this is a simple check, you might want to improve it)
                        if text_color == bg_color and tag.get_text(strip=True):
                            hidden_content.append(tag.get_text(strip=True))
        return hidden_content
    
    def _extract_address(self, content: str, all_links: List[str]) -> List[str]:
        """
        Extract potential company address from the given page or linked contact page.
        """
        if len(content) == 0 and len(all_links) == 0:
            return []
        
        resp = create_and_run_pipeline(
            system_prompt=PROMPT_EXTRACT_ADDRESS,
            parser=address_parser,
            user_inp=content
        )
        addresses = resp.get('addresses', [])

        for link in all_links:
            if 'contact' in link.lower() or 'about' in link.lower():
                try:
                    # Extract content from the linked page
                    linked_content, _ = extract_website_content(link)

                    if len(linked_content) == 0:
                        continue
                    # Check if the linked page has any address
                    resp = create_and_run_pipeline(
                        system_prompt=PROMPT_EXTRACT_ADDRESS,
                        parser=address_parser,
                        user_inp=linked_content
                    )
                    addresses1 = resp.get('addresses', [])
                    if addresses1:
                        addresses.extend(addresses1)
                        break  # Stop searching if we found addresses
                except Exception as e:
                    logger.error(f"Error extracting address from {link}: {e}")
        return list(set(addresses))

def extract_reviews(url: str, merchant_name: str) -> DFPReviews:
    """
    Extract reviews from a website.
    """
    prompt = f"""
    FRAUD INVESTIGATION MISSION:

    Merchant Name: {merchant_name}
    Merchant Domain: {url}

    Your task is to identify and report ONLY negative information (fraud allegations, scams, suspicious activity, consumer complaints, or legal troubles) related to this merchant.

    Use the above system instructions and return your findings in the required JSON format.
    Very Important: Only return the JSON object without any additional text or explanation.
    """

    resp = create_and_run_perp_pipeline(
        system_prompt=PROMPT_EXTRACT_REVIEWS,
        parser=reviews_parser,
        user_inp=prompt
    )
    reviews_overview = resp.get('reviews_overview', {})
    individual_reviews = resp.get('individual_reviews', [])

    reviews = DFPReviews(
        sentiment_status=reviews_overview.get('sentiment_status', ''),
        reviews_summary=reviews_overview.get('reviews_summary', ''),
        reviews=individual_reviews
    )
    return reviews

def extract_reviews_and_ratings(url: str, merchant_name: str) -> DFPReviewsAndRatings:
    """
    Extract reviews and ratings from a website.
    """
    prompt = f"""
    REVIEWS & RATINGS INVESTIGATION:

    Merchant Name: {merchant_name}
    Merchant Domain: {url}

    Your task is to identify and report the reviews and ratings of this merchant. Use the system instructions and return your findings in the required JSON format.
    """

    resp = create_and_run_perp_pipeline(
        system_prompt=PROMPT_EXTRACT_REVIEWS_AND_RATINGS,
        parser=reviews_and_ratings_parser,
        user_inp=prompt
    )
    reviews_overview = resp.get('reviews_overview', {})
    individual_reviews = resp.get('individual_reviews', [])
    reviews = DFPReviewsAndRatings(
        sentiment_status=reviews_overview.get('sentiment_status', ''),
        reviews_summary=reviews_overview.get('reviews_summary', ''),
        reviews=individual_reviews
    )
    return reviews

def extract_product_categories(url: str, merchant_name: str) -> DFPProductsAndServicesCategories:
    """
    Extract product categories from a website.
    """
    prompt = f"""
    PRODUCT CATEGORIES INVESTIGATION:

    Merchant Name: {merchant_name}
    Merchant Domain: {url}

    Your task is to identify and document all product categories provided by this merchant. Use the system instructions and return your findings in the required JSON format.
    """

    resp = create_and_run_perp_pipeline(
        system_prompt=PROMPT_EXTRACT_PRODUCT_CATEGORIES,
        parser=products_and_services_categories_parser,
        user_inp=prompt
    )
    categories = resp.get('categories', [])
    
    product_categories = DFPProductsAndServicesCategories(
        categories=categories
    )
    return product_categories

def extract_product_and_services(url: str, merchant_name: str, categories: List[str]) -> DFPProductAndServices:
    """
    Extract product and services from a website.
    """
    prompt = f"""
    PRODUCT & SERVICES INVESTIGATION:

    Merchant Name: {merchant_name}
    Merchant Domain: {url}
    Product and Services Categories: {categories}

    Your task is to identify and document all products and services provided by this merchant. Use the system instructions and return your findings in the required JSON format.
    """

    resp = create_and_run_perp_pipeline(
        system_prompt=PROMPT_EXTRACT_PRODUCTS_AND_SERVICES,
        parser=products_and_services_parser,
        user_inp=prompt
    )
    products_and_services_summary = resp.get('products_and_services_description_summary', '')
    products_and_services = resp.get('products_and_services', [])    
    
    products = DFPProductAndServices(
        summary=products_and_services_summary,
        products_and_services=products_and_services
    )
    return products

def extract_risk_and_news_analysis(url: str, merchant_name: str) -> DFPRiskAndNewsAnalysis:
    """
    Extract risk and news analysis from a website.
    """
    prompt = f"""
    NEWS & RISK INVESTIGATION:

    Merchant Name: {merchant_name}
    Merchant Domain: {url}

    Search for any news, reports, or articles that indicate fraud, scams, illegal activity, or reputational risks involving this merchant. Return your findings in the required JSON format.
    """

    resp = create_and_run_perp_pipeline(
        system_prompt=PROMPT_RISK_AND_NEWS_ANALYSIS,
        parser=risk_and_news_analysis_parser,
        user_inp=prompt
    )
    news_fraud_scam_illegal_risk_summary = resp.get('news_fraud_scam_illegal_risk_summary', '')
    news_sentiment = resp.get('news_sentiment', '')
    news_incidents = resp.get('news_incidents', [])

    risk_and_news_analysis = DFPRiskAndNewsAnalysis(
        summary=news_fraud_scam_illegal_risk_summary,
        sentiment=news_sentiment,
        incidents=news_incidents
    )
    return risk_and_news_analysis

class PolicyAnalyzer:
    def __init__(self, keywords: List[str], hyperlinks: List[str], policy_type: str):
        self.keywords = keywords
        self.hyperlinks = hyperlinks
        self.type = policy_type
        assert self.type in ['tnc', 'shipping', 'privacy', 'cookies', 'return_and_refund'], "Invalid policy type"
        self.filtered_links = self._filter_links()

    def _filter_links(self):
        """
        Filter links based on keywords
        """
        filtered_links = [link for link in self.hyperlinks if any(keyword.lower() in link.lower() for keyword in self.keywords)]
        return filtered_links
    
    def _analyze_urls(self) -> List[DFPDomain]:
        """
        Analyze the filtered URLs for policies
        """
        urls_analysis_results = []
        for link in self.filtered_links:
            url_analyzer = WebsiteAnalyzerV2()
            analysis = url_analyzer.analyze_website(link)
            urls_analysis_results.append(analysis)
        return urls_analysis_results
    
    def _analyze_content(self, url: str) -> List[DFPContent]:
        """
        Analyze the content of the filtered URLs for policies
        """
        policies_content_results = []
        for link in self.filtered_links:
            content_analyzer = ContentAnalyzer()
            analysis = content_analyzer.analyze_website_content(link)
            policies_content_results.append(analysis)

        return policies_content_results
    
    def _important_policies(self, content: str) -> List[str]:
        """
        Check for important policies in the content
        """
        if len(content) < 50:
            return []
        
        if self.type == 'tnc':
            prompt = PROMPT_EXTRACT_IMPORTANT_TERMS
            parser = terms_and_conditions_parser
            return_key = 'important_terms'
        else:
            prompt = PROMPT_EXTRACT_IMPORTANT_POLICIES
            parser = policies_parser
            return_key = 'important_policies'
            content = f"""
            policy_type: {self.type} policies
            content: {content}
            """
        resp = create_and_run_pipeline(
            system_prompt=prompt,
            parser=parser,
            user_inp=content
        )
        return resp.get(return_key, [])

    def analyze(self) -> DFPPolicies:
        """
        Main function to analyze the policies
        """
        urls_analysis_results = self._analyze_urls()
        policies_content_results = self._analyze_content(urls_analysis_results)

        policies_url = []
        policies_content = []
        for url_anl, conten_anl in zip(urls_analysis_results, policies_content_results):
            url_policy = DFPURLPolicies(
                url=url_anl.domain,
                functional=url_anl.functional,
                status_code=url_anl.status_code,
                redirection=url_anl.redirection,
                redirection_url=url_anl.redirection_url
            )

            content_policy = DFPContentPolicies(
                content=conten_anl.content,
                keywords=conten_anl.keywords,
                placeholders=conten_anl.placeholders,
                spelling_errors=conten_anl.spelling_errors,
                languages=conten_anl.languages,
                default_language=conten_anl.default_language,
                unsafe_content=conten_anl.unsafe_content,
                fraud_content=conten_anl.fraud_content,
                impersonation=conten_anl.impersonation,
                hidden_content=conten_anl.hidden_content,
                important_policies=self._important_policies(conten_anl.content)
            )
            policies_url.append(url_policy)
            policies_content.append(content_policy)

        all_important_policies = []
        for content_policy in policies_content:
            all_important_policies.extend(content_policy.important_policies)
        all_important_policies = list(set(all_important_policies))

        policies = DFPPolicies(
            type=self.type,
            url_analysis=policies_url,
            content_analysis=policies_content,
            all_important_policies=all_important_policies
        )
        return policies
    
def extract_policies(url: str) -> DFPAllPolicies:
    """
    Extract policies from a website.
    """
    # Define keywords for different policy types
    keywords = {
        'tnc': TNC_KEYWORDS,
        'shipping': SHIPPING_POLICY_KEYWORDS,
        'privacy': PRIVACY_POLICY_KEYWORDS,
        'cookies': COOKIES_POLICY_KEYWORDS,
        'return_and_refund': RETURN_REFUND_KEYWORDS
    }
    _, soup = extract_website_content(url)
    # Extract all hyperlinks from the webpage
    all_hyperlinks = extract_hyperlinks(url, soup)
    # Initialize a dictionary to store the results
    results = {}
    for policy_type, policy_keywords in keywords.items():
        # Create an instance of PolicyAnalyzer for each policy type
        policy_analyzer = PolicyAnalyzer(policy_keywords, all_hyperlinks, policy_type)
        # Analyze the policies
        policies = policy_analyzer.analyze()
        # Store the results in the dictionary
        results[policy_type] = policies
    response = DFPAllPolicies(
        all_policies=results
    )

    return response

def full_scrapper_temp(url: str, merchant_name: str) -> AllDFPData:
    """
    Full scrapper function to extract all data from a website.
    """
    # Extract domain information
    domain_analyzer = WebsiteAnalyzerV2()
    domain_info = domain_analyzer.analyze_website(url)
    print("url analysis completed")
    # Extract content information
    content_analyzer = ContentAnalyzer()
    content_info = content_analyzer.analyze_website_content(url)
    print("content analysis completed")
    # product categories
    product_categories = extract_product_categories(url, merchant_name)
    # product and services
    products_and_services = extract_product_and_services(url, merchant_name, product_categories.categories)
    print("product and services analysis completed")
    # reviews
    reviews = extract_reviews(url, merchant_name)
    # reviews and ratings
    reviews_and_ratings = extract_reviews_and_ratings(url, merchant_name)
    # risk and news analysis
    risk_and_news_analysis = extract_risk_and_news_analysis(url, merchant_name)
    print("risk and news analysis completed")
    # policies
    policies = extract_policies(url)
    print("policies analysis completed")
    # Create the final response
    response = AllDFPData(
        domain=domain_info,
        content=content_info,
        products_and_services=products_and_services,
        products_and_services_categories=product_categories,
        reviews=reviews,
        reviews_and_ratings=reviews_and_ratings,
        risk_and_news_analysis=risk_and_news_analysis,
        policies=policies
    )
    print("everything worked")
    return response