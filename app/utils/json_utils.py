from decimal import Decimal
import json
from datetime import datetime
from uuid import UUID

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        if isinstance(obj, UUID):
            return str(obj)
        return super(CustomJSONEncoder, self).default(obj)

def serialize_to_json(data):
    """Serialize data to JSON with custom handling for Decimal, datetime and UUID types"""
    return json.loads(json.dumps(data, cls=CustomJSONEncoder)) 