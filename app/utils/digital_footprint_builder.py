import requests

from ..models.dfbmodels import (
    DFPDomain,
    DFPContent,
    DFPProductAndServices,
    DFPReviews,
    DFPReviewsAndRatings,
    DFPRiskAndNewsAnalysis,
    DFPAllPolicies,
    AllDFPData,
    WebsiteContentProcessed,
    RedFlagsDomain,
    RedFlagsContent,
    RedFlagsPolicies,
    RedFlagsPrices,
    RedFlagsReviews,
    DigitalFootprintRedFlags,
    DigitalFootprint,
)
from ..utils.llm import create_and_run_perp_pipeline
from ..static.prompts import PROMPT_GET_AVERAGE_PRICE
from ..static.parsers import average_price_parser

def make_red_flags_domain(domain_data: DFPDomain) -> RedFlagsDomain:
    return RedFlagsDomain(
        not_functional= not domain_data.functional,
        not_secure= True if not domain_data.ssl_certificates else False,
        redirection=domain_data.redirection,
        redirection_url=domain_data.redirection_url,    
    )

def make_content_red_flags(content_data: DFPContent) -> RedFlagsContent:
    spelling_errors = content_data.spelling_errors
    # if an spelling error has frequency of more than 10 it is mp an entity name and not an spelling error
    spelling_errors = [(word, freq) for word, freq in spelling_errors if freq <= 10]
    
    default_language = content_data.default_language
    indian_language_codes = [
    "en",  # English
    "hi",  # Hindi
    "bn",  # Bengali
    "te",  # Telugu
    "mr",  # Marathi
    "ta",  # Tamil
    "ur",  # Urdu
    "gu",  # Gujarati
    "ml",  # Malayalam
    "kn",  # Kannada
    "or",  # Odia
    "pa",  # Punjabi
    "as",  # Assamese
    "ks",  # Kashmiri
    "sa",  # Sanskrit
    "sd"   # Sindhi
]

    if default_language.lower() not in indian_language_codes:
        language_not_indian = True
    else:
        language_not_indian = False

    # check for social media
    social_media = content_data.social_media
    if len(social_media) == 0:
        no_social_media = True
    else:
        # check if the social media links are valid by sending a request to the url
        no_social_media = False
        for sm in social_media:
            try:
                response = requests.get(sm[1], timeout=5, allow_redirects=False)
                if response.status_code != 200:
                    continue

            except requests.exceptions.RequestException:
                no_social_media = True
                break

    contact_info = content_data.contact_info
    if len(contact_info) == 0:
        no_contact_info = True
    else:
        no_contact_info = False

    # check if emails has bussiness domain or not
    emails_not_verified = True
    for contact in contact_info:
        if contact[0] == "email":
            email = contact[1]
            # check if the email has a business domain
            if "@" in email:
                domain = email.split("@")[1]
                if domain == "gmail.com" or domain == "yahoo.com" or domain == "hotmail.com":
                    emails_not_verified = True
                else:
                    emails_not_verified = False
                    break
            else:
                emails_not_verified = True
                break

    # if an image has frequency of more than 5 it is mp a fraud or scam website
    same_images = content_data.images
    same_images = [(image, freq) for image, freq in same_images if freq >= 5]
    sames_images = bool(len(same_images) > 0)

    content_redflags = RedFlagsContent(
        no_content= len(content_data.content) == 0,
        spelling_errors=spelling_errors,
        placeholders=content_data.placeholders,
        unsafe_content=content_data.unsafe_content,
        fraud_content=content_data.fraud_content,
        impersonation=content_data.impersonation,
        hidden_content=content_data.hidden_content,
        language_not_indian=language_not_indian,
        no_social_media=no_social_media,
        no_contact_info=no_contact_info,
        no_addresses=len(content_data.addresses) == 0,
        emails_not_verified=emails_not_verified,
        same_images=sames_images,
    )
    return content_redflags

def check_the_string_for_non_numeric_characters(string: str) -> bool:
    """
    Check if the string contains any non-numeric characters.
    """
    # Check if the string is empty
    if not string:
        return True

    # Check if the string contains only digits
    for char in string:
        if not char.isdigit():
            return True

    return False

def make_policies_red_flags(policies_data: DFPAllPolicies) -> RedFlagsPolicies:
    # check if policies are present
    no_policies = True
    for policy in policies_data.all_policies.values():
        for policy_content in policy.content_analysis:
            if len(policy_content.content)==0:
                continue
            else:
                no_policies = False
                break

    # check if important policies are present
    no_important_policies = True
    for policy in policies_data.all_policies.values():
        if len(policy.all_important_policies) == 0:
            continue
        else:
            no_important_policies = False
            break

    policy_redflags = RedFlagsPolicies(
        no_policies=no_policies,
        no_important_policies=no_important_policies,
    )
    return policy_redflags

def make_prices_red_flags(prices_data: DFPProductAndServices) -> RedFlagsPrices:
    # check if the prices are mentioned or not
    no_prices_mentioned = True
    for product in prices_data.products_and_services:
        if product['price'] == '':
            continue
        else:
            no_prices_mentioned = False
            break

    # check if currency is indian or not and if it is mentioned or not
    foreign_currency = False
    no_currency_mentioned = True
    for product in prices_data.products_and_services:
        if product['currency']!='' and product['currency'].lower() not in ['inr', 'rs', '₹', 'indian rupee', 'indian rupees', 'rupee', 'rupees']:
            foreign_currency = True
            break
        if product['currency'] != '':
            no_currency_mentioned = False
            break

    # check if the prices are unrealistic or not
    unrealistic_prices = False
    lower_than_market_prices = False
    higher_than_market_prices = False
    for product in prices_data.products_and_services:
        name = product['product_and_service']
        price = product['price']
        summary = product['summary']
        currency = product['currency']
        category = product['category']
        price = price.replace(',', '')
        price = price.replace('₹', '')
        price = price.replace('INR', '')
        price = price.replace('Rs', '')
        price = price.replace(' ', '')

        if price != '' and not check_the_string_for_non_numeric_characters(price):
            # Get the price from the product
            prompt = f"""
            Get the average price of the product or service in Indian Rupees.
            Product name: {name}
            Product summary: {summary}
            Product category: {category}
            """
            response = create_and_run_perp_pipeline(
                PROMPT_GET_AVERAGE_PRICE,
                average_price_parser,
                prompt,
            )
            print("Response from LLM: ", response)
            average_price = response['price']

            average_price = average_price.replace(',', '')
            average_price = average_price.replace('₹', '')
            average_price = average_price.replace('INR', '')
            average_price = average_price.replace('Rs', '')
            average_price = average_price.replace(' ', '')

            if average_price != '' and not check_the_string_for_non_numeric_characters(average_price):
                price = float(price)
                average_price = float(average_price)

                if price < average_price:
                    lower_than_market_prices = True
                elif price > average_price:
                    higher_than_market_prices = True

                # if the price difference is more than 30% then it is unrealistic
                if abs(price - average_price) / average_price > 0.3:
                    unrealistic_prices = True
    
    prices_redflags = RedFlagsPrices(
        no_prices_mentioned=no_prices_mentioned,
        unrealistic_prices=unrealistic_prices,
        lower_than_market_prices=lower_than_market_prices,
        higher_than_market_prices=higher_than_market_prices,
        foreign_currency=foreign_currency,
        no_currency=no_currency_mentioned,
    )
    return prices_redflags

    
def make_digital_footprint(raw_data: AllDFPData) -> DigitalFootprint:
    """Create a Digital Footprint object from raw data."""

    # preserve the information that needs to be passed as it is
    domain_information = raw_data.domain

    # TODO: Website monitoring

    # Website content
    website_content = WebsiteContentProcessed(
        default_language=raw_data.content.default_language,
        languages=raw_data.content.languages,
        social_media=raw_data.content.social_media,
        contact_info=raw_data.content.contact_info,
        addresses=raw_data.content.addresses,
        keywords=raw_data.content.keywords,
    )

    # products and services and categories
    products_and_services = raw_data.products_and_services
    products_and_services_categories = raw_data.products_and_services_categories

    # reviews and ratings and news
    reviews = raw_data.reviews
    reviews_and_ratings = raw_data.reviews_and_ratings
    risk_and_news_analysis = raw_data.risk_and_news_analysis

    # policies
    policies = raw_data.policies

    # red flags
    domain_info = raw_data.domain

    domain_red_flags = make_red_flags_domain(domain_info)
    content_red_flags = make_content_red_flags(raw_data.content)
    policies_red_glags = make_policies_red_flags(policies)
    prices_red_flags = make_prices_red_flags(products_and_services)

    red_flags = DigitalFootprintRedFlags(
        domain=domain_red_flags,
        content=content_red_flags,
        policies=policies_red_glags,
        prices=prices_red_flags,
    )

    # create the digital footprint object
    digital_footprint = DigitalFootprint(
        domain_information=domain_information,
        website_content=website_content,
        products_and_services=products_and_services,
        products_and_services_categories=products_and_services_categories,
        reviews=reviews,
        reviews_and_ratings=reviews_and_ratings,
        risk_and_news_analysis=risk_and_news_analysis,
        policies=policies,
        red_flags=red_flags,
    )
    return digital_footprint