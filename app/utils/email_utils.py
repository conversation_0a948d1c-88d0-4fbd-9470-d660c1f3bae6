import smtplib
from email.mime.text import MIMEText
import os

def send_email(to_email, subject, body, in_reply_to=None, references=None):
    from_email = os.getenv("ADMIN_EMAIL")
    password = os.getenv("ADMIN_EMAIL_PASSWORD")

    # Replace 'smtp.example.com' with your actual SMTP server address
    smtp_server = 'smtp.gmail.com'  # Example for Gmail
    smtp_port = 587  # Port for TLS

    msg = MIMEText(body)
    msg['Subject'] = subject
    msg['From'] = from_email
    msg['To'] = to_email
    if in_reply_to:
        msg['In-Reply-To'] = in_reply_to
    if references:
        msg['References'] = references

    with smtplib.SMTP(smtp_server, smtp_port) as server:
        server.starttls()
        server.login(from_email, password)
        server.sendmail(from_email, to_email, msg.as_string()) 

if __name__ == "__main__":
    send_email("<EMAIL>", "Test Email", "This is a test email")
