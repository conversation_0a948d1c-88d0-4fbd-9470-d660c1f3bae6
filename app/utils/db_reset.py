from sqlalchemy import text, inspect
from ..database import engine, SessionLocal, Base
import logging

logger = logging.getLogger(__name__)

def reset_database():
    try:
        # Create a connection
        with engine.connect() as connection:
            connection.execute(text("COMMIT"))
            
            # Drop schema with CASCADE
            connection.execute(text("DROP SCHEMA public CASCADE"))
            connection.execute(text("CREATE SCHEMA public"))
            
            # Set proper permissions
            connection.execute(text("GRANT ALL ON SCHEMA public TO postgres"))
            connection.execute(text("GRANT ALL ON SCHEMA public TO public"))
            
            connection.commit()
            
        # Recreate all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database reset completed successfully")
        
    except Exception as e:
        logger.error(f"Error resetting database: {str(e)}")
        raise