from typing import Dict, Any, List, Optional
from datetime import datetime
import json


def process_analysis_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process the analysis configuration JSON and extract relevant information
    for SQL generation.

    Args:
        config: The analysis configuration JSON

    Returns:
        Processed configuration with extracted SQL components
    """
    processed = {
        "analysis_id": config.get("analysisId"),
        "analysis_type": config.get("analysisType"),
        "table_name": config.get("dataTable", {}).get("selectedTable"),
        "column_mappings": config.get("configuration", {}).get("columnMappings", {}),
        "filters": config.get("filters", {}),
        "analysis_details": config.get("analysis", {}),
        "bucketing": config.get("analysis", {}).get("bucketing", {})
    }

    return processed


def generate_filter_conditions(filters: Dict[str, Any], column_mappings: Dict[str, Any]) -> str:
    """
    Generate WHERE clause conditions from filters configuration.

    Args:
        filters: Filter configuration from the JSON
        column_mappings: Column mapping configuration

    Returns:
        SQL WHERE clause string
    """
    conditions = []

    # Handle timeframe filter
    timeframe = filters.get("timeframeFilter", {})
    if timeframe.get("startDate") and timeframe.get("endDate"):
        datetime_col = column_mappings.get("datetime", {}).get("mappedTo", "txn_datetime")
        start_date = timeframe["startDate"]
        end_date = timeframe["endDate"]
        conditions.append(f"{datetime_col} >= '{start_date}' AND {datetime_col} <= '{end_date}'")

    # Handle population filters
    population_filters = filters.get("populationFilters", [])
    for pop_filter in population_filters:
        # Add population filter logic here if needed
        pass

    # Default conditions (can be customized)
    conditions.extend([
        "status = 'completed'",
        "channel IN ('online', 'store')",
        "txn_amt > 0"
    ])

    return " AND ".join(conditions) if conditions else "1=1"


def generate_bucket_case_statements(bucketing_config: Dict[str, Any]) -> List[str]:
    """
    Generate CASE statements for bucketing columns.

    Args:
        bucketing_config: Bucketing configuration from the JSON

    Returns:
        List of SQL CASE statements for bucketing
    """
    case_statements = []

    columns = bucketing_config.get("columns", [])
    for col_config in columns:
        column_name = col_config.get("column")
        bucketing = col_config.get("bucketing", {})
        buckets = bucketing.get("buckets", [])
        bucketing_type = bucketing.get("bucketingType", "auto")

        if not buckets:
            continue

        # Generate CASE statement for this column
        case_parts = ["CASE"]

        for i, bucket in enumerate(buckets):
            if bucket.get("type") == "auto" and "range" in bucket:
                range_info = bucket["range"]
                min_val = range_info.get("min")
                max_val = range_info.get("max")
                label = bucket.get("label", f"{min_val}-{max_val}")

                if min_val is not None and max_val is not None:
                    case_parts.append(f"    WHEN {column_name} >= {min_val} AND {column_name} <= {max_val} THEN '{chr(97+i)}.{label}'")
            elif "values" in bucket:
                values = bucket["values"]
                label = bucket.get("label", "custom")
                values_str = "', '".join(str(v) for v in values)
                case_parts.append(f"    WHEN {column_name} IN ('{values_str}') THEN '{chr(97+i)}.{label}'")

        case_parts.append("    ELSE NULL")
        case_parts.append(f"END AS {column_name}_bkt")

        case_statements.append("\n".join(case_parts))

    return case_statements


def generate_rule_conditions(rule_config: Dict[str, Any]) -> str:
    """
    Generate rule conditions from rule configuration.

    Args:
        rule_config: Rule configuration from the JSON

    Returns:
        SQL condition string for the rule
    """
    if not rule_config:
        return "1 = 1"  # Default rule that always matches

    equation = rule_config.get("equation", {})
    operator = equation.get("operator", "AND")
    conditions = equation.get("conditions", [])

    rule_conditions = []
    for condition in conditions:
        metric_name = condition.get("metric_name")
        operation = condition.get("operation")
        value = condition.get("value")

        if metric_name and operation and value is not None:
            rule_conditions.append(f"{metric_name} {operation} {value}")

    if rule_conditions:
        return f" {operator} ".join(rule_conditions)

    return "1 = 1"


def generate_preliminary_sql(config: Dict[str, Any]) -> str:
    """
    Generate the preliminary data SQL query.

    Args:
        config: Processed analysis configuration

    Returns:
        SQL query string for preliminary data
    """
    table_name = config.get("table_name", "transaction_data_table")
    column_mappings = config.get("column_mappings", {})
    filters = config.get("filters", {})
    bucketing = config.get("bucketing", {})
    analysis_details = config.get("analysis_details", {})

    # Get mapped column names
    txn_id = column_mappings.get("rowUniqueId", {}).get("mappedTo", "txn_id")
    txn_amt = column_mappings.get("amount", {}).get("mappedTo", "txn_amt")
    mer_id = column_mappings.get("accountId", {}).get("mappedTo", "mer_id")
    fraud_label = column_mappings.get("fraudLabel", {}).get("mappedTo", "fraud_label")
    txn_datetime = column_mappings.get("datetime", {}).get("mappedTo", "txn_datetime")

    # Generate filter conditions
    where_conditions = generate_filter_conditions(filters, column_mappings)

    # Generate bucket case statements
    bucket_statements = generate_bucket_case_statements(bucketing)

    # Base columns
    select_columns = [
        txn_id,
        txn_amt,
        mer_id,
        fraud_label,
        txn_datetime,
        "city",
        "chargeback_amt_30d",
        "txn_amt_30d"
    ]

    # Add bucket columns
    if bucket_statements:
        select_columns.extend(bucket_statements)

    # Add calculated columns
    calculated_columns = [
        "chargeback_amt_30d / NULLIF(txn_amt_30d, 0) AS chargeback_amt_ratio_30d",
        "TO_CHAR(txn_datetime, 'YYYY-MM') AS txn_mth"
    ]
    select_columns.extend(calculated_columns)

    # Add rule condition if it's a rule analysis
    if config.get("analysis_type") == "rule":
        rule_config = analysis_details.get("rule", {})
        rule_condition = generate_rule_conditions(rule_config)
        rule_column = f"CASE WHEN {rule_condition} THEN 1 ELSE 0 END AS rule_flag"
        select_columns.append(rule_column)

    sql = f"""
WITH prelim_data AS (
    SELECT
        {',\n        '.join(select_columns)}
    FROM {table_name}
    WHERE {where_conditions}
)
SELECT * FROM prelim_data
"""

    return sql.strip()


def generate_pivot_sql(config: Dict[str, Any], analysis_type: str) -> str:
    """
    Generate the pivot table SQL query for metric or rule performance.

    Args:
        config: Processed analysis configuration
        analysis_type: 'metric' or 'rule'

    Returns:
        SQL query string for pivot table
    """
    column_mappings = config.get("column_mappings", {})
    bucketing = config.get("bucketing", {})

    # Get mapped column names
    txn_id = column_mappings.get("rowUniqueId", {}).get("mappedTo", "txn_id")
    txn_amt = column_mappings.get("amount", {}).get("mappedTo", "txn_amt")
    mer_id = column_mappings.get("accountId", {}).get("mappedTo", "mer_id")
    fraud_label = column_mappings.get("fraudLabel", {}).get("mappedTo", "fraud_label")

    # Get bucketing columns for GROUP BY
    bucket_columns = []
    columns = bucketing.get("columns", [])
    for col_config in columns:
        column_name = col_config.get("column")
        if column_name:
            bucket_columns.append(f"{column_name}_bkt")

    # Add standard grouping columns
    group_by_columns = ["txn_mth"] + bucket_columns

    if analysis_type == "rule":
        group_by_columns.append("rule_flag")

    # Generate aggregation columns
    agg_columns = [
        f"COUNT({txn_id}) AS txn_cnt",
        f"SUM({txn_amt}) AS txn_amt",
        f"COUNT(DISTINCT {mer_id}) AS mer_cnt",
        f"COUNT(CASE WHEN {fraud_label} = true THEN {txn_id} END) AS fraud_cnt",
        f"SUM(CASE WHEN {fraud_label} = true THEN {txn_amt} END) AS fraud_amt",
        f"COUNT(DISTINCT CASE WHEN {fraud_label} = true THEN {mer_id} END) AS fraud_mer",
        f"COUNT(CASE WHEN {fraud_label} = true THEN {txn_id} END) * 1.0 / NULLIF(COUNT({txn_id}), 0) AS fraud_cnt_rate",
        f"SUM(CASE WHEN {fraud_label} = true THEN {txn_amt} END) * 1.0 / NULLIF(SUM({txn_amt}), 0) AS fraud_amt_rate",
        f"COUNT(DISTINCT CASE WHEN {fraud_label} = true THEN {mer_id} END) * 1.0 / NULLIF(COUNT(DISTINCT {mer_id}), 0) AS fraud_mer_rate"
    ]

    sql = f"""
WITH prelim_data AS (
    -- This will be replaced with the preliminary SQL
    SELECT * FROM prelim_data_placeholder
),
{analysis_type}_performance_pivot_table AS (
    SELECT
        {',\n        '.join(group_by_columns + agg_columns)}
    FROM prelim_data
    GROUP BY {', '.join(group_by_columns)}
)
SELECT * FROM {analysis_type}_performance_pivot_table
ORDER BY {group_by_columns[0] if group_by_columns else 'txn_cnt'} DESC
"""

    return sql.strip()


def generate_combined_sql(config: Dict[str, Any]) -> Dict[str, str]:
    """
    Generate all SQL queries for the analysis.

    Args:
        config: Analysis configuration JSON

    Returns:
        Dictionary containing all generated SQL queries
    """
    processed_config = process_analysis_config(config)
    analysis_type = processed_config.get("analysis_type", "rule")

    # Generate preliminary SQL
    prelim_sql = generate_preliminary_sql(processed_config)

    # Generate pivot SQL template
    pivot_sql = generate_pivot_sql(processed_config, analysis_type)

    # Create properly formatted combined SQL
    # Extract the prelim_data CTE content
    prelim_cte_content = prelim_sql.split("WITH prelim_data AS (")[1].split("SELECT * FROM prelim_data")[0].strip()

    # Extract the pivot table part
    pivot_parts = pivot_sql.split("WITH prelim_data AS (")[1].split("),")
    pivot_table_part = "),".join(pivot_parts[1:])

    combined_sql = f"""WITH prelim_data AS (
{prelim_cte_content}),
{pivot_table_part}"""

    return {
        "preliminary_sql": prelim_sql,
        "pivot_sql": pivot_sql,
        "combined_sql": combined_sql
    }
