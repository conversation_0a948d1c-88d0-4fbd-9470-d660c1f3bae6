from pydantic import BaseModel
from typing import Dict, List, Optional, Tuple

class DFPDomain(BaseModel):
    domain: str
    server_ip_address: str
    server_ip_country: str
    server_ip_owner: str
    creation_dates: List[str]
    registrar: str
    owner_emails: List[str]
    admin_contact: str
    privacy_protection: bool
    functional: bool
    status_code: int
    response_time: float
    redirection: bool
    redirection_url: str
    ssl_certificates: Dict|None

class DFPContent(BaseModel):
    content: str
    placeholders: List[Tuple[str, int]]
    spelling_errors: List[Tuple[str, int]]
    languages: List[str]
    default_language: str
    social_media: List[Tuple[str, str]]
    contact_info: List[Tuple[str, str]]
    keywords: List[Tuple[str, int]]
    unsafe_content: List[str]
    fraud_content: List[str]
    impersonation: List[Dict]
    hidden_content: List[str]
    addresses: List[str]
    images: List[Tuple[str, int]]

class DFPReviews(BaseModel):
    sentiment_status: str
    reviews_summary: str
    reviews: List[Dict[str, str]]

class DFPReviewsAndRatings(BaseModel):
    sentiment_status: str
    reviews_summary: str
    reviews: List[Dict[str, str]]

class DFPProductAndServices(BaseModel):
    summary: str
    products_and_services: List[Dict[str, str]]

class DFPProductsAndServicesCategories(BaseModel):
    categories: List[Dict[str, str]]

class DFPRiskAndNewsAnalysis(BaseModel):
    summary: str
    sentiment: str
    incidents: List[Dict[str, str]]

class DFPURLPolicies(BaseModel):
    url: str
    functional: bool
    status_code: int
    redirection: bool
    redirection_url: str

class DFPContentPolicies(BaseModel):
    content: str
    keywords: List[Tuple[str, int]]
    placeholders: List[Tuple[str, int]]
    spelling_errors: List[Tuple[str, int]]
    languages: List[str]
    default_language: str
    unsafe_content: List[str]
    fraud_content: List[str]
    impersonation: List[Dict]
    hidden_content: List[str]
    important_policies: List[str]

class DFPPolicies(BaseModel):
    type: str
    url_analysis: List[DFPURLPolicies]
    content_analysis: List[DFPContentPolicies]
    all_important_policies: List[str]

class DFPAllPolicies(BaseModel):
    all_policies: Dict[str, DFPPolicies]

class AllDFPData(BaseModel):
    domain: DFPDomain
    content: DFPContent
    products_and_services: DFPProductAndServices
    products_and_services_categories: DFPProductsAndServicesCategories
    reviews: DFPReviews
    reviews_and_ratings: DFPReviewsAndRatings
    risk_and_news_analysis: DFPRiskAndNewsAnalysis
    policies: DFPAllPolicies

class DFPJobs(BaseModel):
    job_id: str
    job_status: str
    merchant_id: str
    job_error: Optional[str] = None
    job_created_at: str
    job_updated_at: str

class WebsiteMonitoring(BaseModel):
    timestamp: str
    status_code: int
    response_time_ms: float
    is_active: bool
    error: Optional[str] = None

class WebsiteContentProcessed(BaseModel):
    default_language: str
    languages: List[str]
    social_media: List[Tuple[str, str]]
    contact_info: List[Tuple[str, str]]
    addresses: List[str]
    keywords: List[Tuple[str, int]]

class RedFlagsDomain(BaseModel):
    not_functional: bool
    not_secure: bool
    redirection: bool
    redirection_url: str

class RedFlagsContent(BaseModel):
    no_content: bool
    spelling_errors: List[Tuple[str, int]]
    placeholders: List[Tuple[str, int]]
    unsafe_content: List[str]
    fraud_content: List[str]
    impersonation: List[Dict]
    hidden_content: List[str]
    language_not_indian: bool
    no_social_media: bool
    no_contact_info: bool
    no_addresses: bool
    emails_not_verified: bool
    same_images: bool

class RedFlagsPolicies(BaseModel):
    no_policies: bool
    no_important_policies: bool

class RedFlagsPrices(BaseModel):
    no_prices_mentioned: bool
    unrealistic_prices: bool
    lower_than_market_prices: bool
    higher_than_market_prices: bool
    foreign_currency: bool
    no_currency: bool
    
class RedFlagsReviews(BaseModel):
    no_reviews: bool
    no_ratings: bool
    negative_sentiment: bool
    mention_scam: bool
    mention_fraud: bool
    mention_fake: bool
    mention_phishing: bool
    mention_spam: bool
    mention_illegal: bool
    mention_raid: bool

class DigitalFootprintRedFlags(BaseModel):
    domain: RedFlagsDomain
    content: RedFlagsContent
    policies: RedFlagsPolicies
    prices: RedFlagsPrices  

class DigitalFootprint(BaseModel):
    domain_information: DFPDomain
    website_content: WebsiteContentProcessed
    products_and_services: DFPProductAndServices
    products_and_services_categories: DFPProductsAndServicesCategories
    reviews: DFPReviews
    reviews_and_ratings: DFPReviewsAndRatings
    risk_and_news_analysis: DFPRiskAndNewsAnalysis
    policies: DFPAllPolicies  # Need to be handled
    red_flags: DigitalFootprintRedFlags