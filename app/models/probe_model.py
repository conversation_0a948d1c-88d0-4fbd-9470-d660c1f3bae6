from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey, JSON, Date, Numeric, Text, UUID, DECIMAL, ARRAY, Enum, BigInteger, TIMESTAMP
from sqlalchemy.orm import relationship as sqlalchemy_relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from uuid import uuid4
from ..database import Base

class probe_merchant(Base):
    __tablename__ = 'probe_merchant'
    __table_args__ = {'schema': 'public'}

    merchant_id = Column(UUID(as_uuid=True), primary_key=True)
    cin = Column(Text)
    legal_name = Column(Text)
    efiling_status = Column(Text)
    incorporation_date = Column(Date)
    paid_up_capital = Column(Numeric)
    sum_of_charges = Column(Numeric)
    authorized_capital = Column(Numeric)
    active_compliance = Column(Text)
    registered_address = Column(JSONB)
    business_address = Column(JSONB)
    pan = Column(Text)
    website = Column(Text)
    classification = Column(Text)
    status = Column(Text)
    last_agm_date = Column(Date)
    last_filing_date = Column(Date)
    email = Column(Text)
    description = Column(Text)
    contact_email = Column(JSONB)
    contact_phone = Column(JSONB)
    # LEI fields merged from probe_lei
    lei_number = Column(Text)
    lei_status = Column(Text)
    lei_registration_date = Column(Date)
    lei_last_updated_date = Column(Date)
    lei_next_renewal_date = Column(Date)
    name_history = Column(JSONB)
    last_updated_date = Column(Date)

    # Add relationships
    authorized_signatories = sqlalchemy_relationship("probe_authorized_signatories", back_populates="merchant")
    shareholdings = sqlalchemy_relationship("probe_shareholdings", back_populates="merchant")
    shareholdings_summary = sqlalchemy_relationship("probe_shareholdings_summary", back_populates="merchant")
    legal_history = sqlalchemy_relationship("probe_legal_history", back_populates="merchant")
    credit_ratings = sqlalchemy_relationship("probe_credit_ratings", back_populates="merchant")
    defaulter_list = sqlalchemy_relationship("probe_defaulter_list", back_populates="merchant")
    director_shareholdings = sqlalchemy_relationship("probe_director_shareholdings", back_populates="merchant")
    bifr_history = sqlalchemy_relationship("probe_bifr_history", back_populates="merchant")
    cdr_history = sqlalchemy_relationship("probe_cdr_history", back_populates="merchant")
    financial_score = sqlalchemy_relationship("probe_financial_score", back_populates="merchant")
    financials = sqlalchemy_relationship("probe_financials", back_populates="merchant")
    gst_details = sqlalchemy_relationship("probe_gst_details", back_populates="merchant")
    related_party_transactions_company = sqlalchemy_relationship("probe_related_party_transactions_company", back_populates="merchant")
    related_party_transactions_llp = sqlalchemy_relationship("probe_related_party_transactions_llp", back_populates="merchant")
    related_party_transactions_individual = sqlalchemy_relationship("probe_related_party_transactions_individual", back_populates="merchant")
    related_party_transactions_others = sqlalchemy_relationship("probe_related_party_transactions_others", back_populates="merchant")
    director_network = sqlalchemy_relationship("probe_director_network", back_populates="merchant")
    securities_allotment = sqlalchemy_relationship("probe_securities_allotment", back_populates="merchant")
    charge_sequence = sqlalchemy_relationship("probe_charge_sequence", back_populates="merchant")
    epfo_establishments = sqlalchemy_relationship("probe_epfo_establishments", back_populates="merchant")
    credit_rating_rationale = sqlalchemy_relationship("probe_credit_rating_rationale", back_populates="merchant")
    unaccepted_rating = sqlalchemy_relationship("probe_unaccepted_rating", back_populates="merchant")
    struckoff248_details = sqlalchemy_relationship("probe_struckoff248_details", back_populates="merchant")
    financial_parameters = sqlalchemy_relationship("probe_financial_parameters", back_populates="merchant")
    industry_segments = sqlalchemy_relationship("probe_industry_segments", back_populates="merchant")
    principal_business_activities = sqlalchemy_relationship("probe_principal_business_activities", back_populates="merchant")
    legal_cases_receivable = sqlalchemy_relationship("probe_legal_cases_receivable", back_populates="merchant")
    legal_cases_payable = sqlalchemy_relationship("probe_legal_cases_payable", back_populates="merchant")
    msme_supplier_payment_delays = sqlalchemy_relationship("probe_msme_supplier_payment_delays", back_populates="merchant")
    shareholdings_more_than_five_percent = sqlalchemy_relationship("probe_shareholdings_more_than_five_percent", back_populates="merchant")
    holding_entities = sqlalchemy_relationship("probe_holding_entities", back_populates="merchant")
    subsidiary_entities = sqlalchemy_relationship("probe_subsidiary_entities", back_populates="merchant")
    associate_entities = sqlalchemy_relationship("probe_associate_entities", back_populates="merchant")
    joint_ventures = sqlalchemy_relationship("probe_joint_ventures", back_populates="merchant")
    peer_comparison = sqlalchemy_relationship("probe_peer_comparison", back_populates="merchant")
    open_charges = sqlalchemy_relationship("probe_open_charges", back_populates="merchant")
    open_charges_latest_event = sqlalchemy_relationship("probe_open_charges_latest_event", back_populates="merchant")
    auditor_additional = sqlalchemy_relationship("probe_auditor_additional", back_populates="merchant")
    disclosures_auditor_report = sqlalchemy_relationship("probe_disclosures_auditor_report", back_populates="merchant")
    disclosures_director_report = sqlalchemy_relationship("probe_disclosures_director_report", back_populates="merchant")
    metadata_log = sqlalchemy_relationship("probe_metadata_log", back_populates="merchant")
    nbfc_financials = sqlalchemy_relationship("probe_nbfc_financials", back_populates="merchant")

class probe_authorized_signatories(Base):
    __tablename__ = 'probe_authorized_signatories'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    pan = Column(Text)
    din = Column(Text)
    name = Column(Text)
    designation = Column(Text)
    din_status = Column(Text)
    gender = Column(Text)
    date_of_birth = Column(Date)
    age = Column(Integer)
    date_of_appointment = Column(Date)
    date_of_appointment_for_current_designation = Column(Date)
    date_of_cessation = Column(Date)
    nationality = Column(Text)
    address = Column(JSONB)
    association_history = Column(JSONB)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="authorized_signatories")

class probe_shareholdings(Base):
    __tablename__ = 'probe_shareholdings'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    shareholders = Column(Text)
    year = Column(Integer)
    category = Column(Text)
    financial_year = Column(Date)
    indian_held_no_of_shares = Column(BigInteger)
    indian_held_percentage_of_shares = Column(Numeric)
    nri_held_no_of_shares = Column(BigInteger)
    nri_held_percentage_of_shares = Column(Numeric)
    foreign_held_other_than_nri_no_of_shares = Column(BigInteger)
    foreign_held_other_than_nri_percentage_of_shares = Column(Numeric)
    central_government_held_no_of_shares = Column(BigInteger)
    central_government_held_percentage_of_shares = Column(Numeric)
    state_government_held_no_of_shares = Column(BigInteger)
    state_government_held_percentage_of_shares = Column(Numeric)
    government_company_held_no_shares = Column(Numeric)
    government_company_held_no_of_shares = Column(BigInteger)
    government_company_held_percentage_of_shares = Column(Numeric)
    insurance_company_held_no_of_shares = Column(BigInteger)
    insurance_company_held_percentage_of_shares = Column(Numeric)
    bank_held_no_of_shares = Column(BigInteger)
    bank_held_percentage_of_shares = Column(Numeric)
    financial_institutions_held_no_of_shares = Column(BigInteger)
    financial_institutions_held_percentage_of_shares = Column(Numeric)
    financial_institutions_investors_held_no_of_shares = Column(BigInteger)
    financial_institutions_investors_held_percentage_of_shares = Column(Numeric)
    mutual_funds_held_no_of_shares = Column(BigInteger)
    mutual_funds_held_percentage_of_shares = Column(Numeric)
    venture_capital_held_no_of_shares = Column(BigInteger)
    venture_capital_held_percentage_of_shares = Column(Numeric)
    body_corporate_held_no_of_shares = Column(BigInteger)
    body_corporate_held_percentage_of_shares = Column(Numeric)
    others_source = Column(Text)
    others_held_no_of_shares = Column(BigInteger)
    others_held_percentage_of_shares = Column(Numeric)
    total_no_of_shares = Column(BigInteger)
    total_percentage_of_shares = Column(Numeric)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="shareholdings")

class probe_shareholdings_summary(Base):
    __tablename__ = 'probe_shareholdings_summary'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    year = Column(Integer)
    financial_year = Column(Date)
    total_equity_shares = Column(BigInteger)
    total_preference_shares = Column(BigInteger)
    promoter = Column(BigInteger)
    public = Column(BigInteger)
    total = Column(BigInteger)
    meta_data = Column(JSONB)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="shareholdings_summary")

class probe_legal_history(Base):
    __tablename__ = 'probe_legal_history'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    petitioner = Column(Text)
    respondent = Column(Text)
    court = Column(Text)
    date = Column(Date)
    case_status = Column(Text)
    case_number = Column(Text)
    case_category = Column(Text)
    severity = Column(Text)
    case_type = Column(Text)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="legal_history")

class probe_credit_ratings(Base):
    __tablename__ = 'probe_credit_ratings'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    rating_date = Column(Date)
    rating_agency = Column(Text)
    rating = Column(Text)
    type_of_loan = Column(Text)
    currency = Column(Text)
    amount = Column(Numeric)
    rating_details = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="credit_ratings")

class probe_defaulter_list(Base):
    __tablename__ = 'probe_defaulter_list'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    date = Column(Date)
    agency = Column(Text)
    bank = Column(Text)
    amount = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="defaulter_list")

class probe_director_shareholdings(Base):
    __tablename__ = 'probe_director_shareholdings'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    year = Column(Integer)
    financial_year = Column(Date)
    din_pan = Column(Text)
    full_name = Column(Text)
    designation = Column(Text)
    date_of_cessation = Column(Date)
    no_of_shares = Column(BigInteger)
    percentage_holding = Column(Numeric)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="director_shareholdings")

class probe_bifr_history(Base):
    __tablename__ = 'probe_bifr_history'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    date = Column(Date)
    case_number = Column(Text)
    status = Column(Text)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="bifr_history")

class probe_cdr_history(Base):
    __tablename__ = 'probe_cdr_history'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    date = Column(Date)
    description = Column(Text)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="cdr_history")

class probe_financial_score(Base):
    __tablename__ = 'probe_financial_score'
    __table_args__ = {'schema': 'public'}

    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), primary_key=True)
    overall_financial_score = Column(Numeric)
    growth_score = Column(Numeric)
    profitability_score = Column(Numeric)
    liquidity_score = Column(Numeric)
    solvency_score = Column(Numeric)
    efficiency_score = Column(Numeric)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="financial_score")

class probe_financials(Base):
    __tablename__ = 'probe_financials'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    year = Column(Date)
    nature = Column(Text)
    stated_on = Column(Date)
    filing_type = Column(Text)
    filing_standard = Column(Text)

    # Balance Sheet: Assets
    tangible_assets = Column(Numeric)
    producing_properties = Column(Numeric)
    intangible_assets = Column(Numeric)
    preproducing_properties = Column(Numeric)
    tangible_assets_capital_work_in_progress = Column(Numeric)
    intangible_assets_under_development = Column(Numeric)
    noncurrent_investments = Column(Numeric)
    deferred_tax_assets_net = Column(Numeric)
    foreign_curr_monetary_item_trans_diff_asset_account = Column(Numeric)
    long_term_loans_and_advances = Column(Numeric)
    other_noncurrent_assets = Column(Numeric)
    current_investments = Column(Numeric)
    inventories = Column(Numeric)
    trade_receivables = Column(Numeric)
    cash_and_bank_balances = Column(Numeric)
    short_term_loans_and_advances = Column(Numeric)
    other_current_assets = Column(Numeric)
    given_assets_total = Column(Numeric)

    # Balance Sheet: Liabilities
    share_capital = Column(Numeric)
    reserves_and_surplus = Column(Numeric)
    money_received_against_share_warrants = Column(Numeric)
    share_application_money_pending_allotment = Column(Numeric)
    deferred_government_grants = Column(Numeric)
    minority_interest = Column(Numeric)
    long_term_borrowings = Column(Numeric)
    deferred_tax_liabilities_net = Column(Numeric)
    foreign_curr_monetary_item_trans_diff_liability_account = Column(Numeric)
    other_long_term_liabilities = Column(Numeric)
    long_term_provisions = Column(Numeric)
    short_term_borrowings = Column(Numeric)
    trade_payables = Column(Numeric)
    other_current_liabilities = Column(Numeric)
    short_term_provisions = Column(Numeric)
    given_liabilities_total = Column(Numeric)

    # Subtotals
    total_equity = Column(Numeric)
    total_current_liabilities = Column(Numeric)
    total_non_current_liabilities = Column(Numeric)
    net_fixed_assets = Column(Numeric)
    total_current_assets = Column(Numeric)
    capital_wip = Column(Numeric)
    total_debt = Column(Numeric)

    # Notes
    gross_fixed_assets = Column(Numeric)
    trade_receivable_exceeding_six_months = Column(Numeric)

    # Profit & Loss - Line Items
    net_revenue = Column(Numeric)
    total_cost_of_materials_consumed = Column(Numeric)
    total_purchases_of_stock_in_trade = Column(Numeric)
    total_changes_in_inventories_or_finished_goods = Column(Numeric)
    total_employee_benefit_expense = Column(Numeric)
    total_other_expenses = Column(Numeric)
    operating_profit = Column(Numeric)
    other_income = Column(Numeric)
    depreciation = Column(Numeric)
    profit_before_interest_and_tax = Column(Numeric)
    interest = Column(Numeric)
    profit_before_tax_and_exceptional_items_before_tax = Column(Numeric)
    exceptional_items_before_tax = Column(Numeric)
    profit_before_tax = Column(Numeric)
    income_tax = Column(Numeric)
    profit_for_period_from_continuing_operations = Column(Numeric)
    profit_from_discontinuing_operation_after_tax = Column(Numeric)
    minority_interest_and_profit_from_associates_and_joint_ventures = Column(Numeric)
    profit_after_tax = Column(Numeric)

    # P&L Subtotals
    total_operating_cost = Column(Numeric)

    # Revenue Breakup
    revenue_from_operations = Column(Numeric)
    revenue_from_interest = Column(Numeric)
    revenue_from_other_financial_services = Column(Numeric)
    revenue_from_sale_of_products = Column(Numeric)
    revenue_from_sale_of_services = Column(Numeric)
    other_operating_revenues = Column(Numeric)
    excise_duty = Column(Numeric)
    service_tax_collected = Column(Numeric)
    other_duties_taxes_collected = Column(Numeric)
    sale_of_goods_manufactured_domestic = Column(Numeric)
    sale_of_goods_traded_domestic = Column(Numeric)
    sale_or_supply_of_services_domestic = Column(Numeric)
    sale_or_supply_of_services_export = Column(Numeric)
    sale_of_goods_manufactured_export = Column(Numeric)
    sale_of_goods_traded_export = Column(Numeric)

    # Depreciation Breakup
    depreciation_amortisation = Column(Numeric)
    depletion = Column(Numeric)
    depreciation_and_amortization = Column(Numeric)

    # Cash Flow
    profit_before_tax_cf = Column(Numeric)
    adjustment_for_finance_cost_and_depreciation = Column(Numeric)
    adjustment_for_current_and_non_current_assets = Column(Numeric)
    adjustment_for_current_and_non_current_liabilities = Column(Numeric)
    other_adjustments_in_operating_activities = Column(Numeric)
    cash_flows_from_used_in_operating_activities = Column(Numeric)
    cash_outflow_from_purchase_of_assets = Column(Numeric)
    cash_inflow_from_sale_of_assets = Column(Numeric)
    income_from_assets = Column(Numeric)
    other_adjustments_in_investing_activities = Column(Numeric)
    cash_flows_from_used_in_investing_activities = Column(Numeric)
    cash_outflow_from_repayment_of_capital_and_borrowings = Column(Numeric)
    cash_inflow_from_raisng_capital_and_borrowings = Column(Numeric)
    interest_and_dividends_paid = Column(Numeric)
    other_adjustments_in_financing_activities = Column(Numeric)
    cash_flows_from_used_in_financing_activities = Column(Numeric)
    incr_decr_in_cash_cash_equv_before_effect_of_excg_rate_changes = Column(Numeric)
    adjustments_to_cash_and_cash_equivalents = Column(Numeric)
    incr_decr_in_cash_cash_equv = Column(Numeric)
    cash_flow_statement_at_end_of_period = Column(Numeric)

    # PnL Key Schedule
    managerial_remuneration = Column(Numeric)
    payment_to_auditors = Column(Numeric)
    insurance_expenses = Column(Numeric)
    power_and_fuel = Column(Numeric)

    # Auditor
    auditor_name = Column(Text)
    auditor_firm_name = Column(Text)
    pan = Column(Text)
    membership_number = Column(Text)
    firm_registration_number = Column(Text)
    auditor_address = Column(Text)

    # Auditor Comments
    report_has_adverse_remarks = Column(Text)
    auditor_comments = Column(JSONB)
    auditor_additional = Column(JSONB)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="financials")

class probe_gst_details(Base):
    __tablename__ = 'probe_gst_details'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    gstin = Column(Text)
    status = Column(Text)
    company_name = Column(Text)
    trade_name = Column(Text)
    state = Column(Text)
    state_jurisdiction = Column(Text)
    centre_jurisdiction = Column(Text)
    date_of_registration = Column(Text)
    taxpayer_type = Column(Text)
    nature_of_business_activities = Column(Text)
    filings=Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="gst_details")

class probe_related_party_transactions_company(Base):
    __tablename__ = 'probe_related_party_transactions_company'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    financial_year = Column(Date)
    name = Column(Text)
    relationship = Column(Text)
    legal_name = Column(Text)
    type_of_transaction = Column(Text)
    amount = Column(Numeric)
    cin = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="related_party_transactions_company")

class probe_related_party_transactions_llp(Base):
    __tablename__ = 'probe_related_party_transactions_llp'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    financial_year = Column(Date)
    name = Column(Text)
    relationship = Column(Text)
    legal_name = Column(Text)
    type_of_transaction = Column(Text)
    amount = Column(Numeric)
    llpin = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="related_party_transactions_llp")

class probe_related_party_transactions_individual(Base):
    __tablename__ = 'probe_related_party_transactions_individual'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    financial_year = Column(Date)
    name = Column(Text)
    relationship = Column(Text)
    legal_name = Column(Text)
    type_of_transaction = Column(Text)
    amount = Column(Text)
    din = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="related_party_transactions_individual")

class probe_related_party_transactions_others(Base):
    __tablename__ = 'probe_related_party_transactions_others'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    financial_year = Column(Date)
    name = Column(Text)
    relationship = Column(Text)
    legal_name = Column(Text)
    type_of_transaction = Column(Text)
    amount = Column(Numeric)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="related_party_transactions_others")

class probe_director_network(Base):
    __tablename__ = 'probe_director_network'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    name = Column(Text)
    pan = Column(Text)
    din = Column(Text)
    companies = Column(JSONB)
    llps = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="director_network")

class probe_securities_allotment(Base):
    __tablename__ = 'probe_securities_allotment'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    allotment_type = Column(Text)
    allotment_date = Column(Date)
    instrument = Column(Text)
    total_amount_raised = Column(Numeric)
    number_of_securities_allotted = Column(BigInteger)
    nominal_amount_per_security = Column(Numeric)
    premium_amount_per_security = Column(Numeric)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="securities_allotment")

class probe_charge_sequence(Base):
    __tablename__ = 'probe_charge_sequence'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    charge_id = Column(Integer)
    status = Column(Text)
    date = Column(Date)
    amount = Column(Numeric)
    holder_name = Column(Text)
    number_of_holder = Column(Text)
    property_type = Column(Text)
    filing_date = Column(Date)
    property_particulars = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="charge_sequence")

class probe_epfo_establishments(Base):
    __tablename__ = 'probe_epfo_establishments'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    establishment_id = Column(Text)
    address = Column(Text)
    city = Column(Text)
    latest_date_of_credit = Column(Text)
    date_of_setup = Column(Text)
    establishment_name = Column(Text)
    exemption_status_edli = Column(Text)
    exemption_status_pension = Column(Text)
    exemption_status_pf = Column(Text)
    no_of_employees = Column(Integer)
    principal_business_activities = Column(Text)
    amount = Column(Numeric)
    latest_wage_month = Column(Text)
    working_status = Column(Text)
    filing_details = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="epfo_establishments")

class probe_credit_rating_rationale(Base):
    __tablename__ = 'probe_credit_rating_rationale'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    rating_agency = Column(Text)
    financial_year = Column(Date)
    doc_id = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="credit_rating_rationale")

class probe_unaccepted_rating(Base):
    __tablename__ = 'probe_unaccepted_rating'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    rating_agency = Column(Text)
    rating_details = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="unaccepted_rating")

class probe_struckoff248_details(Base):
    __tablename__ = 'probe_struckoff248_details'
    __table_args__ = {'schema': 'public'}

    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), primary_key=True)
    struck_off_status = Column(Text)
    restored_status = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="struckoff248_details")

class probe_financial_parameters(Base):
    __tablename__ = 'probe_financial_parameters'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    year = Column(Text)
    nature = Column(Text)
    earning_fc = Column(Numeric)
    expenditure_fc = Column(Numeric)
    transaction_related_parties_as_18 = Column(Numeric)
    employee_benefit_expense = Column(Numeric)
    number_of_employees = Column(Integer)
    prescribed_csr_expenditure = Column(Numeric)
    total_amount_csr_spent_for_financial_year = Column(Numeric)
    gross_fixed_assets = Column(Numeric)
    trade_receivable_exceeding_six_months = Column(Numeric)
    proposed_dividend = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="financial_parameters")

class probe_industry_segments(Base):
    __tablename__ = 'probe_industry_segments'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    industry = Column(Text)
    segments = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="industry_segments")

class probe_principal_business_activities(Base):
    __tablename__ = 'probe_principal_business_activities'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    year = Column(Integer)
    main_activity_group_code = Column(Text)
    main_activity_group_description = Column(Text)
    business_activity_code = Column(Text)
    business_activity_description = Column(Text)
    percentage_of_turnover = Column(Numeric)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="principal_business_activities")

class probe_legal_cases_receivable(Base):
    __tablename__ = 'probe_legal_cases_receivable'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    type_of_financial_dispute = Column(Text)
    currency = Column(Text)
    amount_under_default = Column(Numeric)
    verdict = Column(Text)
    court = Column(Text)
    litigant = Column(Text)
    case_no = Column(Text)
    amount = Column(Numeric)
    default_date = Column(Date)
    date_of_judgement = Column(Date)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="legal_cases_receivable")

class probe_legal_cases_payable(Base):
    __tablename__ = 'probe_legal_cases_payable'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    type_of_financial_dispute = Column(Text)
    currency = Column(Text)
    amount_under_default = Column(Numeric)
    verdict = Column(Text)
    court = Column(Text)
    litigant = Column(Text)
    case_no = Column(Text)
    amount = Column(Numeric)
    default_date = Column(Date)
    date_of_judgement = Column(Date)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="legal_cases_payable")

class probe_msme_supplier_payment_delays(Base):
    __tablename__ = 'probe_msme_supplier_payment_delays'
    __table_args__ = {'schema': 'public'}

    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), primary_key=True)
    trend = Column(JSONB)
    delays_for_period = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="msme_supplier_payment_delays")

class probe_shareholdings_more_than_five_percent(Base):
    __tablename__ = 'probe_shareholdings_more_than_five_percent'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    financial_year = Column(Date)
    company = Column(JSONB)
    llp = Column(JSONB)
    individual = Column(JSONB)
    others = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="shareholdings_more_than_five_percent")

class probe_holding_entities(Base):
    __tablename__ = 'probe_holding_entities'
    __table_args__ = {'schema': 'public'}

    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), primary_key=True)
    financial_year = Column(Text)
    company = Column(JSONB)
    llp = Column(JSONB)
    others = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="holding_entities")

class probe_subsidiary_entities(Base):
    __tablename__ = 'probe_subsidiary_entities'
    __table_args__ = {'schema': 'public'}

    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), primary_key=True)
    financial_year = Column(Text)
    company = Column(JSONB)
    llp = Column(JSONB)
    others = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="subsidiary_entities")

class probe_associate_entities(Base):
    __tablename__ = 'probe_associate_entities'
    __table_args__ = {'schema': 'public'}

    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), primary_key=True)
    financial_year = Column(Text)
    companies = Column(JSONB)
    llps = Column(JSONB)
    others = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="associate_entities")

class probe_joint_ventures(Base):
    __tablename__ = 'probe_joint_ventures'
    __table_args__ = {'schema': 'public'}

    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), primary_key=True)
    financial_year = Column(Text)
    companies = Column(JSONB)
    llps = Column(JSONB)
    others = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="joint_ventures")

class probe_peer_comparison(Base):
    __tablename__ = 'probe_peer_comparison'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    bizIndustry = Column(Text)
    bizSegment = Column(Text)
    refYear = Column(Text)
    peers = Column(JSONB)
    benchMarks = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="peer_comparison")

class probe_open_charges(Base):
    __tablename__ = 'probe_open_charges'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    charge_id = Column(Integer)
    date = Column(Date)
    holder_name = Column(Text)
    amount = Column(Numeric)
    type = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="open_charges")

class probe_open_charges_latest_event(Base):
    __tablename__ = 'probe_open_charges_latest_event'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    charge_id = Column(Integer)
    date = Column(Date)
    holder_name = Column(Text)
    amount = Column(Numeric)
    type = Column(Text)
    filing_date = Column(Text)
    property_type = Column(Text)
    number_of_chargeholder = Column(Text)
    instrument_description = Column(Text)
    rate_of_interest = Column(Text)
    terms_of_payment = Column(Text)
    property_particulars = Column(Text)
    extent_and_operation = Column(Text)
    other_terms = Column(Text)
    modification_particulars = Column(Text)
    joint_holding = Column(Text)
    consortium_holding = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="open_charges_latest_event")

class probe_auditor_additional(Base):
    __tablename__ = 'probe_auditor_additional'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    year = Column(Date)
    auditor_name = Column(Text)
    auditor_firm_name = Column(Text)
    pan = Column(Text)
    membership_number = Column(Text)
    firm_registration_number = Column(Text)
    address = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="auditor_additional")

class probe_disclosures_auditor_report(Base):
    __tablename__ = 'probe_disclosures_auditor_report'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    year = Column(Date)
    auditor = Column(Text)
    footnotes = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="disclosures_auditor_report")

class probe_disclosures_director_report(Base):
    __tablename__ = 'probe_disclosures_director_report'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'), nullable=False)
    year = Column(Date)
    auditor = Column(Text)
    director = Column(Text)
    footnotes = Column(Text)
    created_at = Column(TIMESTAMP, default=datetime.now())
    updated_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="disclosures_director_report")

class probe_metadata_log(Base):
    __tablename__ = 'probe_metadata_log'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    api_version = Column(Text)
    last_updated = Column(Date)
    raw_payload = Column(JSONB)
    created_at = Column(TIMESTAMP, default=datetime.now())

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="metadata_log")

class probe_nbfc_financials(Base):
    __tablename__ = 'probe_nbfc_financials'
    __table_args__ = {'schema': 'public'}

    id = Column(Integer, primary_key=True, autoincrement=True)
    merchant_id = Column(UUID(as_uuid=True), ForeignKey('public.probe_merchant.merchant_id'))
    year = Column(Date)
    nature = Column(Text)
    stated_on = Column(Date)
    filing_type = Column(Text)
    filing_standard = Column(Text)

    # Balance Sheet: Assets
    cash_equivalents = Column(Numeric)
    bank_balance_other_than_cash = Column(Numeric)
    derivative_financial_assets = Column(Numeric)
    trade_receivables = Column(Numeric)
    other_receivables = Column(Numeric)
    loans = Column(Numeric)
    investments = Column(Numeric)
    other_financial_assets = Column(Numeric)
    inventories = Column(Numeric)
    current_tax_assets_net = Column(Numeric)
    deferred_tax_assets_net = Column(Numeric)
    investment_property = Column(Numeric)
    biological_assets = Column(Numeric)
    property_plant_and_equipment = Column(Numeric)
    capital_work_in_progress = Column(Numeric)
    intangible_under_development = Column(Numeric)
    goodwill = Column(Numeric)
    other_intangibles = Column(Numeric)
    other_non_financial_assets = Column(Numeric)
    given_assets_total = Column(Numeric)

    # Balance Sheet: Liabilities
    derivative_financial_instruments = Column(Numeric)
    dues_of_micro_and_small_enterprises_TP = Column(Numeric)
    dues_of_creditors_TP = Column(Numeric)
    dues_of_micro_and_small_enterprises_OP = Column(Numeric)
    dues_of_creditors_OP = Column(Numeric)
    debt_securities = Column(Numeric)
    deposits = Column(Numeric)
    borrowings_other_than_debt_securities = Column(Numeric)
    provisions = Column(Numeric)
    other_non_financial_liabilities = Column(Numeric)
    equity_share_capital = Column(Numeric)
    sh_app_money_pending_allotment = Column(Numeric)
    eq_comp_of_compound_fin_instruments = Column(Numeric)
    statutory_reserves = Column(Numeric)
    capital_reserves = Column(Numeric)
    securities_premium = Column(Numeric)
    current_tax_liabilities_net = Column(Numeric)
    subordinated_liabilities = Column(Numeric)
    other_financial_liabilities = Column(Numeric)
    other_reserves = Column(Numeric)
    deferred_tax_liabilites_net = Column(Numeric)
    retained_earnings = Column(Numeric)
    debt_through_other_comprehensive_income = Column(Numeric)
    equity_through_other_comprehensive_income = Column(Numeric)
    effective_portion_of_cash_flow_hedges = Column(Numeric)
    exchange_diff_translating_of_foreign_operation = Column(Numeric)
    revaluation_surplus = Column(Numeric)
    other_items_of_other_comprehensive_income = Column(Numeric)
    money_received_against_share_warrants = Column(Numeric)
    non_controlling_interest = Column(Numeric)
    given_other_equity = Column(Numeric)
    given_liabilities_total = Column(Numeric)

    # Subtotals
    total_equity = Column(Numeric)
    total_non_financial_assets = Column(Numeric)
    total_financial_assets = Column(Numeric)
    total_assets = Column(Numeric)
    total_financial_liabilities = Column(Numeric)
    total_non_financial_liabilities = Column(Numeric)
    total_equity_and_liabilities = Column(Numeric)

    # Notes
    gross_fixed_assets = Column(Numeric)
    trade_receivable_exceeding_six_months = Column(Numeric)

    # PnL Line Items
    total_interest_income = Column(Numeric)
    total_dividend_income = Column(Numeric)
    total_other_operating_income = Column(Numeric)
    revenue = Column(Numeric)
    other_income = Column(Numeric)
    total_income = Column(Numeric)
    interest = Column(Numeric)
    total_cost_of_materials_consumed = Column(Numeric)
    total_purchases_of_stock_in_trade = Column(Numeric)
    total_changes_in_inventories_or_finished_goods = Column(Numeric)
    total_employee_benefit_expense = Column(Numeric)
    depreciation = Column(Numeric)
    total_other_expenses = Column(Numeric)
    total_expenses = Column(Numeric)
    profit_before_tax_and_exceptional_items_before_tax = Column(Numeric)
    total_exceptional_items = Column(Numeric)
    profit_before_tax = Column(Numeric)
    income_tax = Column(Numeric)
    profit_Loss_for_the_period_from_continuing_operations = Column(Numeric)
    profit_Loss_from_discontinued_operations_after_tax = Column(Numeric)
    minority_interest_and_profit_from_associates_and_joint_ventures = Column(Numeric)
    profit_after_tax = Column(Numeric)

    # Revenue Breakup
    interest_income = Column(Numeric)
    dividend_income = Column(Numeric)
    rental_income = Column(Numeric)
    fees_and_commission_income = Column(Numeric)
    net_gain_on_fair_value_changes = Column(Numeric)
    net_gain_on_derecoginition_of_fin_instruments = Column(Numeric)
    sale_of_products = Column(Numeric)
    sale_of_services = Column(Numeric)
    other = Column(Numeric)

    # Depreciation Breakup
    depreciation_and_amortization_and_impairment = Column(Numeric)

    # Cash Flow
    profit_before_tax_cf = Column(Numeric)
    adjustment_for_finance_cost_and_depreciation = Column(Numeric)
    adjustment_for_current_and_non_current_assets = Column(Numeric)
    adjustment_for_current_and_non_current_liabilities = Column(Numeric)
    other_adjustments_in_operating_activities = Column(Numeric)
    cash_flows_from_used_in_operating_activities = Column(Numeric)
    cash_outflow_from_purchase_of_assets = Column(Numeric)
    cash_inflow_from_sale_of_assets = Column(Numeric)
    income_from_assets = Column(Numeric)
    other_adjustments_in_investing_activities = Column(Numeric)
    cash_flows_from_used_in_investing_activities = Column(Numeric)
    cash_outflow_from_repayment_of_capital_and_borrowings = Column(Numeric)
    cash_inflow_from_raisng_capital_and_borrowings = Column(Numeric)
    interest_and_dividends_paid = Column(Numeric)
    other_adjustments_in_financing_activities = Column(Numeric)
    cash_flows_from_used_in_financing_activities = Column(Numeric)
    incr_decr_in_cash_cash_equv_before_effect_of_excg_rate_changes = Column(Numeric)
    adjustments_to_cash_and_cash_equivalents = Column(Numeric)
    incr_decr_in_cash_cash_equv = Column(Numeric)
    cash_flow_statement_at_end_of_period = Column(Numeric)

    # PnL Key Schedule
    managerial_remuneration = Column(Numeric)
    payment_to_auditors = Column(Numeric)
    insurance_expenses = Column(Numeric)
    power_and_fuel = Column(Numeric)

    # Auditor
    auditor_name = Column(Text)
    auditor_firm_name = Column(Text)
    pan = Column(Text)
    membership_number = Column(Text)
    firm_registration_number = Column(Text)
    auditor_address = Column(Text)

    # Auditor Comments
    report_has_adverse_remarks = Column(Text)

    # Add relationship with probe_merchant
    merchant = sqlalchemy_relationship("probe_merchant", back_populates="nbfc_financials")