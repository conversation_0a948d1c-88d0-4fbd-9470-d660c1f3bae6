from pydantic import BaseModel, validator
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from decimal import Decimal
from uuid import UUID

class TransactionResponse(BaseModel):
    id: str
    type: str
    merchantType: str
    city: str
    countryCode: str
    amount: Decimal
    transactionId: str
    merchantName: str
    riskScore: int
    riskDescription: Optional[str] = None
    productName: Optional[str] = None
    timestamp: str
    status: str
    disputeDate: Optional[str]  # Add this field
    complainDate: Optional[str]  

    class Config:
        json_encoders = {
            Decimal: lambda v: f"{v:,.2f}"
        }

    class Config:
        from_attributes = True

class TransactionListResponse(BaseModel):
    transactions: List[TransactionResponse]

class PaymentChannelResponse(BaseModel):
    id: str
    type: str
    name: str
    status: str
    added_on: str
    details: Dict[str, Any]

    class Config:
        from_attributes = True

class PaymentChannelListResponse(BaseModel):
    channels: List[PaymentChannelResponse]

class PayoutResponse(BaseModel):
    id: str
    amount: Decimal
    status: str
    bank_account: str
    utr: str
    timestamp: str

    class Config:
        json_encoders = {
            Decimal: lambda v: f"{v:,.0f}"
        }

class PayoutListResponse(BaseModel):
    payouts: List[PayoutResponse]

class CommunicationResponse(BaseModel):
    id: UUID
    type: str
    subject: str
    content: str
    timestamp: datetime
    status: str

    class Config:
        from_attributes = True

class CommunicationListResponse(BaseModel):
    communications: List[CommunicationResponse]

class TimelineEventResponse(BaseModel):
    id: UUID
    time: datetime
    event: str
    type: str

    class Config:
        from_attributes = True

class TimelineEventListResponse(BaseModel):
    events: List[TimelineEventResponse]

class InvestigationResponse(BaseModel):
    id: str
    title: str
    status: str
    priority: str
    assignee: str
    last_updated: str

    class Config:
        from_attributes = True

class InvestigationListResponse(BaseModel):
    investigations: List[InvestigationResponse]

class InvestigationNoteResponse(BaseModel):
    id: str
    case_number: str
    title: str
    description: str
    timestamp: str
    created_at: Optional[str] = None
    created_by: Optional[str] = None

class InvestigationNoteListResponse(BaseModel):
    notes: List[InvestigationNoteResponse]

class KeyMetricsResponse(BaseModel):
    date_of_onboarding: date
    average_daily_transactions: float
    average_payout_size: float