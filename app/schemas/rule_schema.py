from typing import List, Union, Dict, Any, Optional
from pydantic import BaseModel, Field, validator, model_validator
from datetime import datetime
from uuid import UUID
from sqlalchemy import inspect
from ..database import engine

def get_valid_tables():
    inspector = inspect(engine)
    tables = inspector.get_table_names(schema='public')
    return tables

def get_table_columns(table_name: str):
    inspector = inspect(engine)
    columns = inspector.get_columns(table_name, schema='public')
    return [col['name'] for col in columns]

def is_metric_table(table_name: str) -> bool:
    return any(table_name.endswith(suffix) for suffix in ['_metrics', '_metric_value_numeric', '_metric_value_string', '_metric_value_boolean'])

def validate_rule_condition(rule_dict: dict) -> bool:
    try:
        # Validate table
        if rule_dict['table'] not in get_valid_tables():
            print(f"Invalid table: {rule_dict['table']}")
            return False
            
        # Validate operator
        valid_operators = ['>', '<', '>=', '<=', '==', '!=', 'in', 'not in']
        if rule_dict['operator'] not in valid_operators:
            print(f"Invalid operator: {rule_dict['operator']}")
            return False
            
        # Validate value based on operator
        operator = rule_dict['operator']
        value = rule_dict['value']
        if operator in ['in', 'not in']:
            if not isinstance(value, list):
                print(f"Value must be a list when using {operator} operator")
                return False
        elif operator in ['>', '<', '>=', '<=']:
            if not isinstance(value, (int, float)):
                print(f"Value must be a number when using {operator} operator")
                return False
        elif operator in ['==', '!=']:
            if not isinstance(value, (int, float, bool, str)):
                print(f"Value must be a number, boolean, or string when using {operator} operator")
                return False
                
        # Validate condition field if not a metric table
        if not is_metric_table(rule_dict['table']):
            valid_columns = get_table_columns(rule_dict['table'])
            field_name = rule_dict['condition'].split()[0]
            if field_name not in valid_columns:
                print(f"Invalid field {field_name} in table {rule_dict['table']}")
                return False
                
        return True
        
    except Exception as e:
        print(f"Error validating rule: {str(e)}")
        return False

class RuleCondition(BaseModel):
    table: str
    condition: str
    operator: str
    value: Any

    @validator('operator')
    def validate_operator(cls, v):
        valid_operators = ['>', '<', '>=', '<=', '==', '!=', 'in', 'not in']
        if v not in valid_operators:
            raise ValueError(f'Invalid operator. Must be one of {valid_operators}')
        return v

    @validator('table')
    def validate_table(cls, v):
        valid_tables = get_valid_tables()
        if v not in valid_tables:
            raise ValueError(f'Invalid table. Must be one of {valid_tables}')
        return v

    @validator('condition')
    def validate_condition(cls, v, values):
        if 'table' not in values:
            return v
            
        table = values['table']
        
        # Skip column validation for metric tables
        if is_metric_table(table):
            return v
            
        valid_columns = get_table_columns(table)
        
        # Split condition to get field name (assuming format: "field_name operator value")
        field_name = v.split()[0]
        
        if field_name not in valid_columns:
            raise ValueError(f'Invalid field "{field_name}" in table "{table}". Must be one of {valid_columns}')
        return v

    @validator('value')
    def validate_value(cls, v, values):
        if 'operator' not in values:
            return v
            
        operator = values['operator']
        if operator in ['in', 'not in']:
            if not isinstance(v, list):
                raise ValueError(f'Value must be a list when using {operator} operator')
        elif operator in ['>', '<', '>=', '<=']:
            if not isinstance(v, (int, float)):
                raise ValueError(f'Value must be a number when using {operator} operator')
        elif operator in ['==', '!=']:
            if not isinstance(v, (int, float, bool, str)):
                raise ValueError(f'Value must be a number, boolean, or string when using {operator} operator')
        return v

class RuleGroup(BaseModel):
    and_conditions: Optional[List[Union[RuleCondition, 'RuleGroup']]] = Field(None, alias='and')
    or_conditions: Optional[List[Union[RuleCondition, 'RuleGroup']]] = Field(None, alias='or')

    @model_validator(mode='after')
    def validate_group(self):
        and_group = self.and_conditions
        or_group = self.or_conditions
        
        # Allow both to be None for direct conditions
        if and_group is None and or_group is None:
            return self
        
        if and_group is not None and or_group is not None:
            raise ValueError('Rule group cannot have both "and" and "or" operators')
        
        if and_group is not None and len(and_group) < 1:
            raise ValueError('AND group must contain at least one condition')
            
        if or_group is not None and len(or_group) < 1:
            raise ValueError('OR group must contain at least one condition')
            
        return self

    @validator('and_conditions', 'or_conditions', check_fields=False)
    def validate_nested_groups(cls, v):
        if v is None:
            return v
            
        for item in v:
            if isinstance(item, dict):
                if 'and' in item or 'or' in item:
                    # Validate nested group
                    RuleGroup(**item)
                else:
                    # Validate condition
                    RuleCondition(**item)
        return v

    class Config:
        allow_population_by_field_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class RuleSchema(BaseModel):
    code: str = Field(..., min_length=1, max_length=100)
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    type: str = Field(..., min_length=1, max_length=50)
    severity: str = Field(..., min_length=1, max_length=50)
    fraud_type: str = Field(..., min_length=1, max_length=50)
    rule: RuleGroup

    @validator('rule')
    def validate_rule_structure(cls, v):
        if not v:
            raise ValueError('Rule cannot be empty')
        return v

class RuleUpdateSchema(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    status: Optional[bool] = None
    type: Optional[str] = Field(None, min_length=1, max_length=50)
    severity: Optional[str] = Field(None, min_length=1, max_length=50)
    fraud_type: Optional[str] = Field(None, min_length=1, max_length=50)
    rule: Optional[RuleGroup] = None
    is_active: Optional[bool] = None

class RuleResponse(BaseModel):
    id: UUID
    code: str
    name: str
    description: Optional[str]
    status: bool
    type: str
    severity: str
    fraud_type: str
    rule: Dict[str, Any]
    version: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
    updated_by: Optional[str]

    class Config:
        orm_mode = True 