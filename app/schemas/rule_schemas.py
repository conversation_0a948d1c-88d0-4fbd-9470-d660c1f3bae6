from pydantic import BaseModel, Field
from typing import List, Optional
from uuid import UUID
from datetime import datetime

class RuleCreate(BaseModel):
    rule_code: str
    rule_name: str
    rule_description: str
    rule_status: bool
    rule_type: str
    rule_severity: str
    fraud_type: str



class RuleListCreate(BaseModel):
    rules: List[RuleCreate]

class metricupdate_string(BaseModel):
    value : str
    operation : str

class metricupdate_numeric(BaseModel):
    value : float
    operation : str

class metricupdate_boolean(BaseModel):
    value : bool
    operation : str

class LLM_rules(BaseModel):
    severity : str
    active_status : bool

class RuleUpdate(BaseModel):
    severity : str
    active_status : bool

class MetricCreate(BaseModel):
    metric_name : str
    metric_description : str
    metric_status : bool
    metric_value : str
    metric_operation : str
    metric_type : str
    rule_code : str

class MetricCreate_numeric(BaseModel):
    metric_name : str
    metric_description : str
    metric_status : bool
    metric_value : float
    metric_operation : str
    metric_type : str
    rule_code : str

class MetricCreate_string(BaseModel):
    metric_name : str
    metric_description : str
    metric_status : bool
    metric_value : str
    metric_operation : str
    metric_type : str
    rule_code : str

class MetricCreate_boolean(BaseModel):
    metric_name : str
    metric_description : str
    metric_status : bool
    metric_value : bool
    metric_operation : str
    metric_type : str
    rule_code : str

class MetricListCreate(BaseModel):
    metrics: List[MetricCreate]

class MetricListCreate_numeric(BaseModel):
    metrics: List[MetricCreate_numeric]

class MetricListCreate_string(BaseModel):
    metrics: List[MetricCreate_string]

class MetricListCreate_boolean(BaseModel):
    metrics: List[MetricCreate_boolean]

class LLM_metric_create(BaseModel):
    metric_name : str
    metric_description : str
    severity : str
    active_status : bool


class LLM_metric_list_create(BaseModel):
    metrics: List[LLM_metric_create]