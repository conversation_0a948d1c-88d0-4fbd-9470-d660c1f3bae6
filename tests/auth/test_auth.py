# Auth module tests
import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

def test_auth_endpoints_exist(client: TestClient):
    """Test that auth endpoints are accessible"""
    # Test that endpoints return proper error codes (not 404)
    response = client.post("/api/v1/auth/login", json={"email": "<EMAIL>", "password": "test"})
    assert response.status_code != 404  # Should not be "Not Found"

    response = client.post("/api/v1/auth/send-otp", json={"email": "<EMAIL>"})
    assert response.status_code != 404  # Should not be "Not Found"

def test_auth_module_imports():
    """Test that auth module imports work correctly"""
    try:
        from src.auth.models import User, Otp
        from src.auth.schemas import UserCreate, UserAuthenticate
        from src.auth.service import AuthService
        from src.auth.utils import get_password_hash, verify_password
        from src.auth.dependencies import get_current_user
        assert True  # If we get here, imports work
    except ImportError as e:
        pytest.fail(f"Auth module import failed: {e}")

def test_password_hashing():
    """Test password hashing functionality"""
    from src.auth.utils import get_password_hash, verify_password

    password = "test_password_123"
    hashed = get_password_hash(password)

    assert hashed != password  # Should be hashed
    assert verify_password(password, hashed)  # Should verify correctly
    assert not verify_password("wrong_password", hashed)  # Should fail with wrong password
