### Steps to run

0. <PERSON><PERSON>, Aphrodite & Elythia repos. 

1. Docker
  ```
  - follow the docker installation steps below
  - you should have 2 docker containers set up - one for neo4j_db, one for postgres_db
  - run the 2 docker containers (you may use Docker Desktop if on Windows)
  - note: backend zeus does not run on docker as of now
  ```

2. Aphrodite: Frontend
  ```
  - navigate to aprhrodite repo
  - set up the env file, ask admin for details
  - switch to ui_only branch
  - npm run dev
  ```
  
3. Zeus: Backend
  ```
  - navigate to zeus repo
  - set up the env file, ask admin for details
  - use requrirements_pankaj.txt
  - .\venv\Scripts\activate          
  - uvicorn app.main:app --host 0.0.0.0 --port 6969 --reload
  ```

4. Elythia: Tool to push data to DB
  ```
  - run the script (confirm name with admin) that will push data from CSVs to DB
  - you only need to run this once in lifetime (or when you make changes to CSVs)
  - once the operation is complete (no more new logs seen on terminal), exit that terminal
  ```

5. Inside the UI
  ```
  - go to admin page from the sidebar
  - click on the button to run all jobs
  - you only need to run this once in lifetime (or when you want to update the jobs' outputs)
  ```

### Comprehensive Installation Guide (Windows, macOS, and Ubuntu)

1. **Docker Installation (Detailed Steps)**:

   **For Windows:**
   - Visit [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
   - System Requirements:
     - Windows 10/11 Pro, Enterprise, or Education (64-bit)
     - At least 4GB RAM
     - CPU with virtualization support enabled in BIOS
   - Download Steps:
     1. Click "Download for Windows"
     2. Run the downloaded "Docker Desktop Installer.exe"
     3. Follow installer prompts (DO NOT uncheck WSL 2 installation)
     4. Allow the installer to download and install WSL 2 components
   
   WSL 2 Setup (Required):
   1. Open PowerShell as Administrator
   2. Enable WSL:
      ```powershell
      dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
      dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
      ```
   3. Restart your computer
   4. Download and install the [WSL 2 Linux kernel update package](https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi)
   5. Set WSL 2 as default:
      ```powershell
      wsl --set-default-version 2
      ```

   **For macOS:**
   - System Requirements:
     - macOS 10.15 or newer
     - At least 4GB RAM
     - Apple Silicon (M1/M2) or Intel processor
   - Installation Steps:
     1. Visit [Docker Desktop for Mac](https://www.docker.com/products/docker-desktop)
     2. Download the appropriate version (Apple Silicon/Intel)
     3. Drag Docker.app to Applications folder
     4. Open Docker.app and authorize with system password
     5. Wait for "Docker Desktop is running" message
     6. (Optional) Enable Kubernetes if needed in Settings

   **For Ubuntu:**
   1. Update package index:
      ```bash
      sudo apt update
      sudo apt upgrade -y
      ```
   2. Install prerequisites:
      ```bash
      sudo apt install -y \
          apt-transport-https \
          ca-certificates \
          curl \
          gnupg \
          lsb-release
      ```
   3. Add Docker's official GPG key:
      ```bash
      curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
      ```
   4. Add Docker repository:
      ```bash
      echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
      ```
   5. Install Docker Engine and Docker Compose:
      ```bash
      sudo apt update
      sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
      ```
   6. Configure user permissions:
      ```bash
      sudo usermod -aG docker $USER
      newgrp docker
      ```

2. **Docker Service Management**:

   **Windows/macOS:**
   - Launch Docker Desktop
   - Wait for the whale icon to stop animating
   - Check status in system tray/menu bar
   - Verify installation:
     ```bash
     docker --version
     docker-compose --version
     ```

   **macOS Specific:**
   - Start Docker:
     ```bash
     open -a Docker
     ```
   - Stop Docker:
     ```bash
     osascript -e 'quit app "Docker"'
     ```
   - Check Docker status:
     ```bash
     docker info
     ```
   - Reset Docker:
     ```bash
     killall Docker && open -a Docker
     ```

   **Ubuntu:**
   - Start Docker service:
     ```bash
     sudo systemctl start docker
     sudo systemctl enable docker  # Auto-start on boot
     ```
   - Check service status:
     ```bash
     sudo systemctl status docker
     ```

3. **Project Setup and Deployment**:

   1. Clone the repository (if not done):
      ```bash
      git clone <repository-url>
      cd zeus
      ```

   2. Environment Configuration:
      - Copy example environment file:
        ```bash
        cp .env.example .env
        ```
      - Edit .env file with your settings:
        ```bash
        nano .env  # or use your preferred text editor
        ```

   3. Docker Compose Commands:
      - Start services:
        ```bash
        docker-compose up -d
        ```
      - View logs:
        ```bash
        docker-compose logs -f
        ```
      - List containers:
        ```bash
        docker-compose ps
        ```
      - Stop services:
        ```bash
        docker-compose down
        ```

4. **Application Access and Verification**:

   1. Wait for all containers to start (usually 1-2 minutes)
   2. Access points:
      - Main Application: [http://localhost:8001](http://localhost:8001)
      - API Documentation: [http://localhost:8001/docs](http://localhost:8001/docs)
      - API Health Check: [http://localhost:8001/health](http://localhost:8001/health)

   3. Verify System Health:
      ```bash
      curl http://localhost:8001/health
      ```

5. **Troubleshooting Common Issues**:

   - If containers fail to start:
     ```bash
     docker-compose logs
     ```
   - Reset Docker state:
     ```bash
     docker-compose down -v
     docker-compose up -d
     ```
   - Check container resources:
     ```bash
     docker stats
     ```
   - Clear Docker cache if needed:
     ```bash
     docker system prune -a
     ```
