# Python bytecode
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd

# All __pycache__ directories in app structure
app/**/__pycache__/
app/routers/ares/**/__pycache__/
app/routers/ares/config/**/__pycache__/
app/routers/ares/feature_generation/**/__pycache__/
app/routers/ares/model/**/__pycache__/
app/routers/ares/__pycache__/__init__.cpython-310.pyc
app/routers/ares/config/__pycache__/__init__.cpython-310.pyc
app/routers/ares/config/__pycache__/feature_descriptions.cpython-310.pyc
app/routers/ares/feature_generation/__pycache__/incremental_feature_gen.cpython-310.pyc
app/routers/ares/model/__pycache__/__init__.cpython-310.pyc
app/routers/ares/model/__pycache__/inference.cpython-310.pyc
app/routers/ares/model/__pycache__/llm_transaction_analysis.cpython-310.pyc
app/routers/ares/model/__pycache__/train.cpython-310.pyc

# Virtual environment
venv/
venv1/
venv2/
env/
ENV/
.env/
.venv/
pythonenv*/
.env

# Generated files
su.py
uuids.txt
*.log
*.csv
*.json
*.sqlite
*.db

# IDE specific files
.vscode/
.idea/
*.swp
*.swo
*.swn
*.bak
*.sublime-workspace
*.sublime-project

# Environment variables
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# System files
.DS_Store
Thumbs.db
*.bak
*.tmp
*.temp

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
ares_inference_critical.log

# Distribution / packaging
dist/
build/
*.egg-info/
*.egg

# Testing
.coverage
htmlcov/
.tox/
.pytest_cache/
nosetests.xml
coverage.xml

# Documentation
docs/_build/
site/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Local development settings
local_settings.py

#email attachments
email_attachments/

docker-data/

temp_files/
