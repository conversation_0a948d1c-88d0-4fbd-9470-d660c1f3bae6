from app.database import SessionLocal
from app.models import models
from app.utils.auth import get_password_hash
import logging
import os
logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

def add_admin_user():
    db = SessionLocal()
    try:
        # Check if the admin user already exists
        existing_user = db.query(models.User).filter(models.User.email == os.getenv("ADMIN_EMAIL")).first()
        if existing_user:
            logger.info("Admin user already exists.")
            return

        # Hash the password
        hashed_password = get_password_hash(os.getenv("ADMIN_PASSWORD"))
        
        # Create the admin user
        admin_user = models.User(
            email=os.getenv("ADMIN_EMAIL"),
            hashed_password=hashed_password,
            is_active=True
        )
        db.add(admin_user)
        db.commit()
        logger.info("Admin user added successfully.")
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to add admin user: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    add_admin_user() 