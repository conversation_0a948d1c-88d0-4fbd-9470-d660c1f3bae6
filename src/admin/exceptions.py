# admin exceptions - module-specific errors for admin
from fastapi import HTTPException, status

class SystemConfigNotFoundError(HTTPException):
    def __init__(self, config_key: str = None):
        detail = f"System configuration '{config_key}' not found" if config_key else "System configuration not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class SystemConfigAlreadyExistsError(HTTPException):
    def __init__(self, config_key: str = None):
        detail = f"System configuration '{config_key}' already exists" if config_key else "System configuration already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidSystemConfigError(HTTPException):
    def __init__(self, detail: str = "Invalid system configuration"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class SystemConfigValidationError(HTTPException):
    def __init__(self, detail: str = "System configuration validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class SystemConfigCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create system configuration"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class SystemConfigUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update system configuration"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class SystemConfigDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete system configuration"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class AuditLogCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create audit log"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class AuditLogRetrievalError(HTTPException):
    def __init__(self, detail: str = "Failed to retrieve audit logs"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DatabaseOperationError(HTTPException):
    def __init__(self, detail: str = "Database operation failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DatabaseResetError(HTTPException):
    def __init__(self, detail: str = "Database reset failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DatabaseInitializationError(HTTPException):
    def __init__(self, detail: str = "Database initialization failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DatabaseBackupError(HTTPException):
    def __init__(self, detail: str = "Database backup failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DatabaseRestoreError(HTTPException):
    def __init__(self, detail: str = "Database restore failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidDatabaseOperationError(HTTPException):
    def __init__(self, operation_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid database operation type: {operation_type}"
        )

class DatabaseOperationNotFoundError(HTTPException):
    def __init__(self, operation_id: str = None):
        detail = f"Database operation {operation_id} not found" if operation_id else "Database operation not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class SystemHealthRecordError(HTTPException):
    def __init__(self, detail: str = "Failed to record system health"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class SystemHealthRetrievalError(HTTPException):
    def __init__(self, detail: str = "Failed to retrieve system health"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidHealthStatusError(HTTPException):
    def __init__(self, status: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid health status: {status}"
        )

class SystemStatusError(HTTPException):
    def __init__(self, detail: str = "Failed to get system status"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class SystemMetricsError(HTTPException):
    def __init__(self, detail: str = "Failed to get system metrics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class UserManagementStatsError(HTTPException):
    def __init__(self, detail: str = "Failed to get user management statistics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class SecurityAuditError(HTTPException):
    def __init__(self, detail: str = "Failed to get security audit"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class SystemStatisticsError(HTTPException):
    def __init__(self, detail: str = "Failed to get system statistics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class FeatureFlagNotFoundError(HTTPException):
    def __init__(self, flag_name: str = None):
        detail = f"Feature flag '{flag_name}' not found" if flag_name else "Feature flag not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class FeatureFlagAlreadyExistsError(HTTPException):
    def __init__(self, flag_name: str = None):
        detail = f"Feature flag '{flag_name}' already exists" if flag_name else "Feature flag already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidFeatureFlagError(HTTPException):
    def __init__(self, detail: str = "Invalid feature flag"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class FeatureFlagValidationError(HTTPException):
    def __init__(self, detail: str = "Feature flag validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class FeatureFlagCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create feature flag"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class FeatureFlagUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update feature flag"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class FeatureFlagDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete feature flag"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class UserSessionNotFoundError(HTTPException):
    def __init__(self, session_id: str = None):
        detail = f"User session {session_id} not found" if session_id else "User session not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class UserSessionCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create user session"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class UserSessionExpiredError(HTTPException):
    def __init__(self, detail: str = "User session has expired"):
        super().__init__(status_code=status.HTTP_401_UNAUTHORIZED, detail=detail)

class SystemNotificationNotFoundError(HTTPException):
    def __init__(self, notification_id: str = None):
        detail = f"System notification {notification_id} not found" if notification_id else "System notification not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class SystemNotificationCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create system notification"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidNotificationTypeError(HTTPException):
    def __init__(self, notification_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid notification type: {notification_type}"
        )

class InvalidNotificationSeverityError(HTTPException):
    def __init__(self, severity: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid notification severity: {severity}"
        )

class MaintenanceWindowNotFoundError(HTTPException):
    def __init__(self, window_id: str = None):
        detail = f"Maintenance window {window_id} not found" if window_id else "Maintenance window not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class MaintenanceWindowCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create maintenance window"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidMaintenanceTypeError(HTTPException):
    def __init__(self, maintenance_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid maintenance type: {maintenance_type}"
        )

class InvalidMaintenanceStatusError(HTTPException):
    def __init__(self, status: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid maintenance status: {status}"
        )

class SystemBackupNotFoundError(HTTPException):
    def __init__(self, backup_id: str = None):
        detail = f"System backup {backup_id} not found" if backup_id else "System backup not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class SystemBackupCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create system backup"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidBackupTypeError(HTTPException):
    def __init__(self, backup_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid backup type: {backup_type}"
        )

class AdminUserCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create admin user"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class AdminUserAlreadyExistsError(HTTPException):
    def __init__(self, email: str = None):
        detail = f"Admin user with email {email} already exists" if email else "Admin user already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class PasswordMismatchError(HTTPException):
    def __init__(self, detail: str = "Passwords do not match"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class AdminAccessDeniedError(HTTPException):
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )

class SystemMaintenanceModeError(HTTPException):
    def __init__(self, detail: str = "System is in maintenance mode"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class RateLimitExceededError(HTTPException):
    def __init__(self, detail: str = "Rate limit exceeded"):
        super().__init__(status_code=status.HTTP_429_TOO_MANY_REQUESTS, detail=detail)

class SystemOverloadError(HTTPException):
    def __init__(self, detail: str = "System is currently overloaded"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class InvalidConfigurationError(HTTPException):
    def __init__(self, detail: str = "Invalid system configuration"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class SystemIntegrityError(HTTPException):
    def __init__(self, detail: str = "System integrity check failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class MonitoringServiceError(HTTPException):
    def __init__(self, detail: str = "Monitoring service error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class PerformanceThresholdExceededError(HTTPException):
    def __init__(self, metric: str, threshold: float, current: float):
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Performance threshold exceeded for {metric}: {current} > {threshold}"
        )