# admin schemas - Pydantic models for admin module
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime

# System Configuration Schemas
class SystemConfigBase(BaseModel):
    config_key: str = Field(..., min_length=1, max_length=255)
    config_value: Optional[str] = None
    config_type: str = Field(..., max_length=50)
    description: Optional[str] = None
    is_sensitive: bool = False
    category: Optional[str] = Field(None, max_length=100)

class SystemConfigCreate(SystemConfigBase):
    pass

class SystemConfigUpdate(BaseModel):
    config_value: Optional[str] = None
    config_type: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    is_sensitive: Optional[bool] = None
    category: Optional[str] = Field(None, max_length=100)

class SystemConfigResponse(SystemConfigBase):
    id: UUID
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Audit Log Schemas
class AuditLogBase(BaseModel):
    action: str = Field(..., min_length=1, max_length=255)
    resource_type: Optional[str] = Field(None, max_length=100)
    resource_id: Optional[str] = Field(None, max_length=255)
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = Field(None, max_length=45)
    user_agent: Optional[str] = None
    status: str = Field(..., max_length=50)
    error_message: Optional[str] = None

class AuditLogCreate(AuditLogBase):
    user_id: Optional[UUID] = None
    user_email: Optional[str] = Field(None, max_length=255)

class AuditLogResponse(AuditLogBase):
    id: UUID
    user_id: Optional[UUID] = None
    user_email: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# System Health Schemas
class SystemHealthBase(BaseModel):
    service_name: str = Field(..., min_length=1, max_length=100)
    status: str = Field(..., max_length=50)
    response_time_ms: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    memory_usage_percent: Optional[float] = None
    disk_usage_percent: Optional[float] = None
    active_connections: Optional[int] = None
    error_rate_percent: Optional[float] = None
    last_error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class SystemHealthCreate(SystemHealthBase):
    pass

class SystemHealthResponse(SystemHealthBase):
    id: UUID
    checked_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Database Operations Schemas
class DatabaseOperationBase(BaseModel):
    operation_type: str = Field(..., max_length=50)
    operation_status: str = Field(..., max_length=50)
    initiated_by: Optional[str] = Field(None, max_length=255)
    operation_details: Optional[Dict[str, Any]] = None

class DatabaseOperationCreate(DatabaseOperationBase):
    pass

class DatabaseOperationUpdate(BaseModel):
    operation_status: Optional[str] = Field(None, max_length=50)
    operation_details: Optional[Dict[str, Any]] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    records_affected: Optional[int] = None
    error_message: Optional[str] = None

class DatabaseOperationResponse(DatabaseOperationBase):
    id: UUID
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    records_affected: Optional[int] = None
    error_message: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# User Session Schemas
class UserSessionBase(BaseModel):
    user_id: UUID
    user_email: str = Field(..., max_length=255)
    session_token: str = Field(..., max_length=500)
    ip_address: Optional[str] = Field(None, max_length=45)
    user_agent: Optional[str] = None
    expires_at: datetime

class UserSessionCreate(UserSessionBase):
    pass

class UserSessionResponse(UserSessionBase):
    id: UUID
    login_time: datetime
    last_activity: datetime
    is_active: bool
    logout_time: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# System Notification Schemas
class SystemNotificationBase(BaseModel):
    notification_type: str = Field(..., max_length=50)
    title: str = Field(..., min_length=1, max_length=255)
    message: str = Field(..., min_length=1)
    severity: str = Field(..., max_length=50)
    source_service: Optional[str] = Field(None, max_length=100)
    target_users: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class SystemNotificationCreate(SystemNotificationBase):
    pass

class SystemNotificationUpdate(BaseModel):
    is_read: Optional[bool] = None
    is_dismissed: Optional[bool] = None

class SystemNotificationResponse(SystemNotificationBase):
    id: UUID
    is_read: bool
    is_dismissed: bool
    created_at: datetime
    read_at: Optional[datetime] = None
    dismissed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Maintenance Window Schemas
class MaintenanceWindowBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    maintenance_type: str = Field(..., max_length=50)
    status: str = Field(..., max_length=50)
    affected_services: Optional[List[str]] = None
    scheduled_start: datetime
    scheduled_end: datetime

class MaintenanceWindowCreate(MaintenanceWindowBase):
    pass

class MaintenanceWindowUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    maintenance_type: Optional[str] = Field(None, max_length=50)
    status: Optional[str] = Field(None, max_length=50)
    affected_services: Optional[List[str]] = None
    scheduled_start: Optional[datetime] = None
    scheduled_end: Optional[datetime] = None
    actual_start: Optional[datetime] = None
    actual_end: Optional[datetime] = None

class MaintenanceWindowResponse(MaintenanceWindowBase):
    id: UUID
    actual_start: Optional[datetime] = None
    actual_end: Optional[datetime] = None
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Feature Flag Schemas
class FeatureFlagBase(BaseModel):
    flag_name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    is_enabled: bool = False
    target_users: Optional[List[str]] = None
    target_environments: Optional[List[str]] = None
    conditions: Optional[Dict[str, Any]] = None

class FeatureFlagCreate(FeatureFlagBase):
    pass

class FeatureFlagUpdate(BaseModel):
    description: Optional[str] = None
    is_enabled: Optional[bool] = None
    target_users: Optional[List[str]] = None
    target_environments: Optional[List[str]] = None
    conditions: Optional[Dict[str, Any]] = None

class FeatureFlagResponse(FeatureFlagBase):
    id: UUID
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# System Backup Schemas
class SystemBackupBase(BaseModel):
    backup_type: str = Field(..., max_length=50)
    backup_name: str = Field(..., min_length=1, max_length=255)
    compression_type: Optional[str] = Field(None, max_length=50)
    tables_included: Optional[List[str]] = None

class SystemBackupCreate(SystemBackupBase):
    pass

class SystemBackupResponse(SystemBackupBase):
    id: UUID
    file_path: Optional[str] = None
    file_size_bytes: Optional[int] = None
    backup_status: str
    started_by: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    error_message: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Admin Operation Schemas
class DatabaseResetRequest(BaseModel):
    confirm: bool = Field(..., description="Must be true to confirm database reset")
    backup_before_reset: bool = True

class DatabaseInitializeRequest(BaseModel):
    reset_existing: bool = False
    add_admin_user: bool = True
    add_sample_data: bool = False

class AdminUserCreateRequest(BaseModel):
    email: str = Field(..., max_length=255)
    password: str = Field(..., min_length=8)
    confirm_password: str = Field(..., min_length=8)

class SystemStatusResponse(BaseModel):
    database_status: str
    api_status: str
    services_status: Dict[str, str]
    uptime_seconds: float
    version: str
    environment: str
    last_health_check: datetime

class SystemMetricsResponse(BaseModel):
    total_users: int
    active_sessions: int
    total_merchants: int
    total_transactions: int
    total_investigations: int
    total_reports: int
    total_rules: int
    total_red_flags: int
    database_size_mb: float
    api_requests_last_hour: int
    error_rate_percent: float

class UserManagementResponse(BaseModel):
    total_users: int
    active_users: int
    inactive_users: int
    admin_users: int
    recent_registrations: int
    recent_logins: int

class SecurityAuditResponse(BaseModel):
    failed_login_attempts: int
    suspicious_activities: int
    rate_limit_violations: int
    security_alerts: int
    last_security_scan: Optional[datetime] = None

# Response List Schemas
class SystemConfigListResponse(BaseModel):
    data: List[SystemConfigResponse]

class AuditLogListResponse(BaseModel):
    data: List[AuditLogResponse]

class SystemHealthListResponse(BaseModel):
    data: List[SystemHealthResponse]

class DatabaseOperationListResponse(BaseModel):
    data: List[DatabaseOperationResponse]

class UserSessionListResponse(BaseModel):
    data: List[UserSessionResponse]

class SystemNotificationListResponse(BaseModel):
    data: List[SystemNotificationResponse]

class MaintenanceWindowListResponse(BaseModel):
    data: List[MaintenanceWindowResponse]

class FeatureFlagListResponse(BaseModel):
    data: List[FeatureFlagResponse]

class SystemBackupListResponse(BaseModel):
    data: List[SystemBackupResponse]

# Legacy Compatibility Schemas
class LegacyAdminResponse(BaseModel):
    message: str
    status: str = "success"

class LegacyDatabaseResetResponse(BaseModel):
    message: str

class LegacyInitializeResponse(BaseModel):
    message: str

# Bulk Operations Schemas
class BulkSystemConfigUpdate(BaseModel):
    configs: List[SystemConfigCreate]

class BulkFeatureFlagUpdate(BaseModel):
    flags: List[FeatureFlagCreate]

class BulkNotificationCreate(BaseModel):
    notifications: List[SystemNotificationCreate]

# System Statistics Schema
class SystemStatistics(BaseModel):
    total_audit_logs: int
    total_notifications: int
    total_maintenance_windows: int
    total_feature_flags: int
    total_backups: int
    system_uptime_hours: float
    average_response_time_ms: float
    error_rate_last_24h: float
    active_user_sessions: int
    database_connections: int
    memory_usage_percent: float
    cpu_usage_percent: float
    disk_usage_percent: float

# Advanced Admin Schemas
class SystemPerformanceMetrics(BaseModel):
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    database_connections: int
    active_sessions: int
    request_rate: float
    error_rate: float
    response_time_p95: float

class SecurityReport(BaseModel):
    scan_date: datetime
    vulnerabilities_found: int
    security_score: float
    recommendations: List[str]
    failed_logins_24h: int
    suspicious_ips: List[str]
    rate_limit_violations: int
    security_alerts: List[Dict[str, Any]]

class SystemHealthSummary(BaseModel):
    overall_status: str
    services: Dict[str, str]
    alerts: List[str]
    warnings: List[str]
    last_updated: datetime
    uptime_percentage: float
    performance_score: float