# admin constants - module-specific constants for admin

# System configuration types
CONFIG_TYPE_STRING = "string"
CONFIG_TYPE_INTEGER = "integer"
CONFIG_TYPE_BOOLEAN = "boolean"
CONFIG_TYPE_JSON = "json"
CONFIG_TYPE_FLOAT = "float"

CONFIG_TYPES = [
    CONFIG_TYPE_STRING,
    CONFIG_TYPE_INTEGER,
    CONFIG_TYPE_BOOLEAN,
    CONFIG_TYPE_JSON,
    CONFIG_TYPE_FLOAT
]

# System configuration categories
CONFIG_CATEGORY_DATABASE = "database"
CONFIG_CATEGORY_API = "api"
CONFIG_CATEGORY_SECURITY = "security"
CONFIG_CATEGORY_AUTHENTICATION = "authentication"
CONFIG_CATEGORY_LOGGING = "logging"
CONFIG_CATEGORY_MONITORING = "monitoring"
CONFIG_CATEGORY_PERFORMANCE = "performance"
CONFIG_CATEGORY_INTEGRATION = "integration"
CONFIG_CATEGORY_FEATURE_FLAGS = "feature_flags"
CONFIG_CATEGORY_NOTIFICATIONS = "notifications"

CONFIG_CATEGORIES = [
    CONFIG_CATEGORY_DATABASE,
    CONFIG_CATEGORY_API,
    CONFIG_CATEGORY_SECURITY,
    CONFIG_CATEGORY_AUTHENTICATION,
    CONFIG_CATEGORY_LOGGING,
    CONFIG_CATEGORY_MONITORING,
    CONFIG_CATEGORY_PERFORMANCE,
    CONFIG_CATEGORY_INTEGRATION,
    CONFIG_CATEGORY_FEATURE_FLAGS,
    CONFIG_CATEGORY_NOTIFICATIONS
]

# Audit log statuses
AUDIT_STATUS_SUCCESS = "success"
AUDIT_STATUS_FAILURE = "failure"
AUDIT_STATUS_ERROR = "error"
AUDIT_STATUS_WARNING = "warning"

AUDIT_STATUSES = [
    AUDIT_STATUS_SUCCESS,
    AUDIT_STATUS_FAILURE,
    AUDIT_STATUS_ERROR,
    AUDIT_STATUS_WARNING
]

# System health statuses
HEALTH_STATUS_HEALTHY = "healthy"
HEALTH_STATUS_DEGRADED = "degraded"
HEALTH_STATUS_UNHEALTHY = "unhealthy"
HEALTH_STATUS_UNKNOWN = "unknown"

HEALTH_STATUSES = [
    HEALTH_STATUS_HEALTHY,
    HEALTH_STATUS_DEGRADED,
    HEALTH_STATUS_UNHEALTHY,
    HEALTH_STATUS_UNKNOWN
]

# Database operation types
DB_OPERATION_BACKUP = "backup"
DB_OPERATION_RESTORE = "restore"
DB_OPERATION_MIGRATION = "migration"
DB_OPERATION_RESET = "reset"
DB_OPERATION_INITIALIZE = "initialize"
DB_OPERATION_CLEANUP = "cleanup"
DB_OPERATION_OPTIMIZE = "optimize"

DB_OPERATION_TYPES = [
    DB_OPERATION_BACKUP,
    DB_OPERATION_RESTORE,
    DB_OPERATION_MIGRATION,
    DB_OPERATION_RESET,
    DB_OPERATION_INITIALIZE,
    DB_OPERATION_CLEANUP,
    DB_OPERATION_OPTIMIZE
]

# Database operation statuses
DB_STATUS_PENDING = "pending"
DB_STATUS_RUNNING = "running"
DB_STATUS_COMPLETED = "completed"
DB_STATUS_FAILED = "failed"
DB_STATUS_CANCELLED = "cancelled"

DB_OPERATION_STATUSES = [
    DB_STATUS_PENDING,
    DB_STATUS_RUNNING,
    DB_STATUS_COMPLETED,
    DB_STATUS_FAILED,
    DB_STATUS_CANCELLED
]

# System notification types
NOTIFICATION_TYPE_ALERT = "alert"
NOTIFICATION_TYPE_WARNING = "warning"
NOTIFICATION_TYPE_INFO = "info"
NOTIFICATION_TYPE_ERROR = "error"
NOTIFICATION_TYPE_SUCCESS = "success"

NOTIFICATION_TYPES = [
    NOTIFICATION_TYPE_ALERT,
    NOTIFICATION_TYPE_WARNING,
    NOTIFICATION_TYPE_INFO,
    NOTIFICATION_TYPE_ERROR,
    NOTIFICATION_TYPE_SUCCESS
]

# Notification severity levels
SEVERITY_LOW = "low"
SEVERITY_MEDIUM = "medium"
SEVERITY_HIGH = "high"
SEVERITY_CRITICAL = "critical"

SEVERITY_LEVELS = [
    SEVERITY_LOW,
    SEVERITY_MEDIUM,
    SEVERITY_HIGH,
    SEVERITY_CRITICAL
]

# Maintenance window types
MAINTENANCE_TYPE_SCHEDULED = "scheduled"
MAINTENANCE_TYPE_EMERGENCY = "emergency"
MAINTENANCE_TYPE_ROUTINE = "routine"
MAINTENANCE_TYPE_SECURITY = "security"

MAINTENANCE_TYPES = [
    MAINTENANCE_TYPE_SCHEDULED,
    MAINTENANCE_TYPE_EMERGENCY,
    MAINTENANCE_TYPE_ROUTINE,
    MAINTENANCE_TYPE_SECURITY
]

# Maintenance window statuses
MAINTENANCE_STATUS_PLANNED = "planned"
MAINTENANCE_STATUS_ACTIVE = "active"
MAINTENANCE_STATUS_COMPLETED = "completed"
MAINTENANCE_STATUS_CANCELLED = "cancelled"

MAINTENANCE_STATUSES = [
    MAINTENANCE_STATUS_PLANNED,
    MAINTENANCE_STATUS_ACTIVE,
    MAINTENANCE_STATUS_COMPLETED,
    MAINTENANCE_STATUS_CANCELLED
]

# Backup types
BACKUP_TYPE_FULL = "full"
BACKUP_TYPE_INCREMENTAL = "incremental"
BACKUP_TYPE_DIFFERENTIAL = "differential"

BACKUP_TYPES = [
    BACKUP_TYPE_FULL,
    BACKUP_TYPE_INCREMENTAL,
    BACKUP_TYPE_DIFFERENTIAL
]

# Backup statuses
BACKUP_STATUS_PENDING = "pending"
BACKUP_STATUS_RUNNING = "running"
BACKUP_STATUS_COMPLETED = "completed"
BACKUP_STATUS_FAILED = "failed"

BACKUP_STATUSES = [
    BACKUP_STATUS_PENDING,
    BACKUP_STATUS_RUNNING,
    BACKUP_STATUS_COMPLETED,
    BACKUP_STATUS_FAILED
]

# Default values
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100
DEFAULT_CONFIG_TYPE = CONFIG_TYPE_STRING
DEFAULT_HEALTH_STATUS = HEALTH_STATUS_UNKNOWN
DEFAULT_NOTIFICATION_SEVERITY = SEVERITY_MEDIUM

# Validation limits
MAX_CONFIG_KEY_LENGTH = 255
MAX_CONFIG_VALUE_LENGTH = 10000
MAX_ACTION_LENGTH = 255
MAX_RESOURCE_TYPE_LENGTH = 100
MAX_RESOURCE_ID_LENGTH = 255
MAX_SERVICE_NAME_LENGTH = 100
MAX_NOTIFICATION_TITLE_LENGTH = 255
MAX_NOTIFICATION_MESSAGE_LENGTH = 5000
MAX_MAINTENANCE_TITLE_LENGTH = 255
MAX_FLAG_NAME_LENGTH = 255
MAX_BACKUP_NAME_LENGTH = 255

# System limits
MAX_AUDIT_LOG_RETENTION_DAYS = 365
MAX_HEALTH_RECORD_RETENTION_DAYS = 30
MAX_NOTIFICATION_RETENTION_DAYS = 90
MAX_BACKUP_RETENTION_DAYS = 180
MAX_SESSION_DURATION_HOURS = 24
MAX_FAILED_LOGIN_ATTEMPTS = 5

# Performance thresholds
CPU_WARNING_THRESHOLD = 80.0
CPU_CRITICAL_THRESHOLD = 95.0
MEMORY_WARNING_THRESHOLD = 85.0
MEMORY_CRITICAL_THRESHOLD = 95.0
DISK_WARNING_THRESHOLD = 80.0
DISK_CRITICAL_THRESHOLD = 90.0
RESPONSE_TIME_WARNING_MS = 1000.0
RESPONSE_TIME_CRITICAL_MS = 5000.0

# Security settings
AUDIT_LOG_ENABLED = True
SECURITY_MONITORING_ENABLED = True
RATE_LIMITING_ENABLED = True
SESSION_TRACKING_ENABLED = True
FAILED_LOGIN_TRACKING_ENABLED = True

# Feature flag environments
ENVIRONMENT_DEVELOPMENT = "development"
ENVIRONMENT_STAGING = "staging"
ENVIRONMENT_PRODUCTION = "production"

ENVIRONMENTS = [
    ENVIRONMENT_DEVELOPMENT,
    ENVIRONMENT_STAGING,
    ENVIRONMENT_PRODUCTION
]

# System services
SERVICE_DATABASE = "database"
SERVICE_API = "api"
SERVICE_AUTH = "auth"
SERVICE_REPORTS = "reports"
SERVICE_INVESTIGATIONS = "investigations"
SERVICE_MERCHANTS = "merchants"
SERVICE_RULES = "rules"
SERVICE_RED_FLAGS = "red_flags"
SERVICE_METRICS = "metrics"

SYSTEM_SERVICES = [
    SERVICE_DATABASE,
    SERVICE_API,
    SERVICE_AUTH,
    SERVICE_REPORTS,
    SERVICE_INVESTIGATIONS,
    SERVICE_MERCHANTS,
    SERVICE_RULES,
    SERVICE_RED_FLAGS,
    SERVICE_METRICS
]

# Admin actions
ACTION_CREATE = "create"
ACTION_READ = "read"
ACTION_UPDATE = "update"
ACTION_DELETE = "delete"
ACTION_LOGIN = "login"
ACTION_LOGOUT = "logout"
ACTION_RESET_PASSWORD = "reset_password"
ACTION_CHANGE_PASSWORD = "change_password"
ACTION_ENABLE_USER = "enable_user"
ACTION_DISABLE_USER = "disable_user"
ACTION_RESET_DATABASE = "reset_database"
ACTION_INITIALIZE_DATABASE = "initialize_database"
ACTION_CREATE_BACKUP = "create_backup"
ACTION_RESTORE_BACKUP = "restore_backup"

ADMIN_ACTIONS = [
    ACTION_CREATE,
    ACTION_READ,
    ACTION_UPDATE,
    ACTION_DELETE,
    ACTION_LOGIN,
    ACTION_LOGOUT,
    ACTION_RESET_PASSWORD,
    ACTION_CHANGE_PASSWORD,
    ACTION_ENABLE_USER,
    ACTION_DISABLE_USER,
    ACTION_RESET_DATABASE,
    ACTION_INITIALIZE_DATABASE,
    ACTION_CREATE_BACKUP,
    ACTION_RESTORE_BACKUP
]

# Cache settings
CACHE_TTL_SYSTEM_CONFIG = 3600  # 1 hour
CACHE_TTL_FEATURE_FLAGS = 300   # 5 minutes
CACHE_TTL_SYSTEM_STATUS = 60    # 1 minute
CACHE_TTL_HEALTH_CHECK = 30     # 30 seconds

# Monitoring intervals
HEALTH_CHECK_INTERVAL_SECONDS = 60
METRICS_COLLECTION_INTERVAL_SECONDS = 300
AUDIT_LOG_CLEANUP_INTERVAL_HOURS = 24
SESSION_CLEANUP_INTERVAL_HOURS = 1

# File paths
BACKUP_STORAGE_PATH = "/backups"
LOG_STORAGE_PATH = "/logs"
TEMP_STORAGE_PATH = "/tmp"
CONFIG_FILE_PATH = "/config"

# API rate limits
RATE_LIMIT_REQUESTS_PER_MINUTE = 100
RATE_LIMIT_REQUESTS_PER_HOUR = 1000
RATE_LIMIT_REQUESTS_PER_DAY = 10000

# System version
SYSTEM_VERSION = "1.0.0"
API_VERSION = "v1"
BUILD_DATE = "2024-01-01"

# Legacy compatibility
LEGACY_ENDPOINT_SUPPORT = True
LEGACY_FORMAT_CONVERSION = True
BACKWARD_COMPATIBILITY_MODE = True