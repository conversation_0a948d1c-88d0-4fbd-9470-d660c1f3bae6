# admin router - system administration and management endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import datetime
import os
from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    SystemConfigCreate, SystemConfigUpdate, SystemConfigResponse,
    AuditLogResponse, SystemHealthCreate, SystemHealthResponse,
    DatabaseOperationResponse, UserSessionResponse, SystemNotificationCreate,
    SystemNotificationUpdate, SystemNotificationResponse, MaintenanceWindowCreate,
    MaintenanceWindowUpdate, MaintenanceWindowResponse, FeatureFlagCreate,
    FeatureFlagUpdate, FeatureFlagResponse, SystemBackupResponse,
    DatabaseResetRequest, DatabaseInitializeRequest, AdminUserCreateRequest,
    SystemStatusResponse, SystemMetricsResponse, UserManagementResponse,
    SecurityAuditResponse, SystemStatistics, SystemConfigListResponse,
    AuditLogListResponse, SystemHealthListResponse, DatabaseOperationListResponse,
    UserSessionListResponse, SystemNotificationListResponse, MaintenanceWindowListResponse,
    FeatureFlagListResponse, SystemBackupListResponse, LegacyAdminResponse
)
from .service import AdminService

router = APIRouter()

def get_admin_service(db: Session = Depends(get_db)) -> AdminService:
    return AdminService(db)

# System Configuration Endpoints
@router.post("/config", response_model=SystemConfigResponse, status_code=201)
async def create_system_config(
    config: SystemConfigCreate,
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new system configuration."""
    return admin_service.create_system_config(config, current_user.email)

@router.get("/config", response_model=SystemConfigListResponse)
async def get_system_configs(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None, description="Filter by category"),
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of system configurations."""
    pagination = PaginationParams(page=page, size=size)
    result = admin_service.get_system_configs(pagination, category)
    return {"data": result.items}

@router.get("/config/{config_key}", response_model=SystemConfigResponse)
async def get_system_config(
    config_key: str = Path(..., description="The configuration key"),
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get system configuration by key."""
    return admin_service.get_system_config_by_key(config_key)

@router.put("/config/{config_key}", response_model=SystemConfigResponse)
async def update_system_config(
    config_key: str = Path(..., description="The configuration key"),
    config_data: SystemConfigUpdate = None,
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Update an existing system configuration."""
    return admin_service.update_system_config(config_key, config_data, current_user.email)

@router.delete("/config/{config_key}")
async def delete_system_config(
    config_key: str = Path(..., description="The configuration key"),
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Delete a system configuration."""
    return admin_service.delete_system_config(config_key, current_user.email)

# Audit Log Endpoints
@router.get("/audit-logs", response_model=AuditLogListResponse)
async def get_audit_logs(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    user: Optional[str] = Query(None, description="Filter by user email"),
    action: Optional[str] = Query(None, description="Filter by action"),
    resource_type: Optional[str] = Query(None, description="Filter by resource type"),
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of audit logs."""
    pagination = PaginationParams(page=page, size=size)
    result = admin_service.get_audit_logs(pagination, user, action, resource_type)
    return {"data": result.items}

# Database Operations Endpoints
@router.post("/database/reset", response_model=LegacyAdminResponse)
async def reset_database(
    request: DatabaseResetRequest,
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Reset the database (dangerous operation)."""
    return admin_service.reset_database(request, current_user.email)

@router.post("/database/initialize", response_model=LegacyAdminResponse)
async def initialize_database(
    request: DatabaseInitializeRequest,
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Initialize database with default data."""
    return admin_service.initialize_database(request, current_user.email)

@router.post("/users/admin", response_model=LegacyAdminResponse)
async def create_admin_user(
    request: AdminUserCreateRequest,
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new admin user."""
    return admin_service.create_admin_user(request, current_user.email)

@router.get("/database/operations", response_model=DatabaseOperationListResponse)
async def get_database_operations(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of database operations."""
    pagination = PaginationParams(page=page, size=size)
    result = admin_service.get_database_operations(pagination)
    return {"data": result.items}

# System Health Endpoints
@router.post("/health", response_model=SystemHealthResponse, status_code=201)
async def record_system_health(
    health_data: SystemHealthCreate,
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Record system health metrics."""
    return admin_service.record_system_health(health_data)

@router.get("/health", response_model=SystemHealthListResponse)
async def get_system_health(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    service: Optional[str] = Query(None, description="Filter by service name"),
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of system health records."""
    pagination = PaginationParams(page=page, size=size)
    result = admin_service.get_system_health(pagination, service)
    return {"data": result.items}

@router.get("/status", response_model=SystemStatusResponse)
async def get_system_status(
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get current system status and metrics."""
    return admin_service.get_current_system_status()

# Analytics and Metrics Endpoints
@router.get("/metrics", response_model=SystemMetricsResponse)
async def get_system_metrics(
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive system metrics."""
    return admin_service.get_system_metrics()

@router.get("/users/stats", response_model=UserManagementResponse)
async def get_user_management_stats(
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get user management statistics."""
    return admin_service.get_user_management_stats()

@router.get("/security/audit", response_model=SecurityAuditResponse)
async def get_security_audit(
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get security audit information."""
    return admin_service.get_security_audit()

@router.get("/statistics", response_model=SystemStatistics)
async def get_system_statistics(
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive system statistics."""
    return admin_service.get_system_statistics()

# Feature Flags Endpoints
@router.post("/feature-flags", response_model=FeatureFlagResponse, status_code=201)
async def create_feature_flag(
    flag: FeatureFlagCreate,
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new feature flag."""
    return admin_service.create_feature_flag(flag, current_user.email)

@router.get("/feature-flags", response_model=FeatureFlagListResponse)
async def get_feature_flags(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of feature flags."""
    pagination = PaginationParams(page=page, size=size)
    result = admin_service.get_feature_flags(pagination)
    return {"data": result.items}

@router.put("/feature-flags/{flag_name}", response_model=FeatureFlagResponse)
async def update_feature_flag(
    flag_name: str = Path(..., description="The feature flag name"),
    flag_data: FeatureFlagUpdate = None,
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Update an existing feature flag."""
    return admin_service.update_feature_flag(flag_name, flag_data, current_user.email)

@router.delete("/feature-flags/{flag_name}")
async def delete_feature_flag(
    flag_name: str = Path(..., description="The feature flag name"),
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Delete a feature flag."""
    return admin_service.delete_feature_flag(flag_name, current_user.email)

@router.get("/feature-flags/{flag_name}/enabled")
async def check_feature_flag(
    flag_name: str = Path(..., description="The feature flag name"),
    admin_service: AdminService = Depends(get_admin_service),
    current_user: User = Depends(get_current_user)
):
    """Check if a feature flag is enabled for the current user."""
    is_enabled = admin_service.is_feature_enabled(flag_name, current_user.email)
    return {"flag_name": flag_name, "is_enabled": is_enabled}

# Legacy Endpoints (for backward compatibility)
@router.get("/reset-database", include_in_schema=False)
async def reset_database_legacy(
    admin_service: AdminService = Depends(get_admin_service)
):
    """Legacy endpoint for database reset."""
    request = DatabaseResetRequest(confirm=True, backup_before_reset=True)
    return admin_service.reset_database(request, "legacy_admin")

@router.get("/initialize-database", include_in_schema=False)
async def initialize_database_legacy(
    admin_service: AdminService = Depends(get_admin_service)
):
    """Legacy endpoint for database initialization."""
    request = DatabaseInitializeRequest(
        reset_existing=False,
        add_admin_user=True,
        add_sample_data=False
    )
    return admin_service.initialize_database(request, "legacy_admin")

@router.get("/add-admin-user", include_in_schema=False)
async def add_admin_user_legacy(
    email: str,
    password: str,
    admin_service: AdminService = Depends(get_admin_service)
):
    """Legacy endpoint for adding admin user."""
    request = AdminUserCreateRequest(
        email=email,
        password=password,
        confirm_password=password
    )
    return admin_service.create_admin_user(request, "legacy_admin")

# System Monitoring Endpoints (for external monitoring tools)
@router.get("/ping")
async def ping():
    """Simple health check endpoint."""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@router.get("/version")
async def get_version():
    """Get system version information."""
    return {
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "development"),
        "build_date": "2024-01-01"
    }

@router.get("/health-check")
async def health_check(
    admin_service: AdminService = Depends(get_admin_service)
):
    """Comprehensive health check for monitoring."""
    try:
        status = admin_service.get_current_system_status()
        return {
            "status": "healthy" if status.database_status == "healthy" else "unhealthy",
            "database": status.database_status,
            "api": status.api_status,
            "services": status.services_status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }