# admin models - SQLAlchemy models for admin module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# System Configuration Models
class system_config(Base):
    __tablename__ = 'system_config'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    config_key = Column(String(255), unique=True, nullable=False, index=True)
    config_value = Column(Text, nullable=True)
    config_type = Column(String(50), nullable=False)  # string, integer, boolean, json
    description = Column(Text, nullable=True)
    is_sensitive = Column(Boolean, default=False)  # For passwords, API keys, etc.
    category = Column(String(100), nullable=True)  # database, api, security, etc.
    created_by = Column(String(255))
    updated_by = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# System Audit Logs
class audit_logs(Base):
    __tablename__ = 'audit_logs'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    user_email = Column(String(255), nullable=True, index=True)
    action = Column(String(255), nullable=False)
    resource_type = Column(String(100), nullable=True)  # user, merchant, rule, etc.
    resource_id = Column(String(255), nullable=True)
    details = Column(JSONB, nullable=True)  # Additional action details
    ip_address = Column(String(45), nullable=True)  # IPv4 or IPv6
    user_agent = Column(Text, nullable=True)
    status = Column(String(50), nullable=False)  # success, failure, error
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now, index=True)

# System Health Monitoring
class system_health(Base):
    __tablename__ = 'system_health'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    service_name = Column(String(100), nullable=False, index=True)
    status = Column(String(50), nullable=False)  # healthy, degraded, unhealthy
    response_time_ms = Column(Float, nullable=True)
    cpu_usage_percent = Column(Float, nullable=True)
    memory_usage_percent = Column(Float, nullable=True)
    disk_usage_percent = Column(Float, nullable=True)
    active_connections = Column(Integer, nullable=True)
    error_rate_percent = Column(Float, nullable=True)
    last_error = Column(Text, nullable=True)
    metadata = Column(JSONB, nullable=True)  # Additional service-specific metrics
    checked_at = Column(DateTime, default=datetime.now, index=True)

# Database Operations Log
class database_operations(Base):
    __tablename__ = 'database_operations'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    operation_type = Column(String(50), nullable=False)  # backup, restore, migration, reset
    operation_status = Column(String(50), nullable=False)  # pending, running, completed, failed
    initiated_by = Column(String(255), nullable=True)
    operation_details = Column(JSONB, nullable=True)
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)
    records_affected = Column(Integer, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

# User Sessions Management
class user_sessions(Base):
    __tablename__ = 'user_sessions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    user_email = Column(String(255), nullable=False, index=True)
    session_token = Column(String(500), nullable=False, unique=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    login_time = Column(DateTime, default=datetime.now)
    last_activity = Column(DateTime, default=datetime.now)
    expires_at = Column(DateTime, nullable=False)
    is_active = Column(Boolean, default=True)
    logout_time = Column(DateTime, nullable=True)

# System Notifications
class system_notifications(Base):
    __tablename__ = 'system_notifications'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    notification_type = Column(String(50), nullable=False)  # alert, warning, info, error
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    severity = Column(String(50), nullable=False)  # low, medium, high, critical
    source_service = Column(String(100), nullable=True)
    target_users = Column(JSONB, nullable=True)  # List of user emails or roles
    is_read = Column(Boolean, default=False)
    is_dismissed = Column(Boolean, default=False)
    metadata = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now, index=True)
    read_at = Column(DateTime, nullable=True)
    dismissed_at = Column(DateTime, nullable=True)

# System Maintenance Windows
class maintenance_windows(Base):
    __tablename__ = 'maintenance_windows'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    maintenance_type = Column(String(50), nullable=False)  # scheduled, emergency, routine
    status = Column(String(50), nullable=False)  # planned, active, completed, cancelled
    affected_services = Column(JSONB, nullable=True)  # List of affected services
    scheduled_start = Column(DateTime, nullable=False)
    scheduled_end = Column(DateTime, nullable=False)
    actual_start = Column(DateTime, nullable=True)
    actual_end = Column(DateTime, nullable=True)
    created_by = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# API Rate Limiting
class api_rate_limits(Base):
    __tablename__ = 'api_rate_limits'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    ip_address = Column(String(45), nullable=True, index=True)
    endpoint = Column(String(255), nullable=False, index=True)
    request_count = Column(Integer, default=0)
    window_start = Column(DateTime, nullable=False)
    window_end = Column(DateTime, nullable=False)
    limit_exceeded = Column(Boolean, default=False)
    last_request = Column(DateTime, default=datetime.now)

# System Backups
class system_backups(Base):
    __tablename__ = 'system_backups'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    backup_type = Column(String(50), nullable=False)  # full, incremental, differential
    backup_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=True)
    file_size_bytes = Column(Integer, nullable=True)
    compression_type = Column(String(50), nullable=True)
    backup_status = Column(String(50), nullable=False)  # pending, running, completed, failed
    started_by = Column(String(255), nullable=True)
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)
    duration_seconds = Column(Float, nullable=True)
    tables_included = Column(JSONB, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

# Feature Flags
class feature_flags(Base):
    __tablename__ = 'feature_flags'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    flag_name = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    is_enabled = Column(Boolean, default=False)
    target_users = Column(JSONB, nullable=True)  # List of user emails or percentages
    target_environments = Column(JSONB, nullable=True)  # dev, staging, production
    conditions = Column(JSONB, nullable=True)  # Additional conditions for flag activation
    created_by = Column(String(255))
    updated_by = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Add indexes for performance
Index('idx_audit_logs_user_action', audit_logs.user_email, audit_logs.action)
Index('idx_audit_logs_resource', audit_logs.resource_type, audit_logs.resource_id)
Index('idx_system_health_service_time', system_health.service_name, system_health.checked_at)
Index('idx_user_sessions_user_active', user_sessions.user_id, user_sessions.is_active)
Index('idx_notifications_type_severity', system_notifications.notification_type, system_notifications.severity)