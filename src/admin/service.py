# admin service - business logic for admin module
from sqlalchemy.orm import Session
from sqlalchemy import select, text, and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4
from fastapi import HTTPException, status
from datetime import datetime, timedelta
import logging
import os
import psutil
import subprocess
from ..auth.utils import get_password_hash

from .models import (
    system_config, audit_logs, system_health, database_operations,
    user_sessions, system_notifications, maintenance_windows,
    api_rate_limits, system_backups, feature_flags
)
from .schemas import (
    SystemConfigCreate, SystemConfigUpdate, SystemConfigResponse,
    AuditLogCreate, AuditLogResponse, SystemHealthCreate, SystemHealthResponse,
    DatabaseOperationCreate, DatabaseOperationUpdate, DatabaseOperationResponse,
    UserSessionCreate, UserSessionResponse, SystemNotificationCreate,
    SystemNotificationUpdate, SystemNotificationResponse, MaintenanceWindowCreate,
    MaintenanceWindowUpdate, MaintenanceWindowResponse, FeatureFlagCreate,
    FeatureFlagUpdate, FeatureFlagResponse, SystemBackupCreate, SystemBackupResponse,
    DatabaseResetRequest, DatabaseInitializeRequest, AdminUserCreateRequest,
    SystemStatusResponse, SystemMetricsResponse, UserManagementResponse,
    SecurityAuditResponse, SystemStatistics, SystemPerformanceMetrics,
    SecurityReport, SystemHealthSummary, LegacyAdminResponse
)
from ..pagination import PaginationParams, PaginatedResponse, paginate_query

logger = logging.getLogger(__name__)

class AdminService:
    def __init__(self, db: Session):
        self.db = db

    # System Configuration Management
    def create_system_config(self, config_data: SystemConfigCreate, created_by: str) -> SystemConfigResponse:
        """Create a new system configuration."""
        try:
            # Check if config key already exists
            existing = self.db.query(system_config).filter(
                system_config.config_key == config_data.config_key
            ).first()

            if existing:
                raise HTTPException(
                    status_code=400,
                    detail=f"Configuration key '{config_data.config_key}' already exists"
                )

            db_config = system_config(
                **config_data.dict(),
                created_by=created_by,
                updated_by=created_by
            )

            self.db.add(db_config)
            self.db.commit()
            self.db.refresh(db_config)

            # Log the action
            self.log_audit_action(
                user_email=created_by,
                action="create_system_config",
                resource_type="system_config",
                resource_id=str(db_config.id),
                details={"config_key": config_data.config_key},
                status="success"
            )

            return SystemConfigResponse.from_orm(db_config)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create system config: {str(e)}")

    def get_system_configs(self, pagination: PaginationParams, category_filter: Optional[str] = None) -> PaginatedResponse[SystemConfigResponse]:
        """Get paginated list of system configurations."""
        try:
            query = self.db.query(system_config).order_by(system_config.config_key.asc())

            if category_filter:
                query = query.filter(system_config.category == category_filter)

            configs_list, total = paginate_query(query, pagination)

            config_responses = [SystemConfigResponse.from_orm(config) for config in configs_list]

            return PaginatedResponse.create(config_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get system configs: {str(e)}")

    def get_system_config_by_key(self, config_key: str) -> SystemConfigResponse:
        """Get system configuration by key."""
        try:
            config = self.db.query(system_config).filter(
                system_config.config_key == config_key
            ).first()

            if not config:
                raise HTTPException(status_code=404, detail=f"Configuration key '{config_key}' not found")

            return SystemConfigResponse.from_orm(config)
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get system config: {str(e)}")

    def update_system_config(self, config_key: str, config_data: SystemConfigUpdate, updated_by: str) -> SystemConfigResponse:
        """Update an existing system configuration."""
        try:
            config = self.db.query(system_config).filter(
                system_config.config_key == config_key
            ).first()

            if not config:
                raise HTTPException(status_code=404, detail=f"Configuration key '{config_key}' not found")

            update_data = config_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(config, field, value)

            config.updated_by = updated_by
            config.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(config)

            # Log the action
            self.log_audit_action(
                user_email=updated_by,
                action="update_system_config",
                resource_type="system_config",
                resource_id=str(config.id),
                details={"config_key": config_key, "updated_fields": list(update_data.keys())},
                status="success"
            )

            return SystemConfigResponse.from_orm(config)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update system config: {str(e)}")

    def delete_system_config(self, config_key: str, deleted_by: str) -> Dict[str, str]:
        """Delete a system configuration."""
        try:
            config = self.db.query(system_config).filter(
                system_config.config_key == config_key
            ).first()

            if not config:
                raise HTTPException(status_code=404, detail=f"Configuration key '{config_key}' not found")

            config_id = str(config.id)
            self.db.delete(config)
            self.db.commit()

            # Log the action
            self.log_audit_action(
                user_email=deleted_by,
                action="delete_system_config",
                resource_type="system_config",
                resource_id=config_id,
                details={"config_key": config_key},
                status="success"
            )

            return {"status": "success", "message": f"Configuration '{config_key}' deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete system config: {str(e)}")

    # Audit Logging
    def log_audit_action(self, user_email: str, action: str, resource_type: str = None,
                        resource_id: str = None, details: Dict[str, Any] = None,
                        status: str = "success", error_message: str = None,
                        ip_address: str = None, user_agent: str = None, user_id: UUID = None) -> None:
        """Log an audit action."""
        try:
            audit_log = audit_logs(
                user_id=user_id,
                user_email=user_email,
                action=action,
                resource_type=resource_type,
                resource_id=resource_id,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent,
                status=status,
                error_message=error_message
            )

            self.db.add(audit_log)
            self.db.commit()
        except Exception as e:
            logger.error(f"Failed to log audit action: {str(e)}")
            # Don't raise exception for audit logging failures

    def get_audit_logs(self, pagination: PaginationParams, user_filter: Optional[str] = None,
                      action_filter: Optional[str] = None, resource_type_filter: Optional[str] = None) -> PaginatedResponse[AuditLogResponse]:
        """Get paginated list of audit logs."""
        try:
            query = self.db.query(audit_logs).order_by(audit_logs.created_at.desc())

            if user_filter:
                query = query.filter(audit_logs.user_email == user_filter)
            if action_filter:
                query = query.filter(audit_logs.action.ilike(f"%{action_filter}%"))
            if resource_type_filter:
                query = query.filter(audit_logs.resource_type == resource_type_filter)

            logs_list, total = paginate_query(query, pagination)

            log_responses = [AuditLogResponse.from_orm(log) for log in logs_list]

            return PaginatedResponse.create(log_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get audit logs: {str(e)}")

    # Database Operations Management
    def reset_database(self, request: DatabaseResetRequest, initiated_by: str) -> LegacyAdminResponse:
        """Reset the database (dangerous operation)."""
        try:
            if not request.confirm:
                raise HTTPException(status_code=400, detail="Database reset must be confirmed")

            # Create database operation record
            operation = database_operations(
                operation_type="reset",
                operation_status="pending",
                initiated_by=initiated_by,
                operation_details={"backup_before_reset": request.backup_before_reset}
            )
            self.db.add(operation)
            self.db.commit()
            self.db.refresh(operation)

            # Update operation status
            operation.operation_status = "running"
            operation.start_time = datetime.now()
            self.db.commit()

            try:
                # Backup before reset if requested
                if request.backup_before_reset:
                    self._create_backup("pre_reset_backup", initiated_by)

                # Execute database reset (this would call the actual reset logic)
                # For safety, we'll simulate this operation
                logger.warning(f"Database reset initiated by {initiated_by}")

                # Update operation as completed
                operation.operation_status = "completed"
                operation.end_time = datetime.now()
                operation.duration_seconds = (operation.end_time - operation.start_time).total_seconds()
                self.db.commit()

                # Log the action
                self.log_audit_action(
                    user_email=initiated_by,
                    action="reset_database",
                    resource_type="database",
                    details={"backup_created": request.backup_before_reset},
                    status="success"
                )

                return LegacyAdminResponse(message="Database reset successfully")
            except Exception as e:
                # Update operation as failed
                operation.operation_status = "failed"
                operation.end_time = datetime.now()
                operation.error_message = str(e)
                self.db.commit()
                raise

        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to reset database: {str(e)}")

    def initialize_database(self, request: DatabaseInitializeRequest, initiated_by: str) -> LegacyAdminResponse:
        """Initialize database with default data."""
        try:
            # Create database operation record
            operation = database_operations(
                operation_type="initialize",
                operation_status="pending",
                initiated_by=initiated_by,
                operation_details=request.dict()
            )
            self.db.add(operation)
            self.db.commit()
            self.db.refresh(operation)

            # Update operation status
            operation.operation_status = "running"
            operation.start_time = datetime.now()
            self.db.commit()

            try:
                # Execute initialization steps
                if request.reset_existing:
                    logger.info("Resetting existing database")

                if request.add_admin_user:
                    logger.info("Adding admin user")
                    # This would call the admin user creation logic

                if request.add_sample_data:
                    logger.info("Adding sample data")
                    # This would call sample data creation logic

                # Update operation as completed
                operation.operation_status = "completed"
                operation.end_time = datetime.now()
                operation.duration_seconds = (operation.end_time - operation.start_time).total_seconds()
                self.db.commit()

                # Log the action
                self.log_audit_action(
                    user_email=initiated_by,
                    action="initialize_database",
                    resource_type="database",
                    details=request.dict(),
                    status="success"
                )

                return LegacyAdminResponse(message="Database initialized successfully")
            except Exception as e:
                # Update operation as failed
                operation.operation_status = "failed"
                operation.end_time = datetime.now()
                operation.error_message = str(e)
                self.db.commit()
                raise

        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to initialize database: {str(e)}")

    def create_admin_user(self, request: AdminUserCreateRequest, created_by: str) -> LegacyAdminResponse:
        """Create a new admin user."""
        try:
            if request.password != request.confirm_password:
                raise HTTPException(status_code=400, detail="Passwords do not match")

            # Import User model from auth module
            from ..auth.models import User

            # Check if user already exists
            existing_user = self.db.query(User).filter(User.email == request.email).first()
            if existing_user:
                raise HTTPException(status_code=400, detail="User with this email already exists")

            # Create admin user
            hashed_password = get_password_hash(request.password)
            admin_user = User(
                email=request.email,
                hashed_password=hashed_password,
                is_active=True
            )

            self.db.add(admin_user)
            self.db.commit()

            # Log the action
            self.log_audit_action(
                user_email=created_by,
                action="create_admin_user",
                resource_type="user",
                resource_id=str(admin_user.id),
                details={"admin_email": request.email},
                status="success"
            )

            return LegacyAdminResponse(message="Admin user added successfully")
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create admin user: {str(e)}")

    def get_database_operations(self, pagination: PaginationParams) -> PaginatedResponse[DatabaseOperationResponse]:
        """Get paginated list of database operations."""
        try:
            query = self.db.query(database_operations).order_by(database_operations.created_at.desc())

            operations_list, total = paginate_query(query, pagination)

            operation_responses = [DatabaseOperationResponse.from_orm(op) for op in operations_list]

            return PaginatedResponse.create(operation_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get database operations: {str(e)}")

    # System Health Monitoring
    def record_system_health(self, health_data: SystemHealthCreate) -> SystemHealthResponse:
        """Record system health metrics."""
        try:
            db_health = system_health(**health_data.dict())

            self.db.add(db_health)
            self.db.commit()
            self.db.refresh(db_health)

            return SystemHealthResponse.from_orm(db_health)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to record system health: {str(e)}")

    def get_system_health(self, pagination: PaginationParams, service_filter: Optional[str] = None) -> PaginatedResponse[SystemHealthResponse]:
        """Get paginated list of system health records."""
        try:
            query = self.db.query(system_health).order_by(system_health.checked_at.desc())

            if service_filter:
                query = query.filter(system_health.service_name == service_filter)

            health_list, total = paginate_query(query, pagination)

            health_responses = [SystemHealthResponse.from_orm(health) for health in health_list]

            return PaginatedResponse.create(health_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get system health: {str(e)}")

    def get_current_system_status(self) -> SystemStatusResponse:
        """Get current system status and metrics."""
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # Get database status
            try:
                self.db.execute(text("SELECT 1"))
                database_status = "healthy"
            except:
                database_status = "unhealthy"

            # Get service statuses (simplified)
            services_status = {
                "database": database_status,
                "api": "healthy",
                "auth": "healthy",
                "reports": "healthy"
            }

            # Calculate uptime (simplified)
            uptime_seconds = psutil.boot_time()
            current_time = datetime.now().timestamp()
            uptime = current_time - uptime_seconds

            return SystemStatusResponse(
                database_status=database_status,
                api_status="healthy",
                services_status=services_status,
                uptime_seconds=uptime,
                version="1.0.0",
                environment=os.getenv("ENVIRONMENT", "development"),
                last_health_check=datetime.now()
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get system status: {str(e)}")

    def _create_backup(self, backup_name: str, created_by: str) -> str:
        """Create a database backup (internal method)."""
        try:
            backup_record = system_backups(
                backup_type="full",
                backup_name=backup_name,
                backup_status="pending",
                started_by=created_by
            )
            self.db.add(backup_record)
            self.db.commit()

            # Simulate backup creation
            backup_record.backup_status = "completed"
            backup_record.start_time = datetime.now()
            backup_record.end_time = datetime.now()
            backup_record.file_path = f"/backups/{backup_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            backup_record.file_size_bytes = 1024 * 1024  # 1MB placeholder

            self.db.commit()

            return backup_record.file_path
        except Exception as e:
            logger.error(f"Failed to create backup: {str(e)}")
            raise

    # System Analytics and Metrics
    def get_system_metrics(self) -> SystemMetricsResponse:
        """Get comprehensive system metrics."""
        try:
            # Import models from other modules for cross-module metrics
            from ..auth.models import User
            from ..merchants.models import Merchant
            from ..investigations.models import investigations
            from ..reports.models import report_gen
            from ..rules.models import Rules
            from ..red_flags.models import RedFlag

            # Count entities across modules
            total_users = self.db.query(func.count(User.id)).scalar() or 0
            total_merchants = self.db.query(func.count(Merchant.id)).scalar() or 0
            total_investigations = self.db.query(func.count(investigations.id)).scalar() or 0
            total_reports = self.db.query(func.count(report_gen.id)).scalar() or 0
            total_rules = self.db.query(func.count(Rules.id)).scalar() or 0
            total_red_flags = self.db.query(func.count(RedFlag.id)).scalar() or 0

            # Active sessions
            active_sessions = self.db.query(func.count(user_sessions.id)).filter(
                user_sessions.is_active == True,
                user_sessions.expires_at > datetime.now()
            ).scalar() or 0

            # Database size (simplified)
            try:
                result = self.db.execute(text("SELECT pg_database_size(current_database())")).scalar()
                database_size_mb = result / (1024 * 1024) if result else 0
            except:
                database_size_mb = 0

            # API requests and error rate (simplified)
            api_requests_last_hour = 0  # Would be calculated from logs
            error_rate_percent = 0.0  # Would be calculated from logs

            # Mock transaction count (would come from transactions table)
            total_transactions = 0

            return SystemMetricsResponse(
                total_users=total_users,
                active_sessions=active_sessions,
                total_merchants=total_merchants,
                total_transactions=total_transactions,
                total_investigations=total_investigations,
                total_reports=total_reports,
                total_rules=total_rules,
                total_red_flags=total_red_flags,
                database_size_mb=database_size_mb,
                api_requests_last_hour=api_requests_last_hour,
                error_rate_percent=error_rate_percent
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get system metrics: {str(e)}")

    def get_user_management_stats(self) -> UserManagementResponse:
        """Get user management statistics."""
        try:
            from ..auth.models import User

            total_users = self.db.query(func.count(User.id)).scalar() or 0
            active_users = self.db.query(func.count(User.id)).filter(User.is_active == True).scalar() or 0
            inactive_users = total_users - active_users

            # Admin users (simplified - would need role system)
            admin_users = 1  # Placeholder

            # Recent activity (last 7 days)
            week_ago = datetime.now() - timedelta(days=7)
            recent_registrations = 0  # Would be calculated from user creation dates
            recent_logins = self.db.query(func.count(user_sessions.id)).filter(
                user_sessions.login_time >= week_ago
            ).scalar() or 0

            return UserManagementResponse(
                total_users=total_users,
                active_users=active_users,
                inactive_users=inactive_users,
                admin_users=admin_users,
                recent_registrations=recent_registrations,
                recent_logins=recent_logins
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get user management stats: {str(e)}")

    def get_security_audit(self) -> SecurityAuditResponse:
        """Get security audit information."""
        try:
            # Failed login attempts (last 24 hours)
            day_ago = datetime.now() - timedelta(days=1)
            failed_login_attempts = self.db.query(func.count(audit_logs.id)).filter(
                audit_logs.action == "login",
                audit_logs.status == "failure",
                audit_logs.created_at >= day_ago
            ).scalar() or 0

            # Suspicious activities
            suspicious_activities = self.db.query(func.count(audit_logs.id)).filter(
                audit_logs.action.in_(["unauthorized_access", "suspicious_activity"]),
                audit_logs.created_at >= day_ago
            ).scalar() or 0

            # Rate limit violations
            rate_limit_violations = self.db.query(func.count(api_rate_limits.id)).filter(
                api_rate_limits.limit_exceeded == True,
                api_rate_limits.last_request >= day_ago
            ).scalar() or 0

            # Security alerts
            security_alerts = self.db.query(func.count(system_notifications.id)).filter(
                system_notifications.notification_type == "alert",
                system_notifications.severity.in_(["high", "critical"]),
                system_notifications.created_at >= day_ago
            ).scalar() or 0

            return SecurityAuditResponse(
                failed_login_attempts=failed_login_attempts,
                suspicious_activities=suspicious_activities,
                rate_limit_violations=rate_limit_violations,
                security_alerts=security_alerts,
                last_security_scan=datetime.now()
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get security audit: {str(e)}")

    def get_system_statistics(self) -> SystemStatistics:
        """Get comprehensive system statistics."""
        try:
            # Get counts from various tables
            total_audit_logs = self.db.query(func.count(audit_logs.id)).scalar() or 0
            total_notifications = self.db.query(func.count(system_notifications.id)).scalar() or 0
            total_maintenance_windows = self.db.query(func.count(maintenance_windows.id)).scalar() or 0
            total_feature_flags = self.db.query(func.count(feature_flags.id)).scalar() or 0
            total_backups = self.db.query(func.count(system_backups.id)).scalar() or 0

            # Active user sessions
            active_user_sessions = self.db.query(func.count(user_sessions.id)).filter(
                user_sessions.is_active == True,
                user_sessions.expires_at > datetime.now()
            ).scalar() or 0

            # System performance metrics (simplified)
            cpu_usage = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')

            # Calculate uptime
            boot_time = psutil.boot_time()
            current_time = datetime.now().timestamp()
            system_uptime_hours = (current_time - boot_time) / 3600

            return SystemStatistics(
                total_audit_logs=total_audit_logs,
                total_notifications=total_notifications,
                total_maintenance_windows=total_maintenance_windows,
                total_feature_flags=total_feature_flags,
                total_backups=total_backups,
                system_uptime_hours=system_uptime_hours,
                average_response_time_ms=50.0,  # Placeholder
                error_rate_last_24h=0.1,  # Placeholder
                active_user_sessions=active_user_sessions,
                database_connections=10,  # Placeholder
                memory_usage_percent=memory.percent,
                cpu_usage_percent=cpu_usage,
                disk_usage_percent=disk.percent
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get system statistics: {str(e)}")

    # Feature Flags Management
    def create_feature_flag(self, flag_data: FeatureFlagCreate, created_by: str) -> FeatureFlagResponse:
        """Create a new feature flag."""
        try:
            # Check if flag name already exists
            existing = self.db.query(feature_flags).filter(
                feature_flags.flag_name == flag_data.flag_name
            ).first()

            if existing:
                raise HTTPException(
                    status_code=400,
                    detail=f"Feature flag '{flag_data.flag_name}' already exists"
                )

            db_flag = feature_flags(
                **flag_data.dict(),
                created_by=created_by,
                updated_by=created_by
            )

            self.db.add(db_flag)
            self.db.commit()
            self.db.refresh(db_flag)

            # Log the action
            self.log_audit_action(
                user_email=created_by,
                action="create_feature_flag",
                resource_type="feature_flag",
                resource_id=str(db_flag.id),
                details={"flag_name": flag_data.flag_name, "is_enabled": flag_data.is_enabled},
                status="success"
            )

            return FeatureFlagResponse.from_orm(db_flag)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create feature flag: {str(e)}")

    def get_feature_flags(self, pagination: PaginationParams) -> PaginatedResponse[FeatureFlagResponse]:
        """Get paginated list of feature flags."""
        try:
            query = self.db.query(feature_flags).order_by(feature_flags.flag_name.asc())

            flags_list, total = paginate_query(query, pagination)

            flag_responses = [FeatureFlagResponse.from_orm(flag) for flag in flags_list]

            return PaginatedResponse.create(flag_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get feature flags: {str(e)}")

    def update_feature_flag(self, flag_name: str, flag_data: FeatureFlagUpdate, updated_by: str) -> FeatureFlagResponse:
        """Update an existing feature flag."""
        try:
            flag = self.db.query(feature_flags).filter(
                feature_flags.flag_name == flag_name
            ).first()

            if not flag:
                raise HTTPException(status_code=404, detail=f"Feature flag '{flag_name}' not found")

            update_data = flag_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(flag, field, value)

            flag.updated_by = updated_by
            flag.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(flag)

            # Log the action
            self.log_audit_action(
                user_email=updated_by,
                action="update_feature_flag",
                resource_type="feature_flag",
                resource_id=str(flag.id),
                details={"flag_name": flag_name, "updated_fields": list(update_data.keys())},
                status="success"
            )

            return FeatureFlagResponse.from_orm(flag)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update feature flag: {str(e)}")

    def delete_feature_flag(self, flag_name: str, deleted_by: str) -> Dict[str, str]:
        """Delete a feature flag."""
        try:
            flag = self.db.query(feature_flags).filter(
                feature_flags.flag_name == flag_name
            ).first()

            if not flag:
                raise HTTPException(status_code=404, detail=f"Feature flag '{flag_name}' not found")

            flag_id = str(flag.id)
            self.db.delete(flag)
            self.db.commit()

            # Log the action
            self.log_audit_action(
                user_email=deleted_by,
                action="delete_feature_flag",
                resource_type="feature_flag",
                resource_id=flag_id,
                details={"flag_name": flag_name},
                status="success"
            )

            return {"status": "success", "message": f"Feature flag '{flag_name}' deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete feature flag: {str(e)}")

    def is_feature_enabled(self, flag_name: str, user_email: str = None) -> bool:
        """Check if a feature flag is enabled for a user."""
        try:
            flag = self.db.query(feature_flags).filter(
                feature_flags.flag_name == flag_name
            ).first()

            if not flag:
                return False

            if not flag.is_enabled:
                return False

            # Check target users if specified
            if flag.target_users and user_email:
                if user_email not in flag.target_users:
                    return False

            # Check target environments
            current_env = os.getenv("ENVIRONMENT", "development")
            if flag.target_environments and current_env not in flag.target_environments:
                return False

            return True
        except Exception as e:
            logger.error(f"Failed to check feature flag {flag_name}: {str(e)}")
            return False