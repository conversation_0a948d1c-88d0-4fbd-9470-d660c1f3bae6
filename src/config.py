# Global configurations
import os
from dotenv import load_dotenv

load_dotenv()

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL")
if DATABASE_URL and DATABASE_URL.startswith("postgres://"):
    DATABASE_URL = DATABASE_URL.replace("postgres://", "postgresql://", 1)

# JWT configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "400"))

# AWS configuration
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")

# Environment
ENVIRONMENT = os.getenv("ENVIRONMENT", "dev")

# API configuration
API_V1_STR = "/api/v1"
PROJECT_NAME = "Zeus API"
PROJECT_VERSION = "1.0.0"
