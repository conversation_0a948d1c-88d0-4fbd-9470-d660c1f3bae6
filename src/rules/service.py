# rules service - business logic for rules module
from sqlalchemy.orm import Session
from sqlalchemy import select, text
from typing import Optional, List, Dict, Any
from uuid import UUID
from fastapi import HTTPException, status
from datetime import datetime

from .models import (
    rules_store, Rules, RulesList, LLMredFlagsStatus,
    rule_metric_transactions, rule_metric_communications,
    rule_metric_network, rule_metric_legal_and_regulatory,
    rule_metric_digital_footprint
)
from .schemas import (
    RuleStoreCreate, RuleStoreUpdate, RuleStoreResponse,
    LegacyRuleCreate, LegacyRuleUpdate, LegacyRuleResponse,
    RuleListCreate, RulesConfigCreate, RulesConfigResponse,
    MetricUpdateNumeric, MetricUpdateString, MetricUpdateBoolean
)
from ..pagination import PaginationParams, PaginatedResponse, paginate_query

class RulesService:
    def __init__(self, db: Session):
        self.db = db

    def create_rule(self, rule_data: RuleStoreCreate, created_by: str) -> RuleStoreResponse:
        """Create a new rule in the rules store."""
        try:
            # Check if rule code already exists
            existing = self.db.query(rules_store).filter(
                rules_store.code == rule_data.code
            ).first()

            if existing:
                raise HTTPException(
                    status_code=400,
                    detail=f"Rule with code '{rule_data.code}' already exists"
                )

            db_rule = rules_store(
                **rule_data.dict(exclude={'rule'}),
                rule=rule_data.rule.dict(),
                created_by=created_by,
                updated_by=created_by
            )

            self.db.add(db_rule)
            self.db.commit()
            self.db.refresh(db_rule)

            return RuleStoreResponse.from_orm(db_rule)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create rule: {str(e)}")

    def get_rule_by_id(self, rule_id: UUID, include_deleted: bool = False) -> RuleStoreResponse:
        """Get rule by ID."""
        try:
            query = self.db.query(rules_store).filter(rules_store.id == rule_id)
            if not include_deleted:
                query = query.filter(rules_store.is_deleted == False)

            rule = query.first()
            if not rule:
                raise HTTPException(status_code=404, detail="Rule not found")

            return RuleStoreResponse.from_orm(rule)
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get rule: {str(e)}")

    def get_rules(self, pagination: PaginationParams, include_deleted: bool = False) -> PaginatedResponse[RuleStoreResponse]:
        """Get paginated list of rules."""
        try:
            query = self.db.query(rules_store)
            if not include_deleted:
                query = query.filter(rules_store.is_deleted == False)

            rules, total = paginate_query(query, pagination)

            rule_responses = [RuleStoreResponse.from_orm(rule) for rule in rules]

            return PaginatedResponse.create(rule_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get rules: {str(e)}")

    def update_rule(self, rule_id: UUID, rule_data: RuleStoreUpdate, updated_by: str) -> RuleStoreResponse:
        """Update an existing rule."""
        try:
            rule = self.db.query(rules_store).filter(rules_store.id == rule_id).first()
            if not rule:
                raise HTTPException(status_code=404, detail="Rule not found")

            update_data = rule_data.dict(exclude_unset=True, exclude={'rule'})
            for field, value in update_data.items():
                setattr(rule, field, value)

            if rule_data.rule:
                rule.rule = rule_data.rule.dict()

            # Update version and metadata
            rule.version += 1
            rule.updated_by = updated_by
            rule.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(rule)

            return RuleStoreResponse.from_orm(rule)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update rule: {str(e)}")

    def delete_rule(self, rule_id: UUID, updated_by: str) -> Dict[str, str]:
        """Soft delete a rule."""
        try:
            rule = self.db.query(rules_store).filter(rules_store.id == rule_id).first()
            if not rule:
                raise HTTPException(status_code=404, detail="Rule not found")

            # Soft delete
            rule.is_deleted = True
            rule.deleted_at = datetime.now()
            rule.updated_by = updated_by

            self.db.commit()

            return {"status": "success", "message": "Rule deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete rule: {str(e)}")

    def get_legacy_rules(self) -> List[LegacyRuleResponse]:
        """Get legacy rules with metric equations."""
        try:
            # Import metrics models from metrics module
            from ..metrics.models import Metrics_numeric, Metrics_string, Metrics_boolean

            # Fetch all metrics and store them in dictionaries for quick access
            metrics_dict_numeric = {metric.metric_name: metric for metric in self.db.query(Metrics_numeric).all()}
            metrics_dict_string = {metric.metric_name: metric for metric in self.db.query(Metrics_string).all()}
            metrics_dict_boolean = {metric.metric_name: metric for metric in self.db.query(Metrics_boolean).all()}

            # Fetch all rules
            rules = self.db.query(Rules).all()

            # Build metric equations for each rule
            metric_equations = []
            for rule in rules:
                equation = {}

                # Add numeric metrics
                for metric_name, metric in metrics_dict_numeric.items():
                    equation[metric_name] = {
                        "value": metric.metric_value,
                        "operation": metric.metric_operation
                    }

                # Add string metrics
                for metric_name, metric in metrics_dict_string.items():
                    equation[metric_name] = {
                        "value": metric.metric_value,
                        "operation": metric.metric_operation
                    }

                # Add boolean metrics
                for metric_name, metric in metrics_dict_boolean.items():
                    equation[metric_name] = {
                        "value": metric.metric_value,
                        "operation": metric.metric_operation
                    }

                metric_equations.append(equation)

            # Build response data
            data = []
            for i, rule in enumerate(rules):
                data.append(LegacyRuleResponse(
                    rule_code=rule.rule_code,
                    rule_name=rule.rule_name,
                    rule_description=rule.rule_description,
                    rule_status=rule.rule_status,
                    rule_type=rule.rule_type,
                    fraud_type=rule.fraud_type,
                    rule_severity=rule.rule_severity,
                    metric_equation=metric_equations[i] if i < len(metric_equations) else {}
                ))

            return data
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get legacy rules: {str(e)}")

    def update_legacy_rule(self, rule_code: str, rule_data: LegacyRuleUpdate, updated_by: str) -> Dict[str, str]:
        """Update a legacy rule."""
        try:
            existing_rule = self.db.query(Rules).filter(Rules.rule_code == rule_code).first()
            if not existing_rule:
                raise HTTPException(status_code=404, detail="Rule not found")

            existing_rule.rule_severity = rule_data.severity
            existing_rule.rule_status = rule_data.active_status
            existing_rule.last_updated = datetime.now()
            existing_rule.last_updated_by = updated_by

            self.db.commit()

            return {"message": "Rule updated successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update legacy rule: {str(e)}")

    def create_legacy_rules(self, rules_data: RuleListCreate) -> Dict[str, str]:
        """Create legacy rules (bulk operation)."""
        try:
            # Delete existing rules
            self.db.query(Rules).delete()

            # Add new rules
            for rule in rules_data.rules:
                db_rule = Rules(
                    rule_code=rule.rule_code,
                    rule_name=rule.rule_name,
                    rule_description=rule.rule_description,
                    rule_status=rule.rule_status,
                    rule_type=rule.rule_type,
                    fraud_type=rule.fraud_type,
                    rule_severity=rule.rule_severity,
                    last_updated=datetime.now(),
                    last_updated_by="ADMIN"
                )
                self.db.add(db_rule)

            self.db.commit()

            return {"message": "Rules added successfully"}
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create legacy rules: {str(e)}")

    def get_rules_config(self) -> RulesConfigResponse:
        """Get rules configuration."""
        try:
            rules = self.db.query(Rules).first()
            if not rules:
                # Return default configuration if no rules exist
                return RulesConfigResponse()

            # Map the rules attributes to the response schema
            # This would need to be implemented based on the actual Rules model structure
            return RulesConfigResponse(
                max_cx_pii_score=getattr(rules, 'max_cx_pii_score', None),
                max_txn_amt_avg=getattr(rules, 'max_txn_amt_avg', None),
                # Add other fields as needed
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get rules config: {str(e)}")

    def update_rules_config(self, config_data: RulesConfigCreate) -> Dict[str, str]:
        """Update rules configuration."""
        try:
            # Get or create rules
            db_rules = self.db.query(Rules).first()
            if not db_rules:
                db_rules = Rules()
                self.db.add(db_rules)

            # Update rules with new values
            for field, value in config_data.dict().items():
                if value is not None:
                    setattr(db_rules, f"max_{field}", value)

            self.db.commit()

            return {"status": "success", "message": "Rules configuration updated successfully"}
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update rules config: {str(e)}")