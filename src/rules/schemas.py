# rules schemas - Pydantic models for rules module
from pydantic import BaseModel, ConfigDict, Field, validator
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from datetime import datetime

# Rule Condition Schema
class RuleCondition(BaseModel):
    table: str
    condition: str
    operator: str
    value: Any

    @validator('operator')
    def validate_operator(cls, v):
        valid_operators = ['>', '<', '>=', '<=', '==', '!=', 'in', 'not in', 'and', 'or']
        if v not in valid_operators:
            raise ValueError(f'Invalid operator. Must be one of {valid_operators}')
        return v

# Rule Group Schema
class RuleGroup(BaseModel):
    operator: str = Field(..., description="Logical operator (and/or)")
    conditions: List[Union[RuleCondition, 'RuleGroup']]

    @validator('operator')
    def validate_operator(cls, v):
        valid_operators = ['and', 'or']
        if v not in valid_operators:
            raise ValueError(f'Invalid operator. Must be one of {valid_operators}')
        return v

# Enable forward references
RuleGroup.model_rebuild()

# Base Rule Schemas
class RuleStoreBase(BaseModel):
    code: str = Field(..., min_length=1, max_length=100)
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    type: str = Field(..., min_length=1, max_length=50)
    severity: str = Field(..., min_length=1, max_length=50)
    fraud_type: str = Field(..., min_length=1, max_length=50)

class RuleStoreCreate(RuleStoreBase):
    rule: RuleGroup
    status: bool = True
    is_active: bool = True

class RuleStoreUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    status: Optional[bool] = None
    type: Optional[str] = Field(None, min_length=1, max_length=50)
    severity: Optional[str] = Field(None, min_length=1, max_length=50)
    fraud_type: Optional[str] = Field(None, min_length=1, max_length=50)
    rule: Optional[RuleGroup] = None
    is_active: Optional[bool] = None

class RuleStoreResponse(RuleStoreBase):
    id: UUID
    status: bool
    rule: Dict[str, Any]
    version: int
    is_active: bool
    is_deleted: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    deleted_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Legacy Rule Schemas
class LegacyRuleCreate(BaseModel):
    rule_code: str
    rule_name: str
    rule_description: str
    rule_status: bool
    rule_type: str
    rule_severity: str
    fraud_type: str

class LegacyRuleUpdate(BaseModel):
    severity: str
    active_status: bool

class LegacyRuleResponse(BaseModel):
    rule_code: str
    rule_name: str
    rule_description: str
    rule_status: bool
    rule_type: str
    fraud_type: str
    rule_severity: str
    metric_equation: Optional[Dict[str, Any]] = None

# Rule List Schemas
class RuleListCreate(BaseModel):
    rules: List[LegacyRuleCreate]

class LLMRuleUpdate(BaseModel):
    severity: str
    active_status: bool

# Metric Update Schemas
class MetricUpdateNumeric(BaseModel):
    value: float
    operation: str

class MetricUpdateString(BaseModel):
    value: str
    operation: str

class MetricUpdateBoolean(BaseModel):
    value: bool
    operation: str

# Rules Configuration Schema (for merchant rules)
class RulesConfigCreate(BaseModel):
    max_cx_pii_score: Optional[int] = None
    max_txn_amt_avg: Optional[int] = None
    max_cancelled_txn_cnt_pct: Optional[int] = None
    max_card_num_density: Optional[int] = None
    max_curr_diversity_score: Optional[int] = None
    max_customer_density: Optional[int] = None
    max_cx_complaint_txn_pct: Optional[int] = None
    max_day_cos: Optional[int] = None
    max_day_sin: Optional[int] = None
    max_device_id_density: Optional[int] = None
    max_failed_txn_cnt_pct: Optional[int] = None
    max_hour_cos: Optional[int] = None
    max_hour_sin: Optional[int] = None
    max_hrs_since_last_transaction: Optional[int] = None
    max_interntational_txn_cnt_pct: Optional[int] = None
    max_invoice_and_txn_amt_diff_pct: Optional[int] = None
    max_ip_density: Optional[int] = None

class RulesConfigResponse(BaseModel):
    max_cx_pii_score: Optional[int] = None
    max_txn_amt_avg: Optional[int] = None
    max_cancelled_txn_cnt_pct: Optional[int] = None
    max_card_num_density: Optional[int] = None
    max_curr_diversity_score: Optional[int] = None
    max_customer_density: Optional[int] = None
    max_cx_complaint_txn_pct: Optional[int] = None
    max_day_cos: Optional[int] = None
    max_day_sin: Optional[int] = None
    max_device_id_density: Optional[int] = None
    max_failed_txn_cnt_pct: Optional[int] = None
    max_hour_cos: Optional[int] = None
    max_hour_sin: Optional[int] = None
    max_hrs_since_last_transaction: Optional[int] = None
    max_interntational_txn_cnt_pct: Optional[int] = None
    max_invoice_and_txn_amt_diff_pct: Optional[int] = None
    max_ip_density: Optional[int] = None

# Response Schemas
class RulesListResponse(BaseModel):
    data: List[RuleStoreResponse]

class LegacyRulesListResponse(BaseModel):
    data: List[LegacyRuleResponse]

class RulesResponse(BaseModel):
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None