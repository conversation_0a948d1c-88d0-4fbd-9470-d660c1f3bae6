# rules models - SQLAlchemy models for rules module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, JSON, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Core Rules Store
class rules_store(Base):
    __tablename__ = 'rules_store'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(100), nullable=False, unique=True)
    name = Column(String(255), nullable=False)
    description = Column(String(1000), nullable=True)
    status = Column(Boolean, default=True)
    type = Column(String(50), nullable=False)
    severity = Column(String(50), nullable=False)
    fraud_type = Column(String(50), nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(100), nullable=True)
    created_by = Column(String(100), nullable=True)
    rule = Column(JSONB, nullable=False)  # Rule definition in JSON format
    version = Column(Integer, default=1)  # Version tracking for rule updates
    is_active = Column(Boolean, default=True)  # For enabling/disabling rules
    is_deleted = Column(Boolean, default=False)  # For tracking deletion status
    deleted_at = Column(DateTime, nullable=True)  # Track when the rule was soft deleted

# Legacy Rules Model
class Rules(Base):
    __tablename__ = 'rules'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    rule_code = Column(String)
    rule_name = Column(String)
    rule_description = Column(String)
    rule_status = Column(Boolean)
    rule_type = Column(String)
    rule_severity = Column(String)
    fraud_type = Column(String)
    metric_equation = Column(JSON)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

# Rules List Model
class RulesList(Base):
    __tablename__ = 'rules_list'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    rule_number = Column(Integer)
    rule_name = Column(String(255))
    rule_description = Column(String(255))
    rule_status = Column(Boolean)
    rule_type = Column(String(255))
    rule_threshold = Column(Float)
    rule_severity = Column(String(255))
    last_updated = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

# LLM Red Flags Status
class LLMredFlagsStatus(Base):
    __tablename__ = 'llm_red_flags_status'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    rule_number = Column(Integer)
    rule_name = Column(String(255))
    rule_description = Column(String(255))
    status = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

# Rule Metric Models
class rule_metric_transactions(Base):
    __tablename__ = 'rule_metric_transactions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    ip_density_7d = Column(Integer)
    cx_city = Column(String)
    pct_failed_txn_amt_7d = Column(String)
    created_at = Column(DateTime, default=datetime.now)

class rule_metric_communications(Base):
    __tablename__ = 'rule_metric_communications'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    is_voice_mismatch = Column(Boolean)
    neg_intent_score = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

class rule_metric_network(Base):
    __tablename__ = 'rule_metric_network'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    number_connected_entites = Column(Integer)
    number_director = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)

class rule_metric_legal_and_regulatory(Base):
    __tablename__ = 'rule_metric_legal_and_regulatory'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    mer_employee_turnover_30d = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

class rule_metric_digital_footprint(Base):
    __tablename__ = 'digital_footprint'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    num_neg_review_30d = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)