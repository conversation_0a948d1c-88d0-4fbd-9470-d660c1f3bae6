# rules constants - module-specific constants for rules

# Rule types
RULE_TYPE_FRAUD = "fraud"
RULE_TYPE_COMPLIANCE = "compliance"
RULE_TYPE_RISK = "risk"
RULE_TYPE_OPERATIONAL = "operational"
RULE_TYPE_BEHAVIORAL = "behavioral"
RULE_TYPE_FINANCIAL = "financial"

RULE_TYPES = [
    RULE_TYPE_FRAUD,
    RULE_TYPE_COMPLIANCE,
    RULE_TYPE_RISK,
    RULE_TYPE_OPERATIONAL,
    RULE_TYPE_BEHAVIORAL,
    RULE_TYPE_FINANCIAL
]

# Rule severities
RULE_SEVERITY_LOW = "low"
RULE_SEVERITY_MEDIUM = "medium"
RULE_SEVERITY_HIGH = "high"
RULE_SEVERITY_CRITICAL = "critical"

RULE_SEVERITIES = [
    RULE_SEVERITY_LOW,
    RULE_SEVERITY_MEDIUM,
    RULE_SEVERITY_HIGH,
    RULE_SEVERITY_CRITICAL
]

# Fraud types
FRAUD_TYPE_PAYMENT = "payment"
FRAUD_TYPE_IDENTITY = "identity"
FRAUD_TYPE_ACCOUNT = "account"
FRAUD_TYPE_TRANSACTION = "transaction"
FRAUD_TYPE_MERCHANT = "merchant"
FRAUD_TYPE_CHARGEBACK = "chargeback"
FRAUD_TYPE_MONEY_LAUNDERING = "money_laundering"
FRAUD_TYPE_SYNTHETIC = "synthetic"

FRAUD_TYPES = [
    FRAUD_TYPE_PAYMENT,
    FRAUD_TYPE_IDENTITY,
    FRAUD_TYPE_ACCOUNT,
    FRAUD_TYPE_TRANSACTION,
    FRAUD_TYPE_MERCHANT,
    FRAUD_TYPE_CHARGEBACK,
    FRAUD_TYPE_MONEY_LAUNDERING,
    FRAUD_TYPE_SYNTHETIC
]

# Rule operators
RULE_OPERATOR_GREATER = ">"
RULE_OPERATOR_LESS = "<"
RULE_OPERATOR_GREATER_EQUAL = ">="
RULE_OPERATOR_LESS_EQUAL = "<="
RULE_OPERATOR_EQUAL = "=="
RULE_OPERATOR_NOT_EQUAL = "!="
RULE_OPERATOR_IN = "in"
RULE_OPERATOR_NOT_IN = "not in"
RULE_OPERATOR_AND = "and"
RULE_OPERATOR_OR = "or"

RULE_OPERATORS = [
    RULE_OPERATOR_GREATER,
    RULE_OPERATOR_LESS,
    RULE_OPERATOR_GREATER_EQUAL,
    RULE_OPERATOR_LESS_EQUAL,
    RULE_OPERATOR_EQUAL,
    RULE_OPERATOR_NOT_EQUAL,
    RULE_OPERATOR_IN,
    RULE_OPERATOR_NOT_IN,
    RULE_OPERATOR_AND,
    RULE_OPERATOR_OR
]

# Logical operators
LOGICAL_OPERATOR_AND = "and"
LOGICAL_OPERATOR_OR = "or"

LOGICAL_OPERATORS = [
    LOGICAL_OPERATOR_AND,
    LOGICAL_OPERATOR_OR
]

# Rule statuses
RULE_STATUS_ACTIVE = True
RULE_STATUS_INACTIVE = False

# Rule execution modes
RULE_EXECUTION_REAL_TIME = "real_time"
RULE_EXECUTION_BATCH = "batch"
RULE_EXECUTION_SCHEDULED = "scheduled"

RULE_EXECUTION_MODES = [
    RULE_EXECUTION_REAL_TIME,
    RULE_EXECUTION_BATCH,
    RULE_EXECUTION_SCHEDULED
]

# Rule actions
RULE_ACTION_BLOCK = "block"
RULE_ACTION_FLAG = "flag"
RULE_ACTION_REVIEW = "review"
RULE_ACTION_ALERT = "alert"
RULE_ACTION_LOG = "log"

RULE_ACTIONS = [
    RULE_ACTION_BLOCK,
    RULE_ACTION_FLAG,
    RULE_ACTION_REVIEW,
    RULE_ACTION_ALERT,
    RULE_ACTION_LOG
]

# Default values
DEFAULT_RULE_STATUS = RULE_STATUS_ACTIVE
DEFAULT_RULE_SEVERITY = RULE_SEVERITY_MEDIUM
DEFAULT_RULE_TYPE = RULE_TYPE_FRAUD
DEFAULT_FRAUD_TYPE = FRAUD_TYPE_PAYMENT
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100

# Validation limits
MAX_RULE_CODE_LENGTH = 100
MAX_RULE_NAME_LENGTH = 255
MAX_RULE_DESCRIPTION_LENGTH = 1000
MAX_RULE_CONDITIONS = 50
MAX_RULE_NESTING_DEPTH = 10

# Rule versioning
INITIAL_RULE_VERSION = 1
MAX_RULE_VERSIONS = 100

# Rule testing
MAX_TEST_DATA_SIZE = 1000
TEST_TIMEOUT_SECONDS = 30