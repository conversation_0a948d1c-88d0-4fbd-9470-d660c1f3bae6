# rules router - rules management endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    RuleStoreCreate, RuleStoreResponse, RuleStoreUpdate,
    LegacyRuleUpdate, LegacyRuleResponse, RuleListCreate,
    RulesConfigCreate, RulesConfigResponse,
    RulesListResponse, LegacyRulesListResponse, RulesResponse
)
from .service import RulesService

router = APIRouter()

def get_rules_service(db: Session = Depends(get_db)) -> RulesService:
    return RulesService(db)

# Modern Rules Store Endpoints
@router.post("/", response_model=RuleStoreResponse, status_code=201)
async def create_rule(
    rule: RuleStoreCreate,
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new rule in the rules store."""
    return rules_service.create_rule(rule, current_user.email)

@router.get("/", response_model=RulesListResponse)
async def get_rules(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    include_deleted: bool = Query(False),
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of rules."""
    pagination = PaginationParams(page=page, size=size)
    return rules_service.get_rules(pagination, include_deleted)

@router.get("/{rule_id}", response_model=RuleStoreResponse)
async def get_rule(
    rule_id: UUID = Path(..., description="The UUID of the rule"),
    include_deleted: bool = Query(False),
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Get rule by ID."""
    return rules_service.get_rule_by_id(rule_id, include_deleted)

@router.put("/{rule_id}", response_model=RuleStoreResponse)
async def update_rule(
    rule_id: UUID = Path(..., description="The UUID of the rule"),
    rule_update: RuleStoreUpdate = None,
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Update an existing rule."""
    return rules_service.update_rule(rule_id, rule_update, current_user.email)

@router.delete("/{rule_id}", status_code=204)
async def delete_rule(
    rule_id: UUID = Path(..., description="The UUID of the rule"),
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Delete a rule (soft delete)."""
    rules_service.delete_rule(rule_id, current_user.email)
    return None

# Legacy Rules Endpoints (for backward compatibility)
@router.get("/legacy/rules", response_model=LegacyRulesListResponse)
async def get_legacy_rules(
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Get legacy rules with metric equations."""
    data = rules_service.get_legacy_rules()
    return {"data": data}

@router.post("/legacy/rules", response_model=RulesResponse, include_in_schema=False)
async def create_legacy_rules(
    rules: RuleListCreate,
    rules_service: RulesService = Depends(get_rules_service)
):
    """Create legacy rules (bulk operation) - Hidden from schema."""
    return rules_service.create_legacy_rules(rules)

@router.post("/legacy/{investigator_email}/{rule_code}/update_rule", response_model=RulesResponse)
async def update_legacy_rule(
    investigator_email: str = Path(..., description="Email of the investigator"),
    rule_code: str = Path(..., description="Rule code to update"),
    rule: LegacyRuleUpdate = None,
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Update a legacy rule."""
    return rules_service.update_legacy_rule(rule_code, rule, investigator_email)

# Rules Configuration Endpoints
@router.get("/config/rules", response_model=RulesConfigResponse)
async def get_rules_config(
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Get rules configuration."""
    return rules_service.get_rules_config()

@router.post("/config/update_rules", response_model=RulesResponse)
async def update_rules_config(
    rules: RulesConfigCreate,
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Update rules configuration."""
    return rules_service.update_rules_config(rules)

# Metrics Management Endpoints (Legacy compatibility)
@router.get("/legacy/metrics")
async def get_legacy_metrics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get legacy metrics for rules."""
    # Import metrics models from metrics module
    from ..metrics.models import Metrics_numeric, Metrics_string, Metrics_boolean

    metrics_numeric = db.query(Metrics_numeric).all()
    metrics_string = db.query(Metrics_string).all()
    metrics_boolean = db.query(Metrics_boolean).all()

    # Use a set to track unique metric names
    unique_metrics = {}

    # Add metrics while checking for duplicates
    for metric in metrics_numeric:
        unique_metrics[metric.metric_name] = {
            "metric_name": metric.metric_name,
            "metric_description": metric.metric_description,
        }

    for metric in metrics_string:
        unique_metrics[metric.metric_name] = {
            "metric_name": metric.metric_name,
            "metric_description": metric.metric_description,
        }

    for metric in metrics_boolean:
        unique_metrics[metric.metric_name] = {
            "metric_name": metric.metric_name,
            "metric_description": metric.metric_description,
        }

    # Convert to list
    metrics_list = list(unique_metrics.values())

    return {"data": metrics_list}

# Rule Validation Endpoints
@router.post("/validate")
async def validate_rule(
    rule: RuleStoreCreate,
    current_user: User = Depends(get_current_user)
):
    """Validate a rule structure without creating it."""
    try:
        # Basic validation is handled by Pydantic
        # Additional custom validation can be added here
        return {
            "status": "valid",
            "message": "Rule structure is valid",
            "rule_code": rule.code
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Rule validation failed: {str(e)}")

# Rule Execution/Testing Endpoints
@router.post("/{rule_id}/test")
async def test_rule(
    rule_id: UUID = Path(..., description="The UUID of the rule"),
    test_data: dict = None,
    rules_service: RulesService = Depends(get_rules_service),
    current_user: User = Depends(get_current_user)
):
    """Test a rule against sample data."""
    # This would implement rule testing logic
    rule = rules_service.get_rule_by_id(rule_id)

    return {
        "status": "tested",
        "rule_id": str(rule_id),
        "rule_code": rule.code,
        "test_result": "Rule testing functionality will be implemented",
        "message": "Rule test completed"
    }