# rules exceptions - module-specific errors for rules
from fastapi import HTTPException, status

class RuleNotFoundError(HTTPException):
    def __init__(self, rule_id: str = None):
        detail = f"Rule {rule_id} not found" if rule_id else "Rule not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class RuleAlreadyExistsError(HTTPException):
    def __init__(self, rule_code: str = None):
        detail = f"Rule with code '{rule_code}' already exists" if rule_code else "Rule already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidRuleDataError(HTTPException):
    def __init__(self, detail: str = "Invalid rule data"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class RuleValidationError(HTTPException):
    def __init__(self, detail: str = "Rule validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidRuleOperatorError(HTTPException):
    def __init__(self, operator: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid rule operator: {operator}"
        )

class InvalidRuleTypeError(HTTPException):
    def __init__(self, rule_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid rule type: {rule_type}"
        )

class InvalidRuleSeverityError(HTTPException):
    def __init__(self, severity: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid rule severity: {severity}"
        )

class InvalidFraudTypeError(HTTPException):
    def __init__(self, fraud_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid fraud type: {fraud_type}"
        )

class RuleExecutionError(HTTPException):
    def __init__(self, detail: str = "Failed to execute rule"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RuleCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create rule"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RuleUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update rule"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RuleDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete rule"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RuleAccessDeniedError(HTTPException):
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to rule data"
        )

class RuleVersionError(HTTPException):
    def __init__(self, detail: str = "Rule version error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class RuleTestingError(HTTPException):
    def __init__(self, detail: str = "Rule testing failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RuleConditionError(HTTPException):
    def __init__(self, detail: str = "Invalid rule condition"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class RuleComplexityError(HTTPException):
    def __init__(self, detail: str = "Rule is too complex"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class RuleConfigurationError(HTTPException):
    def __init__(self, detail: str = "Rule configuration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class LegacyRuleError(HTTPException):
    def __init__(self, detail: str = "Legacy rule operation failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)