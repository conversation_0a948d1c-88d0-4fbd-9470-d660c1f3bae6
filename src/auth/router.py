# Auth router - authentication endpoints
from fastapi import <PERSON><PERSON>outer, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from .schemas import UserCreate, UserResponse, UserAuthenticate, TokenResponse, OTPRequest, PasswordResetRequest
from .service import AuthService
from .dependencies import get_current_user
from .models import User

router = APIRouter()

def get_auth_service(db: Session = Depends(get_db)) -> AuthService:
    return AuthService(db)

@router.post("/send-otp")
async def send_otp(
    request: OTPRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Generate and send a One-Time Password (OTP) to the provided email address.
    """
    try:
        # Import here to avoid circular imports
        from app.utils.otp import generate_otp
        from app.utils.email_utils import send_email

        otp = generate_otp()
        auth_service.store_otp(request.email, otp)

        # Send OTP via email
        subject = "Your OTP for Zeus Authentication"
        body = f"Your OTP is: {otp}. This OTP will expire in 10 minutes."
        send_email(request.email, subject, body)

        return {"message": "OTP sent successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to send OTP: {str(e)}")

@router.post("/register", response_model=UserResponse)
async def register_user(
    user: UserCreate,
    otp: str,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Register a new user with email verification via OTP.
    """
    db_user = auth_service.register_user(user, otp)
    return UserResponse(email=db_user.email)

@router.post("/login", response_model=TokenResponse)
async def login_user(
    user: UserAuthenticate,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Authenticate user and generate JWT access token.
    """
    return auth_service.login_user(user)

@router.post("/forgot-password")
async def forgot_password(
    request: OTPRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Send OTP for password reset.
    """
    try:
        # Import here to avoid circular imports
        from app.utils.otp import generate_otp
        from app.utils.email_utils import send_email

        # Check if user exists
        from .models import User
        user = auth_service.db.query(User).filter(User.email == request.email).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        otp = generate_otp()
        auth_service.store_otp(request.email, otp)

        # Send OTP via email
        subject = "Password Reset OTP for Zeus"
        body = f"Your password reset OTP is: {otp}. This OTP will expire in 10 minutes."
        send_email(request.email, subject, body)

        return {"message": "Password reset OTP sent successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to send password reset OTP: {str(e)}")

@router.post("/reset-password")
async def reset_password(
    request: PasswordResetRequest,
    auth_service: AuthService = Depends(get_auth_service)
):
    """
    Reset user password with OTP verification.
    """
    return auth_service.reset_password(
        request.email,
        request.otp,
        request.new_password,
        request.confirm_new_password
    )

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """
    Get current authenticated user information.
    """
    return UserResponse(email=current_user.email)

@router.get("/test")
async def test_auth(db: Session = Depends(get_db)):
    """
    Test endpoint to verify authentication system functionality.
    WARNING: This should be disabled in production.
    """
    from .models import User
    db_users = db.query(User).all()
    return {"message": f"Found {len(db_users)} users in the system"}
