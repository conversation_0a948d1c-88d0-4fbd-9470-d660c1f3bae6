# Auth config - local configurations for auth module
import os
from .constants import DEFAULT_ACCESS_TOKEN_EXPIRE_MINUTES, OTP_EXPIRE_MINUTES

# JWT Configuration
JWT_SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", DEFAULT_ACCESS_TOKEN_EXPIRE_MINUTES))

# OTP Configuration
OTP_EXPIRY_MINUTES = int(os.getenv("OTP_EXPIRY_MINUTES", OTP_EXPIRE_MINUTES))

# Email Configuration for OTP
EMAIL_FROM = os.getenv("EMAIL_FROM", "<EMAIL>")
EMAIL_SUBJECT_PREFIX = os.getenv("EMAIL_SUBJECT_PREFIX", "[<PERSON>] ")
