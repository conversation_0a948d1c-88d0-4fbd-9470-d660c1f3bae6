# Auth service - business logic for auth module
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime, timedelta
from fastapi import HTTPException, status
from .models import User, Otp
from .schemas import UserCreate, UserAuthenticate, TokenResponse
from .utils import get_password_hash, verify_password, create_access_token, authenticate_user
from ..config import ACCESS_TOKEN_EXPIRE_MINUTES

class AuthService:
    def __init__(self, db: Session):
        self.db = db

    def register_user(self, user_data: UserCreate, otp: str) -> User:
        """Register a new user with OTP verification."""
        # Verify OTP validity
        stored_otp_record = self.db.query(Otp).filter(Otp.email == user_data.email).first()
        if not stored_otp_record or stored_otp_record.otp != otp:
            raise HTTPException(status_code=400, detail="Invalid or expired OTP")

        # Check OTP expiration
        if stored_otp_record.expires_at < datetime.now():
            raise HTTPException(status_code=400, detail="OTP expired")

        # Check if email already exists
        if self.db.query(User).filter(User.email == user_data.email).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Check if username already exists
        if self.db.query(User).filter(User.username == user_data.username).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )

        # Create new user
        hashed_password = get_password_hash(user_data.password)
        db_user = User(
            email=user_data.email,
            username=user_data.username,
            hashed_password=hashed_password
        )
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)

        # Clean up OTP
        self.db.delete(stored_otp_record)
        self.db.commit()

        return db_user

    def login_user(self, user_data: UserAuthenticate) -> TokenResponse:
        """Authenticate user and return access token."""
        user = authenticate_user(self.db, user_data.email, user_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.email},
            expires_delta=access_token_expires
        )

        return TokenResponse(access_token=access_token, token_type="bearer")

    def store_otp(self, email: str, otp: str, expires_minutes: int = 10):
        """Store OTP for email verification."""
        expires_at = datetime.now() + timedelta(minutes=expires_minutes)

        # Remove existing OTP for this email
        existing_otp = self.db.query(Otp).filter(Otp.email == email).first()
        if existing_otp:
            self.db.delete(existing_otp)

        # Create new OTP record
        otp_record = Otp(
            email=email,
            otp=otp,
            expires_at=expires_at
        )
        self.db.add(otp_record)
        self.db.commit()

    def reset_password(self, email: str, otp: str, new_password: str, confirm_password: str):
        """Reset user password with OTP verification."""
        # Verify OTP
        stored_otp_record = self.db.query(Otp).filter(Otp.email == email).first()
        if not stored_otp_record or stored_otp_record.otp != otp:
            raise HTTPException(status_code=400, detail="Invalid or expired OTP")

        # Check OTP expiration
        if stored_otp_record.expires_at < datetime.now():
            raise HTTPException(status_code=400, detail="OTP expired")

        # Verify password confirmation
        if new_password != confirm_password:
            raise HTTPException(status_code=400, detail="New password and confirm new password do not match")

        # Find user and update password
        user = self.db.query(User).filter(User.email == email).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        user.hashed_password = get_password_hash(new_password)
        self.db.commit()

        # Clean up OTP
        self.db.delete(stored_otp_record)
        self.db.commit()

        return {"message": "Password reset successfully"}
