# red_flags constants - module-specific constants for red_flags

# Red flag severities
RED_FLAG_SEVERITY_LOW = "low"
RED_FLAG_SEVERITY_MEDIUM = "medium"
RED_FLAG_SEVERITY_HIGH = "high"
RED_FLAG_SEVERITY_CRITICAL = "critical"

RED_FLAG_SEVERITIES = [
    RED_FLAG_SEVERITY_LOW,
    RED_FLAG_SEVERITY_MEDIUM,
    RED_FLAG_SEVERITY_HIGH,
    RED_FLAG_SEVERITY_CRITICAL
]

# Red flag types
RED_FLAG_TYPE_TRANSACTIONS = "transactions"
RED_FLAG_TYPE_COMPLIANCE = "compliance"
RED_FLAG_TYPE_NETWORK = "network"
RED_FLAG_TYPE_FINANCIAL = "financial"
RED_FLAG_TYPE_BEHAVIORAL = "behavioral"
RED_FLAG_TYPE_DIGITAL_FOOTPRINT = "digital_footprint"
RED_FLAG_TYPE_ML = "transactions ML"
RED_FLAG_TYPE_RULE_BASED = "rule_based"

RED_FLAG_TYPES = [
    RED_FLAG_TYPE_TRANSACTIONS,
    RED_FLAG_TYPE_COMPLIANCE,
    RED_FLAG_TYPE_NETWORK,
    RED_FLAG_TYPE_FINANCIAL,
    RED_FLAG_TYPE_BEHAVIORAL,
    RED_FLAG_TYPE_DIGITAL_FOOTPRINT,
    RED_FLAG_TYPE_ML,
    RED_FLAG_TYPE_RULE_BASED
]

# Red flag categories
RED_FLAG_CATEGORY_FRAUD = "fraud"
RED_FLAG_CATEGORY_RISK = "risk"
RED_FLAG_CATEGORY_COMPLIANCE = "compliance"
RED_FLAG_CATEGORY_OPERATIONAL = "operational"
RED_FLAG_CATEGORY_FINANCIAL = "financial"
RED_FLAG_CATEGORY_BEHAVIORAL = "behavioral"
RED_FLAG_CATEGORY_TECHNICAL = "technical"

RED_FLAG_CATEGORIES = [
    RED_FLAG_CATEGORY_FRAUD,
    RED_FLAG_CATEGORY_RISK,
    RED_FLAG_CATEGORY_COMPLIANCE,
    RED_FLAG_CATEGORY_OPERATIONAL,
    RED_FLAG_CATEGORY_FINANCIAL,
    RED_FLAG_CATEGORY_BEHAVIORAL,
    RED_FLAG_CATEGORY_TECHNICAL
]

# Red flag processing types
PROCESSING_TYPE_ML_GENERATION = "ml_generation"
PROCESSING_TYPE_RULE_BASED = "rule_based"
PROCESSING_TYPE_BULK_PROCESSING = "bulk_processing"
PROCESSING_TYPE_DIGITAL_FOOTPRINT = "digital_footprint"

PROCESSING_TYPES = [
    PROCESSING_TYPE_ML_GENERATION,
    PROCESSING_TYPE_RULE_BASED,
    PROCESSING_TYPE_BULK_PROCESSING,
    PROCESSING_TYPE_DIGITAL_FOOTPRINT
]

# Red flag job statuses
JOB_STATUS_PENDING = "pending"
JOB_STATUS_RUNNING = "running"
JOB_STATUS_COMPLETED = "completed"
JOB_STATUS_FAILED = "failed"
JOB_STATUS_CANCELLED = "cancelled"

JOB_STATUSES = [
    JOB_STATUS_PENDING,
    JOB_STATUS_RUNNING,
    JOB_STATUS_COMPLETED,
    JOB_STATUS_FAILED,
    JOB_STATUS_CANCELLED
]

# Red flag template types
TEMPLATE_TYPE_MERCHANT = "merchant"
TEMPLATE_TYPE_CUSTOMER = "customer"
TEMPLATE_TYPE_TRANSACTION = "transaction"

TEMPLATE_TYPES = [
    TEMPLATE_TYPE_MERCHANT,
    TEMPLATE_TYPE_CUSTOMER,
    TEMPLATE_TYPE_TRANSACTION
]

# Digital footprint red flag types
DF_FLAG_DOMAIN = "domain"
DF_FLAG_CONTENT = "content"
DF_FLAG_POLICY = "policy"
DF_FLAG_PRICE = "price"
DF_FLAG_REVIEW = "review"

DF_FLAG_TYPES = [
    DF_FLAG_DOMAIN,
    DF_FLAG_CONTENT,
    DF_FLAG_POLICY,
    DF_FLAG_PRICE,
    DF_FLAG_REVIEW
]

# Importance levels
IMPORTANCE_LEVEL_LOW = 0.25
IMPORTANCE_LEVEL_MEDIUM = 0.5
IMPORTANCE_LEVEL_HIGH = 0.75
IMPORTANCE_LEVEL_CRITICAL = 1.0

# Risk score thresholds
RISK_SCORE_LOW_THRESHOLD = 0.3
RISK_SCORE_MEDIUM_THRESHOLD = 0.6
RISK_SCORE_HIGH_THRESHOLD = 0.8

# Default values
DEFAULT_RED_FLAG_SEVERITY = RED_FLAG_SEVERITY_MEDIUM
DEFAULT_RED_FLAG_TYPE = RED_FLAG_TYPE_TRANSACTIONS
DEFAULT_RED_FLAG_CATEGORY = RED_FLAG_CATEGORY_RISK
DEFAULT_IMPORTANCE = IMPORTANCE_LEVEL_MEDIUM
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100

# Processing limits
MAX_BULK_PROCESSING_SIZE = 1000
MAX_CONCURRENT_JOBS = 10
JOB_TIMEOUT_MINUTES = 60

# ML model thresholds
ML_CONFIDENCE_THRESHOLD = 0.7
ML_ANOMALY_THRESHOLD = 0.8

# Rule evaluation limits
MAX_RULE_CONDITIONS = 50
MAX_RULE_COMPLEXITY = 10

# Digital footprint analysis
DF_RISK_SCORE_WEIGHTS = {
    DF_FLAG_DOMAIN: 0.3,
    DF_FLAG_CONTENT: 0.25,
    DF_FLAG_POLICY: 0.2,
    DF_FLAG_PRICE: 0.15,
    DF_FLAG_REVIEW: 0.1
}

# Red flag retention
RED_FLAG_RETENTION_DAYS = 365
ARCHIVED_RED_FLAG_RETENTION_DAYS = 2555  # 7 years

# Notification thresholds
NOTIFICATION_HIGH_SEVERITY_COUNT = 5
NOTIFICATION_CRITICAL_SEVERITY_COUNT = 1
NOTIFICATION_MERCHANT_RISK_THRESHOLD = 0.8