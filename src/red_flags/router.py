# red_flags router - red flags management endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import datetime
from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    RedFlagCreate, RedFlagResponse, MerchantRedFlagCreate, MerchantRedFlagUpdate,
    MerchantRedFlagResponse, CustomerRedFlagCreate, CustomerRedFlagUpdate,
    CustomerRedFlagResponse, RedFlagProcessingRequest, RedFlagJobResponse,
    DigitalFootprintRedFlagCreate, DigitalFootprintRedFlagResponse,
    RedFlagStatistics, RedFlagSummary, RedFlagListResponse,
    MerchantRedFlagListResponse, CustomerRedFlagListResponse
)
from .service import RedFlagsService

router = APIRouter()

def get_red_flags_service(db: Session = Depends(get_db)) -> RedFlagsService:
    return RedFlagsService(db)

# Core Red Flags Endpoints
@router.post("/", response_model=RedFlagResponse, status_code=201)
async def create_red_flag(
    flag: RedFlagCreate,
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new red flag."""
    return red_flags_service.create_red_flag(flag)

@router.get("/merchant/{merchant_id}", response_model=RedFlagListResponse)
async def get_merchant_red_flags_core(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Get core red flags for a merchant."""
    pagination = PaginationParams(page=page, size=size)
    result = red_flags_service.get_red_flags_by_merchant(merchant_id, pagination)
    return {"data": result.items}

# Merchant Red Flags Endpoints
@router.post("/merchants/", response_model=MerchantRedFlagResponse, status_code=201)
async def create_merchant_red_flag(
    flag: MerchantRedFlagCreate,
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new merchant red flag."""
    return red_flags_service.create_merchant_red_flag(flag)

@router.get("/merchants/{merchant_id}", response_model=MerchantRedFlagListResponse)
async def get_merchant_red_flags(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    rule_type: Optional[str] = Query(None, description="Filter by rule type"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Get merchant red flags with rule information."""
    flags = red_flags_service.get_merchant_red_flags(merchant_id, rule_type)
    return {"data": flags}

@router.put("/merchants/{flag_id}", response_model=MerchantRedFlagResponse)
async def update_merchant_red_flag(
    flag_id: UUID = Path(..., description="The UUID of the red flag"),
    flag_data: MerchantRedFlagUpdate = None,
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Update a merchant red flag."""
    return red_flags_service.update_merchant_red_flag(flag_id, flag_data)

@router.delete("/merchants/{flag_id}")
async def delete_merchant_red_flag(
    flag_id: UUID = Path(..., description="The UUID of the red flag"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Delete a merchant red flag."""
    return red_flags_service.delete_merchant_red_flag(flag_id)

# Customer Red Flags Endpoints
@router.post("/customers/", response_model=CustomerRedFlagResponse, status_code=201)
async def create_customer_red_flag(
    flag: CustomerRedFlagCreate,
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new customer red flag."""
    return red_flags_service.create_customer_red_flag(flag)

@router.get("/customers/{customer_id}", response_model=CustomerRedFlagListResponse)
async def get_customer_red_flags(
    customer_id: UUID = Path(..., description="The UUID of the customer"),
    rule_type: Optional[str] = Query(None, description="Filter by rule type"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer red flags with rule information."""
    flags = red_flags_service.get_customer_red_flags(customer_id, rule_type)
    return {"data": flags}

# Red Flag Processing Endpoints
@router.post("/process")
async def process_red_flags(
    request: RedFlagProcessingRequest,
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Process red flags for specified merchants and rules."""
    return red_flags_service.process_red_flags(request)

@router.get("/jobs/{job_id}", response_model=RedFlagJobResponse)
async def get_processing_job_status(
    job_id: str = Path(..., description="The job ID"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Get the status of a red flag processing job."""
    return red_flags_service.get_processing_job_status(job_id)

# Red Flag Analytics Endpoints
@router.get("/statistics", response_model=RedFlagStatistics)
async def get_red_flag_statistics(
    start_date: Optional[datetime] = Query(None, description="Start date for statistics"),
    end_date: Optional[datetime] = Query(None, description="End date for statistics"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Get red flag statistics for a date range."""
    return red_flags_service.get_red_flag_statistics(start_date, end_date)

@router.get("/merchants/{merchant_id}/summary", response_model=RedFlagSummary)
async def get_merchant_red_flag_summary(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Get red flag summary for a merchant."""
    return red_flags_service.get_merchant_red_flag_summary(merchant_id)

# Digital Footprint Red Flags Endpoints
@router.post("/digital-footprint/", response_model=DigitalFootprintRedFlagResponse, status_code=201)
async def create_digital_footprint_red_flag(
    flag: DigitalFootprintRedFlagCreate,
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Create digital footprint red flags."""
    return red_flags_service.create_digital_footprint_red_flag(flag)

@router.get("/digital-footprint/{merchant_id}", response_model=Optional[DigitalFootprintRedFlagResponse])
async def get_digital_footprint_red_flags(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Get digital footprint red flags for a merchant."""
    return red_flags_service.get_digital_footprint_red_flags(merchant_id)

# Red Flag Generation Endpoints
@router.post("/generate/ml/{merchant_id}")
async def generate_ml_red_flags(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Generate ML-based red flags for a merchant."""
    return red_flags_service.generate_ml_red_flags(merchant_id)

@router.post("/generate/rules/{merchant_id}")
async def generate_rule_based_red_flags(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Generate rule-based red flags for a merchant."""
    return red_flags_service.generate_rule_based_red_flags(merchant_id)

# Rule-based Red Flag Queries
@router.get("/rule/{rule_code}/merchants", response_model=MerchantRedFlagListResponse)
async def get_merchant_red_flags_by_rule(
    rule_code: str = Path(..., description="The rule code"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Get merchant red flags by rule code."""
    flags = red_flags_service.get_red_flags_by_rule(rule_code, "merchant")
    return {"data": flags}

@router.get("/rule/{rule_code}/customers", response_model=CustomerRedFlagListResponse)
async def get_customer_red_flags_by_rule(
    rule_code: str = Path(..., description="The rule code"),
    red_flags_service: RedFlagsService = Depends(get_red_flags_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer red flags by rule code."""
    flags = red_flags_service.get_red_flags_by_rule(rule_code, "customer")
    return {"data": flags}