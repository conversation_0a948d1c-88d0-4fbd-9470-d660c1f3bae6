# red_flags schemas - Pydantic models for red_flags module
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime

# Base Red Flag Schemas
class RedFlagBase(BaseModel):
    merchant_id: UUID
    flag_type: str = Field(..., max_length=50)
    severity: str = Field(..., max_length=50)
    importance: float = Field(..., ge=0.0, le=1.0)
    text: str

class RedFlagCreate(RedFlagBase):
    timestamp: Optional[datetime] = None

class RedFlagResponse(RedFlagBase):
    id: UUID
    timestamp: datetime
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Merchant Red Flag Schemas
class MerchantRedFlagBase(BaseModel):
    merchant_id: UUID
    rule_code: str = Field(..., max_length=100)
    description: Optional[str] = None
    severity: Optional[str] = Field(None, max_length=50)
    metric_values: Optional[Dict[str, Any]] = None
    metric_data_timestamp: Optional[datetime] = None
    notes: Optional[str] = None
    category: Optional[str] = Field(None, max_length=50)

class MerchantRedFlagCreate(MerchantRedFlagBase):
    pass

class MerchantRedFlagUpdate(BaseModel):
    description: Optional[str] = None
    severity: Optional[str] = Field(None, max_length=50)
    metric_values: Optional[Dict[str, Any]] = None
    metric_data_timestamp: Optional[datetime] = None
    notes: Optional[str] = None
    category: Optional[str] = Field(None, max_length=50)

class MerchantRedFlagResponse(MerchantRedFlagBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    rule_name: Optional[str] = None
    rule_description: Optional[str] = None
    rule_type: Optional[str] = None
    rule_severity: Optional[str] = None
    rule_fraud_type: Optional[str] = None
    rule: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)

# Customer Red Flag Schemas
class CustomerRedFlagBase(BaseModel):
    customer_id: UUID
    rule_code: str = Field(..., max_length=100)
    description: Optional[str] = None
    severity: Optional[str] = Field(None, max_length=50)
    metric_values: Optional[Dict[str, Any]] = None
    metric_data_timestamp: Optional[datetime] = None
    notes: Optional[str] = None
    category: Optional[str] = Field(None, max_length=50)

class CustomerRedFlagCreate(CustomerRedFlagBase):
    pass

class CustomerRedFlagUpdate(BaseModel):
    description: Optional[str] = None
    severity: Optional[str] = Field(None, max_length=50)
    metric_values: Optional[Dict[str, Any]] = None
    metric_data_timestamp: Optional[datetime] = None
    notes: Optional[str] = None
    category: Optional[str] = Field(None, max_length=50)

class CustomerRedFlagResponse(CustomerRedFlagBase):
    id: UUID
    created_at: datetime
    updated_at: datetime
    rule_name: Optional[str] = None
    rule_description: Optional[str] = None
    rule_type: Optional[str] = None
    rule_severity: Optional[str] = None
    rule_fraud_type: Optional[str] = None
    rule: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)

# Red Flag Processing Schemas
class RedFlagProcessingRequest(BaseModel):
    merchant_ids: Optional[List[UUID]] = None
    rule_codes: Optional[List[str]] = None
    processing_type: str = Field(..., description="ml_generation, rule_based, or bulk_processing")

class RedFlagJobCreate(BaseModel):
    job_type: str = Field(..., max_length=50)
    merchant_id: Optional[UUID] = None
    rule_codes: Optional[List[str]] = None

class RedFlagJobResponse(BaseModel):
    id: UUID
    job_id: str
    job_type: str
    status: str
    merchant_id: Optional[UUID] = None
    rule_codes: Optional[List[str]] = None
    progress: float
    error_message: Optional[str] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Red Flag Template Schemas
class RedFlagTemplateBase(BaseModel):
    template_name: str = Field(..., max_length=255)
    template_type: str = Field(..., max_length=50)
    severity: str = Field(..., max_length=50)
    category: str = Field(..., max_length=50)
    description_template: str
    rule_conditions: Dict[str, Any]

class RedFlagTemplateCreate(RedFlagTemplateBase):
    is_active: bool = True

class RedFlagTemplateUpdate(BaseModel):
    template_name: Optional[str] = Field(None, max_length=255)
    template_type: Optional[str] = Field(None, max_length=50)
    severity: Optional[str] = Field(None, max_length=50)
    category: Optional[str] = Field(None, max_length=50)
    description_template: Optional[str] = None
    rule_conditions: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class RedFlagTemplateResponse(RedFlagTemplateBase):
    id: UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Digital Footprint Red Flag Schemas
class DigitalFootprintRedFlagBase(BaseModel):
    merchant_id: UUID
    domain_flags: Optional[Dict[str, Any]] = None
    content_flags: Optional[Dict[str, Any]] = None
    policy_flags: Optional[Dict[str, Any]] = None
    price_flags: Optional[Dict[str, Any]] = None
    review_flags: Optional[Dict[str, Any]] = None
    overall_risk_score: Optional[float] = Field(None, ge=0.0, le=1.0)

class DigitalFootprintRedFlagCreate(DigitalFootprintRedFlagBase):
    pass

class DigitalFootprintRedFlagResponse(DigitalFootprintRedFlagBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Response List Schemas
class RedFlagListResponse(BaseModel):
    data: List[RedFlagResponse]

class MerchantRedFlagListResponse(BaseModel):
    data: List[MerchantRedFlagResponse]

class CustomerRedFlagListResponse(BaseModel):
    data: List[CustomerRedFlagResponse]

class RedFlagTemplateListResponse(BaseModel):
    data: List[RedFlagTemplateResponse]

# Red Flag Statistics Schema
class RedFlagStatistics(BaseModel):
    total_flags: int
    high_severity_flags: int
    medium_severity_flags: int
    low_severity_flags: int
    ml_generated_flags: int
    rule_based_flags: int
    merchants_flagged: int
    customers_flagged: int
    flags_by_type: Dict[str, int]
    flags_by_category: Dict[str, int]

# Red Flag Summary Schema
class RedFlagSummary(BaseModel):
    merchant_id: UUID
    total_flags: int
    highest_severity: str
    latest_flag_date: Optional[datetime] = None
    flag_categories: List[str]
    risk_score: Optional[float] = None