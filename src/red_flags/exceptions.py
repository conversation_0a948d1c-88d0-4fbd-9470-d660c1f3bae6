# red_flags exceptions - module-specific errors for red_flags
from fastapi import HTTPException, status

class RedFlagNotFoundError(HTTPException):
    def __init__(self, flag_id: str = None):
        detail = f"Red flag {flag_id} not found" if flag_id else "Red flag not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class MerchantRedFlagNotFoundError(HTTPException):
    def __init__(self, flag_id: str = None):
        detail = f"Merchant red flag {flag_id} not found" if flag_id else "Merchant red flag not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerRedFlagNotFoundError(HTTPException):
    def __init__(self, flag_id: str = None):
        detail = f"Customer red flag {flag_id} not found" if flag_id else "Customer red flag not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class InvalidRedFlagDataError(HTTPException):
    def __init__(self, detail: str = "Invalid red flag data"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class RedFlagValidationError(HTTPException):
    def __init__(self, detail: str = "Red flag validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidRedFlagSeverityError(HTTPException):
    def __init__(self, severity: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid red flag severity: {severity}"
        )

class InvalidRedFlagTypeError(HTTPException):
    def __init__(self, flag_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid red flag type: {flag_type}"
        )

class InvalidRedFlagCategoryError(HTTPException):
    def __init__(self, category: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid red flag category: {category}"
        )

class RedFlagProcessingError(HTTPException):
    def __init__(self, detail: str = "Failed to process red flags"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RedFlagGenerationError(HTTPException):
    def __init__(self, detail: str = "Failed to generate red flags"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class MLRedFlagGenerationError(HTTPException):
    def __init__(self, detail: str = "Failed to generate ML-based red flags"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RuleBasedRedFlagGenerationError(HTTPException):
    def __init__(self, detail: str = "Failed to generate rule-based red flags"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RedFlagJobNotFoundError(HTTPException):
    def __init__(self, job_id: str = None):
        detail = f"Red flag job {job_id} not found" if job_id else "Red flag job not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class RedFlagJobError(HTTPException):
    def __init__(self, detail: str = "Red flag job error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RedFlagTemplateNotFoundError(HTTPException):
    def __init__(self, template_id: str = None):
        detail = f"Red flag template {template_id} not found" if template_id else "Red flag template not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class RedFlagTemplateError(HTTPException):
    def __init__(self, detail: str = "Red flag template error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DigitalFootprintRedFlagError(HTTPException):
    def __init__(self, detail: str = "Digital footprint red flag error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RedFlagAccessDeniedError(HTTPException):
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to red flag data"
        )

class RedFlagCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create red flag"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RedFlagUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update red flag"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RedFlagDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete red flag"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RedFlagStatisticsError(HTTPException):
    def __init__(self, detail: str = "Failed to generate red flag statistics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidProcessingTypeError(HTTPException):
    def __init__(self, processing_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid processing type: {processing_type}"
        )

class RedFlagBulkProcessingError(HTTPException):
    def __init__(self, detail: str = "Bulk processing failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RedFlagConfigurationError(HTTPException):
    def __init__(self, detail: str = "Red flag configuration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)