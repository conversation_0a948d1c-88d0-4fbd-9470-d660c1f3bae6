# red_flags service - business logic for red_flags module
from sqlalchemy.orm import Session
from sqlalchemy import select, text, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4
from fastapi import HTTPException, status
from datetime import datetime
import logging

from .models import (
    flags, merchant_red_flags, customer_red_flags, LLMredFlagsStatus,
    red_flag_jobs, red_flag_templates, red_flag_metrics,
    digital_footprint_red_flags
)
from .schemas import (
    RedFlagCreate, RedFlagResponse, MerchantRedFlagCreate, MerchantRedFlagUpdate,
    MerchantRedFlagResponse, CustomerRedFlagCreate, CustomerRedFlagUpdate,
    CustomerRedFlagResponse, RedFlagProcessingRequest, RedFlagJobCreate,
    RedFlagJobResponse, RedFlagTemplateCreate, RedFlagTemplateUpdate,
    RedFlagTemplateResponse, DigitalFootprintRedFlagCreate,
    DigitalFootprintRedFlagResponse, RedFlagStatistics, RedFlagSummary
)
from ..pagination import PaginationParams, PaginatedResponse, paginate_query

logger = logging.getLogger(__name__)

class RedFlagsService:
    def __init__(self, db: Session):
        self.db = db

    def validate_rule_code(self, rule_code: str) -> bool:
        """Validate if rule code exists in rules store."""
        try:
            from ..rules.models import rules_store
            rule = self.db.query(rules_store).filter(rules_store.code == rule_code).first()
            if not rule:
                logger.warning(f"Rule code {rule_code} not found in rules store")
                return False
            return True
        except Exception as e:
            logger.warning(f"Could not validate rule code {rule_code}: {str(e)}")
            return False

    # Core Red Flags Operations
    def create_red_flag(self, flag_data: RedFlagCreate) -> RedFlagResponse:
        """Create a new red flag."""
        try:
            db_flag = flags(
                **flag_data.dict(exclude={'timestamp'}),
                timestamp=flag_data.timestamp or datetime.now()
            )

            self.db.add(db_flag)
            self.db.commit()
            self.db.refresh(db_flag)

            return RedFlagResponse.from_orm(db_flag)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create red flag: {str(e)}")

    def get_red_flags_by_merchant(self, merchant_id: UUID, pagination: PaginationParams) -> PaginatedResponse[RedFlagResponse]:
        """Get red flags for a specific merchant."""
        try:
            query = self.db.query(flags).filter(flags.merchant_id == merchant_id)
            red_flags, total = paginate_query(query, pagination)

            flag_responses = [RedFlagResponse.from_orm(flag) for flag in red_flags]

            return PaginatedResponse.create(flag_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get red flags: {str(e)}")

    # Merchant Red Flags Operations
    def create_merchant_red_flag(self, flag_data: MerchantRedFlagCreate) -> MerchantRedFlagResponse:
        """Create a new merchant red flag."""
        try:
            self.validate_rule_code(flag_data.rule_code)

            db_flag = merchant_red_flags(**flag_data.dict())

            self.db.add(db_flag)
            self.db.commit()
            self.db.refresh(db_flag)

            return MerchantRedFlagResponse.from_orm(db_flag)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create merchant red flag: {str(e)}")

    def get_merchant_red_flags(self, merchant_id: UUID, rule_type: Optional[str] = None) -> List[MerchantRedFlagResponse]:
        """Get merchant red flags with rule information."""
        try:
            from ..rules.models import rules_store

            # Build the query with join to rules_store
            query = self.db.query(
                merchant_red_flags,
                rules_store.name.label('rule_name'),
                rules_store.description.label('rule_description'),
                rules_store.type.label('rule_type'),
                rules_store.severity.label('rule_severity'),
                rules_store.fraud_type.label('rule_fraud_type'),
                rules_store.rule.label('rule')
            ).outerjoin(
                rules_store,
                merchant_red_flags.rule_code == rules_store.code
            ).filter(
                merchant_red_flags.merchant_id == merchant_id
            )

            if rule_type:
                query = query.filter(rules_store.type == rule_type)

            results = query.all()

            # Convert to response objects
            red_flags = []
            for result in results:
                flag_data = result[0].__dict__.copy()
                flag_data.pop('_sa_instance_state', None)

                # Add rule information
                flag_data.update({
                    'rule_name': result.rule_name,
                    'rule_description': result.rule_description,
                    'rule_type': result.rule_type,
                    'rule_severity': result.rule_severity,
                    'rule_fraud_type': result.rule_fraud_type,
                    'rule': result.rule
                })

                red_flags.append(MerchantRedFlagResponse(**flag_data))

            return red_flags
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get merchant red flags: {str(e)}")

    def update_merchant_red_flag(self, flag_id: UUID, flag_data: MerchantRedFlagUpdate) -> MerchantRedFlagResponse:
        """Update a merchant red flag."""
        try:
            db_flag = self.db.query(merchant_red_flags).filter(merchant_red_flags.id == flag_id).first()
            if not db_flag:
                raise HTTPException(status_code=404, detail="Merchant red flag not found")

            update_data = flag_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(db_flag, field, value)

            db_flag.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(db_flag)

            return MerchantRedFlagResponse.from_orm(db_flag)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update merchant red flag: {str(e)}")

    def delete_merchant_red_flag(self, flag_id: UUID) -> Dict[str, str]:
        """Delete a merchant red flag."""
        try:
            db_flag = self.db.query(merchant_red_flags).filter(merchant_red_flags.id == flag_id).first()
            if not db_flag:
                raise HTTPException(status_code=404, detail="Merchant red flag not found")

            self.db.delete(db_flag)
            self.db.commit()

            return {"status": "success", "message": "Merchant red flag deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete merchant red flag: {str(e)}")

    # Customer Red Flags Operations
    def create_customer_red_flag(self, flag_data: CustomerRedFlagCreate) -> CustomerRedFlagResponse:
        """Create a new customer red flag."""
        try:
            self.validate_rule_code(flag_data.rule_code)

            db_flag = customer_red_flags(**flag_data.dict())

            self.db.add(db_flag)
            self.db.commit()
            self.db.refresh(db_flag)

            return CustomerRedFlagResponse.from_orm(db_flag)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create customer red flag: {str(e)}")

    def get_customer_red_flags(self, customer_id: UUID, rule_type: Optional[str] = None) -> List[CustomerRedFlagResponse]:
        """Get customer red flags with rule information."""
        try:
            from ..rules.models import rules_store

            # Build the query with join to rules_store
            query = self.db.query(
                customer_red_flags,
                rules_store.name.label('rule_name'),
                rules_store.description.label('rule_description'),
                rules_store.type.label('rule_type'),
                rules_store.severity.label('rule_severity'),
                rules_store.fraud_type.label('rule_fraud_type'),
                rules_store.rule.label('rule')
            ).outerjoin(
                rules_store,
                customer_red_flags.rule_code == rules_store.code
            ).filter(
                customer_red_flags.customer_id == customer_id
            )

            if rule_type:
                query = query.filter(rules_store.type == rule_type)

            results = query.all()

            # Convert to response objects
            red_flags = []
            for result in results:
                flag_data = result[0].__dict__.copy()
                flag_data.pop('_sa_instance_state', None)

                # Add rule information
                flag_data.update({
                    'rule_name': result.rule_name,
                    'rule_description': result.rule_description,
                    'rule_type': result.rule_type,
                    'rule_severity': result.rule_severity,
                    'rule_fraud_type': result.rule_fraud_type,
                    'rule': result.rule
                })

                red_flags.append(CustomerRedFlagResponse(**flag_data))

            return red_flags
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get customer red flags: {str(e)}")

    def get_red_flags_by_rule(self, rule_code: str, entity_type: str = "merchant") -> List[Any]:
        """Get red flags by rule code."""
        try:
            self.validate_rule_code(rule_code)

            if entity_type == "merchant":
                red_flags = self.db.query(merchant_red_flags).filter(
                    merchant_red_flags.rule_code == rule_code
                ).all()
                return [MerchantRedFlagResponse.from_orm(flag) for flag in red_flags]
            elif entity_type == "customer":
                red_flags = self.db.query(customer_red_flags).filter(
                    customer_red_flags.rule_code == rule_code
                ).all()
                return [CustomerRedFlagResponse.from_orm(flag) for flag in red_flags]
            else:
                raise HTTPException(status_code=400, detail="Invalid entity type")
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get red flags by rule: {str(e)}")

    # Red Flag Processing Operations
    def process_red_flags(self, request: RedFlagProcessingRequest) -> Dict[str, Any]:
        """Process red flags for specified merchants and rules."""
        try:
            job_id = str(uuid4())

            # Create processing job
            job = red_flag_jobs(
                job_id=job_id,
                job_type=request.processing_type,
                status="pending",
                rule_codes=request.rule_codes
            )
            self.db.add(job)
            self.db.commit()

            # This would integrate with the existing red flag generation logic
            # For now, returning a placeholder response
            return {
                "status": "success",
                "message": "Red flag processing started",
                "job_id": job_id,
                "processing_type": request.processing_type
            }
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to process red flags: {str(e)}")

    def get_processing_job_status(self, job_id: str) -> RedFlagJobResponse:
        """Get the status of a red flag processing job."""
        try:
            job = self.db.query(red_flag_jobs).filter(red_flag_jobs.job_id == job_id).first()
            if not job:
                raise HTTPException(status_code=404, detail="Job not found")

            return RedFlagJobResponse.from_orm(job)
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")

    # Red Flag Statistics and Analytics
    def get_red_flag_statistics(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> RedFlagStatistics:
        """Get red flag statistics for a date range."""
        try:
            # Base query for flags
            query = self.db.query(flags)

            if start_date:
                query = query.filter(flags.created_at >= start_date)
            if end_date:
                query = query.filter(flags.created_at <= end_date)

            all_flags = query.all()

            # Calculate statistics
            total_flags = len(all_flags)
            high_severity = len([f for f in all_flags if f.severity == 'high'])
            medium_severity = len([f for f in all_flags if f.severity == 'medium'])
            low_severity = len([f for f in all_flags if f.severity == 'low'])

            # Count by type
            flags_by_type = {}
            for flag in all_flags:
                flags_by_type[flag.flag_type] = flags_by_type.get(flag.flag_type, 0) + 1

            # Count merchants flagged
            merchants_flagged = len(set(f.merchant_id for f in all_flags))

            # Get merchant red flags for additional stats
            merchant_flags = self.db.query(merchant_red_flags)
            if start_date:
                merchant_flags = merchant_flags.filter(merchant_red_flags.created_at >= start_date)
            if end_date:
                merchant_flags = merchant_flags.filter(merchant_red_flags.created_at <= end_date)

            merchant_flags_list = merchant_flags.all()

            # Count by category
            flags_by_category = {}
            for flag in merchant_flags_list:
                if flag.category:
                    flags_by_category[flag.category] = flags_by_category.get(flag.category, 0) + 1

            return RedFlagStatistics(
                total_flags=total_flags,
                high_severity_flags=high_severity,
                medium_severity_flags=medium_severity,
                low_severity_flags=low_severity,
                ml_generated_flags=len([f for f in all_flags if 'ML' in f.flag_type]),
                rule_based_flags=len([f for f in all_flags if 'rule' in f.flag_type.lower()]),
                merchants_flagged=merchants_flagged,
                customers_flagged=0,  # Would need to implement customer counting
                flags_by_type=flags_by_type,
                flags_by_category=flags_by_category
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get statistics: {str(e)}")

    def get_merchant_red_flag_summary(self, merchant_id: UUID) -> RedFlagSummary:
        """Get red flag summary for a merchant."""
        try:
            # Get all red flags for the merchant
            merchant_flags = self.db.query(merchant_red_flags).filter(
                merchant_red_flags.merchant_id == merchant_id
            ).all()

            core_flags = self.db.query(flags).filter(
                flags.merchant_id == merchant_id
            ).all()

            total_flags = len(merchant_flags) + len(core_flags)

            if total_flags == 0:
                return RedFlagSummary(
                    merchant_id=merchant_id,
                    total_flags=0,
                    highest_severity="none",
                    latest_flag_date=None,
                    flag_categories=[],
                    risk_score=0.0
                )

            # Determine highest severity
            severities = [f.severity for f in merchant_flags if f.severity] + [f.severity for f in core_flags if f.severity]
            severity_order = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
            highest_severity = max(severities, key=lambda x: severity_order.get(x, 0)) if severities else "none"

            # Get latest flag date
            dates = [f.created_at for f in merchant_flags] + [f.created_at for f in core_flags]
            latest_flag_date = max(dates) if dates else None

            # Get unique categories
            categories = list(set([f.category for f in merchant_flags if f.category]))

            # Calculate risk score based on flags
            risk_score = min(total_flags * 0.1 + severity_order.get(highest_severity, 0) * 0.2, 1.0)

            return RedFlagSummary(
                merchant_id=merchant_id,
                total_flags=total_flags,
                highest_severity=highest_severity,
                latest_flag_date=latest_flag_date,
                flag_categories=categories,
                risk_score=risk_score
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get merchant summary: {str(e)}")

    # Digital Footprint Red Flags
    def create_digital_footprint_red_flag(self, flag_data: DigitalFootprintRedFlagCreate) -> DigitalFootprintRedFlagResponse:
        """Create digital footprint red flags."""
        try:
            db_flag = digital_footprint_red_flags(**flag_data.dict())

            self.db.add(db_flag)
            self.db.commit()
            self.db.refresh(db_flag)

            return DigitalFootprintRedFlagResponse.from_orm(db_flag)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create digital footprint red flag: {str(e)}")

    def get_digital_footprint_red_flags(self, merchant_id: UUID) -> Optional[DigitalFootprintRedFlagResponse]:
        """Get digital footprint red flags for a merchant."""
        try:
            flag = self.db.query(digital_footprint_red_flags).filter(
                digital_footprint_red_flags.merchant_id == merchant_id
            ).first()

            if not flag:
                return None

            return DigitalFootprintRedFlagResponse.from_orm(flag)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get digital footprint red flags: {str(e)}")

    # ML-based Red Flag Generation (placeholder for integration)
    def generate_ml_red_flags(self, merchant_id: UUID) -> Dict[str, Any]:
        """Generate ML-based red flags for a merchant."""
        try:
            # This would integrate with the existing ML red flag generation logic
            # from app/routers/redFlagGenerationRouter.py

            # For now, returning a placeholder response
            return {
                "status": "success",
                "message": "ML red flag generation functionality will be integrated",
                "merchant_id": str(merchant_id)
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to generate ML red flags: {str(e)}")

    # Rule-based Red Flag Generation (placeholder for integration)
    def generate_rule_based_red_flags(self, merchant_id: UUID) -> Dict[str, Any]:
        """Generate rule-based red flags for a merchant."""
        try:
            # This would integrate with the existing rule-based red flag generation logic
            # from app/routers/redFlagGenerationRouter.py

            # For now, returning a placeholder response
            return {
                "status": "success",
                "message": "Rule-based red flag generation functionality will be integrated",
                "merchant_id": str(merchant_id)
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to generate rule-based red flags: {str(e)}")