# red_flags models - SQLAlchemy models for red_flags module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB, VARCHAR
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Core Red Flags Model
class flags(Base):
    __tablename__ = 'flags'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    flag_type = Column(String(50))  # transactions, compliance, network
    severity = Column(String(50))
    importance = Column(Float)
    text = Column(Text)
    timestamp = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

# Merchant Red Flags
class merchant_red_flags(Base):
    __tablename__ = 'merchant_red_flags'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    rule_code = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    severity = Column(String(50), nullable=True)
    metric_values = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    metric_data_timestamp = Column(DateTime, nullable=True)
    notes = Column(Text, nullable=True)
    category = Column(VARCHAR(50))  # Added category for better classification

# Customer Red Flags
class customer_red_flags(Base):
    __tablename__ = 'customer_red_flags'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(UUID(as_uuid=True))
    rule_code = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    severity = Column(String(50), nullable=True)
    metric_values = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    metric_data_timestamp = Column(DateTime, nullable=True)
    notes = Column(Text, nullable=True)
    category = Column(VARCHAR(50))  # Added category for better classification

# LLM Red Flags Status (from rules module but used here)
class LLMredFlagsStatus(Base):
    __tablename__ = 'llm_red_flags_status'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    rule_number = Column(Integer)
    rule_name = Column(String(255))
    rule_description = Column(String(255))
    status = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

# Red Flag Processing Jobs
class red_flag_jobs(Base):
    __tablename__ = 'red_flag_jobs'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(String(100), unique=True, index=True)
    job_type = Column(String(50))  # ml_generation, rule_based, bulk_processing
    status = Column(String(50))  # pending, running, completed, failed
    merchant_id = Column(UUID(as_uuid=True), nullable=True)
    rule_codes = Column(JSONB, nullable=True)  # List of rule codes to process
    progress = Column(Float, default=0.0)  # Progress percentage
    error_message = Column(Text, nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Red Flag Templates
class red_flag_templates(Base):
    __tablename__ = 'red_flag_templates'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_name = Column(String(255), nullable=False)
    template_type = Column(String(50))  # merchant, customer, transaction
    severity = Column(String(50))
    category = Column(String(50))
    description_template = Column(Text)  # Template with placeholders
    rule_conditions = Column(JSONB)  # Conditions for triggering this template
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Red Flag Metrics (for tracking red flag generation performance)
class red_flag_metrics(Base):
    __tablename__ = 'red_flag_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    date = Column(DateTime, default=datetime.now)
    total_flags_generated = Column(Integer, default=0)
    ml_flags_generated = Column(Integer, default=0)
    rule_based_flags_generated = Column(Integer, default=0)
    high_severity_flags = Column(Integer, default=0)
    medium_severity_flags = Column(Integer, default=0)
    low_severity_flags = Column(Integer, default=0)
    merchants_flagged = Column(Integer, default=0)
    customers_flagged = Column(Integer, default=0)
    processing_time_seconds = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

# Digital Footprint Red Flags (specific to digital footprint analysis)
class digital_footprint_red_flags(Base):
    __tablename__ = 'digital_footprint_red_flags'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    domain_flags = Column(JSONB)  # Domain-related red flags
    content_flags = Column(JSONB)  # Content-related red flags
    policy_flags = Column(JSONB)  # Policy-related red flags
    price_flags = Column(JSONB)  # Price-related red flags
    review_flags = Column(JSONB)  # Review-related red flags
    overall_risk_score = Column(Float)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)