# customers constants - module-specific constants for customers

# Customer Types
CUSTOMER_TYPE_INDIVIDUAL = "individual"
CUSTOMER_TYPE_BUSINESS = "business"
CUSTOMER_TYPE_CORPORATE = "corporate"
CUSTOMER_TYPE_GOVERNMENT = "government"
CUSTOMER_TYPE_NON_PROFIT = "non_profit"

CUSTOMER_TYPES = [
    CUSTOMER_TYPE_INDIVIDUAL,
    CUSTOMER_TYPE_BUSINESS,
    CUSTOMER_TYPE_CORPORATE,
    CUSTOMER_TYPE_GOVERNMENT,
    CUSTOMER_TYPE_NON_PROFIT
]

# Customer Segments
CUSTOMER_SEGMENT_PREMIUM = "premium"
CUSTOMER_SEGMENT_STANDARD = "standard"
CUSTOMER_SEGMENT_BASIC = "basic"
CUSTOMER_SEGMENT_VIP = "vip"
CUSTOMER_SEGMENT_ENTERPRISE = "enterprise"
CUSTOMER_SEGMENT_SME = "sme"
CUSTOMER_SEGMENT_STARTUP = "startup"

CUSTOMER_SEGMENTS = [
    CUSTOMER_SEGMENT_PREMIUM,
    CUSTOMER_SEGMENT_STANDARD,
    CUSTOMER_SEGMENT_BASIC,
    CUSTOMER_SEGMENT_VIP,
    CUSTOMER_SEGMENT_ENTERPRISE,
    CUSTOMER_SEGMENT_SME,
    CUSTOMER_SEGMENT_STARTUP
]

# Customer Categories
CUSTOMER_CATEGORY_NEW = "new"
CUSTOMER_CATEGORY_EXISTING = "existing"
CUSTOMER_CATEGORY_VIP = "vip"
CUSTOMER_CATEGORY_HIGH_RISK = "high_risk"
CUSTOMER_CATEGORY_LOW_RISK = "low_risk"
CUSTOMER_CATEGORY_PROSPECT = "prospect"
CUSTOMER_CATEGORY_CHURNED = "churned"

CUSTOMER_CATEGORIES = [
    CUSTOMER_CATEGORY_NEW,
    CUSTOMER_CATEGORY_EXISTING,
    CUSTOMER_CATEGORY_VIP,
    CUSTOMER_CATEGORY_HIGH_RISK,
    CUSTOMER_CATEGORY_LOW_RISK,
    CUSTOMER_CATEGORY_PROSPECT,
    CUSTOMER_CATEGORY_CHURNED
]

# Customer Statuses
CUSTOMER_STATUS_ACTIVE = "active"
CUSTOMER_STATUS_INACTIVE = "inactive"
CUSTOMER_STATUS_SUSPENDED = "suspended"
CUSTOMER_STATUS_BLOCKED = "blocked"
CUSTOMER_STATUS_CLOSED = "closed"
CUSTOMER_STATUS_PENDING = "pending"
CUSTOMER_STATUS_DORMANT = "dormant"

CUSTOMER_STATUSES = [
    CUSTOMER_STATUS_ACTIVE,
    CUSTOMER_STATUS_INACTIVE,
    CUSTOMER_STATUS_SUSPENDED,
    CUSTOMER_STATUS_BLOCKED,
    CUSTOMER_STATUS_CLOSED,
    CUSTOMER_STATUS_PENDING,
    CUSTOMER_STATUS_DORMANT
]

# Account Statuses
ACCOUNT_STATUS_OPEN = "open"
ACCOUNT_STATUS_CLOSED = "closed"
ACCOUNT_STATUS_FROZEN = "frozen"
ACCOUNT_STATUS_RESTRICTED = "restricted"
ACCOUNT_STATUS_SUSPENDED = "suspended"

ACCOUNT_STATUSES = [
    ACCOUNT_STATUS_OPEN,
    ACCOUNT_STATUS_CLOSED,
    ACCOUNT_STATUS_FROZEN,
    ACCOUNT_STATUS_RESTRICTED,
    ACCOUNT_STATUS_SUSPENDED
]

# Lifecycle Stages
LIFECYCLE_STAGE_PROSPECT = "prospect"
LIFECYCLE_STAGE_NEW = "new"
LIFECYCLE_STAGE_ACTIVE = "active"
LIFECYCLE_STAGE_DORMANT = "dormant"
LIFECYCLE_STAGE_CHURNED = "churned"
LIFECYCLE_STAGE_REACTIVATED = "reactivated"
LIFECYCLE_STAGE_VIP = "vip"

LIFECYCLE_STAGES = [
    LIFECYCLE_STAGE_PROSPECT,
    LIFECYCLE_STAGE_NEW,
    LIFECYCLE_STAGE_ACTIVE,
    LIFECYCLE_STAGE_DORMANT,
    LIFECYCLE_STAGE_CHURNED,
    LIFECYCLE_STAGE_REACTIVATED,
    LIFECYCLE_STAGE_VIP
]

# Onboarding Statuses
ONBOARDING_STATUS_PENDING = "pending"
ONBOARDING_STATUS_IN_PROGRESS = "in_progress"
ONBOARDING_STATUS_COMPLETED = "completed"
ONBOARDING_STATUS_FAILED = "failed"
ONBOARDING_STATUS_ABANDONED = "abandoned"

ONBOARDING_STATUSES = [
    ONBOARDING_STATUS_PENDING,
    ONBOARDING_STATUS_IN_PROGRESS,
    ONBOARDING_STATUS_COMPLETED,
    ONBOARDING_STATUS_FAILED,
    ONBOARDING_STATUS_ABANDONED
]

# Onboarding Channels
ONBOARDING_CHANNEL_WEB = "web"
ONBOARDING_CHANNEL_MOBILE = "mobile"
ONBOARDING_CHANNEL_BRANCH = "branch"
ONBOARDING_CHANNEL_AGENT = "agent"
ONBOARDING_CHANNEL_PHONE = "phone"
ONBOARDING_CHANNEL_EMAIL = "email"
ONBOARDING_CHANNEL_API = "api"

ONBOARDING_CHANNELS = [
    ONBOARDING_CHANNEL_WEB,
    ONBOARDING_CHANNEL_MOBILE,
    ONBOARDING_CHANNEL_BRANCH,
    ONBOARDING_CHANNEL_AGENT,
    ONBOARDING_CHANNEL_PHONE,
    ONBOARDING_CHANNEL_EMAIL,
    ONBOARDING_CHANNEL_API
]

# KYC Statuses
KYC_STATUS_PENDING = "pending"
KYC_STATUS_IN_PROGRESS = "in_progress"
KYC_STATUS_VERIFIED = "verified"
KYC_STATUS_REJECTED = "rejected"
KYC_STATUS_EXPIRED = "expired"
KYC_STATUS_INCOMPLETE = "incomplete"

KYC_STATUSES = [
    KYC_STATUS_PENDING,
    KYC_STATUS_IN_PROGRESS,
    KYC_STATUS_VERIFIED,
    KYC_STATUS_REJECTED,
    KYC_STATUS_EXPIRED,
    KYC_STATUS_INCOMPLETE
]

# KYC Levels
KYC_LEVEL_BASIC = "basic"
KYC_LEVEL_INTERMEDIATE = "intermediate"
KYC_LEVEL_FULL = "full"
KYC_LEVEL_ENHANCED = "enhanced"

KYC_LEVELS = [
    KYC_LEVEL_BASIC,
    KYC_LEVEL_INTERMEDIATE,
    KYC_LEVEL_FULL,
    KYC_LEVEL_ENHANCED
]

# Verification Types
VERIFICATION_TYPE_IDENTITY = "identity"
VERIFICATION_TYPE_ADDRESS = "address"
VERIFICATION_TYPE_PHONE = "phone"
VERIFICATION_TYPE_EMAIL = "email"
VERIFICATION_TYPE_INCOME = "income"
VERIFICATION_TYPE_EMPLOYMENT = "employment"
VERIFICATION_TYPE_BANK_ACCOUNT = "bank_account"

VERIFICATION_TYPES = [
    VERIFICATION_TYPE_IDENTITY,
    VERIFICATION_TYPE_ADDRESS,
    VERIFICATION_TYPE_PHONE,
    VERIFICATION_TYPE_EMAIL,
    VERIFICATION_TYPE_INCOME,
    VERIFICATION_TYPE_EMPLOYMENT,
    VERIFICATION_TYPE_BANK_ACCOUNT
]

# Document Types
DOCUMENT_TYPE_PASSPORT = "passport"
DOCUMENT_TYPE_DRIVERS_LICENSE = "drivers_license"
DOCUMENT_TYPE_NATIONAL_ID = "national_id"
DOCUMENT_TYPE_UTILITY_BILL = "utility_bill"
DOCUMENT_TYPE_BANK_STATEMENT = "bank_statement"
DOCUMENT_TYPE_TAX_RETURN = "tax_return"
DOCUMENT_TYPE_PAYSLIP = "payslip"
DOCUMENT_TYPE_BUSINESS_REGISTRATION = "business_registration"
DOCUMENT_TYPE_ARTICLES_OF_INCORPORATION = "articles_of_incorporation"
DOCUMENT_TYPE_MEMORANDUM = "memorandum"

DOCUMENT_TYPES = [
    DOCUMENT_TYPE_PASSPORT,
    DOCUMENT_TYPE_DRIVERS_LICENSE,
    DOCUMENT_TYPE_NATIONAL_ID,
    DOCUMENT_TYPE_UTILITY_BILL,
    DOCUMENT_TYPE_BANK_STATEMENT,
    DOCUMENT_TYPE_TAX_RETURN,
    DOCUMENT_TYPE_PAYSLIP,
    DOCUMENT_TYPE_BUSINESS_REGISTRATION,
    DOCUMENT_TYPE_ARTICLES_OF_INCORPORATION,
    DOCUMENT_TYPE_MEMORANDUM
]

# Document Categories
DOCUMENT_CATEGORY_IDENTITY = "identity"
DOCUMENT_CATEGORY_ADDRESS = "address"
DOCUMENT_CATEGORY_INCOME = "income"
DOCUMENT_CATEGORY_BUSINESS = "business"
DOCUMENT_CATEGORY_FINANCIAL = "financial"
DOCUMENT_CATEGORY_LEGAL = "legal"

DOCUMENT_CATEGORIES = [
    DOCUMENT_CATEGORY_IDENTITY,
    DOCUMENT_CATEGORY_ADDRESS,
    DOCUMENT_CATEGORY_INCOME,
    DOCUMENT_CATEGORY_BUSINESS,
    DOCUMENT_CATEGORY_FINANCIAL,
    DOCUMENT_CATEGORY_LEGAL
]

# Document Statuses
DOCUMENT_STATUS_UPLOADED = "uploaded"
DOCUMENT_STATUS_PROCESSING = "processing"
DOCUMENT_STATUS_VERIFIED = "verified"
DOCUMENT_STATUS_REJECTED = "rejected"
DOCUMENT_STATUS_EXPIRED = "expired"
DOCUMENT_STATUS_PENDING = "pending"

DOCUMENT_STATUSES = [
    DOCUMENT_STATUS_UPLOADED,
    DOCUMENT_STATUS_PROCESSING,
    DOCUMENT_STATUS_VERIFIED,
    DOCUMENT_STATUS_REJECTED,
    DOCUMENT_STATUS_EXPIRED,
    DOCUMENT_STATUS_PENDING
]

# Risk Levels
RISK_LEVEL_LOW = "low"
RISK_LEVEL_MEDIUM = "medium"
RISK_LEVEL_HIGH = "high"
RISK_LEVEL_CRITICAL = "critical"

RISK_LEVELS = [
    RISK_LEVEL_LOW,
    RISK_LEVEL_MEDIUM,
    RISK_LEVEL_HIGH,
    RISK_LEVEL_CRITICAL
]

# Communication Types
COMMUNICATION_TYPE_EMAIL = "email"
COMMUNICATION_TYPE_SMS = "sms"
COMMUNICATION_TYPE_CALL = "call"
COMMUNICATION_TYPE_CHAT = "chat"
COMMUNICATION_TYPE_NOTIFICATION = "notification"
COMMUNICATION_TYPE_LETTER = "letter"
COMMUNICATION_TYPE_PUSH = "push"

COMMUNICATION_TYPES = [
    COMMUNICATION_TYPE_EMAIL,
    COMMUNICATION_TYPE_SMS,
    COMMUNICATION_TYPE_CALL,
    COMMUNICATION_TYPE_CHAT,
    COMMUNICATION_TYPE_NOTIFICATION,
    COMMUNICATION_TYPE_LETTER,
    COMMUNICATION_TYPE_PUSH
]

# Communication Channels
COMMUNICATION_CHANNEL_EMAIL = "email"
COMMUNICATION_CHANNEL_PHONE = "phone"
COMMUNICATION_CHANNEL_APP = "app"
COMMUNICATION_CHANNEL_WEB = "web"
COMMUNICATION_CHANNEL_SOCIAL = "social"
COMMUNICATION_CHANNEL_BRANCH = "branch"
COMMUNICATION_CHANNEL_ATM = "atm"

COMMUNICATION_CHANNELS = [
    COMMUNICATION_CHANNEL_EMAIL,
    COMMUNICATION_CHANNEL_PHONE,
    COMMUNICATION_CHANNEL_APP,
    COMMUNICATION_CHANNEL_WEB,
    COMMUNICATION_CHANNEL_SOCIAL,
    COMMUNICATION_CHANNEL_BRANCH,
    COMMUNICATION_CHANNEL_ATM
]

# Communication Directions
COMMUNICATION_DIRECTION_INBOUND = "inbound"
COMMUNICATION_DIRECTION_OUTBOUND = "outbound"

COMMUNICATION_DIRECTIONS = [
    COMMUNICATION_DIRECTION_INBOUND,
    COMMUNICATION_DIRECTION_OUTBOUND
]

# Communication Statuses
COMMUNICATION_STATUS_SENT = "sent"
COMMUNICATION_STATUS_DELIVERED = "delivered"
COMMUNICATION_STATUS_READ = "read"
COMMUNICATION_STATUS_FAILED = "failed"
COMMUNICATION_STATUS_BOUNCED = "bounced"
COMMUNICATION_STATUS_PENDING = "pending"

COMMUNICATION_STATUSES = [
    COMMUNICATION_STATUS_SENT,
    COMMUNICATION_STATUS_DELIVERED,
    COMMUNICATION_STATUS_READ,
    COMMUNICATION_STATUS_FAILED,
    COMMUNICATION_STATUS_BOUNCED,
    COMMUNICATION_STATUS_PENDING
]

# Relationship Types
RELATIONSHIP_TYPE_FAMILY = "family"
RELATIONSHIP_TYPE_BUSINESS = "business"
RELATIONSHIP_TYPE_REFERRAL = "referral"
RELATIONSHIP_TYPE_SHARED_DEVICE = "shared_device"
RELATIONSHIP_TYPE_SHARED_ADDRESS = "shared_address"
RELATIONSHIP_TYPE_SHARED_PHONE = "shared_phone"
RELATIONSHIP_TYPE_GUARANTOR = "guarantor"
RELATIONSHIP_TYPE_BENEFICIARY = "beneficiary"

RELATIONSHIP_TYPES = [
    RELATIONSHIP_TYPE_FAMILY,
    RELATIONSHIP_TYPE_BUSINESS,
    RELATIONSHIP_TYPE_REFERRAL,
    RELATIONSHIP_TYPE_SHARED_DEVICE,
    RELATIONSHIP_TYPE_SHARED_ADDRESS,
    RELATIONSHIP_TYPE_SHARED_PHONE,
    RELATIONSHIP_TYPE_GUARANTOR,
    RELATIONSHIP_TYPE_BENEFICIARY
]

# Segment Types
SEGMENT_TYPE_DEMOGRAPHIC = "demographic"
SEGMENT_TYPE_BEHAVIORAL = "behavioral"
SEGMENT_TYPE_VALUE = "value"
SEGMENT_TYPE_RISK = "risk"
SEGMENT_TYPE_GEOGRAPHIC = "geographic"
SEGMENT_TYPE_PSYCHOGRAPHIC = "psychographic"
SEGMENT_TYPE_TRANSACTIONAL = "transactional"

SEGMENT_TYPES = [
    SEGMENT_TYPE_DEMOGRAPHIC,
    SEGMENT_TYPE_BEHAVIORAL,
    SEGMENT_TYPE_VALUE,
    SEGMENT_TYPE_RISK,
    SEGMENT_TYPE_GEOGRAPHIC,
    SEGMENT_TYPE_PSYCHOGRAPHIC,
    SEGMENT_TYPE_TRANSACTIONAL
]

# Analytics Types
ANALYTICS_TYPE_BEHAVIOR = "behavior"
ANALYTICS_TYPE_FINANCIAL = "financial"
ANALYTICS_TYPE_RISK = "risk"
ANALYTICS_TYPE_ENGAGEMENT = "engagement"
ANALYTICS_TYPE_LIFECYCLE = "lifecycle"
ANALYTICS_TYPE_TRANSACTION = "transaction"

ANALYTICS_TYPES = [
    ANALYTICS_TYPE_BEHAVIOR,
    ANALYTICS_TYPE_FINANCIAL,
    ANALYTICS_TYPE_RISK,
    ANALYTICS_TYPE_ENGAGEMENT,
    ANALYTICS_TYPE_LIFECYCLE,
    ANALYTICS_TYPE_TRANSACTION
]

# Analytics Periods
ANALYTICS_PERIOD_DAILY = "daily"
ANALYTICS_PERIOD_WEEKLY = "weekly"
ANALYTICS_PERIOD_MONTHLY = "monthly"
ANALYTICS_PERIOD_QUARTERLY = "quarterly"
ANALYTICS_PERIOD_YEARLY = "yearly"

ANALYTICS_PERIODS = [
    ANALYTICS_PERIOD_DAILY,
    ANALYTICS_PERIOD_WEEKLY,
    ANALYTICS_PERIOD_MONTHLY,
    ANALYTICS_PERIOD_QUARTERLY,
    ANALYTICS_PERIOD_YEARLY
]

# Journey Event Types
EVENT_TYPE_REGISTRATION = "registration"
EVENT_TYPE_LOGIN = "login"
EVENT_TYPE_PURCHASE = "purchase"
EVENT_TYPE_SUPPORT = "support"
EVENT_TYPE_CHURN = "churn"
EVENT_TYPE_VERIFICATION = "verification"
EVENT_TYPE_PROFILE_UPDATE = "profile_update"
EVENT_TYPE_TRANSACTION = "transaction"
EVENT_TYPE_COMMUNICATION = "communication"

EVENT_TYPES = [
    EVENT_TYPE_REGISTRATION,
    EVENT_TYPE_LOGIN,
    EVENT_TYPE_PURCHASE,
    EVENT_TYPE_SUPPORT,
    EVENT_TYPE_CHURN,
    EVENT_TYPE_VERIFICATION,
    EVENT_TYPE_PROFILE_UPDATE,
    EVENT_TYPE_TRANSACTION,
    EVENT_TYPE_COMMUNICATION
]

# Support Case Types
CASE_TYPE_COMPLAINT = "complaint"
CASE_TYPE_INQUIRY = "inquiry"
CASE_TYPE_REQUEST = "request"
CASE_TYPE_TECHNICAL = "technical"
CASE_TYPE_BILLING = "billing"
CASE_TYPE_ACCOUNT = "account"

CASE_TYPES = [
    CASE_TYPE_COMPLAINT,
    CASE_TYPE_INQUIRY,
    CASE_TYPE_REQUEST,
    CASE_TYPE_TECHNICAL,
    CASE_TYPE_BILLING,
    CASE_TYPE_ACCOUNT
]

# Support Case Categories
CASE_CATEGORY_BILLING = "billing"
CASE_CATEGORY_PRODUCT = "product"
CASE_CATEGORY_SERVICE = "service"
CASE_CATEGORY_TECHNICAL = "technical"
CASE_CATEGORY_ACCOUNT = "account"
CASE_CATEGORY_COMPLIANCE = "compliance"

CASE_CATEGORIES = [
    CASE_CATEGORY_BILLING,
    CASE_CATEGORY_PRODUCT,
    CASE_CATEGORY_SERVICE,
    CASE_CATEGORY_TECHNICAL,
    CASE_CATEGORY_ACCOUNT,
    CASE_CATEGORY_COMPLIANCE
]

# Support Case Statuses
CASE_STATUS_OPEN = "open"
CASE_STATUS_IN_PROGRESS = "in_progress"
CASE_STATUS_RESOLVED = "resolved"
CASE_STATUS_CLOSED = "closed"
CASE_STATUS_ESCALATED = "escalated"
CASE_STATUS_PENDING = "pending"

CASE_STATUSES = [
    CASE_STATUS_OPEN,
    CASE_STATUS_IN_PROGRESS,
    CASE_STATUS_RESOLVED,
    CASE_STATUS_CLOSED,
    CASE_STATUS_ESCALATED,
    CASE_STATUS_PENDING
]

# Priorities
PRIORITY_LOW = "low"
PRIORITY_MEDIUM = "medium"
PRIORITY_HIGH = "high"
PRIORITY_URGENT = "urgent"
PRIORITY_CRITICAL = "critical"

PRIORITIES = [
    PRIORITY_LOW,
    PRIORITY_MEDIUM,
    PRIORITY_HIGH,
    PRIORITY_URGENT,
    PRIORITY_CRITICAL
]

# Severities
SEVERITY_MINOR = "minor"
SEVERITY_MAJOR = "major"
SEVERITY_CRITICAL = "critical"

SEVERITIES = [
    SEVERITY_MINOR,
    SEVERITY_MAJOR,
    SEVERITY_CRITICAL
]

# Red Flag Types
RED_FLAG_TYPE_RISK = "risk"
RED_FLAG_TYPE_COMPLIANCE = "compliance"
RED_FLAG_TYPE_FRAUD = "fraud"
RED_FLAG_TYPE_BEHAVIOR = "behavior"
RED_FLAG_TYPE_FINANCIAL = "financial"
RED_FLAG_TYPE_TECHNICAL = "technical"

RED_FLAG_TYPES = [
    RED_FLAG_TYPE_RISK,
    RED_FLAG_TYPE_COMPLIANCE,
    RED_FLAG_TYPE_FRAUD,
    RED_FLAG_TYPE_BEHAVIOR,
    RED_FLAG_TYPE_FINANCIAL,
    RED_FLAG_TYPE_TECHNICAL
]

# Red Flag Statuses
RED_FLAG_STATUS_ACTIVE = "active"
RED_FLAG_STATUS_RESOLVED = "resolved"
RED_FLAG_STATUS_DISMISSED = "dismissed"
RED_FLAG_STATUS_ESCALATED = "escalated"
RED_FLAG_STATUS_INVESTIGATING = "investigating"

RED_FLAG_STATUSES = [
    RED_FLAG_STATUS_ACTIVE,
    RED_FLAG_STATUS_RESOLVED,
    RED_FLAG_STATUS_DISMISSED,
    RED_FLAG_STATUS_ESCALATED,
    RED_FLAG_STATUS_INVESTIGATING
]