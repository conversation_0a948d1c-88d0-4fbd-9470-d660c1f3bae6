# customers models - SQLAlchemy models for customers module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Index, Date
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY, DECIMAL
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Customer Management Models
class Customer(Base):
    __tablename__ = 'customer'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), unique=True, nullable=False, index=True)
    name = Column(String(255), nullable=False)
    email = Column(String(255), nullable=True, index=True)
    phone = Column(String(50), nullable=True)
    address = Column(Text, nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)

    # Customer classification
    customer_type = Column(String(50), nullable=False, default='individual')  # individual, business, corporate
    customer_segment = Column(String(50), nullable=True)  # premium, standard, basic
    customer_category = Column(String(50), nullable=True)  # new, existing, vip, high_risk

    # Registration and onboarding
    registration_date = Column(DateTime, nullable=True)
    onboarding_date = Column(DateTime, nullable=True)
    onboarding_status = Column(String(50), default='pending')  # pending, in_progress, completed, failed
    onboarding_channel = Column(String(50), nullable=True)  # web, mobile, branch, agent

    # Verification and KYC
    kyc_status = Column(String(50), default='pending')  # pending, in_progress, verified, rejected, expired
    kyc_verification_date = Column(DateTime, nullable=True)
    kyc_expiry_date = Column(DateTime, nullable=True)
    kyc_level = Column(String(20), nullable=True)  # basic, intermediate, full
    identity_verified = Column(Boolean, default=False)
    address_verified = Column(Boolean, default=False)
    phone_verified = Column(Boolean, default=False)
    email_verified = Column(Boolean, default=False)

    # Transaction and activity data
    last_transaction_date = Column(DateTime, nullable=True)
    first_transaction_date = Column(DateTime, nullable=True)
    total_transactions = Column(Integer, default=0)
    total_spent = Column(DECIMAL(15,2), default=0.0)
    average_transaction_amount = Column(DECIMAL(15,2), nullable=True)
    last_login_date = Column(DateTime, nullable=True)
    last_activity_date = Column(DateTime, nullable=True)

    # Risk and scoring
    risk_score = Column(Float, default=0.0)
    credit_score = Column(Float, nullable=True)
    fraud_score = Column(Float, default=0.0)
    behavior_score = Column(Float, nullable=True)
    loyalty_score = Column(Float, nullable=True)

    # Status and lifecycle
    status = Column(String(50), default='active')  # active, inactive, suspended, blocked, closed
    lifecycle_stage = Column(String(50), nullable=True)  # prospect, new, active, dormant, churned
    account_status = Column(String(50), default='open')  # open, closed, frozen, restricted

    # Preferences and settings
    communication_preferences = Column(JSONB, nullable=True)
    notification_preferences = Column(JSONB, nullable=True)
    privacy_settings = Column(JSONB, nullable=True)
    language_preference = Column(String(10), default='en')
    timezone = Column(String(50), default='UTC')

    # Additional attributes
    date_of_birth = Column(Date, nullable=True)
    gender = Column(String(20), nullable=True)
    occupation = Column(String(100), nullable=True)
    income_range = Column(String(50), nullable=True)
    education_level = Column(String(50), nullable=True)
    marital_status = Column(String(20), nullable=True)

    # Business customer specific
    business_name = Column(String(255), nullable=True)
    business_type = Column(String(100), nullable=True)
    business_registration_number = Column(String(100), nullable=True)
    tax_id = Column(String(100), nullable=True)
    industry = Column(String(100), nullable=True)
    annual_revenue = Column(DECIMAL(15,2), nullable=True)
    employee_count = Column(Integer, nullable=True)

    # Digital footprint
    ip_address = Column(String(45), nullable=True)
    device_fingerprint = Column(String(255), nullable=True)
    user_agent = Column(Text, nullable=True)
    referral_source = Column(String(255), nullable=True)
    utm_source = Column(String(100), nullable=True)
    utm_medium = Column(String(100), nullable=True)
    utm_campaign = Column(String(100), nullable=True)

    # Relationship and social
    referrer_customer_id = Column(String(100), nullable=True)
    referred_customers_count = Column(Integer, default=0)
    social_media_profiles = Column(JSONB, nullable=True)
    external_ids = Column(JSONB, nullable=True)  # IDs from external systems

    # Compliance and regulatory
    pep_status = Column(Boolean, default=False)  # Politically Exposed Person
    sanctions_check_status = Column(String(50), nullable=True)
    sanctions_check_date = Column(DateTime, nullable=True)
    aml_status = Column(String(50), nullable=True)  # Anti-Money Laundering
    fatca_status = Column(String(50), nullable=True)  # Foreign Account Tax Compliance Act
    crs_status = Column(String(50), nullable=True)  # Common Reporting Standard

    # Tags and metadata
    tags = Column(ARRAY(String), nullable=True)
    custom_fields = Column(JSONB, nullable=True)
    notes = Column(Text, nullable=True)

    # Audit fields
    created_by = Column(String(255), nullable=False)
    updated_by = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Profiles
class customer_profiles(Base):
    __tablename__ = 'customer_profiles'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    profile_type = Column(String(50), nullable=False)  # personal, business, financial, behavioral

    # Profile data
    profile_data = Column(JSONB, nullable=False)
    profile_version = Column(String(20), nullable=True)
    profile_source = Column(String(100), nullable=True)  # manual, automated, imported, api

    # Verification status
    is_verified = Column(Boolean, default=False)
    verified_by = Column(String(255), nullable=True)
    verified_at = Column(DateTime, nullable=True)
    verification_method = Column(String(100), nullable=True)

    # Profile metadata
    completeness_score = Column(Float, nullable=True)
    accuracy_score = Column(Float, nullable=True)
    last_updated_field = Column(String(100), nullable=True)
    update_frequency = Column(Integer, default=0)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Documents
class customer_documents(Base):
    __tablename__ = 'customer_documents'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    document_type = Column(String(100), nullable=False)  # passport, license, utility_bill, bank_statement
    document_category = Column(String(50), nullable=False)  # identity, address, income, business

    # Document details
    document_number = Column(String(100), nullable=True)
    document_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=True)
    file_size = Column(Integer, nullable=True)
    file_type = Column(String(50), nullable=True)
    file_hash = Column(String(255), nullable=True)

    # Document status
    status = Column(String(50), default='uploaded')  # uploaded, processing, verified, rejected, expired
    verification_status = Column(String(50), nullable=True)
    verification_notes = Column(Text, nullable=True)

    # Document metadata
    issue_date = Column(Date, nullable=True)
    expiry_date = Column(Date, nullable=True)
    issuing_authority = Column(String(255), nullable=True)
    issuing_country = Column(String(100), nullable=True)

    # Processing information
    ocr_data = Column(JSONB, nullable=True)
    extracted_data = Column(JSONB, nullable=True)
    confidence_score = Column(Float, nullable=True)

    # Audit fields
    uploaded_by = Column(String(255), nullable=False)
    verified_by = Column(String(255), nullable=True)
    uploaded_at = Column(DateTime, default=datetime.now)
    verified_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Metrics
class customer_metrics(Base):
    __tablename__ = 'customer_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    metric_type = Column(String(255), nullable=False)
    metric_name = Column(String(255), nullable=False)
    metric_value = Column(JSONB, nullable=False)
    metric_unit = Column(String(50), nullable=True)

    # Metric metadata
    calculation_method = Column(String(100), nullable=True)
    data_source = Column(String(100), nullable=True)
    confidence_level = Column(Float, nullable=True)

    # Time-based metrics
    year = Column(Integer, nullable=True)
    quarter = Column(Integer, nullable=True)
    month = Column(Integer, nullable=True)
    week = Column(Integer, nullable=True)
    financials_date = Column(Date, nullable=True)
    metric_date = Column(Date, nullable=True)

    # Metric status
    is_active = Column(Boolean, default=True)
    is_benchmark = Column(Boolean, default=False)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Red Flags
class customer_red_flags(Base):
    __tablename__ = 'customer_red_flags'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    rule_code = Column(String(100), nullable=False, index=True)
    flag_type = Column(String(50), nullable=False)  # risk, compliance, fraud, behavior

    # Flag details
    description = Column(Text, nullable=True)
    severity = Column(String(50), nullable=True)  # low, medium, high, critical
    priority = Column(String(50), nullable=True)  # low, medium, high, urgent
    category = Column(String(50), nullable=True)  # financial, behavioral, compliance, technical

    # Flag data
    metric_values = Column(JSONB, nullable=True)
    trigger_conditions = Column(JSONB, nullable=True)
    additional_data = Column(JSONB, nullable=True)

    # Flag status
    status = Column(String(50), default='active')  # active, resolved, dismissed, escalated
    resolution_status = Column(String(50), nullable=True)
    resolution_notes = Column(Text, nullable=True)

    # Timing information
    metric_data_timestamp = Column(DateTime, nullable=True)
    detected_at = Column(DateTime, default=datetime.now)
    resolved_at = Column(DateTime, nullable=True)

    # Assignment and handling
    assigned_to = Column(String(255), nullable=True)
    reviewed_by = Column(String(255), nullable=True)
    notes = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Transactions
class customer_transactions(Base):
    __tablename__ = 'customer_transactions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_id = Column(String(100), unique=True, nullable=False, index=True)
    customer_id = Column(String(100), nullable=False, index=True)
    merchant_id = Column(String(100), nullable=True, index=True)

    # Transaction details
    transaction_type = Column(String(50), nullable=False)  # payment, refund, chargeback, transfer
    transaction_category = Column(String(50), nullable=True)  # purchase, subscription, fee, penalty
    amount = Column(DECIMAL(15,2), nullable=False)
    currency = Column(String(10), nullable=False, default='USD')
    exchange_rate = Column(Float, nullable=True)

    # Transaction status
    status = Column(String(50), nullable=False)  # pending, completed, failed, cancelled, disputed
    payment_method = Column(String(50), nullable=True)  # card, bank_transfer, wallet, crypto
    payment_channel = Column(String(50), nullable=True)  # online, mobile, pos, atm

    # Transaction metadata
    description = Column(Text, nullable=True)
    reference_number = Column(String(100), nullable=True)
    authorization_code = Column(String(50), nullable=True)

    # Risk and fraud
    risk_score = Column(Float, nullable=True)
    fraud_score = Column(Float, nullable=True)
    is_fraud_transaction = Column(Boolean, default=False)
    is_suspicious = Column(Boolean, default=False)

    # Customer context
    customer_ip = Column(String(45), nullable=True)
    customer_device_id = Column(String(255), nullable=True)
    customer_location = Column(String(255), nullable=True)
    is_customer_international = Column(Boolean, default=False)

    # Transaction timing
    transaction_timestamp = Column(DateTime, nullable=False)
    processed_at = Column(DateTime, nullable=True)
    settled_at = Column(DateTime, nullable=True)

    # Additional flags
    is_chargeback = Column(Boolean, default=False)
    is_refund = Column(Boolean, default=False)
    is_cancelled = Column(Boolean, default=False)
    has_customer_complaint = Column(Boolean, default=False)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Relationships
class customer_relationships(Base):
    __tablename__ = 'customer_relationships'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    related_customer_id = Column(String(100), nullable=False, index=True)
    relationship_type = Column(String(50), nullable=False)  # family, business, referral, shared_device

    # Relationship details
    relationship_strength = Column(Float, nullable=True)  # 0.0 to 1.0
    relationship_status = Column(String(50), default='active')  # active, inactive, terminated
    relationship_direction = Column(String(20), nullable=True)  # bidirectional, unidirectional

    # Relationship metadata
    confidence_score = Column(Float, nullable=True)
    evidence_data = Column(JSONB, nullable=True)
    detection_method = Column(String(100), nullable=True)  # manual, automated, ml_model

    # Relationship timing
    relationship_start_date = Column(DateTime, nullable=True)
    relationship_end_date = Column(DateTime, nullable=True)
    last_interaction_date = Column(DateTime, nullable=True)

    # Verification
    is_verified = Column(Boolean, default=False)
    verified_by = Column(String(255), nullable=True)
    verified_at = Column(DateTime, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Communications
class customer_communications(Base):
    __tablename__ = 'customer_communications'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    communication_type = Column(String(50), nullable=False)  # email, sms, call, chat, notification
    communication_channel = Column(String(50), nullable=False)  # email, phone, app, web, social

    # Communication details
    subject = Column(String(500), nullable=True)
    message = Column(Text, nullable=True)
    template_id = Column(String(100), nullable=True)
    campaign_id = Column(String(100), nullable=True)

    # Communication direction
    direction = Column(String(20), nullable=False)  # inbound, outbound
    sender = Column(String(255), nullable=True)
    recipient = Column(String(255), nullable=True)

    # Communication status
    status = Column(String(50), nullable=False)  # sent, delivered, read, failed, bounced
    delivery_status = Column(String(50), nullable=True)
    read_status = Column(String(50), nullable=True)
    response_status = Column(String(50), nullable=True)

    # Communication metadata
    priority = Column(String(20), default='normal')  # low, normal, high, urgent
    tags = Column(ARRAY(String), nullable=True)
    attachments = Column(JSONB, nullable=True)

    # Timing information
    scheduled_at = Column(DateTime, nullable=True)
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    read_at = Column(DateTime, nullable=True)
    responded_at = Column(DateTime, nullable=True)

    # Response tracking
    response_data = Column(JSONB, nullable=True)
    interaction_score = Column(Float, nullable=True)

    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Segments
class customer_segments(Base):
    __tablename__ = 'customer_segments'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    segment_id = Column(String(100), unique=True, nullable=False, index=True)
    segment_name = Column(String(255), nullable=False)
    segment_description = Column(Text, nullable=True)
    segment_type = Column(String(50), nullable=False)  # demographic, behavioral, value, risk

    # Segment criteria
    criteria = Column(JSONB, nullable=False)
    criteria_logic = Column(String(20), default='AND')  # AND, OR

    # Segment metadata
    is_active = Column(Boolean, default=True)
    is_dynamic = Column(Boolean, default=True)  # Auto-update membership
    customer_count = Column(Integer, default=0)

    # Segment settings
    priority = Column(Integer, default=0)
    color_code = Column(String(10), nullable=True)
    tags = Column(ARRAY(String), nullable=True)

    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Segment Memberships
class customer_segment_memberships(Base):
    __tablename__ = 'customer_segment_memberships'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    segment_id = Column(String(100), nullable=False, index=True)

    # Membership details
    membership_type = Column(String(20), default='automatic')  # automatic, manual
    membership_score = Column(Float, nullable=True)  # How well customer fits segment

    # Membership timing
    joined_at = Column(DateTime, default=datetime.now)
    left_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)

    # Membership metadata
    assigned_by = Column(String(255), nullable=True)
    notes = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Analytics
class customer_analytics(Base):
    __tablename__ = 'customer_analytics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    analytics_type = Column(String(50), nullable=False)  # behavior, financial, risk, engagement
    analytics_period = Column(String(20), nullable=False)  # daily, weekly, monthly, quarterly, yearly

    # Analytics data
    analytics_data = Column(JSONB, nullable=False)
    summary_metrics = Column(JSONB, nullable=True)

    # Time period
    period_start = Column(Date, nullable=False)
    period_end = Column(Date, nullable=False)

    # Analytics metadata
    calculation_date = Column(DateTime, default=datetime.now)
    data_quality_score = Column(Float, nullable=True)
    confidence_level = Column(Float, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Support Cases
class customer_support_cases(Base):
    __tablename__ = 'customer_support_cases'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(100), unique=True, nullable=False, index=True)
    customer_id = Column(String(100), nullable=False, index=True)

    # Case details
    case_type = Column(String(50), nullable=False)  # complaint, inquiry, request, technical
    case_category = Column(String(50), nullable=True)  # billing, product, service, technical
    subject = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)

    # Case status
    status = Column(String(50), default='open')  # open, in_progress, resolved, closed, escalated
    priority = Column(String(20), default='medium')  # low, medium, high, urgent
    severity = Column(String(20), nullable=True)  # minor, major, critical

    # Case assignment
    assigned_to = Column(String(255), nullable=True)
    assigned_team = Column(String(100), nullable=True)
    escalated_to = Column(String(255), nullable=True)

    # Case resolution
    resolution = Column(Text, nullable=True)
    resolution_category = Column(String(50), nullable=True)
    customer_satisfaction_score = Column(Integer, nullable=True)  # 1-5

    # Case timing
    opened_at = Column(DateTime, default=datetime.now)
    first_response_at = Column(DateTime, nullable=True)
    resolved_at = Column(DateTime, nullable=True)
    closed_at = Column(DateTime, nullable=True)

    # Case metadata
    channel = Column(String(50), nullable=True)  # email, phone, chat, web, social
    tags = Column(ARRAY(String), nullable=True)
    attachments = Column(JSONB, nullable=True)

    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Preferences
class customer_preferences(Base):
    __tablename__ = 'customer_preferences'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    preference_category = Column(String(50), nullable=False)  # communication, privacy, product, service
    preference_key = Column(String(100), nullable=False)
    preference_value = Column(JSONB, nullable=False)

    # Preference metadata
    preference_source = Column(String(50), nullable=True)  # customer, system, inferred, default
    is_explicit = Column(Boolean, default=True)  # Explicitly set by customer vs inferred
    confidence_score = Column(Float, nullable=True)

    # Preference timing
    set_at = Column(DateTime, default=datetime.now)
    last_used_at = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Customer Journey Events
class customer_journey_events(Base):
    __tablename__ = 'customer_journey_events'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(String(100), nullable=False, index=True)
    event_type = Column(String(50), nullable=False)  # registration, login, purchase, support, churn
    event_name = Column(String(255), nullable=False)
    event_category = Column(String(50), nullable=True)

    # Event details
    event_data = Column(JSONB, nullable=True)
    event_value = Column(DECIMAL(15,2), nullable=True)
    event_properties = Column(JSONB, nullable=True)

    # Event context
    session_id = Column(String(100), nullable=True)
    page_url = Column(String(500), nullable=True)
    referrer_url = Column(String(500), nullable=True)
    user_agent = Column(Text, nullable=True)
    ip_address = Column(String(45), nullable=True)

    # Event timing
    event_timestamp = Column(DateTime, nullable=False)
    duration_seconds = Column(Integer, nullable=True)

    # Event metadata
    source = Column(String(100), nullable=True)  # web, mobile, api, system
    channel = Column(String(50), nullable=True)
    campaign_id = Column(String(100), nullable=True)

    created_at = Column(DateTime, default=datetime.now)

# Add indexes for performance
Index('idx_customers_email_status', Customer.email, Customer.status)
Index('idx_customers_type_segment', Customer.customer_type, Customer.customer_segment)
Index('idx_customers_kyc_status', Customer.kyc_status, Customer.kyc_verification_date)
Index('idx_customers_risk_score', Customer.risk_score.desc())
Index('idx_customers_created_date', Customer.created_at.desc())
Index('idx_customer_profiles_type', customer_profiles.customer_id, customer_profiles.profile_type)
Index('idx_customer_documents_type_status', customer_documents.customer_id, customer_documents.document_type, customer_documents.status)
Index('idx_customer_metrics_type_date', customer_metrics.customer_id, customer_metrics.metric_type, customer_metrics.metric_date.desc())
Index('idx_customer_red_flags_severity', customer_red_flags.customer_id, customer_red_flags.severity, customer_red_flags.status)
Index('idx_customer_transactions_date', customer_transactions.customer_id, customer_transactions.transaction_timestamp.desc())
Index('idx_customer_relationships_type', customer_relationships.customer_id, customer_relationships.relationship_type)
Index('idx_customer_communications_type_status', customer_communications.customer_id, customer_communications.communication_type, customer_communications.status)
Index('idx_customer_segments_active', customer_segments.is_active, customer_segments.segment_type)
Index('idx_customer_segment_memberships_active', customer_segment_memberships.customer_id, customer_segment_memberships.is_active)
Index('idx_customer_analytics_type_period', customer_analytics.customer_id, customer_analytics.analytics_type, customer_analytics.period_start.desc())
Index('idx_customer_support_cases_status', customer_support_cases.customer_id, customer_support_cases.status, customer_support_cases.priority)
Index('idx_customer_preferences_category', customer_preferences.customer_id, customer_preferences.preference_category)
Index('idx_customer_journey_events_type_timestamp', customer_journey_events.customer_id, customer_journey_events.event_type, customer_journey_events.event_timestamp.desc())