# customers exceptions - module-specific errors for customers
from fastapi import HTTPException, status

class CustomerNotFoundError(HTTPException):
    def __init__(self, customer_id: str = None):
        detail = f"Customer '{customer_id}' not found" if customer_id else "Customer not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerAlreadyExistsError(HTTPException):
    def __init__(self, detail: str = "Customer already exists"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidCustomerError(HTTPException):
    def __init__(self, detail: str = "Invalid customer data"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerValidationError(HTTPException):
    def __init__(self, detail: str = "Customer validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create customer"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update customer"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete customer"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerVerificationError(HTTPException):
    def __init__(self, detail: str = "Customer verification failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerKYCError(HTTPException):
    def __init__(self, detail: str = "Customer KYC process failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerRiskAssessmentError(HTTPException):
    def __init__(self, detail: str = "Customer risk assessment failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerOnboardingError(HTTPException):
    def __init__(self, detail: str = "Customer onboarding failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerProfileError(HTTPException):
    def __init__(self, detail: str = "Customer profile error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerProfileNotFoundError(HTTPException):
    def __init__(self, profile_id: str = None):
        detail = f"Customer profile '{profile_id}' not found" if profile_id else "Customer profile not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerDocumentError(HTTPException):
    def __init__(self, detail: str = "Customer document error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerDocumentNotFoundError(HTTPException):
    def __init__(self, document_id: str = None):
        detail = f"Customer document '{document_id}' not found" if document_id else "Customer document not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerDocumentUploadError(HTTPException):
    def __init__(self, detail: str = "Document upload failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerDocumentVerificationError(HTTPException):
    def __init__(self, detail: str = "Document verification failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerTransactionError(HTTPException):
    def __init__(self, detail: str = "Customer transaction error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerTransactionNotFoundError(HTTPException):
    def __init__(self, transaction_id: str = None):
        detail = f"Customer transaction '{transaction_id}' not found" if transaction_id else "Customer transaction not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerRelationshipError(HTTPException):
    def __init__(self, detail: str = "Customer relationship error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerRelationshipNotFoundError(HTTPException):
    def __init__(self, relationship_id: str = None):
        detail = f"Customer relationship '{relationship_id}' not found" if relationship_id else "Customer relationship not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerCommunicationError(HTTPException):
    def __init__(self, detail: str = "Customer communication error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerCommunicationNotFoundError(HTTPException):
    def __init__(self, communication_id: str = None):
        detail = f"Customer communication '{communication_id}' not found" if communication_id else "Customer communication not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerSegmentError(HTTPException):
    def __init__(self, detail: str = "Customer segment error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerSegmentNotFoundError(HTTPException):
    def __init__(self, segment_id: str = None):
        detail = f"Customer segment '{segment_id}' not found" if segment_id else "Customer segment not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerAnalyticsError(HTTPException):
    def __init__(self, detail: str = "Customer analytics error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerMetricsError(HTTPException):
    def __init__(self, detail: str = "Customer metrics error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerRedFlagError(HTTPException):
    def __init__(self, detail: str = "Customer red flag error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerRedFlagNotFoundError(HTTPException):
    def __init__(self, flag_id: str = None):
        detail = f"Customer red flag '{flag_id}' not found" if flag_id else "Customer red flag not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerSupportCaseError(HTTPException):
    def __init__(self, detail: str = "Customer support case error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerSupportCaseNotFoundError(HTTPException):
    def __init__(self, case_id: str = None):
        detail = f"Customer support case '{case_id}' not found" if case_id else "Customer support case not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CustomerPreferenceError(HTTPException):
    def __init__(self, detail: str = "Customer preference error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerJourneyError(HTTPException):
    def __init__(self, detail: str = "Customer journey error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerStatusError(HTTPException):
    def __init__(self, detail: str = "Invalid customer status"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerTypeError(HTTPException):
    def __init__(self, detail: str = "Invalid customer type"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerSegmentationError(HTTPException):
    def __init__(self, detail: str = "Customer segmentation error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerSearchError(HTTPException):
    def __init__(self, detail: str = "Customer search error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerBulkOperationError(HTTPException):
    def __init__(self, detail: str = "Customer bulk operation error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerStatisticsError(HTTPException):
    def __init__(self, detail: str = "Failed to calculate customer statistics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerComplianceError(HTTPException):
    def __init__(self, detail: str = "Customer compliance error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CustomerSanctionsError(HTTPException):
    def __init__(self, detail: str = "Customer sanctions check error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerAMLError(HTTPException):
    def __init__(self, detail: str = "Customer AML check error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerPEPError(HTTPException):
    def __init__(self, detail: str = "Customer PEP check error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerFraudError(HTTPException):
    def __init__(self, detail: str = "Customer fraud detection error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerCreditError(HTTPException):
    def __init__(self, detail: str = "Customer credit assessment error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerBehaviorError(HTTPException):
    def __init__(self, detail: str = "Customer behavior analysis error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerEngagementError(HTTPException):
    def __init__(self, detail: str = "Customer engagement analysis error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerLifecycleError(HTTPException):
    def __init__(self, detail: str = "Customer lifecycle management error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerRetentionError(HTTPException):
    def __init__(self, detail: str = "Customer retention analysis error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerChurnError(HTTPException):
    def __init__(self, detail: str = "Customer churn prediction error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerValueError(HTTPException):
    def __init__(self, detail: str = "Customer value calculation error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerPermissionError(HTTPException):
    def __init__(self, detail: str = "Insufficient permissions for customer operation"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class CustomerAccessDeniedError(HTTPException):
    def __init__(self, customer_id: str = None):
        detail = f"Access denied to customer '{customer_id}'" if customer_id else "Customer access denied"
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class CustomerDataIntegrityError(HTTPException):
    def __init__(self, detail: str = "Customer data integrity error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerConcurrencyError(HTTPException):
    def __init__(self, detail: str = "Customer concurrency error"):
        super().__init__(status_code=status.HTTP_409_CONFLICT, detail=detail)

class CustomerLockError(HTTPException):
    def __init__(self, detail: str = "Customer lock error"):
        super().__init__(status_code=status.HTTP_409_CONFLICT, detail=detail)

class CustomerMaintenanceError(HTTPException):
    def __init__(self, detail: str = "Customer system is under maintenance"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class CustomerCapacityError(HTTPException):
    def __init__(self, detail: str = "Customer system capacity exceeded"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class CustomerNetworkError(HTTPException):
    def __init__(self, detail: str = "Customer network error"):
        super().__init__(status_code=status.HTTP_502_BAD_GATEWAY, detail=detail)

class CustomerStorageError(HTTPException):
    def __init__(self, detail: str = "Customer storage error"):
        super().__init__(status_code=status.HTTP_507_INSUFFICIENT_STORAGE, detail=detail)

class LegacyCustomerError(HTTPException):
    def __init__(self, detail: str = "Legacy customer operation failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CustomerFeatureNotAvailableError(HTTPException):
    def __init__(self, feature: str):
        super().__init__(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail=f"Customer management feature '{feature}' is not available"
        )