# customers router - customer management endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    CustomerCreate, CustomerUpdate, CustomerResponse, CustomerListResponse,
    CustomerProfileCreate, CustomerProfileUpdate, CustomerProfileResponse,
    CustomerDocumentCreate, CustomerDocumentUpdate, CustomerDocumentResponse,
    CustomerVerificationRequest, CustomerKYCRequest, CustomerRiskAssessmentRequest,
    CustomerVerificationResponse, CustomerKYCResponse, CustomerRiskAssessmentResponse,
    CustomerTransactionResponse, CustomerAnalyticsResponse, CustomerJourneyResponse,
    CustomerCommunicationCreate, CustomerCommunicationResponse,
    CustomerSegmentCreate, CustomerSegmentUpdate, CustomerSegmentResponse,
    CustomerStatistics, CustomerBulkOperation, CustomerSearchParams,
    LegacyCustomerCreate, LegacyCustomerResponse, CustomJSONResponse
)
from .service import CustomerManagementService
from .exceptions import CustomerNotFoundError

router = APIRouter()

def get_customer_service(db: Session = Depends(get_db)) -> CustomerManagementService:
    return CustomerManagementService(db)

# Customer Management Endpoints
@router.post("/", response_model=CustomerResponse, status_code=201)
async def create_customer(
    customer: CustomerCreate,
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new customer."""
    return customer_service.create_customer(customer, current_user.email)

@router.get("/", response_model=CustomerListResponse)
async def get_customers(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    customer_type: Optional[str] = Query(None, description="Filter by customer type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    segment: Optional[str] = Query(None, description="Filter by segment"),
    created_from: Optional[str] = Query(None, description="Filter by creation date from"),
    created_to: Optional[str] = Query(None, description="Filter by creation date to"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of customers with filtering."""
    pagination = PaginationParams(page=page, size=size)
    filters = {
        "customer_type": customer_type,
        "status": status,
        "segment": segment,
        "created_from": created_from,
        "created_to": created_to
    }
    return customer_service.get_customers(pagination, filters)

@router.get("/{customer_id}", response_model=CustomerResponse)
async def get_customer(
    customer_id: str = Path(..., description="The customer ID"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer by ID."""
    return customer_service.get_customer_by_id(customer_id)

@router.put("/{customer_id}", response_model=CustomerResponse)
async def update_customer(
    customer_id: str = Path(..., description="The customer ID"),
    customer_data: CustomerUpdate = ...,
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Update customer information."""
    return customer_service.update_customer(customer_id, customer_data, current_user.email)

@router.delete("/{customer_id}")
async def delete_customer(
    customer_id: str = Path(..., description="The customer ID"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Delete customer."""
    customer_service.delete_customer(customer_id, current_user.email)
    return {"message": "Customer deleted successfully"}

# Customer Verification Endpoints
@router.post("/{customer_id}/verify", response_model=CustomerVerificationResponse)
async def verify_customer(
    customer_id: str = Path(..., description="The customer ID"),
    verification_request: CustomerVerificationRequest = ...,
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Verify customer identity, address, phone, or email."""
    return customer_service.verify_customer(customer_id, verification_request, current_user.email)

@router.get("/{customer_id}/verification-status")
async def get_verification_status(
    customer_id: str = Path(..., description="The customer ID"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer verification status."""
    return customer_service.get_verification_status(customer_id)

# KYC Endpoints
@router.post("/{customer_id}/kyc", response_model=CustomerKYCResponse)
async def perform_kyc(
    customer_id: str = Path(..., description="The customer ID"),
    kyc_request: CustomerKYCRequest = ...,
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Perform KYC process for customer."""
    return customer_service.perform_kyc(customer_id, kyc_request, current_user.email)

@router.get("/{customer_id}/kyc-status")
async def get_kyc_status(
    customer_id: str = Path(..., description="The customer ID"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer KYC status."""
    return customer_service.get_kyc_status(customer_id)

# Document Management Endpoints
@router.post("/{customer_id}/documents", response_model=CustomerDocumentResponse)
async def upload_customer_document(
    customer_id: str = Path(..., description="The customer ID"),
    document_type: str = Query(..., description="Type of document"),
    document_category: str = Query(..., description="Category of document"),
    file: UploadFile = File(...),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Upload customer document."""
    return customer_service.upload_document(customer_id, document_type, document_category, file, current_user.email)

@router.get("/{customer_id}/documents", response_model=List[CustomerDocumentResponse])
async def get_customer_documents(
    customer_id: str = Path(..., description="The customer ID"),
    document_type: Optional[str] = Query(None, description="Filter by document type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer documents."""
    return customer_service.get_customer_documents(customer_id, document_type, status)

@router.delete("/documents/{document_id}")
async def delete_customer_document(
    document_id: str = Path(..., description="The document ID"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Delete customer document."""
    customer_service.delete_document(document_id, current_user.email)
    return {"message": "Document deleted successfully"}

# Risk Assessment Endpoints
@router.post("/{customer_id}/risk-assessment", response_model=CustomerRiskAssessmentResponse)
async def perform_risk_assessment(
    customer_id: str = Path(..., description="The customer ID"),
    risk_request: CustomerRiskAssessmentRequest = ...,
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Perform risk assessment for customer."""
    return customer_service.perform_risk_assessment(customer_id, risk_request, current_user.email)

@router.get("/{customer_id}/risk-profile")
async def get_risk_profile(
    customer_id: str = Path(..., description="The customer ID"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer risk profile."""
    return customer_service.get_risk_profile(customer_id)

# Transaction and Analytics Endpoints
@router.get("/{customer_id}/transactions", response_model=List[CustomerTransactionResponse])
async def get_customer_transactions(
    customer_id: str = Path(..., description="The customer ID"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    transaction_type: Optional[str] = Query(None, description="Filter by transaction type"),
    date_from: Optional[str] = Query(None, description="Filter by date from"),
    date_to: Optional[str] = Query(None, description="Filter by date to"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer transaction history."""
    pagination = PaginationParams(page=page, size=size)
    filters = {
        "transaction_type": transaction_type,
        "date_from": date_from,
        "date_to": date_to
    }
    return customer_service.get_customer_transactions(customer_id, pagination, filters)

@router.get("/{customer_id}/analytics", response_model=CustomerAnalyticsResponse)
async def get_customer_analytics(
    customer_id: str = Path(..., description="The customer ID"),
    period: str = Query("monthly", description="Analytics period"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer analytics and insights."""
    return customer_service.get_customer_analytics(customer_id, period)

@router.get("/{customer_id}/journey", response_model=CustomerJourneyResponse)
async def get_customer_journey(
    customer_id: str = Path(..., description="The customer ID"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer journey and lifecycle events."""
    return customer_service.get_customer_journey(customer_id)

# Communication Endpoints
@router.post("/{customer_id}/communications", response_model=CustomerCommunicationResponse)
async def create_customer_communication(
    customer_id: str = Path(..., description="The customer ID"),
    communication: CustomerCommunicationCreate = ...,
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Create customer communication record."""
    return customer_service.create_communication(customer_id, communication, current_user.email)

@router.get("/{customer_id}/communications", response_model=List[CustomerCommunicationResponse])
async def get_customer_communications(
    customer_id: str = Path(..., description="The customer ID"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    communication_type: Optional[str] = Query(None, description="Filter by communication type"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer communication history."""
    pagination = PaginationParams(page=page, size=size)
    return customer_service.get_customer_communications(customer_id, pagination, communication_type)

# Customer Segmentation Endpoints
@router.get("/segments", response_model=List[CustomerSegmentResponse])
async def get_customer_segments(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    segment_type: Optional[str] = Query(None, description="Filter by segment type"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get customer segments."""
    pagination = PaginationParams(page=page, size=size)
    return customer_service.get_customer_segments(pagination, segment_type)

@router.post("/segments", response_model=CustomerSegmentResponse)
async def create_customer_segment(
    segment: CustomerSegmentCreate = ...,
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Create customer segment."""
    return customer_service.create_customer_segment(segment, current_user.email)

@router.put("/segments/{segment_id}", response_model=CustomerSegmentResponse)
async def update_customer_segment(
    segment_id: str = Path(..., description="The segment ID"),
    segment_data: CustomerSegmentUpdate = ...,
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Update customer segment."""
    return customer_service.update_customer_segment(segment_id, segment_data, current_user.email)

# Bulk Operations and Statistics
@router.post("/bulk-operations", response_model=CustomJSONResponse)
async def perform_bulk_operation(
    operation: CustomerBulkOperation = ...,
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Perform bulk customer operations."""
    return customer_service.perform_bulk_operation(operation, current_user.email)

@router.get("/statistics", response_model=CustomerStatistics)
async def get_customer_statistics(
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive customer statistics."""
    return customer_service.get_customer_statistics()

@router.get("/analytics", response_model=CustomJSONResponse)
async def get_customers_analytics(
    period: str = Query("monthly", description="Analytics period"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Get overall customer analytics."""
    return customer_service.get_customers_analytics(period)

# Search and Export
@router.post("/search", response_model=CustomerListResponse)
async def search_customers(
    search_params: CustomerSearchParams = ...,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Search customers with advanced criteria."""
    pagination = PaginationParams(page=page, size=size)
    return customer_service.search_customers(search_params, pagination)

@router.get("/export")
async def export_customers(
    format: str = Query("csv", description="Export format"),
    customer_type: Optional[str] = Query(None, description="Filter by customer type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    customer_service: CustomerManagementService = Depends(get_customer_service),
    current_user: User = Depends(get_current_user)
):
    """Export customer data."""
    filters = {"customer_type": customer_type, "status": status}
    return customer_service.export_customers(format, filters, current_user.email)

# Legacy Compatibility Endpoints
@router.post("/legacy/create", response_model=LegacyCustomerResponse)
async def create_customer_legacy(
    customer: LegacyCustomerCreate = ...,
    customer_service: CustomerManagementService = Depends(get_customer_service)
):
    """Legacy customer creation endpoint."""
    try:
        customer_data = CustomerCreate(
            customer_id=customer.customer_id,
            customer_type=customer.customer_type or "individual",
            first_name=customer.first_name,
            last_name=customer.last_name,
            email=customer.email,
            phone=customer.phone
        )
        result = customer_service.create_customer(customer_data, "legacy_user")
        return LegacyCustomerResponse(
            success=True,
            message="Customer created successfully",
            data={"customer_id": result.customer_id}
        )
    except Exception as e:
        return LegacyCustomerResponse(
            success=False,
            message=f"Failed to create customer: {str(e)}"
        )

# Health Check
@router.get("/health", response_model=CustomJSONResponse)
async def health_check():
    """Customer module health check."""
    return CustomJSONResponse(
        data={"status": "healthy", "module": "customers"},
        message="Customer module is operational"
    )