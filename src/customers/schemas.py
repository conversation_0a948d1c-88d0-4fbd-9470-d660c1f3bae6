# customers schemas - Pydantic models for customers module
from pydantic import BaseModel, ConfigDict, Field, EmailStr
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date
from decimal import Decimal

# Customer Base Schemas
class CustomerBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, max_length=50)
    address: Optional[str] = None
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    customer_type: str = Field(default='individual', max_length=50)
    customer_segment: Optional[str] = Field(None, max_length=50)
    customer_category: Optional[str] = Field(None, max_length=50)
    onboarding_channel: Optional[str] = Field(None, max_length=50)
    kyc_level: Optional[str] = Field(None, max_length=20)
    communication_preferences: Optional[Dict[str, Any]] = None
    notification_preferences: Optional[Dict[str, Any]] = None
    privacy_settings: Optional[Dict[str, Any]] = None
    language_preference: str = Field(default='en', max_length=10)
    timezone: str = Field(default='UTC', max_length=50)
    date_of_birth: Optional[date] = None
    gender: Optional[str] = Field(None, max_length=20)
    occupation: Optional[str] = Field(None, max_length=100)
    income_range: Optional[str] = Field(None, max_length=50)
    education_level: Optional[str] = Field(None, max_length=50)
    marital_status: Optional[str] = Field(None, max_length=20)
    business_name: Optional[str] = Field(None, max_length=255)
    business_type: Optional[str] = Field(None, max_length=100)
    business_registration_number: Optional[str] = Field(None, max_length=100)
    tax_id: Optional[str] = Field(None, max_length=100)
    industry: Optional[str] = Field(None, max_length=100)
    annual_revenue: Optional[Decimal] = None
    employee_count: Optional[int] = None
    referral_source: Optional[str] = Field(None, max_length=255)
    utm_source: Optional[str] = Field(None, max_length=100)
    utm_medium: Optional[str] = Field(None, max_length=100)
    utm_campaign: Optional[str] = Field(None, max_length=100)
    referrer_customer_id: Optional[str] = Field(None, max_length=100)
    social_media_profiles: Optional[Dict[str, Any]] = None
    external_ids: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None

class CustomerCreate(CustomerBase):
    customer_id: Optional[str] = Field(None, max_length=100)

class CustomerUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    email: Optional[EmailStr] = None
    phone: Optional[str] = Field(None, max_length=50)
    address: Optional[str] = None
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    country: Optional[str] = Field(None, max_length=100)
    postal_code: Optional[str] = Field(None, max_length=20)
    customer_type: Optional[str] = Field(None, max_length=50)
    customer_segment: Optional[str] = Field(None, max_length=50)
    customer_category: Optional[str] = Field(None, max_length=50)
    onboarding_status: Optional[str] = Field(None, max_length=50)
    kyc_status: Optional[str] = Field(None, max_length=50)
    kyc_level: Optional[str] = Field(None, max_length=20)
    identity_verified: Optional[bool] = None
    address_verified: Optional[bool] = None
    phone_verified: Optional[bool] = None
    email_verified: Optional[bool] = None
    status: Optional[str] = Field(None, max_length=50)
    lifecycle_stage: Optional[str] = Field(None, max_length=50)
    account_status: Optional[str] = Field(None, max_length=50)
    communication_preferences: Optional[Dict[str, Any]] = None
    notification_preferences: Optional[Dict[str, Any]] = None
    privacy_settings: Optional[Dict[str, Any]] = None
    language_preference: Optional[str] = Field(None, max_length=10)
    timezone: Optional[str] = Field(None, max_length=50)
    date_of_birth: Optional[date] = None
    gender: Optional[str] = Field(None, max_length=20)
    occupation: Optional[str] = Field(None, max_length=100)
    income_range: Optional[str] = Field(None, max_length=50)
    education_level: Optional[str] = Field(None, max_length=50)
    marital_status: Optional[str] = Field(None, max_length=20)
    business_name: Optional[str] = Field(None, max_length=255)
    business_type: Optional[str] = Field(None, max_length=100)
    business_registration_number: Optional[str] = Field(None, max_length=100)
    tax_id: Optional[str] = Field(None, max_length=100)
    industry: Optional[str] = Field(None, max_length=100)
    annual_revenue: Optional[Decimal] = None
    employee_count: Optional[int] = None
    pep_status: Optional[bool] = None
    sanctions_check_status: Optional[str] = Field(None, max_length=50)
    aml_status: Optional[str] = Field(None, max_length=50)
    fatca_status: Optional[str] = Field(None, max_length=50)
    crs_status: Optional[str] = Field(None, max_length=50)
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None

class CustomerResponse(CustomerBase):
    id: UUID
    customer_id: str
    registration_date: Optional[datetime] = None
    onboarding_date: Optional[datetime] = None
    onboarding_status: str
    kyc_status: str
    kyc_verification_date: Optional[datetime] = None
    kyc_expiry_date: Optional[datetime] = None
    identity_verified: bool
    address_verified: bool
    phone_verified: bool
    email_verified: bool
    last_transaction_date: Optional[datetime] = None
    first_transaction_date: Optional[datetime] = None
    total_transactions: int
    total_spent: Decimal
    average_transaction_amount: Optional[Decimal] = None
    last_login_date: Optional[datetime] = None
    last_activity_date: Optional[datetime] = None
    risk_score: float
    credit_score: Optional[float] = None
    fraud_score: float
    behavior_score: Optional[float] = None
    loyalty_score: Optional[float] = None
    status: str
    lifecycle_stage: Optional[str] = None
    account_status: str
    ip_address: Optional[str] = None
    device_fingerprint: Optional[str] = None
    user_agent: Optional[str] = None
    referred_customers_count: int
    pep_status: bool
    sanctions_check_status: Optional[str] = None
    sanctions_check_date: Optional[datetime] = None
    aml_status: Optional[str] = None
    fatca_status: Optional[str] = None
    crs_status: Optional[str] = None
    created_by: str
    updated_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Customer Profile Schemas
class CustomerProfileBase(BaseModel):
    customer_id: str = Field(..., max_length=100)
    profile_type: str = Field(..., max_length=50)
    profile_data: Dict[str, Any] = Field(..., description="Profile data in JSON format")
    profile_version: Optional[str] = Field(None, max_length=20)
    profile_source: Optional[str] = Field(None, max_length=100)

class CustomerProfileCreate(CustomerProfileBase):
    pass

class CustomerProfileUpdate(BaseModel):
    profile_data: Optional[Dict[str, Any]] = None
    profile_version: Optional[str] = Field(None, max_length=20)
    profile_source: Optional[str] = Field(None, max_length=100)
    is_verified: Optional[bool] = None
    verification_method: Optional[str] = Field(None, max_length=100)

class CustomerProfileResponse(CustomerProfileBase):
    id: UUID
    is_verified: bool
    verified_by: Optional[str] = None
    verified_at: Optional[datetime] = None
    verification_method: Optional[str] = None
    completeness_score: Optional[float] = None
    accuracy_score: Optional[float] = None
    last_updated_field: Optional[str] = None
    update_frequency: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Customer Document Schemas
class CustomerDocumentBase(BaseModel):
    customer_id: str = Field(..., max_length=100)
    document_type: str = Field(..., max_length=100)
    document_category: str = Field(..., max_length=50)
    document_name: str = Field(..., min_length=1, max_length=255)
    document_number: Optional[str] = Field(None, max_length=100)
    issue_date: Optional[date] = None
    expiry_date: Optional[date] = None
    issuing_authority: Optional[str] = Field(None, max_length=255)
    issuing_country: Optional[str] = Field(None, max_length=100)

class CustomerDocumentCreate(CustomerDocumentBase):
    file_path: Optional[str] = Field(None, max_length=500)
    file_size: Optional[int] = None
    file_type: Optional[str] = Field(None, max_length=50)
    file_hash: Optional[str] = Field(None, max_length=255)

class CustomerDocumentUpdate(BaseModel):
    document_name: Optional[str] = Field(None, min_length=1, max_length=255)
    document_number: Optional[str] = Field(None, max_length=100)
    status: Optional[str] = Field(None, max_length=50)
    verification_status: Optional[str] = Field(None, max_length=50)
    verification_notes: Optional[str] = None
    issue_date: Optional[date] = None
    expiry_date: Optional[date] = None
    issuing_authority: Optional[str] = Field(None, max_length=255)
    issuing_country: Optional[str] = Field(None, max_length=100)
    ocr_data: Optional[Dict[str, Any]] = None
    extracted_data: Optional[Dict[str, Any]] = None
    confidence_score: Optional[float] = None

class CustomerDocumentResponse(CustomerDocumentBase):
    id: UUID
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    file_type: Optional[str] = None
    file_hash: Optional[str] = None
    status: str
    verification_status: Optional[str] = None
    verification_notes: Optional[str] = None
    ocr_data: Optional[Dict[str, Any]] = None
    extracted_data: Optional[Dict[str, Any]] = None
    confidence_score: Optional[float] = None
    uploaded_by: str
    verified_by: Optional[str] = None
    uploaded_at: datetime
    verified_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Customer Red Flag Schemas
class CustomerRedFlagBase(BaseModel):
    customer_id: str = Field(..., max_length=100)
    rule_code: str = Field(..., max_length=100)
    flag_type: str = Field(..., max_length=50)
    description: Optional[str] = None
    severity: Optional[str] = Field(None, max_length=50)
    priority: Optional[str] = Field(None, max_length=50)
    category: Optional[str] = Field(None, max_length=50)
    metric_values: Optional[Dict[str, Any]] = None
    trigger_conditions: Optional[Dict[str, Any]] = None
    additional_data: Optional[Dict[str, Any]] = None
    metric_data_timestamp: Optional[datetime] = None
    notes: Optional[str] = None

class CustomerRedFlagCreate(CustomerRedFlagBase):
    pass

class CustomerRedFlagUpdate(BaseModel):
    description: Optional[str] = None
    severity: Optional[str] = Field(None, max_length=50)
    priority: Optional[str] = Field(None, max_length=50)
    category: Optional[str] = Field(None, max_length=50)
    status: Optional[str] = Field(None, max_length=50)
    resolution_status: Optional[str] = Field(None, max_length=50)
    resolution_notes: Optional[str] = None
    assigned_to: Optional[str] = Field(None, max_length=255)
    reviewed_by: Optional[str] = Field(None, max_length=255)
    notes: Optional[str] = None

class CustomerRedFlagResponse(CustomerRedFlagBase):
    id: UUID
    status: str
    resolution_status: Optional[str] = None
    resolution_notes: Optional[str] = None
    detected_at: datetime
    resolved_at: Optional[datetime] = None
    assigned_to: Optional[str] = None
    reviewed_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Customer Transaction Schemas
class CustomerTransactionBase(BaseModel):
    customer_id: str = Field(..., max_length=100)
    merchant_id: Optional[str] = Field(None, max_length=100)
    transaction_type: str = Field(..., max_length=50)
    transaction_category: Optional[str] = Field(None, max_length=50)
    amount: Decimal = Field(..., gt=0)
    currency: str = Field(default='USD', max_length=10)
    exchange_rate: Optional[float] = None
    status: str = Field(..., max_length=50)
    payment_method: Optional[str] = Field(None, max_length=50)
    payment_channel: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    reference_number: Optional[str] = Field(None, max_length=100)
    authorization_code: Optional[str] = Field(None, max_length=50)
    transaction_timestamp: datetime

class CustomerTransactionCreate(CustomerTransactionBase):
    transaction_id: Optional[str] = Field(None, max_length=100)

class CustomerTransactionUpdate(BaseModel):
    status: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    risk_score: Optional[float] = None
    fraud_score: Optional[float] = None
    is_fraud_transaction: Optional[bool] = None
    is_suspicious: Optional[bool] = None
    is_chargeback: Optional[bool] = None
    is_refund: Optional[bool] = None
    is_cancelled: Optional[bool] = None
    has_customer_complaint: Optional[bool] = None
    processed_at: Optional[datetime] = None
    settled_at: Optional[datetime] = None

class CustomerTransactionResponse(CustomerTransactionBase):
    id: UUID
    transaction_id: str
    risk_score: Optional[float] = None
    fraud_score: Optional[float] = None
    is_fraud_transaction: bool
    is_suspicious: bool
    customer_ip: Optional[str] = None
    customer_device_id: Optional[str] = None
    customer_location: Optional[str] = None
    is_customer_international: bool
    processed_at: Optional[datetime] = None
    settled_at: Optional[datetime] = None
    is_chargeback: bool
    is_refund: bool
    is_cancelled: bool
    has_customer_complaint: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Customer Relationship Schemas
class CustomerRelationshipBase(BaseModel):
    customer_id: str = Field(..., max_length=100)
    related_customer_id: str = Field(..., max_length=100)
    relationship_type: str = Field(..., max_length=50)
    relationship_strength: Optional[float] = Field(None, ge=0.0, le=1.0)
    relationship_direction: Optional[str] = Field(None, max_length=20)
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    evidence_data: Optional[Dict[str, Any]] = None
    detection_method: Optional[str] = Field(None, max_length=100)
    relationship_start_date: Optional[datetime] = None
    relationship_end_date: Optional[datetime] = None

class CustomerRelationshipCreate(CustomerRelationshipBase):
    pass

class CustomerRelationshipUpdate(BaseModel):
    relationship_type: Optional[str] = Field(None, max_length=50)
    relationship_strength: Optional[float] = Field(None, ge=0.0, le=1.0)
    relationship_status: Optional[str] = Field(None, max_length=50)
    relationship_direction: Optional[str] = Field(None, max_length=20)
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    evidence_data: Optional[Dict[str, Any]] = None
    detection_method: Optional[str] = Field(None, max_length=100)
    relationship_end_date: Optional[datetime] = None
    is_verified: Optional[bool] = None

class CustomerRelationshipResponse(CustomerRelationshipBase):
    id: UUID
    relationship_status: str
    last_interaction_date: Optional[datetime] = None
    is_verified: bool
    verified_by: Optional[str] = None
    verified_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Customer Communication Schemas
class CustomerCommunicationBase(BaseModel):
    customer_id: str = Field(..., max_length=100)
    communication_type: str = Field(..., max_length=50)
    communication_channel: str = Field(..., max_length=50)
    direction: str = Field(..., max_length=20)
    subject: Optional[str] = Field(None, max_length=500)
    message: Optional[str] = None
    template_id: Optional[str] = Field(None, max_length=100)
    campaign_id: Optional[str] = Field(None, max_length=100)
    sender: Optional[str] = Field(None, max_length=255)
    recipient: Optional[str] = Field(None, max_length=255)
    priority: str = Field(default='normal', max_length=20)
    tags: Optional[List[str]] = None
    attachments: Optional[Dict[str, Any]] = None
    scheduled_at: Optional[datetime] = None

class CustomerCommunicationCreate(CustomerCommunicationBase):
    pass

class CustomerCommunicationUpdate(BaseModel):
    status: Optional[str] = Field(None, max_length=50)
    delivery_status: Optional[str] = Field(None, max_length=50)
    read_status: Optional[str] = Field(None, max_length=50)
    response_status: Optional[str] = Field(None, max_length=50)
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    responded_at: Optional[datetime] = None
    response_data: Optional[Dict[str, Any]] = None
    interaction_score: Optional[float] = None

class CustomerCommunicationResponse(CustomerCommunicationBase):
    id: UUID
    status: str
    delivery_status: Optional[str] = None
    read_status: Optional[str] = None
    response_status: Optional[str] = None
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    responded_at: Optional[datetime] = None
    response_data: Optional[Dict[str, Any]] = None
    interaction_score: Optional[float] = None
    created_by: str
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Customer Segment Schemas
class CustomerSegmentBase(BaseModel):
    segment_name: str = Field(..., min_length=1, max_length=255)
    segment_description: Optional[str] = None
    segment_type: str = Field(..., max_length=50)
    criteria: Dict[str, Any] = Field(..., description="Segment criteria in JSON format")
    criteria_logic: str = Field(default='AND', max_length=20)
    is_active: bool = True
    is_dynamic: bool = True
    priority: int = 0
    color_code: Optional[str] = Field(None, max_length=10)
    tags: Optional[List[str]] = None

class CustomerSegmentCreate(CustomerSegmentBase):
    segment_id: Optional[str] = Field(None, max_length=100)

class CustomerSegmentUpdate(BaseModel):
    segment_name: Optional[str] = Field(None, min_length=1, max_length=255)
    segment_description: Optional[str] = None
    segment_type: Optional[str] = Field(None, max_length=50)
    criteria: Optional[Dict[str, Any]] = None
    criteria_logic: Optional[str] = Field(None, max_length=20)
    is_active: Optional[bool] = None
    is_dynamic: Optional[bool] = None
    priority: Optional[int] = None
    color_code: Optional[str] = Field(None, max_length=10)
    tags: Optional[List[str]] = None

class CustomerSegmentResponse(CustomerSegmentBase):
    id: UUID
    segment_id: str
    customer_count: int
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Utility and Request Schemas
class PaginationParams(BaseModel):
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=100)

class CustomerFilterParams(BaseModel):
    customer_type: Optional[str] = None
    customer_segment: Optional[str] = None
    customer_category: Optional[str] = None
    status: Optional[str] = None
    kyc_status: Optional[str] = None
    lifecycle_stage: Optional[str] = None
    account_status: Optional[str] = None
    onboarding_channel: Optional[str] = None
    country: Optional[str] = None
    city: Optional[str] = None
    industry: Optional[str] = None
    risk_score_min: Optional[float] = None
    risk_score_max: Optional[float] = None
    total_spent_min: Optional[Decimal] = None
    total_spent_max: Optional[Decimal] = None
    registration_date_from: Optional[datetime] = None
    registration_date_to: Optional[datetime] = None
    last_activity_from: Optional[datetime] = None
    last_activity_to: Optional[datetime] = None
    tags: Optional[List[str]] = None

class CustomerSearchParams(BaseModel):
    query: str = Field(..., min_length=1)
    search_fields: Optional[List[str]] = None
    customer_types: Optional[List[str]] = None
    statuses: Optional[List[str]] = None

class CustomerVerificationRequest(BaseModel):
    customer_id: str = Field(..., max_length=100)
    verification_type: str = Field(..., max_length=50)  # identity, address, phone, email
    verification_data: Optional[Dict[str, Any]] = None
    verification_method: Optional[str] = Field(None, max_length=100)

class CustomerKYCRequest(BaseModel):
    customer_id: str = Field(..., max_length=100)
    kyc_level: str = Field(..., max_length=20)
    documents: Optional[List[str]] = None  # Document IDs
    verification_notes: Optional[str] = None

class CustomerRiskAssessmentRequest(BaseModel):
    customer_id: str = Field(..., max_length=100)
    assessment_type: str = Field(..., max_length=50)  # fraud, credit, behavior, compliance
    assessment_data: Optional[Dict[str, Any]] = None
    force_recalculation: bool = False

class CustomerStatusUpdateRequest(BaseModel):
    status: Optional[str] = Field(None, max_length=50)
    lifecycle_stage: Optional[str] = Field(None, max_length=50)
    account_status: Optional[str] = Field(None, max_length=50)
    reason: Optional[str] = None
    notes: Optional[str] = None

class BulkCustomerOperation(BaseModel):
    customer_ids: List[str] = Field(..., min_items=1)
    operation: str = Field(..., max_length=50)
    parameters: Optional[Dict[str, Any]] = None

class CustomerStatistics(BaseModel):
    total_customers: int
    active_customers: int
    inactive_customers: int
    suspended_customers: int
    blocked_customers: int
    customers_by_type: Dict[str, int]
    customers_by_segment: Dict[str, int]
    customers_by_status: Dict[str, int]
    customers_by_kyc_status: Dict[str, int]
    customers_by_lifecycle_stage: Dict[str, int]
    customers_by_country: Dict[str, int]
    average_risk_score: Optional[float] = None
    average_transaction_amount: Optional[Decimal] = None
    total_transaction_volume: Optional[Decimal] = None
    kyc_completion_rate: Optional[float] = None
    verification_rates: Optional[Dict[str, float]] = None

class CustomerAnalytics(BaseModel):
    customer_id: str
    analytics_period: str
    transaction_analytics: Optional[Dict[str, Any]] = None
    behavior_analytics: Optional[Dict[str, Any]] = None
    risk_analytics: Optional[Dict[str, Any]] = None
    engagement_analytics: Optional[Dict[str, Any]] = None
    financial_analytics: Optional[Dict[str, Any]] = None
    summary_metrics: Optional[Dict[str, Any]] = None

class CustomerJourneyEvent(BaseModel):
    customer_id: str = Field(..., max_length=100)
    event_type: str = Field(..., max_length=50)
    event_name: str = Field(..., max_length=255)
    event_category: Optional[str] = Field(None, max_length=50)
    event_data: Optional[Dict[str, Any]] = None
    event_value: Optional[Decimal] = None
    event_properties: Optional[Dict[str, Any]] = None
    session_id: Optional[str] = Field(None, max_length=100)
    page_url: Optional[str] = Field(None, max_length=500)
    referrer_url: Optional[str] = Field(None, max_length=500)
    user_agent: Optional[str] = None
    ip_address: Optional[str] = Field(None, max_length=45)
    event_timestamp: datetime
    duration_seconds: Optional[int] = None
    source: Optional[str] = Field(None, max_length=100)
    channel: Optional[str] = Field(None, max_length=50)
    campaign_id: Optional[str] = Field(None, max_length=100)

class CustomerSupportCase(BaseModel):
    customer_id: str = Field(..., max_length=100)
    case_type: str = Field(..., max_length=50)
    case_category: Optional[str] = Field(None, max_length=50)
    subject: str = Field(..., max_length=500)
    description: Optional[str] = None
    priority: str = Field(default='medium', max_length=20)
    severity: Optional[str] = Field(None, max_length=20)
    channel: Optional[str] = Field(None, max_length=50)
    tags: Optional[List[str]] = None
    attachments: Optional[Dict[str, Any]] = None

class CustomerPreference(BaseModel):
    customer_id: str = Field(..., max_length=100)
    preference_category: str = Field(..., max_length=50)
    preference_key: str = Field(..., max_length=100)
    preference_value: Dict[str, Any] = Field(..., description="Preference value in JSON format")
    preference_source: Optional[str] = Field(None, max_length=50)
    is_explicit: bool = True
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    expires_at: Optional[datetime] = None

# Response List Schemas
class CustomerListResponse(BaseModel):
    items: List[CustomerResponse]
    total: int
    page: int
    size: int
    pages: int

class CustomerProfileListResponse(BaseModel):
    items: List[CustomerProfileResponse]
    total: int
    page: int
    size: int
    pages: int

class CustomerDocumentListResponse(BaseModel):
    items: List[CustomerDocumentResponse]
    total: int
    page: int
    size: int
    pages: int

class CustomerRedFlagListResponse(BaseModel):
    items: List[CustomerRedFlagResponse]
    total: int
    page: int
    size: int
    pages: int

class CustomerTransactionListResponse(BaseModel):
    items: List[CustomerTransactionResponse]
    total: int
    page: int
    size: int
    pages: int

class CustomerRelationshipListResponse(BaseModel):
    items: List[CustomerRelationshipResponse]
    total: int
    page: int
    size: int
    pages: int

class CustomerCommunicationListResponse(BaseModel):
    items: List[CustomerCommunicationResponse]
    total: int
    page: int
    size: int
    pages: int

class CustomerSegmentListResponse(BaseModel):
    items: List[CustomerSegmentResponse]
    total: int
    page: int
    size: int
    pages: int

# Legacy Compatibility Schemas
class LegacyCustomerResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class LegacyCustomerCreate(BaseModel):
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    customer_type: Optional[str] = "individual"

class LegacyCustomerUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[str] = None

# Custom Response Schemas
class CustomJSONResponse(BaseModel):
    data: Optional[Any] = None
    status: str = "success"
    message: str = "Operation completed successfully"
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

class CustomerVerificationResponse(BaseModel):
    customer_id: str
    verification_type: str
    verification_status: str
    verification_result: Dict[str, Any]
    confidence_score: Optional[float] = None
    verification_timestamp: datetime
    next_steps: Optional[List[str]] = None

class CustomerRiskAssessmentResponse(BaseModel):
    customer_id: str
    assessment_type: str
    risk_score: float
    risk_level: str
    risk_factors: List[Dict[str, Any]]
    recommendations: List[str]
    assessment_timestamp: datetime
    next_assessment_date: Optional[datetime] = None

class CustomerOnboardingStatus(BaseModel):
    customer_id: str
    onboarding_stage: str
    completion_percentage: float
    completed_steps: List[str]
    pending_steps: List[str]
    required_documents: List[str]
    uploaded_documents: List[str]
    verification_status: Dict[str, str]
    estimated_completion_date: Optional[datetime] = None

class CustomerLifecycleMetrics(BaseModel):
    customer_id: str
    lifecycle_stage: str
    days_in_current_stage: int
    total_customer_lifetime_days: int
    customer_lifetime_value: Optional[Decimal] = None
    acquisition_cost: Optional[Decimal] = None
    retention_probability: Optional[float] = None
    churn_risk_score: Optional[float] = None
    next_best_action: Optional[str] = None

class CustomerEngagementMetrics(BaseModel):
    customer_id: str
    engagement_score: float
    last_engagement_date: Optional[datetime] = None
    engagement_frequency: str
    preferred_channels: List[str]
    response_rates: Dict[str, float]
    interaction_history: List[Dict[str, Any]]
    engagement_trends: Dict[str, Any]