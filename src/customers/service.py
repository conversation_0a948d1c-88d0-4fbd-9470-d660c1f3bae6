# customers service - business logic for customers module
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func, text
from fastapi import HTTPException
from typing import Optional, List, Dict, Any
from uuid import uuid4
from datetime import datetime, timedelta, date
import math
import hashlib
import re

from .models import (
    Customer, customer_profiles, customer_documents, customer_metrics,
    customer_red_flags, customer_transactions, customer_relationships,
    customer_communications, customer_segments, customer_segment_memberships,
    customer_analytics, customer_support_cases, customer_preferences,
    customer_journey_events
)
from .schemas import (
    CustomerCreate, CustomerUpdate, CustomerResponse, CustomerListResponse,
    CustomerProfileCreate, CustomerProfileUpdate, CustomerProfileResponse,
    CustomerDocumentCreate, CustomerDocumentUpdate, CustomerDocumentResponse,
    CustomerRedFlagCreate, CustomerRedFlagUpdate, CustomerRedFlagResponse,
    CustomerTransactionCreate, CustomerTransactionUpdate, CustomerTransactionResponse,
    CustomerRelationshipCreate, CustomerRelationshipUpdate, CustomerRelationshipResponse,
    CustomerCommunicationCreate, CustomerCommunicationUpdate, CustomerCommunicationResponse,
    CustomerSegmentCreate, CustomerSegmentUpdate, CustomerSegmentResponse,
    PaginationParams, CustomerFilterParams, CustomerSearchParams,
    CustomerVerificationRequest, CustomerKYCRequest, CustomerRiskAssessmentRequest,
    CustomerStatusUpdateRequest, BulkCustomerOperation, CustomerStatistics,
    CustomerVerificationResponse, CustomerRiskAssessmentResponse,
    CustomerOnboardingStatus, CustomerLifecycleMetrics, CustomerEngagementMetrics
)
from .exceptions import (
    CustomerNotFoundError, CustomerAlreadyExistsError, InvalidCustomerError,
    CustomerCreationError, CustomerUpdateError, CustomerDeletionError,
    CustomerVerificationError, CustomerKYCError, CustomerRiskAssessmentError,
    CustomerDocumentError, CustomerProfileError, CustomerRelationshipError,
    CustomerCommunicationError, CustomerSegmentError, CustomerAnalyticsError
)

class CustomerManagementService:
    def __init__(self, db: Session):
        self.db = db

    # Customer Management
    def create_customer(self, customer_data: CustomerCreate, created_by: str) -> CustomerResponse:
        """Create a new customer."""
        try:
            # Generate customer ID if not provided
            customer_id = customer_data.customer_id or self.generate_customer_id()

            # Check for duplicates
            existing = self.db.query(Customer).filter(
                or_(
                    Customer.customer_id == customer_id,
                    and_(Customer.email == customer_data.email, customer_data.email is not None)
                )
            ).first()

            if existing:
                if existing.customer_id == customer_id:
                    raise CustomerAlreadyExistsError(f"Customer with ID {customer_id} already exists")
                else:
                    raise CustomerAlreadyExistsError(f"Customer with email {customer_data.email} already exists")

            # Validate customer data
            self._validate_customer_data(customer_data)

            # Create customer
            db_customer = Customer(
                customer_id=customer_id,
                **customer_data.dict(exclude={'customer_id'}),
                registration_date=datetime.now(),
                created_by=created_by
            )

            self.db.add(db_customer)
            self.db.commit()
            self.db.refresh(db_customer)

            # Create initial customer journey event
            self._create_journey_event(
                customer_id, "registration", "Customer Registration",
                {"channel": customer_data.onboarding_channel, "created_by": created_by}
            )

            # Initialize customer analytics
            self._initialize_customer_analytics(customer_id)

            return CustomerResponse.from_orm(db_customer)
        except Exception as e:
            self.db.rollback()
            if isinstance(e, HTTPException):
                raise
            raise CustomerCreationError(f"Failed to create customer: {str(e)}")

    def get_customers(self, pagination: PaginationParams, filters: Optional[CustomerFilterParams] = None) -> CustomerListResponse:
        """Get paginated list of customers with optional filtering."""
        try:
            query = self.db.query(Customer)

            # Apply filters
            if filters:
                if filters.customer_type:
                    query = query.filter(Customer.customer_type == filters.customer_type)
                if filters.customer_segment:
                    query = query.filter(Customer.customer_segment == filters.customer_segment)
                if filters.customer_category:
                    query = query.filter(Customer.customer_category == filters.customer_category)
                if filters.status:
                    query = query.filter(Customer.status == filters.status)
                if filters.kyc_status:
                    query = query.filter(Customer.kyc_status == filters.kyc_status)
                if filters.lifecycle_stage:
                    query = query.filter(Customer.lifecycle_stage == filters.lifecycle_stage)
                if filters.account_status:
                    query = query.filter(Customer.account_status == filters.account_status)
                if filters.onboarding_channel:
                    query = query.filter(Customer.onboarding_channel == filters.onboarding_channel)
                if filters.country:
                    query = query.filter(Customer.country == filters.country)
                if filters.city:
                    query = query.filter(Customer.city == filters.city)
                if filters.industry:
                    query = query.filter(Customer.industry == filters.industry)
                if filters.risk_score_min is not None:
                    query = query.filter(Customer.risk_score >= filters.risk_score_min)
                if filters.risk_score_max is not None:
                    query = query.filter(Customer.risk_score <= filters.risk_score_max)
                if filters.total_spent_min is not None:
                    query = query.filter(Customer.total_spent >= filters.total_spent_min)
                if filters.total_spent_max is not None:
                    query = query.filter(Customer.total_spent <= filters.total_spent_max)
                if filters.registration_date_from:
                    query = query.filter(Customer.registration_date >= filters.registration_date_from)
                if filters.registration_date_to:
                    query = query.filter(Customer.registration_date <= filters.registration_date_to)
                if filters.last_activity_from:
                    query = query.filter(Customer.last_activity_date >= filters.last_activity_from)
                if filters.last_activity_to:
                    query = query.filter(Customer.last_activity_date <= filters.last_activity_to)
                if filters.tags:
                    query = query.filter(Customer.tags.overlap(filters.tags))

            # Get total count
            total = query.count()

            # Apply pagination
            offset = (pagination.page - 1) * pagination.size
            customers = query.order_by(desc(Customer.created_at)).offset(offset).limit(pagination.size).all()

            # Calculate pages
            pages = math.ceil(total / pagination.size) if total > 0 else 1

            return CustomerListResponse(
                items=[CustomerResponse.from_orm(customer) for customer in customers],
                total=total,
                page=pagination.page,
                size=pagination.size,
                pages=pages
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get customers: {str(e)}")

    def get_customer_by_id(self, customer_id: str) -> CustomerResponse:
        """Get a specific customer by ID."""
        customer = self.db.query(Customer).filter(Customer.customer_id == customer_id).first()
        if not customer:
            raise CustomerNotFoundError(customer_id)

        # Update last activity
        customer.last_activity_date = datetime.now()
        self.db.commit()

        return CustomerResponse.from_orm(customer)

    def update_customer(self, customer_id: str, customer_data: CustomerUpdate, updated_by: str) -> CustomerResponse:
        """Update an existing customer."""
        try:
            customer = self.db.query(Customer).filter(Customer.customer_id == customer_id).first()
            if not customer:
                raise CustomerNotFoundError(customer_id)

            # Store old values for audit
            old_values = {}
            new_values = {}

            # Update fields
            update_data = customer_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(customer, field):
                    old_value = getattr(customer, field)
                    if old_value != value:
                        old_values[field] = old_value
                        new_values[field] = value
                        setattr(customer, field, value)

            customer.updated_by = updated_by

            self.db.commit()
            self.db.refresh(customer)

            # Create journey event for significant updates
            if old_values:
                self._create_journey_event(
                    customer_id, "profile_update", "Profile Updated",
                    {"updated_by": updated_by, "changes": {"old": old_values, "new": new_values}}
                )

            return CustomerResponse.from_orm(customer)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise CustomerUpdateError(f"Failed to update customer: {str(e)}")

    # Customer Verification
    def verify_customer(self, verification_request: CustomerVerificationRequest, verified_by: str) -> CustomerVerificationResponse:
        """Verify customer identity, address, phone, or email."""
        try:
            customer = self.db.query(Customer).filter(Customer.customer_id == verification_request.customer_id).first()
            if not customer:
                raise CustomerNotFoundError(verification_request.customer_id)

            verification_type = verification_request.verification_type
            verification_data = verification_request.verification_data or {}
            verification_method = verification_request.verification_method or "manual"

            # Perform verification based on type
            verification_result = self._perform_verification(
                customer, verification_type, verification_data, verification_method
            )

            # Update customer verification status
            if verification_type == "identity":
                customer.identity_verified = verification_result["verified"]
            elif verification_type == "address":
                customer.address_verified = verification_result["verified"]
            elif verification_type == "phone":
                customer.phone_verified = verification_result["verified"]
            elif verification_type == "email":
                customer.email_verified = verification_result["verified"]

            # Update KYC status if all verifications are complete
            self._update_kyc_status(customer)

            customer.updated_by = verified_by
            self.db.commit()

            # Create journey event
            self._create_journey_event(
                verification_request.customer_id, "verification", f"{verification_type.title()} Verification",
                {"verification_type": verification_type, "verified": verification_result["verified"], "verified_by": verified_by}
            )

            return CustomerVerificationResponse(
                customer_id=verification_request.customer_id,
                verification_type=verification_type,
                verification_status="verified" if verification_result["verified"] else "failed",
                verification_result=verification_result,
                confidence_score=verification_result.get("confidence_score"),
                verification_timestamp=datetime.now(),
                next_steps=verification_result.get("next_steps", [])
            )
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise CustomerVerificationError(f"Failed to verify customer: {str(e)}")

    # Helper methods
    def generate_customer_id(self) -> str:
        """Generate a unique customer ID."""
        return f"CUST-{uuid4().hex[:8].upper()}"

    def _validate_customer_data(self, customer_data: CustomerCreate) -> None:
        """Validate customer data."""
        # Email validation
        if customer_data.email:
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, customer_data.email):
                raise InvalidCustomerError("Invalid email format")

        # Phone validation
        if customer_data.phone:
            phone_pattern = r'^\+?[\d\s\-\(\)]{10,}$'
            if not re.match(phone_pattern, customer_data.phone):
                raise InvalidCustomerError("Invalid phone format")

        # Business customer validation
        if customer_data.customer_type == "business":
            if not customer_data.business_name:
                raise InvalidCustomerError("Business name is required for business customers")

    def _perform_verification(self, customer: Customer, verification_type: str,
                            verification_data: Dict[str, Any], verification_method: str) -> Dict[str, Any]:
        """Perform customer verification."""
        # This is a simplified verification - in production, integrate with verification services
        verification_result = {
            "verified": True,
            "confidence_score": 0.95,
            "verification_method": verification_method,
            "verification_data": verification_data,
            "next_steps": []
        }

        if verification_type == "identity":
            # Identity verification logic
            if not verification_data.get("document_id"):
                verification_result["verified"] = False
                verification_result["next_steps"].append("Upload identity document")
        elif verification_type == "address":
            # Address verification logic
            if not verification_data.get("address_proof"):
                verification_result["verified"] = False
                verification_result["next_steps"].append("Upload address proof")
        elif verification_type == "phone":
            # Phone verification logic
            if not verification_data.get("otp_verified"):
                verification_result["verified"] = False
                verification_result["next_steps"].append("Verify OTP")
        elif verification_type == "email":
            # Email verification logic
            if not verification_data.get("email_verified"):
                verification_result["verified"] = False
                verification_result["next_steps"].append("Click email verification link")

        return verification_result

    def _update_kyc_status(self, customer: Customer) -> None:
        """Update customer KYC status based on verification status."""
        if (customer.identity_verified and customer.address_verified and
            customer.phone_verified and customer.email_verified):
            customer.kyc_status = "verified"
            customer.kyc_verification_date = datetime.now()
            customer.kyc_expiry_date = datetime.now() + timedelta(days=365)  # 1 year validity
        elif any([customer.identity_verified, customer.address_verified,
                 customer.phone_verified, customer.email_verified]):
            customer.kyc_status = "in_progress"
        else:
            customer.kyc_status = "pending"

    def _create_journey_event(self, customer_id: str, event_type: str, event_name: str,
                            event_data: Optional[Dict[str, Any]] = None) -> None:
        """Create a customer journey event."""
        try:
            journey_event = customer_journey_events(
                customer_id=customer_id,
                event_type=event_type,
                event_name=event_name,
                event_data=event_data,
                event_timestamp=datetime.now(),
                source="system"
            )
            self.db.add(journey_event)
        except Exception:
            # Don't fail the main operation if journey event creation fails
            pass

    def _initialize_customer_analytics(self, customer_id: str) -> None:
        """Initialize customer analytics."""
        try:
            # Create initial analytics record
            analytics = customer_analytics(
                customer_id=customer_id,
                analytics_type="behavior",
                analytics_period="monthly",
                analytics_data={"initialized": True},
                period_start=date.today().replace(day=1),
                period_end=date.today()
            )
            self.db.add(analytics)
        except Exception:
            # Don't fail the main operation if analytics initialization fails
            pass