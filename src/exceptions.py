# Global exceptions
from fastapi import HTTP<PERSON>xception
from typing import Any, Dict, Optional

class ZeusException(Exception):
    """Base exception for Zeus application"""
    pass

class DatabaseException(ZeusException):
    """Database related exceptions"""
    pass

class AuthenticationException(ZeusException):
    """Authentication related exceptions"""
    pass

class ValidationException(ZeusException):
    """Validation related exceptions"""
    pass

class NotFoundError(HTTPException):
    def __init__(self, detail: str = "Resource not found"):
        super().__init__(status_code=404, detail=detail)

class BadRequestError(HTTPException):
    def __init__(self, detail: str = "Bad request"):
        super().__init__(status_code=400, detail=detail)

class UnauthorizedError(HTTPException):
    def __init__(self, detail: str = "Unauthorized"):
        super().__init__(status_code=401, detail=detail)

class ForbiddenError(HTTPException):
    def __init__(self, detail: str = "Forbidden"):
        super().__init__(status_code=403, detail=detail)

class InternalServerError(HTTPException):
    def __init__(self, detail: str = "Internal server error"):
        super().__init__(status_code=500, detail=detail)
