# investigations constants - module-specific constants for investigations

# Investigation statuses
INVESTIGATION_STATUS_OPEN = "open"
INVESTIGATION_STATUS_IN_PROGRESS = "in_progress"
INVESTIGATION_STATUS_PENDING_REVIEW = "pending_review"
INVESTIGATION_STATUS_CLOSED = "closed"
INVESTIGATION_STATUS_RESOLVED = "resolved"
INVESTIGATION_STATUS_ESCALATED = "escalated"
INVESTIGATION_STATUS_ON_HOLD = "on_hold"

INVESTIGATION_STATUSES = [
    INVESTIGATION_STATUS_OPEN,
    INVESTIGATION_STATUS_IN_PROGRESS,
    INVESTIGATION_STATUS_PENDING_REVIEW,
    INVESTIGATION_STATUS_CLOSED,
    INVESTIGATION_STATUS_RESOLVED,
    INVESTIGATION_STATUS_ESCALATED,
    INVESTIGATION_STATUS_ON_HOLD
]

# Investigation priorities
INVESTIGATION_PRIORITY_LOW = "low"
INVESTIGATION_PRIORITY_MEDIUM = "medium"
INVESTIGATION_PRIORITY_HIGH = "high"
INVESTIGATION_PRIORITY_CRITICAL = "critical"
INVESTIGATION_PRIORITY_URGENT = "urgent"

INVESTIGATION_PRIORITIES = [
    INVESTIGATION_PRIORITY_LOW,
    INVESTIGATION_PRIORITY_MEDIUM,
    INVESTIGATION_PRIORITY_HIGH,
    INVESTIGATION_PRIORITY_CRITICAL,
    INVESTIGATION_PRIORITY_URGENT
]

# Case event types
CASE_EVENT_TYPE_CREATED = "investigation_created"
CASE_EVENT_TYPE_UPDATED = "investigation_updated"
CASE_EVENT_TYPE_STATUS_CHANGED = "status_changed"
CASE_EVENT_TYPE_ASSIGNED = "assigned"
CASE_EVENT_TYPE_NOTE_ADDED = "note_added"
CASE_EVENT_TYPE_DOCUMENT_UPLOADED = "document_uploaded"
CASE_EVENT_TYPE_COMMUNICATION_SENT = "communication_sent"
CASE_EVENT_TYPE_ESCALATED = "escalated"
CASE_EVENT_TYPE_RESOLVED = "resolved"
CASE_EVENT_TYPE_CLOSED = "closed"

CASE_EVENT_TYPES = [
    CASE_EVENT_TYPE_CREATED,
    CASE_EVENT_TYPE_UPDATED,
    CASE_EVENT_TYPE_STATUS_CHANGED,
    CASE_EVENT_TYPE_ASSIGNED,
    CASE_EVENT_TYPE_NOTE_ADDED,
    CASE_EVENT_TYPE_DOCUMENT_UPLOADED,
    CASE_EVENT_TYPE_COMMUNICATION_SENT,
    CASE_EVENT_TYPE_ESCALATED,
    CASE_EVENT_TYPE_RESOLVED,
    CASE_EVENT_TYPE_CLOSED
]

# Communication types
COMMUNICATION_TYPE_EMAIL = "email"
COMMUNICATION_TYPE_PHONE = "phone"
COMMUNICATION_TYPE_SMS = "sms"
COMMUNICATION_TYPE_CHAT = "chat"
COMMUNICATION_TYPE_INTERNAL_NOTE = "internal_note"
COMMUNICATION_TYPE_EXTERNAL_NOTE = "external_note"

COMMUNICATION_TYPES = [
    COMMUNICATION_TYPE_EMAIL,
    COMMUNICATION_TYPE_PHONE,
    COMMUNICATION_TYPE_SMS,
    COMMUNICATION_TYPE_CHAT,
    COMMUNICATION_TYPE_INTERNAL_NOTE,
    COMMUNICATION_TYPE_EXTERNAL_NOTE
]

# Document types
DOCUMENT_TYPE_EVIDENCE = "evidence"
DOCUMENT_TYPE_REPORT = "report"
DOCUMENT_TYPE_SCREENSHOT = "screenshot"
DOCUMENT_TYPE_LOG = "log"
DOCUMENT_TYPE_CORRESPONDENCE = "correspondence"
DOCUMENT_TYPE_LEGAL = "legal"
DOCUMENT_TYPE_FINANCIAL = "financial"

DOCUMENT_TYPES = [
    DOCUMENT_TYPE_EVIDENCE,
    DOCUMENT_TYPE_REPORT,
    DOCUMENT_TYPE_SCREENSHOT,
    DOCUMENT_TYPE_LOG,
    DOCUMENT_TYPE_CORRESPONDENCE,
    DOCUMENT_TYPE_LEGAL,
    DOCUMENT_TYPE_FINANCIAL
]

# Chat message writers
CHAT_WRITER_USER = "user"
CHAT_WRITER_ASSISTANT = "assistant"
CHAT_WRITER_SYSTEM = "system"

CHAT_WRITERS = [
    CHAT_WRITER_USER,
    CHAT_WRITER_ASSISTANT,
    CHAT_WRITER_SYSTEM
]

# Timeline event types
TIMELINE_EVENT_TYPE_TRANSACTION = "transaction"
TIMELINE_EVENT_TYPE_LOGIN = "login"
TIMELINE_EVENT_TYPE_REGISTRATION = "registration"
TIMELINE_EVENT_TYPE_VERIFICATION = "verification"
TIMELINE_EVENT_TYPE_COMPLAINT = "complaint"
TIMELINE_EVENT_TYPE_CHARGEBACK = "chargeback"
TIMELINE_EVENT_TYPE_REFUND = "refund"
TIMELINE_EVENT_TYPE_SUSPENSION = "suspension"

TIMELINE_EVENT_TYPES = [
    TIMELINE_EVENT_TYPE_TRANSACTION,
    TIMELINE_EVENT_TYPE_LOGIN,
    TIMELINE_EVENT_TYPE_REGISTRATION,
    TIMELINE_EVENT_TYPE_VERIFICATION,
    TIMELINE_EVENT_TYPE_COMPLAINT,
    TIMELINE_EVENT_TYPE_CHARGEBACK,
    TIMELINE_EVENT_TYPE_REFUND,
    TIMELINE_EVENT_TYPE_SUSPENSION
]

# Graph types for visualizations
GRAPH_TYPE_LINE = "line"
GRAPH_TYPE_BAR = "bar"
GRAPH_TYPE_PIE = "pie"
GRAPH_TYPE_SCATTER = "scatter"
GRAPH_TYPE_HISTOGRAM = "histogram"
GRAPH_TYPE_HEATMAP = "heatmap"
GRAPH_TYPE_NETWORK = "network"

GRAPH_TYPES = [
    GRAPH_TYPE_LINE,
    GRAPH_TYPE_BAR,
    GRAPH_TYPE_PIE,
    GRAPH_TYPE_SCATTER,
    GRAPH_TYPE_HISTOGRAM,
    GRAPH_TYPE_HEATMAP,
    GRAPH_TYPE_NETWORK
]

# SLA timeframes (in hours)
SLA_LOW_PRIORITY = 72  # 3 days
SLA_MEDIUM_PRIORITY = 48  # 2 days
SLA_HIGH_PRIORITY = 24  # 1 day
SLA_CRITICAL_PRIORITY = 8  # 8 hours
SLA_URGENT_PRIORITY = 4  # 4 hours

SLA_TIMEFRAMES = {
    INVESTIGATION_PRIORITY_LOW: SLA_LOW_PRIORITY,
    INVESTIGATION_PRIORITY_MEDIUM: SLA_MEDIUM_PRIORITY,
    INVESTIGATION_PRIORITY_HIGH: SLA_HIGH_PRIORITY,
    INVESTIGATION_PRIORITY_CRITICAL: SLA_CRITICAL_PRIORITY,
    INVESTIGATION_PRIORITY_URGENT: SLA_URGENT_PRIORITY
}

# Default values
DEFAULT_INVESTIGATION_STATUS = INVESTIGATION_STATUS_OPEN
DEFAULT_INVESTIGATION_PRIORITY = INVESTIGATION_PRIORITY_MEDIUM
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100

# Validation limits
MAX_INVESTIGATION_TITLE_LENGTH = 255
MAX_INVESTIGATION_DESCRIPTION_LENGTH = 5000
MAX_CASE_EVENT_DESCRIPTION_LENGTH = 2000
MAX_NOTE_TITLE_LENGTH = 255
MAX_NOTE_DESCRIPTION_LENGTH = 5000
MAX_CHAT_MESSAGE_LENGTH = 2000

# Chat configuration
CHAT_HISTORY_LIMIT = 100
CHAT_CONTEXT_WINDOW = 20
CHAT_TIMEOUT_MINUTES = 30

# Investigation metrics
MAX_CASELOAD_PER_INVESTIGATOR = 20
MIN_SLA_ADHERENCE_PERCENTAGE = 80
ESCALATION_THRESHOLD_HOURS = 48

# File upload limits
MAX_FILE_SIZE_MB = 50
ALLOWED_FILE_EXTENSIONS = ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.csv', '.xlsx']

# Notification settings
NOTIFICATION_SLA_WARNING_HOURS = 4
NOTIFICATION_ESCALATION_HOURS = 2
NOTIFICATION_OVERDUE_HOURS = 1