# investigations schemas - Pydantic models for investigations module
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime

# Investigation Schemas
class InvestigationBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    status: str = Field(..., min_length=1, max_length=50)
    priority: str = Field(..., min_length=1, max_length=50)
    merchant_id: UUID
    merchant_name: str = Field(..., min_length=1, max_length=255)
    sla_deadline: Optional[str] = None

class InvestigationCreate(InvestigationBase):
    investigation_id: Optional[str] = None
    case_number: Optional[str] = None
    assignee_Name: Optional[str] = Field(None, max_length=100)
    assignee_Email: Optional[str] = Field(None, max_length=100)
    last_updated: Optional[str] = None

class InvestigationUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    status: Optional[str] = Field(None, min_length=1, max_length=50)
    priority: Optional[str] = Field(None, min_length=1, max_length=50)
    assignee_Name: Optional[str] = Field(None, max_length=100)
    assignee_Email: Optional[str] = Field(None, max_length=100)
    sla_deadline: Optional[str] = None
    last_updated: Optional[str] = None

class InvestigationResponse(InvestigationBase):
    id: UUID
    investigation_id: str
    created_by: Optional[str] = None
    case_number: str
    assignee_Name: Optional[str] = None
    assignee_Email: Optional[str] = None
    last_updated: Optional[str] = None
    initiating_email_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Event Schemas
class CaseEventBase(BaseModel):
    investigation_id: str = Field(..., max_length=50)
    timestamp: str = Field(..., max_length=50)
    type: str = Field(..., max_length=255)
    description: Optional[str] = None
    user: str = Field(..., max_length=255)
    content: Optional[str] = None

class CaseEventCreate(CaseEventBase):
    case_event_id: Optional[str] = None

class CaseEventResponse(CaseEventBase):
    id: UUID
    case_event_id: str
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Event Metadata Schemas
class CaseEventMetaDataBase(BaseModel):
    case_event_id: str = Field(..., max_length=50)
    channel: Optional[str] = Field(None, max_length=50)
    oldStatus: Optional[str] = Field(None, max_length=50)
    newStatus: Optional[str] = Field(None, max_length=50)
    documentType: Optional[str] = Field(None, max_length=50)
    communicationType: Optional[str] = Field(None, max_length=50)

class CaseEventMetaDataCreate(CaseEventMetaDataBase):
    pass

class CaseEventMetaDataResponse(CaseEventMetaDataBase):
    id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Investigation Notes Schemas
class InvestigationNoteBase(BaseModel):
    merchant_id: UUID
    investigation_id: str = Field(..., max_length=50)
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    timestamp: Optional[str] = Field(None, max_length=50)

class InvestigationNoteCreate(InvestigationNoteBase):
    pass

class InvestigationNoteResponse(InvestigationNoteBase):
    id: UUID
    created_by: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Investigator Schemas
class InvestigatorBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    email: str = Field(..., min_length=1, max_length=255)
    expertise: Optional[str] = Field(None, max_length=255)

class InvestigatorCreate(InvestigatorBase):
    current_caseload: int = Field(0, ge=0)
    SLA_adherence_percentage: int = Field(0, ge=0, le=100)

class InvestigatorUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    email: Optional[str] = Field(None, min_length=1, max_length=255)
    current_caseload: Optional[int] = Field(None, ge=0)
    expertise: Optional[str] = Field(None, max_length=255)
    SLA_adherence_percentage: Optional[int] = Field(None, ge=0, le=100)
    is_active: Optional[bool] = None

class InvestigatorResponse(InvestigatorBase):
    id: UUID
    current_caseload: int
    SLA_adherence_percentage: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Chat Schemas
class ChatCreateRequest(BaseModel):
    merchant_id: Optional[UUID] = None
    has_visualization: bool = False
    visualization_id: Optional[str] = None

class ChatResponse(BaseModel):
    chat_id: UUID
    user_id: Optional[UUID] = None
    merchant_id: Optional[UUID] = None
    chat_title: Optional[str] = None
    has_visualization: bool
    visualization_id: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class ChatMessage(BaseModel):
    chat_id: UUID
    message: str = Field(..., min_length=1)

class ChatHistoryResponse(BaseModel):
    id: UUID
    chat_id: UUID
    message_id: UUID
    writer: str
    message: str
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class ChatPromptGenerationRequest(BaseModel):
    data: str = Field(..., description="Context data for generating prompts")

class ChatPromptGenerationResponse(BaseModel):
    prompts: List[str]

# Timeline Event Schemas
class TimelineEventBase(BaseModel):
    merchant_id: UUID
    time: datetime
    event: str = Field(..., max_length=255)
    type: str = Field(..., max_length=50)
    investigation_id: Optional[str] = Field(None, max_length=50)

class TimelineEventCreate(TimelineEventBase):
    pass

class TimelineEventResponse(TimelineEventBase):
    id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Communication Schemas
class CommunicationBase(BaseModel):
    sender_id: UUID
    receiver_id: UUID
    type: str = Field(..., max_length=50)
    subject: Optional[str] = Field(None, max_length=255)
    content: Optional[str] = None
    timestamp: datetime
    investigation_id: Optional[str] = Field(None, max_length=50)

class CommunicationCreate(CommunicationBase):
    pass

class CommunicationResponse(CommunicationBase):
    id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Graph Specification Schemas
class GraphSpecificationBase(BaseModel):
    chat_id: UUID
    title: Optional[str] = Field(None, max_length=255)
    graph_type: Optional[str] = Field(None, max_length=100)
    timeframe: Optional[str] = Field(None, max_length=100)
    specifications: Optional[Dict[str, Any]] = None

class GraphSpecificationCreate(GraphSpecificationBase):
    pass

class GraphSpecificationResponse(GraphSpecificationBase):
    id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Bulk Operation Schemas
class InvestigationListCreate(BaseModel):
    investigations: List[InvestigationCreate]

class CaseEventListCreate(BaseModel):
    events: List[CaseEventCreate]

class CaseEventMetaDataListCreate(BaseModel):
    meta_data: List[CaseEventMetaDataCreate]

class InvestigatorListCreate(BaseModel):
    investigators: List[InvestigatorCreate]

class TimelineEventListCreate(BaseModel):
    events: List[TimelineEventCreate]

class CommunicationListCreate(BaseModel):
    communications: List[CommunicationCreate]

# Response List Schemas
class InvestigationListResponse(BaseModel):
    data: List[InvestigationResponse]

class CaseEventListResponse(BaseModel):
    data: List[CaseEventResponse]

class CaseEventMetaDataListResponse(BaseModel):
    data: List[CaseEventMetaDataResponse]

class InvestigationNoteListResponse(BaseModel):
    data: List[InvestigationNoteResponse]

class InvestigatorListResponse(BaseModel):
    data: List[InvestigatorResponse]

class ChatHistoryListResponse(BaseModel):
    data: List[ChatHistoryResponse]

class TimelineEventListResponse(BaseModel):
    data: List[TimelineEventResponse]

class CommunicationListResponse(BaseModel):
    data: List[CommunicationResponse]

# Investigation Analytics Schemas
class InvestigationStatistics(BaseModel):
    total_investigations: int
    open_investigations: int
    closed_investigations: int
    high_priority_investigations: int
    overdue_investigations: int
    investigations_by_status: Dict[str, int]
    investigations_by_priority: Dict[str, int]
    average_resolution_time_days: Optional[float] = None

class InvestigatorWorkload(BaseModel):
    investigator_id: UUID
    investigator_name: str
    investigator_email: str
    current_caseload: int
    assigned_cases: List[str]
    sla_adherence_percentage: int
    expertise: Optional[str] = None