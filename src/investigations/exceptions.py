# investigations exceptions - module-specific errors for investigations
from fastapi import HTTPException, status

class InvestigationNotFoundError(HTTPException):
    def __init__(self, investigation_id: str = None):
        detail = f"Investigation {investigation_id} not found" if investigation_id else "Investigation not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class InvestigationAlreadyExistsError(HTTPException):
    def __init__(self, investigation_id: str = None):
        detail = f"Investigation {investigation_id} already exists" if investigation_id else "Investigation already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidInvestigationDataError(HTTPException):
    def __init__(self, detail: str = "Invalid investigation data"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvestigationValidationError(HTTPException):
    def __init__(self, detail: str = "Investigation validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidInvestigationStatusError(HTTPException):
    def __init__(self, status: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid investigation status: {status}"
        )

class InvalidInvestigationPriorityError(HTTPException):
    def __init__(self, priority: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid investigation priority: {priority}"
        )

class InvestigationCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create investigation"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvestigationUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update investigation"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvestigationDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete investigation"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseEventNotFoundError(HTTPException):
    def __init__(self, event_id: str = None):
        detail = f"Case event {event_id} not found" if event_id else "Case event not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseEventCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create case event"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidCaseEventTypeError(HTTPException):
    def __init__(self, event_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid case event type: {event_type}"
        )

class InvestigationNoteNotFoundError(HTTPException):
    def __init__(self, note_id: str = None):
        detail = f"Investigation note {note_id} not found" if note_id else "Investigation note not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class InvestigationNoteCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create investigation note"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvestigatorNotFoundError(HTTPException):
    def __init__(self, investigator_id: str = None):
        detail = f"Investigator {investigator_id} not found" if investigator_id else "Investigator not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class InvestigatorAlreadyExistsError(HTTPException):
    def __init__(self, email: str = None):
        detail = f"Investigator with email {email} already exists" if email else "Investigator already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvestigatorCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create investigator"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvestigatorUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update investigator"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ChatNotFoundError(HTTPException):
    def __init__(self, chat_id: str = None):
        detail = f"Chat {chat_id} not found" if chat_id else "Chat not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class ChatCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create chat"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ChatMessageError(HTTPException):
    def __init__(self, detail: str = "Failed to process chat message"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidChatMessageError(HTTPException):
    def __init__(self, detail: str = "Invalid chat message"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class ChatHistoryError(HTTPException):
    def __init__(self, detail: str = "Failed to retrieve chat history"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class TimelineEventNotFoundError(HTTPException):
    def __init__(self, event_id: str = None):
        detail = f"Timeline event {event_id} not found" if event_id else "Timeline event not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class TimelineEventCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create timeline event"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CommunicationNotFoundError(HTTPException):
    def __init__(self, communication_id: str = None):
        detail = f"Communication {communication_id} not found" if communication_id else "Communication not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CommunicationCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create communication"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidCommunicationTypeError(HTTPException):
    def __init__(self, communication_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid communication type: {communication_type}"
        )

class InvestigationAccessDeniedError(HTTPException):
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to investigation data"
        )

class InvestigationStatisticsError(HTTPException):
    def __init__(self, detail: str = "Failed to generate investigation statistics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvestigatorWorkloadError(HTTPException):
    def __init__(self, detail: str = "Failed to get investigator workload"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class SLAViolationError(HTTPException):
    def __init__(self, detail: str = "SLA violation detected"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvestigationContextError(HTTPException):
    def __init__(self, detail: str = "Failed to get investigation context"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class VisualizationError(HTTPException):
    def __init__(self, detail: str = "Visualization error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidGraphTypeError(HTTPException):
    def __init__(self, graph_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid graph type: {graph_type}"
        )

class DocumentUploadError(HTTPException):
    def __init__(self, detail: str = "Failed to upload document"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidDocumentTypeError(HTTPException):
    def __init__(self, document_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid document type: {document_type}"
        )

class InvestigationConfigurationError(HTTPException):
    def __init__(self, detail: str = "Investigation configuration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)