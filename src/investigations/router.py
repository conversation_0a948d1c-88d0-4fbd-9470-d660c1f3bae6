# investigations router - investigations and chat management endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    InvestigationCreate, InvestigationUpdate, InvestigationResponse,
    CaseEventCreate, CaseEventResponse, CaseEventMetaDataCreate, CaseEventMetaDataResponse,
    InvestigationNoteCreate, InvestigationNoteResponse, InvestigatorCreate,
    InvestigatorUpdate, InvestigatorResponse, ChatCreateRequest, ChatResponse,
    ChatMessage, ChatHistoryResponse, TimelineEventCreate, TimelineEventResponse,
    CommunicationCreate, CommunicationResponse, InvestigationStatistics,
    InvestigatorWorkload, ChatPromptGenerationRequest, ChatPromptGenerationResponse,
    InvestigationListResponse, CaseEventListResponse, CaseEventMetaDataListResponse,
    InvestigationNoteListResponse, InvestigatorListResponse, ChatHistoryListResponse,
    TimelineEventListResponse, CommunicationListResponse
)
from .service import InvestigationsService

router = APIRouter()

def get_investigations_service(db: Session = Depends(get_db)) -> InvestigationsService:
    return InvestigationsService(db)

# Investigation Management Endpoints
@router.post("/", response_model=InvestigationResponse, status_code=201)
async def create_investigation(
    investigation: InvestigationCreate,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new investigation."""
    return investigations_service.create_investigation(investigation, current_user.email)

@router.get("/", response_model=InvestigationListResponse)
async def get_investigations(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by status"),
    priority: Optional[str] = Query(None, description="Filter by priority"),
    assignee: Optional[str] = Query(None, description="Filter by assignee email"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of investigations with filters."""
    pagination = PaginationParams(page=page, size=size)
    result = investigations_service.get_investigations(pagination, status, priority, assignee)
    return {"data": result.items}

@router.get("/{investigation_id}", response_model=InvestigationResponse)
async def get_investigation(
    investigation_id: str = Path(..., description="The investigation ID"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get investigation by ID."""
    return investigations_service.get_investigation_by_id(investigation_id)

@router.put("/{investigation_id}", response_model=InvestigationResponse)
async def update_investigation(
    investigation_id: str = Path(..., description="The investigation ID"),
    investigation_data: InvestigationUpdate = None,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Update an investigation."""
    return investigations_service.update_investigation(investigation_id, investigation_data, current_user.email)

@router.delete("/{investigation_id}")
async def delete_investigation(
    investigation_id: str = Path(..., description="The investigation ID"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Delete an investigation."""
    return investigations_service.delete_investigation(investigation_id)

@router.get("/merchants/{merchant_id}", response_model=InvestigationListResponse)
async def get_merchant_investigations(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get investigations for a specific merchant."""
    pagination = PaginationParams(page=page, size=size)
    result = investigations_service.get_merchant_investigations(merchant_id, pagination)
    return {"data": result.items}

# Chat Management Endpoints
@router.post("/chat/new", response_model=ChatResponse)
async def create_new_chat(
    chat_request: ChatCreateRequest,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new chat session."""
    return investigations_service.create_chat(chat_request, current_user.id)

@router.get("/chat/{chat_id}/history", response_model=ChatHistoryListResponse)
async def get_chat_history(
    chat_id: UUID = Path(..., description="The chat ID"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get chat history for a specific chat."""
    history = investigations_service.get_chat_history(chat_id)
    return {"data": history}

@router.post("/chat/{chat_id}/message", response_model=ChatHistoryResponse)
async def add_chat_message(
    chat_id: UUID = Path(..., description="The chat ID"),
    message_data: ChatMessage = None,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Add a message to chat history."""
    return investigations_service.add_chat_message(chat_id, message_data.message, "user")

@router.post("/chat/prompts/generate", response_model=ChatPromptGenerationResponse)
async def generate_chat_prompts(
    request: ChatPromptGenerationRequest,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Generate investigation prompts based on context data."""
    return investigations_service.generate_chat_prompts(request)

# Legacy Chat Endpoints (for backward compatibility)
@router.get("/new-chat")
async def new_chat_legacy(
    investigations_service: InvestigationsService = Depends(get_investigations_service)
):
    """Create new chat (legacy endpoint)."""
    chat_request = ChatCreateRequest(has_visualization=False)
    # For legacy compatibility, we'll use a default user ID
    # In production, this should be properly authenticated
    from uuid import uuid4
    default_user_id = uuid4()  # This should be replaced with proper auth
    chat = investigations_service.create_chat(chat_request, default_user_id)
    return {"chat_id": chat.chat_id}

@router.get("/new-visualization-chat")
async def new_visualization_chat_legacy(
    investigations_service: InvestigationsService = Depends(get_investigations_service)
):
    """Create new visualization chat (legacy endpoint)."""
    chat_request = ChatCreateRequest(has_visualization=True)
    # For legacy compatibility, we'll use a default user ID
    from uuid import uuid4
    default_user_id = uuid4()  # This should be replaced with proper auth
    chat = investigations_service.create_chat(chat_request, default_user_id)
    return {"chat_id": chat.chat_id}

@router.post("/chat/{chat_id}")
async def chat_endpoint_legacy(
    chat_id: str,
    request: Request,
    investigations_service: InvestigationsService = Depends(get_investigations_service)
):
    """Legacy chat endpoint for backward compatibility."""
    try:
        body = await request.json()
        prompt = body.get("prompt")

        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")

        # Add user message
        user_message = investigations_service.add_chat_message(UUID(chat_id), prompt, "user")

        # This would integrate with the existing chat logic
        # For now, returning a placeholder response
        assistant_response = "Chat functionality will be integrated with existing logic"
        assistant_message = investigations_service.add_chat_message(UUID(chat_id), assistant_response, "assistant")

        return {
            "status": "success",
            "user_message": user_message.dict(),
            "assistant_message": assistant_message.dict()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Case Events Endpoints
@router.post("/case-events/", response_model=CaseEventResponse, status_code=201)
async def create_case_event(
    event: CaseEventCreate,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new case event."""
    return investigations_service.create_case_event(event)

@router.get("/{investigation_id}/case-events", response_model=CaseEventListResponse)
async def get_case_events(
    investigation_id: str = Path(..., description="The investigation ID"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get case events for an investigation."""
    events = investigations_service.get_case_events(investigation_id)
    return {"data": events}

@router.post("/case-event-metadata/", response_model=CaseEventMetaDataResponse, status_code=201)
async def create_case_event_metadata(
    metadata: CaseEventMetaDataCreate,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Create case event metadata."""
    return investigations_service.create_case_event_metadata(metadata)

# Investigation Notes Endpoints
@router.post("/notes/", response_model=InvestigationNoteResponse, status_code=201)
async def create_investigation_note(
    note: InvestigationNoteCreate,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Create an investigation note."""
    return investigations_service.create_investigation_note(note, current_user.email)

@router.get("/{investigation_id}/notes", response_model=InvestigationNoteListResponse)
async def get_investigation_notes(
    investigation_id: str = Path(..., description="The investigation ID"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get notes for an investigation."""
    notes = investigations_service.get_investigation_notes(investigation_id)
    return {"data": notes}

# Investigators Management Endpoints
@router.post("/investigators/", response_model=InvestigatorResponse, status_code=201)
async def create_investigator(
    investigator: InvestigatorCreate,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new investigator."""
    return investigations_service.create_investigator(investigator)

@router.get("/investigators/", response_model=InvestigatorListResponse)
async def get_investigators(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    active_only: bool = Query(True, description="Show only active investigators"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of investigators."""
    pagination = PaginationParams(page=page, size=size)
    result = investigations_service.get_investigators(pagination, active_only)
    return {"data": result.items}

@router.get("/investigators/{investigator_id}", response_model=InvestigatorResponse)
async def get_investigator_by_id(
    investigator_id: UUID = Path(..., description="The investigator ID"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get investigator by ID."""
    # This would need to be implemented in the service
    raise HTTPException(status_code=501, detail="Get investigator by ID not implemented")

@router.get("/investigators/email/{email}", response_model=InvestigatorResponse)
async def get_investigator_by_email(
    email: str = Path(..., description="The investigator email"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get investigator by email."""
    return investigations_service.get_investigator_by_email(email)

@router.put("/investigators/{investigator_id}", response_model=InvestigatorResponse)
async def update_investigator(
    investigator_id: UUID = Path(..., description="The investigator ID"),
    investigator_data: InvestigatorUpdate = None,
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Update an investigator."""
    return investigations_service.update_investigator(investigator_id, investigator_data)

@router.get("/investigators/{investigator_email}/assigned-cases", response_model=InvestigationListResponse)
async def get_assigned_cases(
    investigator_email: str = Path(..., description="The investigator email"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get cases assigned to an investigator."""
    cases = investigations_service.get_assigned_cases(investigator_email)
    return {"data": cases}

@router.get("/investigators/{investigator_email}/workload", response_model=InvestigatorWorkload)
async def get_investigator_workload(
    investigator_email: str = Path(..., description="The investigator email"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get workload information for an investigator."""
    return investigations_service.get_investigator_workload(investigator_email)

# Analytics Endpoints
@router.get("/analytics/statistics", response_model=InvestigationStatistics)
async def get_investigation_statistics(
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get investigation statistics."""
    return investigations_service.get_investigation_statistics()

@router.get("/{investigation_id}/context")
async def get_investigation_context(
    investigation_id: str = Path(..., description="The investigation ID"),
    investigations_service: InvestigationsService = Depends(get_investigations_service),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive context data for an investigation."""
    return investigations_service.get_investigation_context_data(investigation_id)

# Legacy Endpoints (for backward compatibility)
@router.post("/investigations", include_in_schema=False)
async def create_investigation_legacy(
    investigation: InvestigationCreate,
    investigations_service: InvestigationsService = Depends(get_investigations_service)
):
    """Legacy endpoint for creating investigations."""
    # For legacy compatibility, we'll use a default created_by
    result = investigations_service.create_investigation(investigation, "LEGACY_USER")
    return {"investigations": [{"status": "success"}]}

@router.get("/investigations", include_in_schema=False)
async def get_investigations_legacy(
    investigations_service: InvestigationsService = Depends(get_investigations_service)
):
    """Legacy endpoint for getting investigations."""
    pagination = PaginationParams(page=1, size=100)
    result = investigations_service.get_investigations(pagination)

    # Format for legacy compatibility
    data = []
    for investigation in result.items:
        data.append({
            "investigation_id": investigation.investigation_id,
            "case_number": investigation.case_number,
            "title": investigation.title,
            "description": investigation.description,
            "status": investigation.status,
            "priority": investigation.priority,
            "assignee_Name": investigation.assignee_Name,
            "assignee_Email": investigation.assignee_Email,
            "merchant_id": str(investigation.merchant_id),
            "merchant_name": investigation.merchant_name,
            "last_updated": investigation.last_updated,
            "sla_deadline": investigation.sla_deadline,
            "created_at": investigation.created_at.isoformat()
        })

    return {"data": data}