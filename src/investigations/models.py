# investigations models - SQLAlchemy models for investigations module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, JSON, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Core Investigations Model
class investigations(Base):
    __tablename__ = 'investigations'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    investigation_id = Column(String(50), unique=True, index=True)
    created_by = Column(String(50))
    case_number = Column(String(50), unique=True, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(50), nullable=False, index=True)
    priority = Column(String(50), nullable=False)
    assignee_Name = Column(String(100))
    assignee_Email = Column(String(100), index=True)
    merchant_id = Column(UUID(as_uuid=True), index=True)
    merchant_name = Column(String(255))
    last_updated = Column(String(50))
    sla_deadline = Column(String(50))
    initiating_email_id = Column(String(50), default="")
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Case Events Model
class caseEvents(Base):
    __tablename__ = 'caseEvents'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_event_id = Column(String(50), unique=True, index=True)
    investigation_id = Column(String(50), index=True)
    timestamp = Column(String(50))
    type = Column(String(255))
    description = Column(Text)
    user = Column(String(255))
    content = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

# Case Event Metadata Model
class caseEventMetaData(Base):
    __tablename__ = 'caseEventMetaData'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_event_id = Column(String(50), index=True)
    channel = Column(String(50))
    oldStatus = Column(String(50))
    newStatus = Column(String(50))
    documentType = Column(String(50))
    communicationType = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

# Investigation Notes Model
class investigation_notes(Base):
    __tablename__ = 'investigation_notes'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True), index=True)
    investigation_id = Column(String(50), index=True)
    title = Column(String(255))
    description = Column(Text)
    timestamp = Column(String(50))
    created_by = Column(String(100))
    created_at = Column(DateTime, default=datetime.now)

# Investigators Model
class investigators(Base):
    __tablename__ = 'investigators'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    email = Column(String(255), unique=True, nullable=False, index=True)
    current_caseload = Column(Integer, default=0)
    expertise = Column(String(255))
    SLA_adherence_percentage = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Chat Models
class ChatHistory(Base):
    __tablename__ = 'chat_history'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    chat_id = Column(UUID(as_uuid=True), index=True)
    message_id = Column(UUID(as_uuid=True), unique=True)
    writer = Column(String(50))  # 'user' or 'assistant'
    message = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class active_chatids(Base):
    __tablename__ = 'active_chatids'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    chat_id = Column(UUID(as_uuid=True), unique=True, index=True)
    user_id = Column(UUID(as_uuid=True), index=True)
    merchant_id = Column(UUID(as_uuid=True), index=True, nullable=True)
    chat_title = Column(String(255))
    has_visualization = Column(Boolean, default=False)
    visualization_id = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class active_chats(Base):
    __tablename__ = 'active_chats'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    chat_id = Column(UUID(as_uuid=True), unique=True, index=True)
    title = Column(String(255))
    last_chated = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)

# Conversation Summary Context
class SummaryContext(Base):
    __tablename__ = 'conversation_summary_context'
    __table_args__ = {'schema': 'public'}

    chat_id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    summary = Column(Text)
    message_count = Column(Integer, default=0)
    message_history = Column(Text)
    last_updated = Column(DateTime, default=datetime.now)
    last_updated_by = Column(String(100))

# Graph Specifications for Visualizations
class GraphSpecification(Base):
    __tablename__ = "graph_specifications"
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    chat_id = Column(UUID(as_uuid=True), index=True)
    title = Column(String(255))
    graph_type = Column(String(100))
    timeframe = Column(String(100))
    specifications = Column(JSONB)
    created_at = Column(DateTime, default=datetime.now)

# Timeline Events Model
class timeline_events(Base):
    __tablename__ = 'timeline_events'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True), index=True)
    time = Column(DateTime)
    event = Column(String(255))
    type = Column(String(50))
    investigation_id = Column(String(50), nullable=True, index=True)
    created_at = Column(DateTime, default=datetime.now)

# Communications Model
class communications(Base):
    __tablename__ = 'communications'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sender_id = Column(UUID(as_uuid=True), index=True)
    receiver_id = Column(UUID(as_uuid=True), index=True)
    investigation_id = Column(String(50), nullable=True, index=True)
    type = Column(String(50))
    subject = Column(String(255))
    content = Column(Text)
    timestamp = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)