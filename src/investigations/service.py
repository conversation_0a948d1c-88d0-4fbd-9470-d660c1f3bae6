# investigations service - business logic for investigations module
from sqlalchemy.orm import Session
from sqlalchemy import select, text, and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4
from fastapi import HTTPException, status
from datetime import datetime
import logging

from .models import (
    investigations, caseEvents, caseEventMetaData, investigation_notes,
    investigators, ChatHistory, active_chatids, active_chats, SummaryContext,
    GraphSpecification, timeline_events, communications
)
from .schemas import (
    InvestigationCreate, InvestigationUpdate, InvestigationResponse,
    CaseEventCreate, CaseEventResponse, CaseEventMetaDataCreate, CaseEventMetaDataResponse,
    InvestigationNoteCreate, InvestigationNoteResponse, InvestigatorCreate,
    InvestigatorUpdate, InvestigatorResponse, ChatCreateRequest, ChatResponse,
    ChatMessage, ChatHistoryResponse, TimelineEventCreate, TimelineEventResponse,
    CommunicationCreate, CommunicationResponse, GraphSpecificationCreate,
    GraphSpecificationResponse, InvestigationStatistics, InvestigatorWorkload,
    ChatPromptGenerationRequest, ChatPromptGenerationResponse
)
from ..pagination import PaginationParams, PaginatedResponse, paginate_query

logger = logging.getLogger(__name__)

class InvestigationsService:
    def __init__(self, db: Session):
        self.db = db

    def generate_investigation_id(self) -> str:
        """Generate a unique investigation ID."""
        return f"INV-{datetime.now().strftime('%Y%m%d')}-{str(uuid4())[:8].upper()}"

    def generate_case_number(self) -> str:
        """Generate a unique case number."""
        return f"CASE-{datetime.now().strftime('%Y%m%d')}-{str(uuid4())[:8].upper()}"

    def generate_case_event_id(self) -> str:
        """Generate a unique case event ID."""
        return f"EVENT-{datetime.now().strftime('%Y%m%d')}-{str(uuid4())[:8].upper()}"

    # Investigation Management
    def create_investigation(self, investigation_data: InvestigationCreate, created_by: str) -> InvestigationResponse:
        """Create a new investigation."""
        try:
            # Generate IDs if not provided
            investigation_id = investigation_data.investigation_id or self.generate_investigation_id()
            case_number = investigation_data.case_number or self.generate_case_number()

            # Check for duplicates
            existing = self.db.query(investigations).filter(
                or_(
                    investigations.investigation_id == investigation_id,
                    investigations.case_number == case_number
                )
            ).first()

            if existing:
                raise HTTPException(
                    status_code=400,
                    detail="Investigation ID or case number already exists"
                )

            db_investigation = investigations(
                investigation_id=investigation_id,
                case_number=case_number,
                created_by=created_by,
                last_updated=investigation_data.last_updated or datetime.now().isoformat(),
                **investigation_data.dict(exclude={'investigation_id', 'case_number', 'last_updated'})
            )

            self.db.add(db_investigation)
            self.db.commit()
            self.db.refresh(db_investigation)

            return InvestigationResponse.from_orm(db_investigation)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create investigation: {str(e)}")

    def get_investigation_by_id(self, investigation_id: str) -> InvestigationResponse:
        """Get investigation by investigation ID."""
        try:
            investigation = self.db.query(investigations).filter(
                investigations.investigation_id == investigation_id
            ).first()

            if not investigation:
                raise HTTPException(status_code=404, detail="Investigation not found")

            return InvestigationResponse.from_orm(investigation)
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get investigation: {str(e)}")

    def get_investigations(self, pagination: PaginationParams, status_filter: Optional[str] = None,
                          priority_filter: Optional[str] = None, assignee_filter: Optional[str] = None) -> PaginatedResponse[InvestigationResponse]:
        """Get paginated list of investigations with filters."""
        try:
            query = self.db.query(investigations).order_by(investigations.created_at.desc())

            if status_filter:
                query = query.filter(investigations.status == status_filter)
            if priority_filter:
                query = query.filter(investigations.priority == priority_filter)
            if assignee_filter:
                query = query.filter(investigations.assignee_Email == assignee_filter)

            investigation_list, total = paginate_query(query, pagination)

            investigation_responses = [InvestigationResponse.from_orm(inv) for inv in investigation_list]

            return PaginatedResponse.create(investigation_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get investigations: {str(e)}")

    def get_merchant_investigations(self, merchant_id: UUID, pagination: PaginationParams) -> PaginatedResponse[InvestigationResponse]:
        """Get investigations for a specific merchant."""
        try:
            query = self.db.query(investigations).filter(
                investigations.merchant_id == merchant_id
            ).order_by(investigations.created_at.desc())

            investigation_list, total = paginate_query(query, pagination)

            investigation_responses = [InvestigationResponse.from_orm(inv) for inv in investigation_list]

            return PaginatedResponse.create(investigation_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get merchant investigations: {str(e)}")

    def update_investigation(self, investigation_id: str, investigation_data: InvestigationUpdate, updated_by: str) -> InvestigationResponse:
        """Update an existing investigation."""
        try:
            investigation = self.db.query(investigations).filter(
                investigations.investigation_id == investigation_id
            ).first()

            if not investigation:
                raise HTTPException(status_code=404, detail="Investigation not found")

            update_data = investigation_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(investigation, field, value)

            investigation.last_updated = datetime.now().isoformat()
            investigation.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(investigation)

            return InvestigationResponse.from_orm(investigation)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update investigation: {str(e)}")

    def delete_investigation(self, investigation_id: str) -> Dict[str, str]:
        """Delete an investigation and related data."""
        try:
            investigation = self.db.query(investigations).filter(
                investigations.investigation_id == investigation_id
            ).first()

            if not investigation:
                raise HTTPException(status_code=404, detail="Investigation not found")

            # Delete related case events and metadata
            case_events_list = self.db.query(caseEvents).filter(
                caseEvents.investigation_id == investigation_id
            ).all()

            for event in case_events_list:
                # Delete metadata for this event
                self.db.query(caseEventMetaData).filter(
                    caseEventMetaData.case_event_id == event.case_event_id
                ).delete()
                # Delete the event
                self.db.delete(event)

            # Delete investigation notes
            self.db.query(investigation_notes).filter(
                investigation_notes.investigation_id == investigation_id
            ).delete()

            # Delete the investigation
            self.db.delete(investigation)
            self.db.commit()

            return {"status": "success", "message": "Investigation deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete investigation: {str(e)}")

    # Chat Management
    def create_chat(self, chat_request: ChatCreateRequest, user_id: UUID) -> ChatResponse:
        """Create a new chat session."""
        try:
            chat_id = uuid4()

            db_chat = active_chatids(
                chat_id=chat_id,
                user_id=user_id,
                merchant_id=chat_request.merchant_id,
                has_visualization=chat_request.has_visualization,
                visualization_id=chat_request.visualization_id
            )

            self.db.add(db_chat)
            self.db.commit()
            self.db.refresh(db_chat)

            return ChatResponse.from_orm(db_chat)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create chat: {str(e)}")

    def get_chat_history(self, chat_id: UUID) -> List[ChatHistoryResponse]:
        """Get chat history for a specific chat."""
        try:
            # Verify chat exists
            chat = self.db.query(active_chatids).filter(active_chatids.chat_id == chat_id).first()
            if not chat:
                raise HTTPException(status_code=404, detail="Chat not found")

            history = self.db.query(ChatHistory).filter(
                ChatHistory.chat_id == chat_id
            ).order_by(ChatHistory.created_at.asc()).all()

            return [ChatHistoryResponse.from_orm(msg) for msg in history]
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get chat history: {str(e)}")

    def add_chat_message(self, chat_id: UUID, message: str, writer: str) -> ChatHistoryResponse:
        """Add a message to chat history."""
        try:
            # Verify chat exists
            chat = self.db.query(active_chatids).filter(active_chatids.chat_id == chat_id).first()
            if not chat:
                raise HTTPException(status_code=404, detail="Chat not found")

            message_id = uuid4()

            db_message = ChatHistory(
                chat_id=chat_id,
                message_id=message_id,
                writer=writer,
                message=message
            )

            self.db.add(db_message)
            self.db.commit()
            self.db.refresh(db_message)

            return ChatHistoryResponse.from_orm(db_message)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to add chat message: {str(e)}")

    def generate_chat_prompts(self, request: ChatPromptGenerationRequest) -> ChatPromptGenerationResponse:
        """Generate investigation prompts based on context data."""
        try:
            # This would integrate with the existing prompt generation logic
            # For now, returning sample prompts
            sample_prompts = [
                "Transaction patterns",
                "Risk indicators",
                "Merchant behavior",
                "Customer complaints",
                "Compliance issues",
                "Financial anomalies",
                "Network analysis",
                "Digital footprint"
            ]

            return ChatPromptGenerationResponse(prompts=sample_prompts)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to generate prompts: {str(e)}")

    # Case Events Management
    def create_case_event(self, event_data: CaseEventCreate) -> CaseEventResponse:
        """Create a new case event."""
        try:
            # Verify investigation exists
            investigation = self.db.query(investigations).filter(
                investigations.investigation_id == event_data.investigation_id
            ).first()

            if not investigation:
                raise HTTPException(status_code=404, detail="Investigation not found")

            case_event_id = event_data.case_event_id or self.generate_case_event_id()

            db_event = caseEvents(
                case_event_id=case_event_id,
                **event_data.dict(exclude={'case_event_id'})
            )

            self.db.add(db_event)
            self.db.commit()
            self.db.refresh(db_event)

            return CaseEventResponse.from_orm(db_event)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create case event: {str(e)}")

    def get_case_events(self, investigation_id: str) -> List[CaseEventResponse]:
        """Get case events for an investigation."""
        try:
            events = self.db.query(caseEvents).filter(
                caseEvents.investigation_id == investigation_id
            ).order_by(caseEvents.created_at.asc()).all()

            return [CaseEventResponse.from_orm(event) for event in events]
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get case events: {str(e)}")

    def create_case_event_metadata(self, metadata_data: CaseEventMetaDataCreate) -> CaseEventMetaDataResponse:
        """Create case event metadata."""
        try:
            # Verify case event exists
            event = self.db.query(caseEvents).filter(
                caseEvents.case_event_id == metadata_data.case_event_id
            ).first()

            if not event:
                raise HTTPException(status_code=404, detail="Case event not found")

            db_metadata = caseEventMetaData(**metadata_data.dict())

            self.db.add(db_metadata)
            self.db.commit()
            self.db.refresh(db_metadata)

            return CaseEventMetaDataResponse.from_orm(db_metadata)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create case event metadata: {str(e)}")

    # Investigation Notes Management
    def create_investigation_note(self, note_data: InvestigationNoteCreate, created_by: str) -> InvestigationNoteResponse:
        """Create an investigation note."""
        try:
            # Verify investigation exists
            investigation = self.db.query(investigations).filter(
                investigations.investigation_id == note_data.investigation_id
            ).first()

            if not investigation:
                raise HTTPException(status_code=404, detail="Investigation not found")

            db_note = investigation_notes(
                **note_data.dict(),
                created_by=created_by,
                timestamp=note_data.timestamp or datetime.now().isoformat()
            )

            self.db.add(db_note)
            self.db.commit()
            self.db.refresh(db_note)

            return InvestigationNoteResponse.from_orm(db_note)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create investigation note: {str(e)}")

    def get_investigation_notes(self, investigation_id: str) -> List[InvestigationNoteResponse]:
        """Get notes for an investigation."""
        try:
            notes = self.db.query(investigation_notes).filter(
                investigation_notes.investigation_id == investigation_id
            ).order_by(investigation_notes.created_at.desc()).all()

            return [InvestigationNoteResponse.from_orm(note) for note in notes]
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get investigation notes: {str(e)}")

    # Investigators Management
    def create_investigator(self, investigator_data: InvestigatorCreate) -> InvestigatorResponse:
        """Create a new investigator."""
        try:
            # Check if email already exists
            existing = self.db.query(investigators).filter(
                investigators.email == investigator_data.email
            ).first()

            if existing:
                raise HTTPException(status_code=400, detail="Investigator with this email already exists")

            db_investigator = investigators(**investigator_data.dict())

            self.db.add(db_investigator)
            self.db.commit()
            self.db.refresh(db_investigator)

            return InvestigatorResponse.from_orm(db_investigator)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create investigator: {str(e)}")

    def get_investigators(self, pagination: PaginationParams, active_only: bool = True) -> PaginatedResponse[InvestigatorResponse]:
        """Get paginated list of investigators."""
        try:
            query = self.db.query(investigators)

            if active_only:
                query = query.filter(investigators.is_active == True)

            query = query.order_by(investigators.name.asc())

            investigator_list, total = paginate_query(query, pagination)

            investigator_responses = [InvestigatorResponse.from_orm(inv) for inv in investigator_list]

            return PaginatedResponse.create(investigator_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get investigators: {str(e)}")

    def get_investigator_by_email(self, email: str) -> InvestigatorResponse:
        """Get investigator by email."""
        try:
            investigator = self.db.query(investigators).filter(
                investigators.email == email
            ).first()

            if not investigator:
                raise HTTPException(status_code=404, detail="Investigator not found")

            return InvestigatorResponse.from_orm(investigator)
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get investigator: {str(e)}")

    def update_investigator(self, investigator_id: UUID, investigator_data: InvestigatorUpdate) -> InvestigatorResponse:
        """Update an investigator."""
        try:
            investigator = self.db.query(investigators).filter(
                investigators.id == investigator_id
            ).first()

            if not investigator:
                raise HTTPException(status_code=404, detail="Investigator not found")

            update_data = investigator_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(investigator, field, value)

            investigator.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(investigator)

            return InvestigatorResponse.from_orm(investigator)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update investigator: {str(e)}")

    def get_assigned_cases(self, investigator_email: str) -> List[InvestigationResponse]:
        """Get cases assigned to an investigator."""
        try:
            assigned_investigations = self.db.query(investigations).filter(
                investigations.assignee_Email == investigator_email
            ).order_by(investigations.created_at.desc()).all()

            return [InvestigationResponse.from_orm(inv) for inv in assigned_investigations]
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get assigned cases: {str(e)}")

    # Analytics and Statistics
    def get_investigation_statistics(self) -> InvestigationStatistics:
        """Get investigation statistics."""
        try:
            total_investigations = self.db.query(func.count(investigations.id)).scalar()

            # Count by status
            status_counts = self.db.query(
                investigations.status,
                func.count(investigations.id)
            ).group_by(investigations.status).all()

            investigations_by_status = {status: count for status, count in status_counts}

            # Count by priority
            priority_counts = self.db.query(
                investigations.priority,
                func.count(investigations.id)
            ).group_by(investigations.priority).all()

            investigations_by_priority = {priority: count for priority, count in priority_counts}

            # Calculate specific metrics
            open_investigations = investigations_by_status.get('open', 0) + investigations_by_status.get('in_progress', 0)
            closed_investigations = investigations_by_status.get('closed', 0) + investigations_by_status.get('resolved', 0)
            high_priority_investigations = investigations_by_priority.get('high', 0) + investigations_by_priority.get('critical', 0)

            # Calculate overdue investigations (simplified - would need proper SLA logic)
            overdue_investigations = 0  # This would require SLA deadline comparison

            return InvestigationStatistics(
                total_investigations=total_investigations,
                open_investigations=open_investigations,
                closed_investigations=closed_investigations,
                high_priority_investigations=high_priority_investigations,
                overdue_investigations=overdue_investigations,
                investigations_by_status=investigations_by_status,
                investigations_by_priority=investigations_by_priority,
                average_resolution_time_days=None  # Would require calculation
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get investigation statistics: {str(e)}")

    def get_investigator_workload(self, investigator_email: str) -> InvestigatorWorkload:
        """Get workload information for an investigator."""
        try:
            investigator = self.db.query(investigators).filter(
                investigators.email == investigator_email
            ).first()

            if not investigator:
                raise HTTPException(status_code=404, detail="Investigator not found")

            assigned_investigations = self.db.query(investigations).filter(
                investigations.assignee_Email == investigator_email,
                investigations.status.in_(['open', 'in_progress'])
            ).all()

            assigned_cases = [inv.case_number for inv in assigned_investigations]

            return InvestigatorWorkload(
                investigator_id=investigator.id,
                investigator_name=investigator.name,
                investigator_email=investigator.email,
                current_caseload=len(assigned_cases),
                assigned_cases=assigned_cases,
                sla_adherence_percentage=investigator.SLA_adherence_percentage,
                expertise=investigator.expertise
            )
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get investigator workload: {str(e)}")

    # Integration with other modules
    def get_investigation_context_data(self, investigation_id: str) -> Dict[str, Any]:
        """Get context data for an investigation (merchants, transactions, red flags, etc.)."""
        try:
            investigation = self.db.query(investigations).filter(
                investigations.investigation_id == investigation_id
            ).first()

            if not investigation:
                raise HTTPException(status_code=404, detail="Investigation not found")

            # This would integrate with other modules to get comprehensive data
            context_data = {
                "investigation": InvestigationResponse.from_orm(investigation).dict(),
                "merchant_id": str(investigation.merchant_id),
                "case_events": [event.dict() for event in self.get_case_events(investigation_id)],
                "notes": [note.dict() for note in self.get_investigation_notes(investigation_id)]
            }

            # Integration points with other modules would be added here:
            # - Merchant data from merchants module
            # - Red flags from red_flags module
            # - Rules from rules module
            # - Metrics from metrics module

            return context_data
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get investigation context: {str(e)}")