# Global pagination module
from typing import Generic, TypeVar, List, Optional
from pydantic import BaseModel
from sqlalchemy.orm import Query

T = TypeVar('T')

class PaginationParams(BaseModel):
    page: int = 1
    size: int = 20
    
    @property
    def offset(self) -> int:
        return (self.page - 1) * self.size

class PaginatedResponse(BaseModel, Generic[T]):
    items: List[T]
    total: int
    page: int
    size: int
    pages: int
    
    @classmethod
    def create(cls, items: List[T], total: int, page: int, size: int):
        pages = (total + size - 1) // size  # Ceiling division
        return cls(
            items=items,
            total=total,
            page=page,
            size=size,
            pages=pages
        )

def paginate_query(query: Query, pagination: PaginationParams) -> tuple:
    """
    Paginate a SQLAlchemy query
    Returns: (items, total_count)
    """
    total = query.count()
    items = query.offset(pagination.offset).limit(pagination.size).all()
    return items, total
