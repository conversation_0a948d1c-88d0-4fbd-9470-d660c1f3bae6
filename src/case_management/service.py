# case_management service - business logic for case_management module
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func, text
from fastapi import HTTPException
from typing import Optional, List, Dict, Any
from uuid import uuid4
from datetime import datetime, timedelta
import math

from .models import (
    Case, case_workflows, case_activities, case_assignments, case_escalations,
    case_documents, case_comments, case_teams, case_team_members, case_sla_tracking,
    case_relationships, case_templates, case_metrics, case_notifications, case_audit_trail
)
from .schemas import (
    CaseCreate, CaseUpdate, CaseResponse, CaseListResponse,
    CaseWorkflowCreate, CaseWorkflowUpdate, CaseWorkflowResponse,
    CaseActivityCreate, CaseActivityResponse, CaseActivityListResponse,
    CaseAssignmentCreate, CaseAssignmentUpdate, CaseAssignmentResponse, CaseAssignmentListResponse,
    CaseEscalationCreate, CaseEscalationUpdate, CaseEscalationResponse, CaseEscalationListResponse,
    CaseDocumentCreate, CaseDocumentUpdate, CaseDocumentResponse, CaseDocumentListResponse,
    CaseCommentCreate, CaseCommentUpdate, CaseCommentResponse, CaseCommentListResponse,
    CaseTeamCreate, CaseTeamUpdate, CaseTeamResponse, CaseTeamListResponse,
    CaseTeamMemberCreate, CaseTeamMemberUpdate, CaseTeamMemberResponse,
    CaseSLATrackingCreate, CaseSLATrackingUpdate, CaseSLATrackingResponse,
    CaseRelationshipCreate, CaseRelationshipUpdate, CaseRelationshipResponse,
    CaseTemplateCreate, CaseTemplateUpdate, CaseTemplateResponse, CaseTemplateListResponse,
    CaseMetricsCreate, CaseMetricsResponse,
    CaseNotificationCreate, CaseNotificationUpdate, CaseNotificationResponse,
    CaseAuditTrailCreate, CaseAuditTrailResponse,
    PaginationParams, CaseFilterParams, CaseSearchParams,
    CaseAssignmentRequest, CaseStatusUpdateRequest, CaseEscalationRequest,
    BulkCaseOperation, CaseStatistics, CaseWorkloadSummary
)
from .exceptions import (
    CaseNotFoundError, CaseAlreadyExistsError, InvalidCaseError,
    CaseCreationError, CaseUpdateError, CaseDeletionError,
    CaseWorkflowNotFoundError, CaseActivityCreationError,
    CaseAssignmentError, CaseEscalationError, CaseDocumentError,
    CaseTeamNotFoundError, CaseSLAViolationError, CaseAccessDeniedError
)

class CaseManagementService:
    def __init__(self, db: Session):
        self.db = db

    # Case Management
    def create_case(self, case_data: CaseCreate, created_by: str) -> CaseResponse:
        """Create a new case."""
        try:
            # Generate IDs if not provided
            case_id = case_data.case_id or self.generate_case_id()
            case_number = case_data.case_number or self.generate_case_number()

            # Check for duplicates
            existing = self.db.query(Case).filter(
                or_(
                    Case.case_id == case_id,
                    Case.case_number == case_number
                )
            ).first()

            if existing:
                raise CaseAlreadyExistsError(f"Case with ID {case_id} or number {case_number} already exists")

            # Create case
            db_case = Case(
                case_id=case_id,
                case_number=case_number,
                **case_data.dict(exclude={'case_id', 'case_number'}),
                created_by=created_by
            )

            self.db.add(db_case)
            self.db.commit()
            self.db.refresh(db_case)

            # Create initial activity
            self._create_case_activity(
                case_id=case_id,
                activity_type="case_created",
                description=f"Case created: {case_data.title}",
                performed_by=created_by,
                performed_by_type="user"
            )

            # Initialize SLA tracking if applicable
            self._initialize_case_sla(case_id, case_data.case_type, case_data.priority)

            # Create audit trail
            self._create_audit_trail(
                case_id=case_id,
                action="create_case",
                action_category="create",
                user_email=created_by,
                new_value=f"Case {case_number} created"
            )

            return CaseResponse.from_orm(db_case)
        except Exception as e:
            self.db.rollback()
            if isinstance(e, HTTPException):
                raise
            raise CaseCreationError(f"Failed to create case: {str(e)}")

    def get_cases(self, pagination: PaginationParams, filters: Optional[CaseFilterParams] = None) -> CaseListResponse:
        """Get paginated list of cases with optional filtering."""
        try:
            query = self.db.query(Case)

            # Apply filters
            if filters:
                if filters.case_type:
                    query = query.filter(Case.case_type == filters.case_type)
                if filters.category:
                    query = query.filter(Case.category == filters.category)
                if filters.status:
                    query = query.filter(Case.status == filters.status)
                if filters.priority:
                    query = query.filter(Case.priority == filters.priority)
                if filters.assigned_email:
                    query = query.filter(Case.assigned_email == filters.assigned_email)
                if filters.assigned_team:
                    query = query.filter(Case.assigned_team == filters.assigned_team)
                if filters.merchant_id:
                    query = query.filter(Case.merchant_id == filters.merchant_id)
                if filters.created_by:
                    query = query.filter(Case.created_by == filters.created_by)
                if filters.date_from:
                    query = query.filter(Case.created_at >= filters.date_from)
                if filters.date_to:
                    query = query.filter(Case.created_at <= filters.date_to)
                if filters.tags:
                    query = query.filter(Case.tags.overlap(filters.tags))

            # Get total count
            total = query.count()

            # Apply pagination
            offset = (pagination.page - 1) * pagination.size
            cases = query.order_by(desc(Case.created_at)).offset(offset).limit(pagination.size).all()

            # Calculate pages
            pages = math.ceil(total / pagination.size) if total > 0 else 1

            return CaseListResponse(
                items=[CaseResponse.from_orm(case) for case in cases],
                total=total,
                page=pagination.page,
                size=pagination.size,
                pages=pages
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get cases: {str(e)}")

    def get_case_by_id(self, case_id: str) -> CaseResponse:
        """Get a specific case by ID."""
        case = self.db.query(Case).filter(Case.case_id == case_id).first()
        if not case:
            raise CaseNotFoundError(case_id)

        # Update last activity
        case.last_activity_at = datetime.now()
        self.db.commit()

        return CaseResponse.from_orm(case)

    # Helper methods
    def generate_case_id(self) -> str:
        """Generate a unique case ID."""
        return f"CASE-{uuid4().hex[:8].upper()}"

    def generate_case_number(self) -> str:
        """Generate a unique case number."""
        # Get current year and count
        current_year = datetime.now().year
        count = self.db.query(Case).filter(
            func.extract('year', Case.created_at) == current_year
        ).count() + 1
        return f"C{current_year}{count:06d}"

    def _create_case_activity(self, case_id: str, activity_type: str, description: str,
                            performed_by: str, performed_by_type: str = "user",
                            old_values: Optional[Dict] = None, new_values: Optional[Dict] = None) -> None:
        """Create a case activity record."""
        activity = case_activities(
            case_id=case_id,
            activity_id=f"ACT-{uuid4().hex[:8].upper()}",
            activity_type=activity_type,
            description=description,
            performed_by=performed_by,
            performed_by_type=performed_by_type,
            old_values=old_values,
            new_values=new_values
        )
        self.db.add(activity)

    def _initialize_case_sla(self, case_id: str, case_type: str, priority: str) -> None:
        """Initialize SLA tracking for a case."""
        # Define SLA targets based on case type and priority
        sla_targets = {
            ("investigation", "critical"): {"response": 1, "resolution": 24},
            ("investigation", "high"): {"response": 2, "resolution": 48},
            ("investigation", "medium"): {"response": 4, "resolution": 72},
            ("investigation", "low"): {"response": 8, "resolution": 168},
            ("compliance", "critical"): {"response": 0.5, "resolution": 12},
            ("compliance", "high"): {"response": 1, "resolution": 24},
            ("fraud", "critical"): {"response": 0.25, "resolution": 8},
            ("fraud", "high"): {"response": 0.5, "resolution": 16},
        }

        targets = sla_targets.get((case_type, priority))
        if targets:
            start_time = datetime.now()

            # Create response SLA
            if "response" in targets:
                response_target = start_time + timedelta(hours=targets["response"])
                response_sla = case_sla_tracking(
                    case_id=case_id,
                    sla_type="response",
                    sla_name=f"{case_type.title()} Response SLA",
                    target_hours=targets["response"],
                    warning_threshold_hours=targets["response"] * 0.8,
                    status="on_track",
                    start_time=start_time,
                    target_time=response_target,
                    warning_time=start_time + timedelta(hours=targets["response"] * 0.8)
                )
                self.db.add(response_sla)

            # Create resolution SLA
            if "resolution" in targets:
                resolution_target = start_time + timedelta(hours=targets["resolution"])
                resolution_sla = case_sla_tracking(
                    case_id=case_id,
                    sla_type="resolution",
                    sla_name=f"{case_type.title()} Resolution SLA",
                    target_hours=targets["resolution"],
                    warning_threshold_hours=targets["resolution"] * 0.8,
                    status="on_track",
                    start_time=start_time,
                    target_time=resolution_target,
                    warning_time=start_time + timedelta(hours=targets["resolution"] * 0.8)
                )
                self.db.add(resolution_sla)

    def _create_audit_trail(self, case_id: str, action: str, action_category: str,
                          user_email: str, field_name: Optional[str] = None,
                          old_value: Optional[str] = None, new_value: Optional[str] = None) -> None:
        """Create an audit trail record."""
        audit = case_audit_trail(
            case_id=case_id,
            audit_id=f"AUD-{uuid4().hex[:8].upper()}",
            action=action,
            action_category=action_category,
            field_name=field_name,
            old_value=old_value,
            new_value=new_value,
            user_email=user_email
        )
        self.db.add(audit)