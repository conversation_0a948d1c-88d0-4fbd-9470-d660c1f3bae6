# case_management router - case management and investigation endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import datetime

from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    CaseCreate, CaseUpdate, CaseResponse, CaseListResponse,
    CaseNoteCreate, CaseNoteUpdate, CaseNoteResponse,
    CaseDocumentCreate, CaseDocumentUpdate, CaseDocumentResponse,
    CaseAssignmentCreate, CaseAssignmentUpdate, CaseAssignmentResponse,
    CaseTimelineResponse, CaseAnalyticsResponse, CaseStatistics,
    CaseSearchParams, CaseBulkOperation, CaseWorkflowAction,
    CaseEscalationRequest, CaseResolutionRequest, CaseReviewRequest,
    LegacyCaseCreate, LegacyCaseResponse, CustomJSONResponse
)
from .service import CaseManagementService
from .exceptions import CaseNotFoundError

router = APIRouter()

def get_case_service(db: Session = Depends(get_db)) -> CaseManagementService:
    return CaseManagementService(db)

# Case Management Endpoints
@router.post("/", response_model=CaseResponse, status_code=201)
async def create_case(
    case: CaseCreate,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new case."""
    return case_service.create_case(case, current_user.email)

@router.get("/", response_model=CaseListResponse)
async def get_cases(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by case status"),
    priority: Optional[str] = Query(None, description="Filter by priority"),
    category: Optional[str] = Query(None, description="Filter by category"),
    assigned_to: Optional[str] = Query(None, description="Filter by assignee"),
    created_from: Optional[str] = Query(None, description="Filter by creation date from"),
    created_to: Optional[str] = Query(None, description="Filter by creation date to"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of cases with filtering."""
    pagination = PaginationParams(page=page, size=size)
    filters = {
        "status": status,
        "priority": priority,
        "category": category,
        "assigned_to": assigned_to,
        "created_from": created_from,
        "created_to": created_to
    }
    return case_service.get_cases(pagination, filters)

@router.get("/{case_id}", response_model=CaseResponse)
async def get_case(
    case_id: str = Path(..., description="The case ID"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get case by ID."""
    return case_service.get_case_by_id(case_id)

@router.put("/{case_id}", response_model=CaseResponse)
async def update_case(
    case_id: str = Path(..., description="The case ID"),
    case_data: CaseUpdate = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Update case information."""
    return case_service.update_case(case_id, case_data, current_user.email)

@router.delete("/{case_id}")
async def delete_case(
    case_id: str = Path(..., description="The case ID"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Delete case."""
    case_service.delete_case(case_id, current_user.email)
    return {"message": "Case deleted successfully"}

# Case Assignment Endpoints
@router.post("/{case_id}/assign", response_model=CaseAssignmentResponse)
async def assign_case(
    case_id: str = Path(..., description="The case ID"),
    assignment: CaseAssignmentCreate = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Assign case to user or team."""
    return case_service.assign_case(case_id, assignment, current_user.email)

@router.get("/{case_id}/assignments", response_model=List[CaseAssignmentResponse])
async def get_case_assignments(
    case_id: str = Path(..., description="The case ID"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get case assignment history."""
    return case_service.get_case_assignments(case_id)

@router.put("/assignments/{assignment_id}", response_model=CaseAssignmentResponse)
async def update_case_assignment(
    assignment_id: str = Path(..., description="The assignment ID"),
    assignment_data: CaseAssignmentUpdate = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Update case assignment."""
    return case_service.update_case_assignment(assignment_id, assignment_data, current_user.email)

# Case Notes Endpoints
@router.post("/{case_id}/notes", response_model=CaseNoteResponse)
async def add_case_note(
    case_id: str = Path(..., description="The case ID"),
    note: CaseNoteCreate = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Add note to case."""
    return case_service.add_case_note(case_id, note, current_user.email)

@router.get("/{case_id}/notes", response_model=List[CaseNoteResponse])
async def get_case_notes(
    case_id: str = Path(..., description="The case ID"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get case notes."""
    pagination = PaginationParams(page=page, size=size)
    return case_service.get_case_notes(case_id, pagination)

@router.put("/notes/{note_id}", response_model=CaseNoteResponse)
async def update_case_note(
    note_id: str = Path(..., description="The note ID"),
    note_data: CaseNoteUpdate = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Update case note."""
    return case_service.update_case_note(note_id, note_data, current_user.email)

@router.delete("/notes/{note_id}")
async def delete_case_note(
    note_id: str = Path(..., description="The note ID"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Delete case note."""
    case_service.delete_case_note(note_id, current_user.email)
    return {"message": "Case note deleted successfully"}

# Case Documents Endpoints
@router.post("/{case_id}/documents", response_model=CaseDocumentResponse)
async def upload_case_document(
    case_id: str = Path(..., description="The case ID"),
    document_type: str = Query(..., description="Type of document"),
    file: UploadFile = File(...),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Upload document to case."""
    return case_service.upload_case_document(case_id, document_type, file, current_user.email)

@router.get("/{case_id}/documents", response_model=List[CaseDocumentResponse])
async def get_case_documents(
    case_id: str = Path(..., description="The case ID"),
    document_type: Optional[str] = Query(None, description="Filter by document type"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get case documents."""
    return case_service.get_case_documents(case_id, document_type)

@router.delete("/documents/{document_id}")
async def delete_case_document(
    document_id: str = Path(..., description="The document ID"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Delete case document."""
    case_service.delete_case_document(document_id, current_user.email)
    return {"message": "Case document deleted successfully"}

# Case Workflow Endpoints
@router.post("/{case_id}/escalate", response_model=CaseResponse)
async def escalate_case(
    case_id: str = Path(..., description="The case ID"),
    escalation: CaseEscalationRequest = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Escalate case to higher priority or different team."""
    return case_service.escalate_case(case_id, escalation, current_user.email)

@router.post("/{case_id}/resolve", response_model=CaseResponse)
async def resolve_case(
    case_id: str = Path(..., description="The case ID"),
    resolution: CaseResolutionRequest = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Resolve case with resolution details."""
    return case_service.resolve_case(case_id, resolution, current_user.email)

@router.post("/{case_id}/reopen", response_model=CaseResponse)
async def reopen_case(
    case_id: str = Path(..., description="The case ID"),
    reason: str = Query(..., description="Reason for reopening"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Reopen a closed case."""
    return case_service.reopen_case(case_id, reason, current_user.email)

@router.post("/{case_id}/review", response_model=CaseResponse)
async def review_case(
    case_id: str = Path(..., description="The case ID"),
    review: CaseReviewRequest = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Review case and provide feedback."""
    return case_service.review_case(case_id, review, current_user.email)

@router.post("/{case_id}/workflow-action", response_model=CaseResponse)
async def perform_workflow_action(
    case_id: str = Path(..., description="The case ID"),
    action: CaseWorkflowAction = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Perform workflow action on case."""
    return case_service.perform_workflow_action(case_id, action, current_user.email)

# Case Analytics and Reporting
@router.get("/{case_id}/timeline", response_model=CaseTimelineResponse)
async def get_case_timeline(
    case_id: str = Path(..., description="The case ID"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get case timeline and activity history."""
    return case_service.get_case_timeline(case_id)

@router.get("/{case_id}/analytics", response_model=CaseAnalyticsResponse)
async def get_case_analytics(
    case_id: str = Path(..., description="The case ID"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get case analytics and metrics."""
    return case_service.get_case_analytics(case_id)

@router.get("/statistics", response_model=CaseStatistics)
async def get_case_statistics(
    period: str = Query("monthly", description="Statistics period"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive case statistics."""
    return case_service.get_case_statistics(period)

@router.get("/analytics", response_model=CustomJSONResponse)
async def get_cases_analytics(
    period: str = Query("monthly", description="Analytics period"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get overall case management analytics."""
    return case_service.get_cases_analytics(period)

# Search and Bulk Operations
@router.post("/search", response_model=CaseListResponse)
async def search_cases(
    search_params: CaseSearchParams = ...,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Search cases with advanced criteria."""
    pagination = PaginationParams(page=page, size=size)
    return case_service.search_cases(search_params, pagination)

@router.post("/bulk-operations", response_model=CustomJSONResponse)
async def perform_bulk_operation(
    operation: CaseBulkOperation = ...,
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Perform bulk case operations."""
    return case_service.perform_bulk_operation(operation, current_user.email)

@router.get("/export")
async def export_cases(
    format: str = Query("csv", description="Export format"),
    status: Optional[str] = Query(None, description="Filter by status"),
    priority: Optional[str] = Query(None, description="Filter by priority"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Export case data."""
    filters = {"status": status, "priority": priority}
    return case_service.export_cases(format, filters, current_user.email)

# My Cases Endpoints
@router.get("/my-cases", response_model=CaseListResponse)
async def get_my_cases(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by status"),
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get cases assigned to current user."""
    pagination = PaginationParams(page=page, size=size)
    return case_service.get_my_cases(current_user.email, pagination, status)

@router.get("/my-cases/statistics")
async def get_my_case_statistics(
    case_service: CaseManagementService = Depends(get_case_service),
    current_user: User = Depends(get_current_user)
):
    """Get statistics for cases assigned to current user."""
    return case_service.get_my_case_statistics(current_user.email)

# Legacy Compatibility Endpoints
@router.post("/legacy/create", response_model=LegacyCaseResponse)
async def create_case_legacy(
    case: LegacyCaseCreate = ...,
    case_service: CaseManagementService = Depends(get_case_service)
):
    """Legacy case creation endpoint."""
    try:
        case_data = CaseCreate(
            title=case.title,
            description=case.description,
            category=case.category or "general",
            priority=case.priority or "medium",
            status="open"
        )
        result = case_service.create_case(case_data, "legacy_user")
        return LegacyCaseResponse(
            success=True,
            message="Case created successfully",
            data={"case_id": result.case_id}
        )
    except Exception as e:
        return LegacyCaseResponse(
            success=False,
            message=f"Failed to create case: {str(e)}"
        )

# Health Check
@router.get("/health", response_model=CustomJSONResponse)
async def health_check():
    """Case management module health check."""
    return CustomJSONResponse(
        data={"status": "healthy", "module": "case_management"},
        message="Case management module is operational"
    )