# case_management constants - module-specific constants for case_management

# Case Types
CASE_TYPE_INVESTIGATION = "investigation"
CASE_TYPE_COMPLIANCE = "compliance"
CASE_TYPE_FRAUD = "fraud"
CASE_TYPE_DISPUTE = "dispute"
CASE_TYPE_AML = "aml"
CASE_TYPE_KYC = "kyc"
CASE_TYPE_CHARGEBACK = "chargeback"
CASE_TYPE_RISK = "risk"
CASE_TYPE_OPERATIONAL = "operational"
CASE_TYPE_CUSTOMER_COMPLAINT = "customer_complaint"

CASE_TYPES = [
    CASE_TYPE_INVESTIGATION,
    CASE_TYPE_COMPLIANCE,
    CASE_TYPE_FRAUD,
    CASE_TYPE_DISPUTE,
    CASE_TYPE_AML,
    CASE_TYPE_KYC,
    CASE_TYPE_CHARGEBACK,
    CASE_TYPE_RISK,
    CASE_TYPE_OPERATIONAL,
    CASE_TYPE_CUSTOMER_COMPLAINT
]

# Case Categories
CASE_CATEGORY_FINANCIAL_CRIME = "financial_crime"
CASE_CATEGORY_MONEY_LAUNDERING = "money_laundering"
CASE_CATEGORY_TERRORIST_FINANCING = "terrorist_financing"
CASE_CATEGORY_SANCTIONS = "sanctions"
CASE_CATEGORY_FRAUD_PREVENTION = "fraud_prevention"
CASE_CATEGORY_IDENTITY_VERIFICATION = "identity_verification"
CASE_CATEGORY_TRANSACTION_MONITORING = "transaction_monitoring"
CASE_CATEGORY_REGULATORY_COMPLIANCE = "regulatory_compliance"
CASE_CATEGORY_CUSTOMER_DUE_DILIGENCE = "customer_due_diligence"
CASE_CATEGORY_ENHANCED_DUE_DILIGENCE = "enhanced_due_diligence"

CASE_CATEGORIES = [
    CASE_CATEGORY_FINANCIAL_CRIME,
    CASE_CATEGORY_MONEY_LAUNDERING,
    CASE_CATEGORY_TERRORIST_FINANCING,
    CASE_CATEGORY_SANCTIONS,
    CASE_CATEGORY_FRAUD_PREVENTION,
    CASE_CATEGORY_IDENTITY_VERIFICATION,
    CASE_CATEGORY_TRANSACTION_MONITORING,
    CASE_CATEGORY_REGULATORY_COMPLIANCE,
    CASE_CATEGORY_CUSTOMER_DUE_DILIGENCE,
    CASE_CATEGORY_ENHANCED_DUE_DILIGENCE
]

# Case Statuses
CASE_STATUS_OPEN = "open"
CASE_STATUS_IN_PROGRESS = "in_progress"
CASE_STATUS_PENDING_REVIEW = "pending_review"
CASE_STATUS_UNDER_INVESTIGATION = "under_investigation"
CASE_STATUS_ESCALATED = "escalated"
CASE_STATUS_RESOLVED = "resolved"
CASE_STATUS_CLOSED = "closed"
CASE_STATUS_CANCELLED = "cancelled"
CASE_STATUS_ON_HOLD = "on_hold"
CASE_STATUS_REOPENED = "reopened"

CASE_STATUSES = [
    CASE_STATUS_OPEN,
    CASE_STATUS_IN_PROGRESS,
    CASE_STATUS_PENDING_REVIEW,
    CASE_STATUS_UNDER_INVESTIGATION,
    CASE_STATUS_ESCALATED,
    CASE_STATUS_RESOLVED,
    CASE_STATUS_CLOSED,
    CASE_STATUS_CANCELLED,
    CASE_STATUS_ON_HOLD,
    CASE_STATUS_REOPENED
]

# Case Priorities
CASE_PRIORITY_LOW = "low"
CASE_PRIORITY_MEDIUM = "medium"
CASE_PRIORITY_HIGH = "high"
CASE_PRIORITY_CRITICAL = "critical"
CASE_PRIORITY_URGENT = "urgent"

CASE_PRIORITIES = [
    CASE_PRIORITY_LOW,
    CASE_PRIORITY_MEDIUM,
    CASE_PRIORITY_HIGH,
    CASE_PRIORITY_CRITICAL,
    CASE_PRIORITY_URGENT
]

# Case Severities
CASE_SEVERITY_MINOR = "minor"
CASE_SEVERITY_MODERATE = "moderate"
CASE_SEVERITY_MAJOR = "major"
CASE_SEVERITY_CRITICAL = "critical"

CASE_SEVERITIES = [
    CASE_SEVERITY_MINOR,
    CASE_SEVERITY_MODERATE,
    CASE_SEVERITY_MAJOR,
    CASE_SEVERITY_CRITICAL
]

# Case Sources
CASE_SOURCE_SYSTEM = "system"
CASE_SOURCE_MANUAL = "manual"
CASE_SOURCE_EXTERNAL = "external"
CASE_SOURCE_AUTOMATED = "automated"
CASE_SOURCE_API = "api"
CASE_SOURCE_IMPORT = "import"
CASE_SOURCE_ALERT = "alert"
CASE_SOURCE_CUSTOMER = "customer"
CASE_SOURCE_REGULATOR = "regulator"

CASE_SOURCES = [
    CASE_SOURCE_SYSTEM,
    CASE_SOURCE_MANUAL,
    CASE_SOURCE_EXTERNAL,
    CASE_SOURCE_AUTOMATED,
    CASE_SOURCE_API,
    CASE_SOURCE_IMPORT,
    CASE_SOURCE_ALERT,
    CASE_SOURCE_CUSTOMER,
    CASE_SOURCE_REGULATOR
]

# Activity Types
ACTIVITY_TYPE_CASE_CREATED = "case_created"
ACTIVITY_TYPE_CASE_UPDATED = "case_updated"
ACTIVITY_TYPE_CASE_ASSIGNED = "case_assigned"
ACTIVITY_TYPE_CASE_ESCALATED = "case_escalated"
ACTIVITY_TYPE_CASE_CLOSED = "case_closed"
ACTIVITY_TYPE_STATUS_CHANGED = "status_changed"
ACTIVITY_TYPE_PRIORITY_CHANGED = "priority_changed"
ACTIVITY_TYPE_NOTE_ADDED = "note_added"
ACTIVITY_TYPE_DOCUMENT_UPLOADED = "document_uploaded"
ACTIVITY_TYPE_COMMENT_ADDED = "comment_added"
ACTIVITY_TYPE_WORKFLOW_TRANSITION = "workflow_transition"
ACTIVITY_TYPE_SLA_WARNING = "sla_warning"
ACTIVITY_TYPE_SLA_BREACH = "sla_breach"

ACTIVITY_TYPES = [
    ACTIVITY_TYPE_CASE_CREATED,
    ACTIVITY_TYPE_CASE_UPDATED,
    ACTIVITY_TYPE_CASE_ASSIGNED,
    ACTIVITY_TYPE_CASE_ESCALATED,
    ACTIVITY_TYPE_CASE_CLOSED,
    ACTIVITY_TYPE_STATUS_CHANGED,
    ACTIVITY_TYPE_PRIORITY_CHANGED,
    ACTIVITY_TYPE_NOTE_ADDED,
    ACTIVITY_TYPE_DOCUMENT_UPLOADED,
    ACTIVITY_TYPE_COMMENT_ADDED,
    ACTIVITY_TYPE_WORKFLOW_TRANSITION,
    ACTIVITY_TYPE_SLA_WARNING,
    ACTIVITY_TYPE_SLA_BREACH
]

# Activity Categories
ACTIVITY_CATEGORY_USER_ACTION = "user_action"
ACTIVITY_CATEGORY_SYSTEM_ACTION = "system_action"
ACTIVITY_CATEGORY_EXTERNAL_ACTION = "external_action"
ACTIVITY_CATEGORY_AUTOMATED_ACTION = "automated_action"

ACTIVITY_CATEGORIES = [
    ACTIVITY_CATEGORY_USER_ACTION,
    ACTIVITY_CATEGORY_SYSTEM_ACTION,
    ACTIVITY_CATEGORY_EXTERNAL_ACTION,
    ACTIVITY_CATEGORY_AUTOMATED_ACTION
]

# Assignment Types
ASSIGNMENT_TYPE_PRIMARY = "primary"
ASSIGNMENT_TYPE_SECONDARY = "secondary"
ASSIGNMENT_TYPE_REVIEWER = "reviewer"
ASSIGNMENT_TYPE_SUPERVISOR = "supervisor"
ASSIGNMENT_TYPE_COLLABORATOR = "collaborator"

ASSIGNMENT_TYPES = [
    ASSIGNMENT_TYPE_PRIMARY,
    ASSIGNMENT_TYPE_SECONDARY,
    ASSIGNMENT_TYPE_REVIEWER,
    ASSIGNMENT_TYPE_SUPERVISOR,
    ASSIGNMENT_TYPE_COLLABORATOR
]

# Assignment Methods
ASSIGNMENT_METHOD_MANUAL = "manual"
ASSIGNMENT_METHOD_AUTO = "auto"
ASSIGNMENT_METHOD_ESCALATION = "escalation"
ASSIGNMENT_METHOD_ROUND_ROBIN = "round_robin"
ASSIGNMENT_METHOD_WORKLOAD_BASED = "workload_based"
ASSIGNMENT_METHOD_SKILL_BASED = "skill_based"

ASSIGNMENT_METHODS = [
    ASSIGNMENT_METHOD_MANUAL,
    ASSIGNMENT_METHOD_AUTO,
    ASSIGNMENT_METHOD_ESCALATION,
    ASSIGNMENT_METHOD_ROUND_ROBIN,
    ASSIGNMENT_METHOD_WORKLOAD_BASED,
    ASSIGNMENT_METHOD_SKILL_BASED
]

# Assignment Statuses
ASSIGNMENT_STATUS_ACTIVE = "active"
ASSIGNMENT_STATUS_TRANSFERRED = "transferred"
ASSIGNMENT_STATUS_COMPLETED = "completed"
ASSIGNMENT_STATUS_PENDING = "pending"
ASSIGNMENT_STATUS_ACCEPTED = "accepted"
ASSIGNMENT_STATUS_DECLINED = "declined"

ASSIGNMENT_STATUSES = [
    ASSIGNMENT_STATUS_ACTIVE,
    ASSIGNMENT_STATUS_TRANSFERRED,
    ASSIGNMENT_STATUS_COMPLETED,
    ASSIGNMENT_STATUS_PENDING,
    ASSIGNMENT_STATUS_ACCEPTED,
    ASSIGNMENT_STATUS_DECLINED
]

# Escalation Types
ESCALATION_TYPE_SLA_BREACH = "sla_breach"
ESCALATION_TYPE_MANUAL = "manual"
ESCALATION_TYPE_AUTOMATIC = "automatic"
ESCALATION_TYPE_PRIORITY_CHANGE = "priority_change"
ESCALATION_TYPE_COMPLEXITY = "complexity"
ESCALATION_TYPE_REGULATORY = "regulatory"

ESCALATION_TYPES = [
    ESCALATION_TYPE_SLA_BREACH,
    ESCALATION_TYPE_MANUAL,
    ESCALATION_TYPE_AUTOMATIC,
    ESCALATION_TYPE_PRIORITY_CHANGE,
    ESCALATION_TYPE_COMPLEXITY,
    ESCALATION_TYPE_REGULATORY
]

# Escalation Statuses
ESCALATION_STATUS_PENDING = "pending"
ESCALATION_STATUS_ACKNOWLEDGED = "acknowledged"
ESCALATION_STATUS_IN_PROGRESS = "in_progress"
ESCALATION_STATUS_RESOLVED = "resolved"
ESCALATION_STATUS_CANCELLED = "cancelled"

ESCALATION_STATUSES = [
    ESCALATION_STATUS_PENDING,
    ESCALATION_STATUS_ACKNOWLEDGED,
    ESCALATION_STATUS_IN_PROGRESS,
    ESCALATION_STATUS_RESOLVED,
    ESCALATION_STATUS_CANCELLED
]

# Document Types
DOCUMENT_TYPE_EVIDENCE = "evidence"
DOCUMENT_TYPE_REPORT = "report"
DOCUMENT_TYPE_CORRESPONDENCE = "correspondence"
DOCUMENT_TYPE_SCREENSHOT = "screenshot"
DOCUMENT_TYPE_TRANSACTION_RECORD = "transaction_record"
DOCUMENT_TYPE_IDENTITY_DOCUMENT = "identity_document"
DOCUMENT_TYPE_FINANCIAL_STATEMENT = "financial_statement"
DOCUMENT_TYPE_COMPLIANCE_DOCUMENT = "compliance_document"
DOCUMENT_TYPE_LEGAL_DOCUMENT = "legal_document"
DOCUMENT_TYPE_AUDIT_TRAIL = "audit_trail"

DOCUMENT_TYPES = [
    DOCUMENT_TYPE_EVIDENCE,
    DOCUMENT_TYPE_REPORT,
    DOCUMENT_TYPE_CORRESPONDENCE,
    DOCUMENT_TYPE_SCREENSHOT,
    DOCUMENT_TYPE_TRANSACTION_RECORD,
    DOCUMENT_TYPE_IDENTITY_DOCUMENT,
    DOCUMENT_TYPE_FINANCIAL_STATEMENT,
    DOCUMENT_TYPE_COMPLIANCE_DOCUMENT,
    DOCUMENT_TYPE_LEGAL_DOCUMENT,
    DOCUMENT_TYPE_AUDIT_TRAIL
]

# Document Access Levels
DOCUMENT_ACCESS_PUBLIC = "public"
DOCUMENT_ACCESS_CASE_TEAM = "case_team"
DOCUMENT_ACCESS_RESTRICTED = "restricted"
DOCUMENT_ACCESS_CONFIDENTIAL = "confidential"

DOCUMENT_ACCESS_LEVELS = [
    DOCUMENT_ACCESS_PUBLIC,
    DOCUMENT_ACCESS_CASE_TEAM,
    DOCUMENT_ACCESS_RESTRICTED,
    DOCUMENT_ACCESS_CONFIDENTIAL
]

# Document Statuses
DOCUMENT_STATUS_ACTIVE = "active"
DOCUMENT_STATUS_ARCHIVED = "archived"
DOCUMENT_STATUS_DELETED = "deleted"
DOCUMENT_STATUS_PENDING_REVIEW = "pending_review"
DOCUMENT_STATUS_APPROVED = "approved"
DOCUMENT_STATUS_REJECTED = "rejected"

DOCUMENT_STATUSES = [
    DOCUMENT_STATUS_ACTIVE,
    DOCUMENT_STATUS_ARCHIVED,
    DOCUMENT_STATUS_DELETED,
    DOCUMENT_STATUS_PENDING_REVIEW,
    DOCUMENT_STATUS_APPROVED,
    DOCUMENT_STATUS_REJECTED
]

# Comment Types
COMMENT_TYPE_NOTE = "note"
COMMENT_TYPE_UPDATE = "update"
COMMENT_TYPE_DECISION = "decision"
COMMENT_TYPE_RECOMMENDATION = "recommendation"
COMMENT_TYPE_QUESTION = "question"
COMMENT_TYPE_ANSWER = "answer"
COMMENT_TYPE_OBSERVATION = "observation"
COMMENT_TYPE_CONCLUSION = "conclusion"

COMMENT_TYPES = [
    COMMENT_TYPE_NOTE,
    COMMENT_TYPE_UPDATE,
    COMMENT_TYPE_DECISION,
    COMMENT_TYPE_RECOMMENDATION,
    COMMENT_TYPE_QUESTION,
    COMMENT_TYPE_ANSWER,
    COMMENT_TYPE_OBSERVATION,
    COMMENT_TYPE_CONCLUSION
]

# Default values
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100
DEFAULT_CASE_STATUS = CASE_STATUS_OPEN
DEFAULT_CASE_PRIORITY = CASE_PRIORITY_MEDIUM
DEFAULT_ASSIGNMENT_TYPE = ASSIGNMENT_TYPE_PRIMARY
DEFAULT_ASSIGNMENT_METHOD = ASSIGNMENT_METHOD_MANUAL
DEFAULT_DOCUMENT_ACCESS_LEVEL = DOCUMENT_ACCESS_CASE_TEAM
DEFAULT_COMMENT_TYPE = COMMENT_TYPE_NOTE

# Validation limits
MAX_CASE_TITLE_LENGTH = 255
MAX_CASE_DESCRIPTION_LENGTH = 5000
MAX_ACTIVITY_DESCRIPTION_LENGTH = 2000
MAX_COMMENT_CONTENT_LENGTH = 5000
MAX_DOCUMENT_NAME_LENGTH = 255
MAX_TEAM_NAME_LENGTH = 255
MAX_WORKFLOW_NAME_LENGTH = 255
MAX_TEMPLATE_NAME_LENGTH = 255

# SLA Configuration (in hours)
SLA_RESPONSE_CRITICAL = 1
SLA_RESPONSE_HIGH = 2
SLA_RESPONSE_MEDIUM = 4
SLA_RESPONSE_LOW = 8

SLA_RESOLUTION_CRITICAL = 24
SLA_RESOLUTION_HIGH = 48
SLA_RESOLUTION_MEDIUM = 72
SLA_RESOLUTION_LOW = 168

# Workload limits
MAX_CASES_PER_USER = 50
MAX_CRITICAL_CASES_PER_USER = 5
MAX_HIGH_PRIORITY_CASES_PER_USER = 15

# System limits
MAX_ESCALATION_LEVEL = 5
MAX_DOCUMENT_SIZE_MB = 100
MAX_DOCUMENTS_PER_CASE = 100
MAX_COMMENTS_PER_CASE = 500
MAX_ACTIVITIES_PER_CASE = 1000

# Notification settings
NOTIFICATION_SLA_WARNING_THRESHOLD = 0.8  # 80% of SLA time
NOTIFICATION_ESCALATION_DELAY_HOURS = 2
NOTIFICATION_RETRY_ATTEMPTS = 3

# Audit settings
AUDIT_RETENTION_DAYS = 2555  # 7 years
CASE_RETENTION_DAYS = 2555  # 7 years
DOCUMENT_RETENTION_DAYS = 2555  # 7 years

# Performance settings
CASE_SEARCH_LIMIT = 1000
BULK_OPERATION_LIMIT = 100
EXPORT_LIMIT = 10000

# Module metadata
CASE_MANAGEMENT_MODULE_VERSION = "1.0.0"
CASE_MANAGEMENT_MODULE_NAME = "case_management"
CASE_MANAGEMENT_API_VERSION = "v1"