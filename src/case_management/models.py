# case_management models - SQLAlchemy models for case_management module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Case Management Models
class Case(Base):
    __tablename__ = 'cases'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), unique=True, nullable=False, index=True)
    case_number = Column(String(50), unique=True, nullable=False, index=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    case_type = Column(String(50), nullable=False)  # investigation, compliance, fraud, dispute
    category = Column(String(100), nullable=True)  # financial_crime, aml, kyc, chargeback
    subcategory = Column(String(100), nullable=True)
    status = Column(String(50), nullable=False, default='open')  # open, in_progress, closed, escalated
    priority = Column(String(50), nullable=False, default='medium')  # low, medium, high, critical
    severity = Column(String(50), nullable=True)  # minor, major, critical
    source = Column(String(100), nullable=True)  # system, manual, external, automated

    # Assignment and ownership
    assigned_to = Column(String(255), nullable=True, index=True)
    assigned_email = Column(String(255), nullable=True, index=True)
    assigned_team = Column(String(100), nullable=True)
    created_by = Column(String(255), nullable=False, index=True)
    updated_by = Column(String(255), nullable=True)

    # Related entities
    merchant_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    merchant_name = Column(String(255), nullable=True)
    customer_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    transaction_id = Column(String(255), nullable=True, index=True)

    # Workflow and SLA
    workflow_stage = Column(String(100), nullable=True)
    workflow_step = Column(String(100), nullable=True)
    sla_deadline = Column(DateTime, nullable=True)
    escalation_level = Column(Integer, default=0)
    escalation_reason = Column(Text, nullable=True)

    # Case metadata
    tags = Column(ARRAY(String), nullable=True)
    custom_fields = Column(JSONB, nullable=True)
    external_references = Column(JSONB, nullable=True)  # External system references

    # Timestamps
    opened_at = Column(DateTime, default=datetime.now)
    closed_at = Column(DateTime, nullable=True)
    last_activity_at = Column(DateTime, default=datetime.now)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Case Workflow Management
class case_workflows(Base):
    __tablename__ = 'case_workflows'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_name = Column(String(255), nullable=False, unique=True)
    workflow_description = Column(Text, nullable=True)
    case_type = Column(String(50), nullable=False)
    workflow_config = Column(JSONB, nullable=False)  # Workflow definition
    stages = Column(JSONB, nullable=False)  # Workflow stages
    transitions = Column(JSONB, nullable=False)  # Stage transitions
    sla_config = Column(JSONB, nullable=True)  # SLA configuration
    escalation_rules = Column(JSONB, nullable=True)  # Escalation rules
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.now)

# Case Activities and Events
class case_activities(Base):
    __tablename__ = 'case_activities'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    activity_id = Column(String(50), nullable=False, unique=True)
    activity_type = Column(String(50), nullable=False)  # note, status_change, assignment, escalation
    activity_category = Column(String(100), nullable=True)  # user_action, system_action, external
    title = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    content = Column(Text, nullable=True)

    # Activity metadata
    metadata = Column(JSONB, nullable=True)
    old_values = Column(JSONB, nullable=True)  # Previous values for changes
    new_values = Column(JSONB, nullable=True)  # New values for changes

    # User and system info
    performed_by = Column(String(255), nullable=False, index=True)
    performed_by_type = Column(String(50), nullable=False)  # user, system, api
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)

    # Timestamps
    activity_timestamp = Column(DateTime, default=datetime.now)
    created_at = Column(DateTime, default=datetime.now, index=True)

# Case Assignments and Workload
class case_assignments(Base):
    __tablename__ = 'case_assignments'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    assigned_to = Column(String(255), nullable=False, index=True)
    assigned_email = Column(String(255), nullable=False, index=True)
    assigned_team = Column(String(100), nullable=True)
    assignment_type = Column(String(50), nullable=False)  # primary, secondary, reviewer
    assignment_reason = Column(Text, nullable=True)
    assignment_method = Column(String(50), nullable=False)  # manual, auto, escalation

    # Assignment status
    status = Column(String(50), nullable=False, default='active')  # active, transferred, completed
    is_active = Column(Boolean, default=True)

    # Assignment metadata
    workload_priority = Column(Integer, nullable=True)
    estimated_effort_hours = Column(Float, nullable=True)
    actual_effort_hours = Column(Float, nullable=True)

    # Timestamps
    assigned_at = Column(DateTime, default=datetime.now)
    accepted_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    transferred_at = Column(DateTime, nullable=True)

    # Assignment history
    assigned_by = Column(String(255), nullable=False)
    transferred_to = Column(String(255), nullable=True)
    transfer_reason = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Case Escalations
class case_escalations(Base):
    __tablename__ = 'case_escalations'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    escalation_id = Column(String(50), nullable=False, unique=True)
    escalation_level = Column(Integer, nullable=False)
    escalation_type = Column(String(50), nullable=False)  # sla_breach, manual, automatic
    escalation_reason = Column(Text, nullable=False)
    escalation_trigger = Column(String(100), nullable=True)  # sla_deadline, priority_change

    # Escalation details
    escalated_from = Column(String(255), nullable=True)
    escalated_to = Column(String(255), nullable=False)
    escalated_team = Column(String(100), nullable=True)
    escalation_deadline = Column(DateTime, nullable=True)

    # Escalation status
    status = Column(String(50), nullable=False, default='pending')  # pending, acknowledged, resolved
    resolution = Column(Text, nullable=True)
    resolved_by = Column(String(255), nullable=True)
    resolved_at = Column(DateTime, nullable=True)

    # Escalation metadata
    escalation_data = Column(JSONB, nullable=True)
    notification_sent = Column(Boolean, default=False)

    # Timestamps
    escalated_at = Column(DateTime, default=datetime.now)
    acknowledged_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

# Case Documents and Attachments
class case_documents(Base):
    __tablename__ = 'case_documents'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    document_id = Column(String(50), nullable=False, unique=True)
    document_name = Column(String(255), nullable=False)
    document_type = Column(String(100), nullable=False)  # evidence, report, correspondence
    document_category = Column(String(100), nullable=True)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size_bytes = Column(Integer, nullable=True)
    file_mime_type = Column(String(100), nullable=True)
    file_hash = Column(String(255), nullable=True)  # For integrity verification

    # Document metadata
    description = Column(Text, nullable=True)
    tags = Column(ARRAY(String), nullable=True)
    metadata = Column(JSONB, nullable=True)

    # Access control
    access_level = Column(String(50), nullable=False, default='case_team')  # public, case_team, restricted
    is_confidential = Column(Boolean, default=False)
    is_evidence = Column(Boolean, default=False)

    # Document lifecycle
    status = Column(String(50), nullable=False, default='active')  # active, archived, deleted
    version = Column(Integer, default=1)
    parent_document_id = Column(String(50), nullable=True)  # For versioning

    # User tracking
    uploaded_by = Column(String(255), nullable=False)
    reviewed_by = Column(String(255), nullable=True)
    approved_by = Column(String(255), nullable=True)

    # Timestamps
    uploaded_at = Column(DateTime, default=datetime.now)
    reviewed_at = Column(DateTime, nullable=True)
    approved_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

# Case Comments and Notes
class case_comments(Base):
    __tablename__ = 'case_comments'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    comment_id = Column(String(50), nullable=False, unique=True)
    comment_type = Column(String(50), nullable=False)  # note, update, decision, recommendation
    title = Column(String(255), nullable=True)
    content = Column(Text, nullable=False)

    # Comment metadata
    is_internal = Column(Boolean, default=True)  # Internal vs external visibility
    is_confidential = Column(Boolean, default=False)
    priority = Column(String(50), nullable=True)  # low, medium, high
    tags = Column(ARRAY(String), nullable=True)

    # Threading and replies
    parent_comment_id = Column(String(50), nullable=True)  # For threaded comments
    thread_id = Column(String(50), nullable=True)

    # User tracking
    created_by = Column(String(255), nullable=False, index=True)
    updated_by = Column(String(255), nullable=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.now)

# Add indexes for performance
Index('idx_cases_status_priority', Case.status, Case.priority)
Index('idx_cases_assigned_status', Case.assigned_email, Case.status)
Index('idx_cases_merchant_type', Case.merchant_id, Case.case_type)
Index('idx_cases_created_date', Case.created_at.desc())
Index('idx_case_activities_case_type', case_activities.case_id, case_activities.activity_type)
Index('idx_case_activities_timestamp', case_activities.activity_timestamp.desc())
Index('idx_case_assignments_assignee_active', case_assignments.assigned_email, case_assignments.is_active)
Index('idx_case_escalations_level_status', case_escalations.escalation_level, case_escalations.status)

# Case Teams and Collaboration
class case_teams(Base):
    __tablename__ = 'case_teams'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    team_name = Column(String(255), nullable=False, unique=True)
    team_description = Column(Text, nullable=True)
    team_type = Column(String(50), nullable=False)  # investigation, compliance, fraud, support
    department = Column(String(100), nullable=True)
    team_lead = Column(String(255), nullable=True)
    team_lead_email = Column(String(255), nullable=True)

    # Team configuration
    max_concurrent_cases = Column(Integer, nullable=True)
    specializations = Column(ARRAY(String), nullable=True)  # case types they handle
    escalation_threshold = Column(Integer, nullable=True)
    sla_targets = Column(JSONB, nullable=True)

    # Team status
    is_active = Column(Boolean, default=True)
    is_available = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Case Team Members
class case_team_members(Base):
    __tablename__ = 'case_team_members'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    team_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    member_email = Column(String(255), nullable=False, index=True)
    member_name = Column(String(255), nullable=False)
    role = Column(String(50), nullable=False)  # lead, senior, junior, reviewer
    permissions = Column(ARRAY(String), nullable=True)

    # Member status
    is_active = Column(Boolean, default=True)
    is_available = Column(Boolean, default=True)
    current_caseload = Column(Integer, default=0)
    max_caseload = Column(Integer, nullable=True)

    # Member metadata
    skills = Column(ARRAY(String), nullable=True)
    certifications = Column(ARRAY(String), nullable=True)
    experience_level = Column(String(50), nullable=True)

    # Timestamps
    joined_at = Column(DateTime, default=datetime.now)
    last_active = Column(DateTime, default=datetime.now)
    created_at = Column(DateTime, default=datetime.now)

# Case SLA Tracking
class case_sla_tracking(Base):
    __tablename__ = 'case_sla_tracking'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    sla_type = Column(String(50), nullable=False)  # response, resolution, escalation
    sla_name = Column(String(255), nullable=False)
    target_hours = Column(Float, nullable=False)
    warning_threshold_hours = Column(Float, nullable=True)

    # SLA status
    status = Column(String(50), nullable=False)  # on_track, at_risk, breached, met
    start_time = Column(DateTime, nullable=False)
    target_time = Column(DateTime, nullable=False)
    warning_time = Column(DateTime, nullable=True)
    actual_completion_time = Column(DateTime, nullable=True)

    # SLA metrics
    elapsed_hours = Column(Float, nullable=True)
    remaining_hours = Column(Float, nullable=True)
    breach_duration_hours = Column(Float, nullable=True)

    # Notifications
    warning_sent = Column(Boolean, default=False)
    breach_notification_sent = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Case Related Cases (for linking cases)
class case_relationships(Base):
    __tablename__ = 'case_relationships'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    related_case_id = Column(String(50), nullable=False, index=True)
    relationship_type = Column(String(50), nullable=False)  # duplicate, related, parent, child
    relationship_description = Column(Text, nullable=True)

    # Relationship metadata
    strength = Column(String(50), nullable=True)  # weak, moderate, strong
    confidence_score = Column(Float, nullable=True)  # 0.0 to 1.0
    created_by = Column(String(255), nullable=False)
    verified_by = Column(String(255), nullable=True)

    # Status
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.now)
    verified_at = Column(DateTime, nullable=True)

# Case Templates
class case_templates(Base):
    __tablename__ = 'case_templates'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_name = Column(String(255), nullable=False, unique=True)
    template_description = Column(Text, nullable=True)
    case_type = Column(String(50), nullable=False)
    category = Column(String(100), nullable=True)

    # Template configuration
    default_priority = Column(String(50), nullable=True)
    default_severity = Column(String(50), nullable=True)
    default_workflow = Column(String(255), nullable=True)
    default_team = Column(String(255), nullable=True)

    # Template fields
    required_fields = Column(JSONB, nullable=True)
    optional_fields = Column(JSONB, nullable=True)
    custom_fields = Column(JSONB, nullable=True)
    default_values = Column(JSONB, nullable=True)

    # Template checklist
    checklist_items = Column(JSONB, nullable=True)
    required_documents = Column(JSONB, nullable=True)

    # Template status
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    usage_count = Column(Integer, default=0)

    # Timestamps
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.now)

# Case Metrics and Analytics
class case_metrics(Base):
    __tablename__ = 'case_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    metric_date = Column(DateTime, nullable=False, index=True)

    # Time metrics
    time_to_assignment_hours = Column(Float, nullable=True)
    time_to_first_response_hours = Column(Float, nullable=True)
    time_to_resolution_hours = Column(Float, nullable=True)
    total_active_time_hours = Column(Float, nullable=True)

    # Activity metrics
    total_activities = Column(Integer, default=0)
    total_comments = Column(Integer, default=0)
    total_documents = Column(Integer, default=0)
    total_escalations = Column(Integer, default=0)
    total_assignments = Column(Integer, default=0)

    # Quality metrics
    customer_satisfaction_score = Column(Float, nullable=True)
    resolution_quality_score = Column(Float, nullable=True)
    compliance_score = Column(Float, nullable=True)

    # SLA metrics
    sla_breaches = Column(Integer, default=0)
    sla_met_percentage = Column(Float, nullable=True)

    # Timestamps
    calculated_at = Column(DateTime, default=datetime.now)
    created_at = Column(DateTime, default=datetime.now)

# Case Notifications
class case_notifications(Base):
    __tablename__ = 'case_notifications'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    notification_id = Column(String(50), nullable=False, unique=True)
    notification_type = Column(String(50), nullable=False)  # assignment, escalation, sla_warning
    notification_category = Column(String(100), nullable=True)

    # Notification content
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    priority = Column(String(50), nullable=False)  # low, medium, high, urgent

    # Recipients
    recipient_email = Column(String(255), nullable=False, index=True)
    recipient_type = Column(String(50), nullable=False)  # assignee, team_lead, manager

    # Delivery
    delivery_method = Column(String(50), nullable=False)  # email, sms, push, in_app
    delivery_status = Column(String(50), nullable=False)  # pending, sent, delivered, failed
    delivery_attempts = Column(Integer, default=0)

    # Notification metadata
    metadata = Column(JSONB, nullable=True)
    template_used = Column(String(255), nullable=True)

    # Timestamps
    scheduled_at = Column(DateTime, nullable=True)
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    read_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

# Case Audit Trail
class case_audit_trail(Base):
    __tablename__ = 'case_audit_trail'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    case_id = Column(String(50), nullable=False, index=True)
    audit_id = Column(String(50), nullable=False, unique=True)

    # Audit details
    action = Column(String(100), nullable=False)
    action_category = Column(String(50), nullable=False)  # create, read, update, delete
    table_name = Column(String(100), nullable=True)
    record_id = Column(String(50), nullable=True)

    # Change tracking
    field_name = Column(String(100), nullable=True)
    old_value = Column(Text, nullable=True)
    new_value = Column(Text, nullable=True)
    change_reason = Column(Text, nullable=True)

    # User context
    user_email = Column(String(255), nullable=False, index=True)
    user_role = Column(String(100), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    session_id = Column(String(255), nullable=True)

    # Audit metadata
    metadata = Column(JSONB, nullable=True)
    compliance_flags = Column(ARRAY(String), nullable=True)

    # Timestamps
    action_timestamp = Column(DateTime, default=datetime.now)
    created_at = Column(DateTime, default=datetime.now, index=True)

# Additional indexes for new models
Index('idx_case_teams_type_active', case_teams.team_type, case_teams.is_active)
Index('idx_case_team_members_email_active', case_team_members.member_email, case_team_members.is_active)
Index('idx_case_sla_tracking_status_target', case_sla_tracking.status, case_sla_tracking.target_time)
Index('idx_case_relationships_type', case_relationships.relationship_type, case_relationships.is_active)
Index('idx_case_metrics_date', case_metrics.metric_date.desc())
Index('idx_case_notifications_recipient_status', case_notifications.recipient_email, case_notifications.delivery_status)
Index('idx_case_audit_trail_action_time', case_audit_trail.action, case_audit_trail.action_timestamp.desc())