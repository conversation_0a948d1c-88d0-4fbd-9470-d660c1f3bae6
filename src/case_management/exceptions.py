# case_management exceptions - module-specific errors for case_management
from fastapi import HTTPException, status

class CaseNotFoundError(HTTPException):
    def __init__(self, case_id: str = None):
        detail = f"Case '{case_id}' not found" if case_id else "Case not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseAlreadyExistsError(HTTPException):
    def __init__(self, case_identifier: str = None):
        detail = f"Case '{case_identifier}' already exists" if case_identifier else "Case already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidCaseError(HTTPException):
    def __init__(self, detail: str = "Invalid case"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseValidationError(HTTPException):
    def __init__(self, detail: str = "Case validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create case"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update case"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete case"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseWorkflowNotFoundError(HTTPException):
    def __init__(self, workflow_id: str = None):
        detail = f"Case workflow '{workflow_id}' not found" if workflow_id else "Case workflow not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseWorkflowError(HTTPException):
    def __init__(self, detail: str = "Case workflow error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseActivityCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create case activity"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseActivityNotFoundError(HTTPException):
    def __init__(self, activity_id: str = None):
        detail = f"Case activity '{activity_id}' not found" if activity_id else "Case activity not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseAssignmentError(HTTPException):
    def __init__(self, detail: str = "Case assignment error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseAssignmentNotFoundError(HTTPException):
    def __init__(self, assignment_id: str = None):
        detail = f"Case assignment '{assignment_id}' not found" if assignment_id else "Case assignment not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseEscalationError(HTTPException):
    def __init__(self, detail: str = "Case escalation error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseEscalationNotFoundError(HTTPException):
    def __init__(self, escalation_id: str = None):
        detail = f"Case escalation '{escalation_id}' not found" if escalation_id else "Case escalation not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseDocumentError(HTTPException):
    def __init__(self, detail: str = "Case document error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseDocumentNotFoundError(HTTPException):
    def __init__(self, document_id: str = None):
        detail = f"Case document '{document_id}' not found" if document_id else "Case document not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseCommentNotFoundError(HTTPException):
    def __init__(self, comment_id: str = None):
        detail = f"Case comment '{comment_id}' not found" if comment_id else "Case comment not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseCommentError(HTTPException):
    def __init__(self, detail: str = "Case comment error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseTeamNotFoundError(HTTPException):
    def __init__(self, team_id: str = None):
        detail = f"Case team '{team_id}' not found" if team_id else "Case team not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseTeamError(HTTPException):
    def __init__(self, detail: str = "Case team error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseTeamMemberNotFoundError(HTTPException):
    def __init__(self, member_id: str = None):
        detail = f"Case team member '{member_id}' not found" if member_id else "Case team member not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseSLAViolationError(HTTPException):
    def __init__(self, detail: str = "Case SLA violation"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseSLANotFoundError(HTTPException):
    def __init__(self, sla_id: str = None):
        detail = f"Case SLA '{sla_id}' not found" if sla_id else "Case SLA not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseRelationshipError(HTTPException):
    def __init__(self, detail: str = "Case relationship error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseRelationshipNotFoundError(HTTPException):
    def __init__(self, relationship_id: str = None):
        detail = f"Case relationship '{relationship_id}' not found" if relationship_id else "Case relationship not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseTemplateNotFoundError(HTTPException):
    def __init__(self, template_id: str = None):
        detail = f"Case template '{template_id}' not found" if template_id else "Case template not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CaseTemplateError(HTTPException):
    def __init__(self, detail: str = "Case template error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseMetricsError(HTTPException):
    def __init__(self, detail: str = "Case metrics error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseNotificationError(HTTPException):
    def __init__(self, detail: str = "Case notification error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseAuditTrailError(HTTPException):
    def __init__(self, detail: str = "Case audit trail error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseAccessDeniedError(HTTPException):
    def __init__(self, case_id: str = None):
        detail = f"Access denied to case '{case_id}'" if case_id else "Case access denied"
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class CasePermissionError(HTTPException):
    def __init__(self, detail: str = "Insufficient permissions for case operation"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class CaseWorkloadExceededError(HTTPException):
    def __init__(self, detail: str = "Case workload limit exceeded"):
        super().__init__(status_code=status.HTTP_429_TOO_MANY_REQUESTS, detail=detail)

class CaseStatusTransitionError(HTTPException):
    def __init__(self, detail: str = "Invalid case status transition"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CasePriorityError(HTTPException):
    def __init__(self, detail: str = "Invalid case priority"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseTypeError(HTTPException):
    def __init__(self, detail: str = "Invalid case type"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseCategoryError(HTTPException):
    def __init__(self, detail: str = "Invalid case category"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseSearchError(HTTPException):
    def __init__(self, detail: str = "Case search error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseBulkOperationError(HTTPException):
    def __init__(self, detail: str = "Case bulk operation error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CaseStatisticsError(HTTPException):
    def __init__(self, detail: str = "Failed to calculate case statistics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseWorkloadCalculationError(HTTPException):
    def __init__(self, detail: str = "Failed to calculate case workload"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseConfigurationError(HTTPException):
    def __init__(self, detail: str = "Case configuration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseIntegrationError(HTTPException):
    def __init__(self, detail: str = "Case integration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseDataIntegrityError(HTTPException):
    def __init__(self, detail: str = "Case data integrity error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseTimeoutError(HTTPException):
    def __init__(self, detail: str = "Case operation timed out"):
        super().__init__(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=detail)

class CaseMaintenanceError(HTTPException):
    def __init__(self, detail: str = "Case management system is under maintenance"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class LegacyCaseError(HTTPException):
    def __init__(self, detail: str = "Legacy case operation failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CaseFeatureNotAvailableError(HTTPException):
    def __init__(self, feature: str):
        super().__init__(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail=f"Case management feature '{feature}' is not available"
        )