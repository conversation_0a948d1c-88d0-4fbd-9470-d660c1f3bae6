# case_management schemas - Pydantic models for case_management module
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime

# Case Base Schemas
class CaseBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    case_type: str = Field(..., max_length=50)
    category: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    status: str = Field(default='open', max_length=50)
    priority: str = Field(default='medium', max_length=50)
    severity: Optional[str] = Field(None, max_length=50)
    source: Optional[str] = Field(None, max_length=100)
    assigned_to: Optional[str] = Field(None, max_length=255)
    assigned_email: Optional[str] = Field(None, max_length=255)
    assigned_team: Optional[str] = Field(None, max_length=100)
    merchant_id: Optional[UUID] = None
    merchant_name: Optional[str] = Field(None, max_length=255)
    customer_id: Optional[UUID] = None
    transaction_id: Optional[str] = Field(None, max_length=255)
    workflow_stage: Optional[str] = Field(None, max_length=100)
    workflow_step: Optional[str] = Field(None, max_length=100)
    sla_deadline: Optional[datetime] = None
    escalation_level: int = 0
    escalation_reason: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    external_references: Optional[Dict[str, Any]] = None

class CaseCreate(CaseBase):
    case_id: Optional[str] = Field(None, max_length=50)
    case_number: Optional[str] = Field(None, max_length=50)

class CaseUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    case_type: Optional[str] = Field(None, max_length=50)
    category: Optional[str] = Field(None, max_length=100)
    subcategory: Optional[str] = Field(None, max_length=100)
    status: Optional[str] = Field(None, max_length=50)
    priority: Optional[str] = Field(None, max_length=50)
    severity: Optional[str] = Field(None, max_length=50)
    assigned_to: Optional[str] = Field(None, max_length=255)
    assigned_email: Optional[str] = Field(None, max_length=255)
    assigned_team: Optional[str] = Field(None, max_length=100)
    workflow_stage: Optional[str] = Field(None, max_length=100)
    workflow_step: Optional[str] = Field(None, max_length=100)
    sla_deadline: Optional[datetime] = None
    escalation_level: Optional[int] = None
    escalation_reason: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    external_references: Optional[Dict[str, Any]] = None

class CaseResponse(CaseBase):
    id: UUID
    case_id: str
    case_number: str
    created_by: str
    updated_by: Optional[str] = None
    opened_at: datetime
    closed_at: Optional[datetime] = None
    last_activity_at: datetime
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Workflow Schemas
class CaseWorkflowBase(BaseModel):
    workflow_name: str = Field(..., min_length=1, max_length=255)
    workflow_description: Optional[str] = None
    case_type: str = Field(..., max_length=50)
    workflow_config: Dict[str, Any] = Field(..., description="Workflow definition")
    stages: Dict[str, Any] = Field(..., description="Workflow stages")
    transitions: Dict[str, Any] = Field(..., description="Stage transitions")
    sla_config: Optional[Dict[str, Any]] = None
    escalation_rules: Optional[Dict[str, Any]] = None
    is_active: bool = True
    is_default: bool = False

class CaseWorkflowCreate(CaseWorkflowBase):
    pass

class CaseWorkflowUpdate(BaseModel):
    workflow_name: Optional[str] = Field(None, min_length=1, max_length=255)
    workflow_description: Optional[str] = None
    workflow_config: Optional[Dict[str, Any]] = None
    stages: Optional[Dict[str, Any]] = None
    transitions: Optional[Dict[str, Any]] = None
    sla_config: Optional[Dict[str, Any]] = None
    escalation_rules: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None

class CaseWorkflowResponse(CaseWorkflowBase):
    id: UUID
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Case Activity Schemas
class CaseActivityBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    activity_type: str = Field(..., max_length=50)
    activity_category: Optional[str] = Field(None, max_length=100)
    title: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    content: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    old_values: Optional[Dict[str, Any]] = None
    new_values: Optional[Dict[str, Any]] = None
    performed_by_type: str = Field(..., max_length=50)
    ip_address: Optional[str] = Field(None, max_length=45)
    user_agent: Optional[str] = None

class CaseActivityCreate(CaseActivityBase):
    activity_id: Optional[str] = Field(None, max_length=50)
    performed_by: str = Field(..., max_length=255)

class CaseActivityResponse(CaseActivityBase):
    id: UUID
    activity_id: str
    performed_by: str
    activity_timestamp: datetime
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Assignment Schemas
class CaseAssignmentBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    assigned_to: str = Field(..., max_length=255)
    assigned_email: str = Field(..., max_length=255)
    assigned_team: Optional[str] = Field(None, max_length=100)
    assignment_type: str = Field(..., max_length=50)
    assignment_reason: Optional[str] = None
    assignment_method: str = Field(..., max_length=50)
    workload_priority: Optional[int] = None
    estimated_effort_hours: Optional[float] = None

class CaseAssignmentCreate(CaseAssignmentBase):
    assigned_by: str = Field(..., max_length=255)

class CaseAssignmentUpdate(BaseModel):
    assigned_to: Optional[str] = Field(None, max_length=255)
    assigned_email: Optional[str] = Field(None, max_length=255)
    assigned_team: Optional[str] = Field(None, max_length=100)
    assignment_type: Optional[str] = Field(None, max_length=50)
    assignment_reason: Optional[str] = None
    workload_priority: Optional[int] = None
    estimated_effort_hours: Optional[float] = None
    actual_effort_hours: Optional[float] = None
    status: Optional[str] = Field(None, max_length=50)

class CaseAssignmentResponse(CaseAssignmentBase):
    id: UUID
    status: str
    is_active: bool
    actual_effort_hours: Optional[float] = None
    assigned_at: datetime
    accepted_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    transferred_at: Optional[datetime] = None
    assigned_by: str
    transferred_to: Optional[str] = None
    transfer_reason: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Escalation Schemas
class CaseEscalationBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    escalation_level: int = Field(..., ge=1)
    escalation_type: str = Field(..., max_length=50)
    escalation_reason: str = Field(..., min_length=1)
    escalation_trigger: Optional[str] = Field(None, max_length=100)
    escalated_from: Optional[str] = Field(None, max_length=255)
    escalated_to: str = Field(..., max_length=255)
    escalated_team: Optional[str] = Field(None, max_length=100)
    escalation_deadline: Optional[datetime] = None
    escalation_data: Optional[Dict[str, Any]] = None

class CaseEscalationCreate(CaseEscalationBase):
    escalation_id: Optional[str] = Field(None, max_length=50)

class CaseEscalationUpdate(BaseModel):
    status: Optional[str] = Field(None, max_length=50)
    resolution: Optional[str] = None
    resolved_by: Optional[str] = Field(None, max_length=255)
    resolved_at: Optional[datetime] = None

class CaseEscalationResponse(CaseEscalationBase):
    id: UUID
    escalation_id: str
    status: str
    resolution: Optional[str] = None
    resolved_by: Optional[str] = None
    resolved_at: Optional[datetime] = None
    notification_sent: bool
    escalated_at: datetime
    acknowledged_at: Optional[datetime] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Document Schemas
class CaseDocumentBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    document_name: str = Field(..., min_length=1, max_length=255)
    document_type: str = Field(..., max_length=100)
    document_category: Optional[str] = Field(None, max_length=100)
    file_name: str = Field(..., max_length=255)
    file_path: str = Field(..., max_length=500)
    file_size_bytes: Optional[int] = None
    file_mime_type: Optional[str] = Field(None, max_length=100)
    file_hash: Optional[str] = Field(None, max_length=255)
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    access_level: str = Field(default='case_team', max_length=50)
    is_confidential: bool = False
    is_evidence: bool = False

class CaseDocumentCreate(CaseDocumentBase):
    document_id: Optional[str] = Field(None, max_length=50)

class CaseDocumentUpdate(BaseModel):
    document_name: Optional[str] = Field(None, min_length=1, max_length=255)
    document_type: Optional[str] = Field(None, max_length=100)
    document_category: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    access_level: Optional[str] = Field(None, max_length=50)
    is_confidential: Optional[bool] = None
    is_evidence: Optional[bool] = None
    status: Optional[str] = Field(None, max_length=50)

class CaseDocumentResponse(CaseDocumentBase):
    id: UUID
    document_id: str
    status: str
    version: int
    parent_document_id: Optional[str] = None
    uploaded_by: str
    reviewed_by: Optional[str] = None
    approved_by: Optional[str] = None
    uploaded_at: datetime
    reviewed_at: Optional[datetime] = None
    approved_at: Optional[datetime] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Comment Schemas
class CaseCommentBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    comment_type: str = Field(..., max_length=50)
    title: Optional[str] = Field(None, max_length=255)
    content: str = Field(..., min_length=1)
    is_internal: bool = True
    is_confidential: bool = False
    priority: Optional[str] = Field(None, max_length=50)
    tags: Optional[List[str]] = None
    parent_comment_id: Optional[str] = Field(None, max_length=50)
    thread_id: Optional[str] = Field(None, max_length=50)

class CaseCommentCreate(CaseCommentBase):
    comment_id: Optional[str] = Field(None, max_length=50)

class CaseCommentUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=255)
    content: Optional[str] = Field(None, min_length=1)
    is_internal: Optional[bool] = None
    is_confidential: Optional[bool] = None
    priority: Optional[str] = Field(None, max_length=50)
    tags: Optional[List[str]] = None

class CaseCommentResponse(CaseCommentBase):
    id: UUID
    comment_id: str
    created_by: str
    updated_by: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Case Team Schemas
class CaseTeamBase(BaseModel):
    team_name: str = Field(..., min_length=1, max_length=255)
    team_description: Optional[str] = None
    team_type: str = Field(..., max_length=50)
    department: Optional[str] = Field(None, max_length=100)
    team_lead: Optional[str] = Field(None, max_length=255)
    team_lead_email: Optional[str] = Field(None, max_length=255)
    max_concurrent_cases: Optional[int] = None
    specializations: Optional[List[str]] = None
    escalation_threshold: Optional[int] = None
    sla_targets: Optional[Dict[str, Any]] = None
    is_active: bool = True
    is_available: bool = True

class CaseTeamCreate(CaseTeamBase):
    pass

class CaseTeamUpdate(BaseModel):
    team_name: Optional[str] = Field(None, min_length=1, max_length=255)
    team_description: Optional[str] = None
    team_type: Optional[str] = Field(None, max_length=50)
    department: Optional[str] = Field(None, max_length=100)
    team_lead: Optional[str] = Field(None, max_length=255)
    team_lead_email: Optional[str] = Field(None, max_length=255)
    max_concurrent_cases: Optional[int] = None
    specializations: Optional[List[str]] = None
    escalation_threshold: Optional[int] = None
    sla_targets: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_available: Optional[bool] = None

class CaseTeamResponse(CaseTeamBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Team Member Schemas
class CaseTeamMemberBase(BaseModel):
    team_id: UUID
    member_email: str = Field(..., max_length=255)
    member_name: str = Field(..., max_length=255)
    role: str = Field(..., max_length=50)
    permissions: Optional[List[str]] = None
    is_active: bool = True
    is_available: bool = True
    current_caseload: int = 0
    max_caseload: Optional[int] = None
    skills: Optional[List[str]] = None
    certifications: Optional[List[str]] = None
    experience_level: Optional[str] = Field(None, max_length=50)

class CaseTeamMemberCreate(CaseTeamMemberBase):
    pass

class CaseTeamMemberUpdate(BaseModel):
    member_name: Optional[str] = Field(None, max_length=255)
    role: Optional[str] = Field(None, max_length=50)
    permissions: Optional[List[str]] = None
    is_active: Optional[bool] = None
    is_available: Optional[bool] = None
    max_caseload: Optional[int] = None
    skills: Optional[List[str]] = None
    certifications: Optional[List[str]] = None
    experience_level: Optional[str] = Field(None, max_length=50)

class CaseTeamMemberResponse(CaseTeamMemberBase):
    id: UUID
    joined_at: datetime
    last_active: datetime
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case SLA Tracking Schemas
class CaseSLATrackingBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    sla_type: str = Field(..., max_length=50)
    sla_name: str = Field(..., max_length=255)
    target_hours: float = Field(..., gt=0)
    warning_threshold_hours: Optional[float] = None
    start_time: datetime
    target_time: datetime
    warning_time: Optional[datetime] = None

class CaseSLATrackingCreate(CaseSLATrackingBase):
    pass

class CaseSLATrackingUpdate(BaseModel):
    status: Optional[str] = Field(None, max_length=50)
    actual_completion_time: Optional[datetime] = None
    elapsed_hours: Optional[float] = None
    remaining_hours: Optional[float] = None
    breach_duration_hours: Optional[float] = None
    warning_sent: Optional[bool] = None
    breach_notification_sent: Optional[bool] = None

class CaseSLATrackingResponse(CaseSLATrackingBase):
    id: UUID
    status: str
    actual_completion_time: Optional[datetime] = None
    elapsed_hours: Optional[float] = None
    remaining_hours: Optional[float] = None
    breach_duration_hours: Optional[float] = None
    warning_sent: bool
    breach_notification_sent: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Relationship Schemas
class CaseRelationshipBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    related_case_id: str = Field(..., max_length=50)
    relationship_type: str = Field(..., max_length=50)
    relationship_description: Optional[str] = None
    strength: Optional[str] = Field(None, max_length=50)
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)

class CaseRelationshipCreate(CaseRelationshipBase):
    pass

class CaseRelationshipUpdate(BaseModel):
    relationship_description: Optional[str] = None
    strength: Optional[str] = Field(None, max_length=50)
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None

class CaseRelationshipResponse(CaseRelationshipBase):
    id: UUID
    created_by: str
    verified_by: Optional[str] = None
    is_active: bool
    is_verified: bool
    created_at: datetime
    verified_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Case Template Schemas
class CaseTemplateBase(BaseModel):
    template_name: str = Field(..., min_length=1, max_length=255)
    template_description: Optional[str] = None
    case_type: str = Field(..., max_length=50)
    category: Optional[str] = Field(None, max_length=100)
    default_priority: Optional[str] = Field(None, max_length=50)
    default_severity: Optional[str] = Field(None, max_length=50)
    default_workflow: Optional[str] = Field(None, max_length=255)
    default_team: Optional[str] = Field(None, max_length=255)
    required_fields: Optional[Dict[str, Any]] = None
    optional_fields: Optional[Dict[str, Any]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    default_values: Optional[Dict[str, Any]] = None
    checklist_items: Optional[Dict[str, Any]] = None
    required_documents: Optional[Dict[str, Any]] = None
    is_active: bool = True
    is_default: bool = False

class CaseTemplateCreate(CaseTemplateBase):
    pass

class CaseTemplateUpdate(BaseModel):
    template_name: Optional[str] = Field(None, min_length=1, max_length=255)
    template_description: Optional[str] = None
    case_type: Optional[str] = Field(None, max_length=50)
    category: Optional[str] = Field(None, max_length=100)
    default_priority: Optional[str] = Field(None, max_length=50)
    default_severity: Optional[str] = Field(None, max_length=50)
    default_workflow: Optional[str] = Field(None, max_length=255)
    default_team: Optional[str] = Field(None, max_length=255)
    required_fields: Optional[Dict[str, Any]] = None
    optional_fields: Optional[Dict[str, Any]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    default_values: Optional[Dict[str, Any]] = None
    checklist_items: Optional[Dict[str, Any]] = None
    required_documents: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None

class CaseTemplateResponse(CaseTemplateBase):
    id: UUID
    usage_count: int
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Case Metrics Schemas
class CaseMetricsBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    metric_date: datetime
    time_to_assignment_hours: Optional[float] = None
    time_to_first_response_hours: Optional[float] = None
    time_to_resolution_hours: Optional[float] = None
    total_active_time_hours: Optional[float] = None
    total_activities: int = 0
    total_comments: int = 0
    total_documents: int = 0
    total_escalations: int = 0
    total_assignments: int = 0
    customer_satisfaction_score: Optional[float] = None
    resolution_quality_score: Optional[float] = None
    compliance_score: Optional[float] = None
    sla_breaches: int = 0
    sla_met_percentage: Optional[float] = None

class CaseMetricsCreate(CaseMetricsBase):
    pass

class CaseMetricsResponse(CaseMetricsBase):
    id: UUID
    calculated_at: datetime
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Notification Schemas
class CaseNotificationBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    notification_type: str = Field(..., max_length=50)
    notification_category: Optional[str] = Field(None, max_length=100)
    title: str = Field(..., min_length=1, max_length=255)
    message: str = Field(..., min_length=1)
    priority: str = Field(..., max_length=50)
    recipient_email: str = Field(..., max_length=255)
    recipient_type: str = Field(..., max_length=50)
    delivery_method: str = Field(..., max_length=50)
    metadata: Optional[Dict[str, Any]] = None
    template_used: Optional[str] = Field(None, max_length=255)
    scheduled_at: Optional[datetime] = None

class CaseNotificationCreate(CaseNotificationBase):
    notification_id: Optional[str] = Field(None, max_length=50)

class CaseNotificationUpdate(BaseModel):
    delivery_status: Optional[str] = Field(None, max_length=50)
    delivery_attempts: Optional[int] = None
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None

class CaseNotificationResponse(CaseNotificationBase):
    id: UUID
    notification_id: str
    delivery_status: str
    delivery_attempts: int
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Case Audit Trail Schemas
class CaseAuditTrailBase(BaseModel):
    case_id: str = Field(..., max_length=50)
    action: str = Field(..., max_length=100)
    action_category: str = Field(..., max_length=50)
    table_name: Optional[str] = Field(None, max_length=100)
    record_id: Optional[str] = Field(None, max_length=50)
    field_name: Optional[str] = Field(None, max_length=100)
    old_value: Optional[str] = None
    new_value: Optional[str] = None
    change_reason: Optional[str] = None
    user_email: str = Field(..., max_length=255)
    user_role: Optional[str] = Field(None, max_length=100)
    ip_address: Optional[str] = Field(None, max_length=45)
    user_agent: Optional[str] = None
    session_id: Optional[str] = Field(None, max_length=255)
    metadata: Optional[Dict[str, Any]] = None
    compliance_flags: Optional[List[str]] = None

class CaseAuditTrailCreate(CaseAuditTrailBase):
    audit_id: Optional[str] = Field(None, max_length=50)

class CaseAuditTrailResponse(CaseAuditTrailBase):
    id: UUID
    audit_id: str
    action_timestamp: datetime
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Utility and Request Schemas
class PaginationParams(BaseModel):
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=100)

class CaseFilterParams(BaseModel):
    case_type: Optional[str] = None
    category: Optional[str] = None
    status: Optional[str] = None
    priority: Optional[str] = None
    assigned_email: Optional[str] = None
    assigned_team: Optional[str] = None
    merchant_id: Optional[UUID] = None
    created_by: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    tags: Optional[List[str]] = None

class CaseSearchParams(BaseModel):
    query: str = Field(..., min_length=1)
    search_fields: Optional[List[str]] = None
    case_types: Optional[List[str]] = None
    statuses: Optional[List[str]] = None

class CaseAssignmentRequest(BaseModel):
    assigned_to: str = Field(..., max_length=255)
    assigned_email: str = Field(..., max_length=255)
    assigned_team: Optional[str] = Field(None, max_length=100)
    assignment_type: str = Field(default='primary', max_length=50)
    assignment_reason: Optional[str] = None
    assignment_method: str = Field(default='manual', max_length=50)
    workload_priority: Optional[int] = None
    estimated_effort_hours: Optional[float] = None

class CaseStatusUpdateRequest(BaseModel):
    status: str = Field(..., max_length=50)
    reason: Optional[str] = None
    workflow_stage: Optional[str] = Field(None, max_length=100)
    workflow_step: Optional[str] = Field(None, max_length=100)

class CaseEscalationRequest(BaseModel):
    escalation_level: int = Field(..., ge=1)
    escalation_type: str = Field(..., max_length=50)
    escalation_reason: str = Field(..., min_length=1)
    escalated_to: str = Field(..., max_length=255)
    escalated_team: Optional[str] = Field(None, max_length=100)
    escalation_deadline: Optional[datetime] = None

class BulkCaseOperation(BaseModel):
    case_ids: List[str] = Field(..., min_items=1)
    operation: str = Field(..., max_length=50)
    parameters: Optional[Dict[str, Any]] = None

class CaseStatistics(BaseModel):
    total_cases: int
    open_cases: int
    in_progress_cases: int
    closed_cases: int
    escalated_cases: int
    overdue_cases: int
    cases_by_priority: Dict[str, int]
    cases_by_type: Dict[str, int]
    cases_by_team: Dict[str, int]
    average_resolution_time_hours: Optional[float] = None
    sla_compliance_percentage: Optional[float] = None

class CaseWorkloadSummary(BaseModel):
    assignee_email: str
    assignee_name: Optional[str] = None
    team: Optional[str] = None
    active_cases: int
    overdue_cases: int
    cases_this_week: int
    cases_this_month: int
    average_resolution_time_hours: Optional[float] = None
    workload_percentage: Optional[float] = None

# Response List Schemas
class CaseListResponse(BaseModel):
    items: List[CaseResponse]
    total: int
    page: int
    size: int
    pages: int

class CaseActivityListResponse(BaseModel):
    items: List[CaseActivityResponse]
    total: int
    page: int
    size: int
    pages: int

class CaseAssignmentListResponse(BaseModel):
    items: List[CaseAssignmentResponse]
    total: int
    page: int
    size: int
    pages: int

class CaseEscalationListResponse(BaseModel):
    items: List[CaseEscalationResponse]
    total: int
    page: int
    size: int
    pages: int

class CaseDocumentListResponse(BaseModel):
    items: List[CaseDocumentResponse]
    total: int
    page: int
    size: int
    pages: int

class CaseCommentListResponse(BaseModel):
    items: List[CaseCommentResponse]
    total: int
    page: int
    size: int
    pages: int

class CaseTeamListResponse(BaseModel):
    items: List[CaseTeamResponse]
    total: int
    page: int
    size: int
    pages: int

class CaseTemplateListResponse(BaseModel):
    items: List[CaseTemplateResponse]
    total: int
    page: int
    size: int
    pages: int

# Legacy Compatibility Schemas
class LegacyCaseResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class LegacyCaseCreate(BaseModel):
    title: str
    description: Optional[str] = None
    case_type: str
    priority: Optional[str] = "medium"
    merchant_id: Optional[UUID] = None

class LegacyCaseUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    priority: Optional[str] = None
    assigned_to: Optional[str] = None
    assigned_email: Optional[str] = None

# Custom Response Schemas
class CustomJSONResponse(BaseModel):
    data: Optional[Any] = None
    status: str = "success"
    message: str = "Operation completed successfully"
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None