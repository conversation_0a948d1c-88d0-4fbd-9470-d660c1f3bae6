# Main FastAPI application
from fastapi import FastAPI, HTTPException, Depends
from fastapi.responses import JSONResponse
from fastapi import status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import logging
from pathlib import Path
import os
import boto3
import json
from botocore.exceptions import ClientError

# Import routers from modules
from .auth.router import router as auth_router
from .merchants.router import router as merchants_router
from .metrics.router import router as metrics_router
from .rules.router import router as rules_router
from .red_flags.router import router as red_flags_router
from .investigations.router import router as investigations_router
from .reports.router import router as reports_router
from .admin.router import router as admin_router
from .dashboard.router import router as dashboard_router
from .case_management.router import router as case_management_router
from .jobs.router import router as jobs_router
from .customers.router import router as customers_router
from .credit.router import router as credit_router

from .config import PROJECT_NAME, PROJECT_VERSION, API_V1_STR, ENVIRONMENT

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def get_secret():
    if os.getenv('ENVIRONMENT') == 'dev':
        secret_name = "zeus/dev"
        region_name = "us-east-1"
        session = boto3.session.Session()
        client = session.client(service_name='secretsmanager', region_name=region_name)
        try:
            response = client.get_secret_value(SecretId=secret_name)
            secret = json.loads(response['SecretString'])
            for key, value in secret.items():
                os.environ[key] = value
        except ClientError as e:
            logger.error(f"Error fetching secrets: {e}")
            raise

# Get secrets before app initialization
get_secret()

app = FastAPI(
    title=PROJECT_NAME,
    description="Backend API for Zeus Payment Processing System",
    version=PROJECT_VERSION,
    openapi_tags=[{"name": "Authentication", "description": "Operations with authentication"}],
    swagger_ui_init_oauth={
        "usePkceWithAuthorizationCodeGrant": True,
        "useBasicAuthenticationWithAccessCodeGrant": True
    }
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router, prefix=f"{API_V1_STR}/auth", tags=["Authentication"])
app.include_router(merchants_router, prefix=f"{API_V1_STR}/merchants", tags=["Merchants"])
app.include_router(metrics_router, prefix=f"{API_V1_STR}/metrics", tags=["Metrics"])
app.include_router(rules_router, prefix=f"{API_V1_STR}/rules", tags=["Rules"])
app.include_router(red_flags_router, prefix=f"{API_V1_STR}/red-flags", tags=["Red Flags"])
app.include_router(investigations_router, prefix=f"{API_V1_STR}/investigations", tags=["Investigations"])
app.include_router(reports_router, prefix=f"{API_V1_STR}/reports", tags=["Reports"])
app.include_router(admin_router, prefix=f"{API_V1_STR}/admin", tags=["Admin"])
app.include_router(dashboard_router, prefix=f"{API_V1_STR}/dashboard", tags=["Dashboard"])
app.include_router(case_management_router, prefix=f"{API_V1_STR}/case-management", tags=["Case Management"])
app.include_router(jobs_router, prefix=f"{API_V1_STR}/jobs", tags=["Jobs"])
app.include_router(customers_router, prefix=f"{API_V1_STR}/customers", tags=["Customers"])
app.include_router(credit_router, prefix=f"{API_V1_STR}/credit", tags=["Credit"])

# Configure templates and static files
templates_dir = Path(__file__).resolve().parent / "templates"
static_dir = Path(__file__).resolve().parent / "static"
templates_dir.mkdir(exist_ok=True)
static_dir.mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

@app.get("/")
async def root():
    return {"message": "Welcome to Zeus Payment Processing API"}

@app.middleware("http")
async def log_requests(request, call_next):
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        logger.error(f"Request failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "detail": "Internal server error",
                "message": str(e)
            }
        )
