# metrics schemas - Pydantic models for metrics module
from pydantic import BaseModel, ConfigDict
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from datetime import datetime, date
from decimal import Decimal

# Base Metric Schemas
class MetricStoreBase(BaseModel):
    name: str
    description: Optional[str] = None
    status: str = "pending"
    type: Optional[str] = None
    priority: Optional[str] = None
    query: Optional[str] = None
    metric_value_type: str
    frequency: Optional[str] = None
    tags: Optional[List[str]] = None

class MetricStoreCreate(MetricStoreBase):
    metric_code: str
    metric_table: Optional[str] = None
    source_fetching_query: Optional[str] = None

class MetricStoreResponse(MetricStoreBase):
    id: UUID
    metric_code: Optional[str] = None
    metric_table: Optional[str] = None
    is_active: bool
    schedule: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class MetricStoreUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    type: Optional[str] = None
    priority: Optional[str] = None
    query: Optional[str] = None
    is_active: Optional[bool] = None
    schedule: Optional[str] = None
    frequency: Optional[str] = None
    tags: Optional[List[str]] = None

# Metric Generation Schemas
class MetricGenerationRequest(BaseModel):
    name: str
    description: str
    frequency: str
    tags: List[str]
    metric_code: str

class MetricGenerationResponse(BaseModel):
    status: str
    message: str
    metric_id: Optional[UUID] = None
    query: Optional[str] = None
    source_query: Optional[str] = None

# Metric Value Schemas
class MetricValueBase(BaseModel):
    metric_type: str
    metric_value: Union[str, float, bool, Dict[str, Any]]

class MerchantMetricValue(MetricValueBase):
    merchant_id: UUID
    year: Optional[int] = None
    financials_date: Optional[date] = None

class TransactionMetricValue(MetricValueBase):
    transaction_id: UUID
    year: Optional[int] = None
    financials_date: Optional[date] = None

class CustomerMetricValue(MetricValueBase):
    customer_id: UUID
    year: Optional[int] = None
    financials_date: Optional[date] = None

# Metric Calculation Schemas
class MetricCalculationRequest(BaseModel):
    merchant_ids: Optional[str] = None
    metric_codes: Optional[str] = None

class MetricCalculationResponse(BaseModel):
    status: str
    message: str
    processed_metrics: int
    processed_merchants: int
    execution_time: Optional[float] = None

# Financial Metrics Schemas
class FinancialMetricsBase(BaseModel):
    monthlyVolume: Optional[Decimal] = None
    averageTicketSize: Optional[Decimal] = None
    successRate: Optional[Decimal] = None
    refundRate: Optional[Decimal] = None
    chargebackRate: Optional[Decimal] = None
    disputeRate: Optional[Decimal] = None

class FinancialMetricsResponse(FinancialMetricsBase):
    id: UUID
    merchant_id: UUID
    accountNumber: Optional[str] = None
    ifsc: Optional[str] = None
    bankName: Optional[str] = None
    accountType: Optional[str] = None
    verificationStatus: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Risk Metrics Schemas
class RiskMetricsBase(BaseModel):
    overall_score: Optional[int] = None
    indicator_type: Optional[str] = None
    frequency: Optional[str] = None
    severity: Optional[str] = None
    status: Optional[str] = None

class RiskMetricsResponse(RiskMetricsBase):
    id: UUID
    merchant_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Website Metrics Schemas
class WebsiteMetricsBase(BaseModel):
    domain: Optional[str] = None
    age: Optional[str] = None
    monthly_traffic: Optional[str] = None
    traffic_trend_value: Optional[str] = None
    traffic_trend_positive: Optional[bool] = None
    trust_score: Optional[str] = None
    security: Optional[Dict[str, Any]] = None

class WebsiteMetricsResponse(WebsiteMetricsBase):
    id: UUID
    merchant_id: UUID
    last_updated: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Metrics GPT Schemas
class MetricsQuestion(BaseModel):
    user_prompt: str
    chat_id: Optional[str] = None

class MetricsGPTResponse(BaseModel):
    id: str
    chat_id: str
    response: str
    timestamp: str
    type: str = "report"

# Condition Fields Schema
class ConditionFieldsResponse(BaseModel):
    fields: List[str]

# Metrics List Response
class MetricsListResponse(BaseModel):
    metrics: List[MetricStoreResponse]

# Generic Response
class MetricsResponse(BaseModel):
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None