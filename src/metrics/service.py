# metrics service - business logic for metrics module
from sqlalchemy.orm import Session
from sqlalchemy import select, text, inspect
from typing import Optional, List, Dict, Any, Tuple
from uuid import UUID
from fastapi import HTTPException, status
from datetime import datetime
import json
import pandas as pd

from .models import (
    MetricStore, merchant_metrics, transaction_metrics, customer_metrics,
    financial_metrics, risk_metrics, website_metrics, merchant_temporal_metrics,
    Metrics_numeric, Metrics_string, Metrics_boolean, LLM_metrics,
    merchant_metric_value_numeric, merchant_metric_value_string, merchant_metric_value_boolean
)
from .schemas import (
    MetricStoreCreate, MetricStoreUpdate, MetricStoreResponse,
    MetricGenerationRequest, MetricGenerationResponse,
    MetricCalculationRequest, MetricCalculationResponse,
    FinancialMetricsResponse, RiskMetricsResponse, WebsiteMetricsResponse
)
from ..pagination import PaginationParams, PaginatedResponse, paginate_query

class MetricsService:
    def __init__(self, db: Session):
        self.db = db

    def get_metrics(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get metrics based on status filter."""
        try:
            query = select(MetricStore)

            if status:
                query = query.where(MetricStore.status == status)

            result = self.db.execute(query).scalars().all()

            metrics = []
            for metric in result:
                metric_dict = {
                    "table_name": metric.metric_table,
                    "metric_code": metric.metric_code,
                    "name": metric.name,
                    "description": metric.description,
                    "status": metric.status,
                    "type": metric.type,
                    "priority": metric.priority,
                    "is_active": metric.is_active,
                    "schedule": metric.schedule,
                    "created_at": metric.created_at,
                    "updated_at": metric.updated_at
                }
                metrics.append(metric_dict)

            return {"metrics": metrics}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def get_condition_fields(self) -> Dict[str, List[str]]:
        """Get available fields that can be used in conditions."""
        try:
            condition_fields = []

            # Get all active metrics
            metrics_query = select(MetricStore).where(
                MetricStore.is_active == True,
                MetricStore.metric_table.isnot(None),
                MetricStore.metric_code.isnot(None)
            )
            metrics = self.db.execute(metrics_query).scalars().all()

            # Add metric values
            for metric in metrics:
                if metric.metric_table and metric.metric_code:
                    condition_fields.append(f"{metric.metric_table}.{metric.metric_code}")

            # Add transaction fields (would need to import transactions model)
            # For now, adding common fields
            transaction_fields = [
                "transactions.amount", "transactions.status", "transactions.payment_method",
                "transactions.merchant_id", "transactions.customer_id", "transactions.timestamp"
            ]
            condition_fields.extend(transaction_fields)

            # Add customer fields
            customer_fields = [
                "customer.email", "customer.phone", "customer.city", "customer.country"
            ]
            condition_fields.extend(customer_fields)

            return {"fields": condition_fields}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def create_metric(self, metric_data: MetricStoreCreate) -> MetricStoreResponse:
        """Create a new metric in the store."""
        try:
            # Check if metric code already exists
            existing = self.db.query(MetricStore).filter(
                MetricStore.metric_code == metric_data.metric_code
            ).first()

            if existing:
                raise HTTPException(
                    status_code=400,
                    detail=f"Metric with code '{metric_data.metric_code}' already exists"
                )

            db_metric = MetricStore(**metric_data.dict())
            self.db.add(db_metric)
            self.db.commit()
            self.db.refresh(db_metric)

            return MetricStoreResponse.from_orm(db_metric)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create metric: {str(e)}")

    def update_metric(self, metric_id: UUID, metric_data: MetricStoreUpdate) -> MetricStoreResponse:
        """Update an existing metric."""
        try:
            metric = self.db.query(MetricStore).filter(MetricStore.id == metric_id).first()
            if not metric:
                raise HTTPException(status_code=404, detail="Metric not found")

            update_data = metric_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(metric, field, value)

            metric.updated_at = datetime.now()
            self.db.commit()
            self.db.refresh(metric)

            return MetricStoreResponse.from_orm(metric)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update metric: {str(e)}")

    def delete_metric(self, metric_id: UUID) -> Dict[str, str]:
        """Delete a metric."""
        try:
            metric = self.db.query(MetricStore).filter(MetricStore.id == metric_id).first()
            if not metric:
                raise HTTPException(status_code=404, detail="Metric not found")

            self.db.delete(metric)
            self.db.commit()

            return {"status": "success", "message": "Metric deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete metric: {str(e)}")

    def calculate_metrics(self, request: MetricCalculationRequest) -> MetricCalculationResponse:
        """Calculate metrics for specified merchants and metric codes."""
        try:
            start_time = datetime.now()

            # Parse merchant IDs if provided
            merchant_id_list = None
            if request.merchant_ids:
                merchant_id_list = [UUID(id.strip()) for id in request.merchant_ids.split(',')]

            # Parse metric codes if provided
            metric_code_list = None
            if request.metric_codes:
                metric_code_list = [code.strip() for code in request.metric_codes.split(',')]

            # Get approved and active metrics
            query = self.db.query(MetricStore).filter(
                MetricStore.status == 'approved',
                MetricStore.is_active == True
            )

            if metric_code_list:
                query = query.filter(MetricStore.metric_code.in_(metric_code_list))

            metrics = query.all()

            processed_metrics = 0
            processed_merchants = 0

            # This would contain the actual metric calculation logic
            # For now, returning a placeholder response
            for metric in metrics:
                processed_metrics += 1
                # Here you would execute the metric calculation
                # and store results in appropriate metric tables

            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            return MetricCalculationResponse(
                status="success",
                message=f"Calculated {processed_metrics} metrics",
                processed_metrics=processed_metrics,
                processed_merchants=processed_merchants,
                execution_time=execution_time
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to calculate metrics: {str(e)}")

    def get_merchant_financial_metrics(self, merchant_id: UUID) -> Optional[FinancialMetricsResponse]:
        """Get financial metrics for a merchant."""
        try:
            metric = self.db.query(financial_metrics).filter(
                financial_metrics.merchant_id == merchant_id
            ).first()

            if not metric:
                return None

            return FinancialMetricsResponse.from_orm(metric)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get financial metrics: {str(e)}")

    def get_merchant_risk_metrics(self, merchant_id: UUID) -> List[RiskMetricsResponse]:
        """Get risk metrics for a merchant."""
        try:
            metrics = self.db.query(risk_metrics).filter(
                risk_metrics.merchant_id == merchant_id
            ).all()

            return [RiskMetricsResponse.from_orm(metric) for metric in metrics]
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get risk metrics: {str(e)}")

    def get_merchant_website_metrics(self, merchant_id: UUID) -> Optional[WebsiteMetricsResponse]:
        """Get website metrics for a merchant."""
        try:
            metric = self.db.query(website_metrics).filter(
                website_metrics.merchant_id == merchant_id
            ).first()

            if not metric:
                return None

            return WebsiteMetricsResponse.from_orm(metric)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get website metrics: {str(e)}")