# metrics router - metrics management endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from .schemas import (
    MetricStoreCreate, MetricStoreResponse, MetricStoreUpdate,
    MetricGenerationRequest, MetricGenerationResponse,
    MetricCalculationRequest, MetricCalculationResponse,
    FinancialMetricsResponse, RiskMetricsResponse, WebsiteMetricsResponse,
    MetricsQuestion, MetricsGPTResponse, ConditionFieldsResponse,
    MetricsListResponse, MetricsResponse
)
from .service import MetricsService

router = APIRouter()

def get_metrics_service(db: Session = Depends(get_db)) -> MetricsService:
    return MetricsService(db)

@router.get("/", response_model=MetricsListResponse)
async def get_metrics(
    status: Optional[str] = Query(None, description="Filter metrics by status"),
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """
    Get metrics based on status filter.
    Returns metrics in format: [{"table_name": "metric_table", "metric_code": "metric_code", ...}]
    """
    return metrics_service.get_metrics(status)

@router.post("/", response_model=MetricStoreResponse)
async def create_metric(
    metric: MetricStoreCreate,
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new metric in the store."""
    return metrics_service.create_metric(metric)

@router.put("/{metric_id}", response_model=MetricStoreResponse)
async def update_metric(
    metric_id: UUID = Path(..., description="The UUID of the metric"),
    metric_data: MetricStoreUpdate = None,
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """Update an existing metric."""
    return metrics_service.update_metric(metric_id, metric_data)

@router.delete("/{metric_id}")
async def delete_metric(
    metric_id: UUID = Path(..., description="The UUID of the metric"),
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """Delete a metric."""
    return metrics_service.delete_metric(metric_id)

@router.get("/condition-fields", response_model=ConditionFieldsResponse)
async def get_condition_fields(
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """
    Get available fields that can be used in conditions.
    Returns a single array containing all available fields in format table_name.field_name
    """
    return metrics_service.get_condition_fields()

@router.post("/calculate", response_model=MetricCalculationResponse)
async def calculate_metrics(
    request: MetricCalculationRequest,
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """Calculate metrics for specified merchants and metric codes."""
    return metrics_service.calculate_metrics(request)

@router.post("/generate", response_model=MetricGenerationResponse)
async def generate_metric(
    request: MetricGenerationRequest,
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """
    Generate a new metric based on name and description using AI.
    This endpoint would integrate with the metric generation logic.
    """
    # This would integrate with the existing metric generation logic
    # For now, returning a placeholder response
    return MetricGenerationResponse(
        status="success",
        message="Metric generation functionality will be integrated",
        metric_id=None,
        query=None,
        source_query=None
    )

# Merchant-specific metric endpoints
@router.get("/merchants/{merchant_id}/financial", response_model=Optional[FinancialMetricsResponse])
async def get_merchant_financial_metrics(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """Get financial metrics for a specific merchant."""
    return metrics_service.get_merchant_financial_metrics(merchant_id)

@router.get("/merchants/{merchant_id}/risk", response_model=List[RiskMetricsResponse])
async def get_merchant_risk_metrics(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """Get risk metrics for a specific merchant."""
    return metrics_service.get_merchant_risk_metrics(merchant_id)

@router.get("/merchants/{merchant_id}/website", response_model=Optional[WebsiteMetricsResponse])
async def get_merchant_website_metrics(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """Get website metrics for a specific merchant."""
    return metrics_service.get_merchant_website_metrics(merchant_id)

# Metrics GPT endpoints
@router.post("/gpt/ask", response_model=MetricsGPTResponse)
async def ask_metrics_gpt(
    question: MetricsQuestion,
    current_user: User = Depends(get_current_user)
):
    """
    Ask a question to the metrics GPT.
    This endpoint would integrate with the existing MetricsGPT functionality.
    """
    # This would integrate with the existing MetricsGPT logic
    # For now, returning a placeholder response
    import uuid
    from datetime import datetime

    return MetricsGPTResponse(
        id=str(uuid.uuid4()),
        chat_id=question.chat_id or str(uuid.uuid4()),
        response="MetricsGPT functionality will be integrated",
        timestamp=datetime.now().isoformat(),
        type="report"
    )

@router.get("/gpt/status")
async def get_metrics_gpt_status():
    """Get the status of the MetricsGPT service."""
    return {"status": "active", "version": "1.0.0"}

# Legacy compatibility endpoints
@router.post("/calculate-metrics")
async def calculate_metrics_legacy(
    merchant_ids: str = None,
    metric_codes: str = None,
    metrics_service: MetricsService = Depends(get_metrics_service),
    current_user: User = Depends(get_current_user)
):
    """Legacy endpoint for metric calculation (maintains backward compatibility)."""
    request = MetricCalculationRequest(
        merchant_ids=merchant_ids,
        metric_codes=metric_codes
    )
    return metrics_service.calculate_metrics(request)