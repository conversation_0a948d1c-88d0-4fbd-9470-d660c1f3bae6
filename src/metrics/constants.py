# metrics constants - module-specific constants for metrics

# Metric statuses
METRIC_STATUS_PENDING = "pending"
METRIC_STATUS_APPROVED = "approved"
METRIC_STATUS_REJECTED = "rejected"
METRIC_STATUS_ACTIVE = "active"
METRIC_STATUS_INACTIVE = "inactive"

METRIC_STATUSES = [
    METRIC_STATUS_PENDING,
    METRIC_STATUS_APPROVED,
    METRIC_STATUS_REJECTED,
    METRIC_STATUS_ACTIVE,
    METRIC_STATUS_INACTIVE
]

# Metric value types
METRIC_VALUE_TYPE_NUMERIC = "numeric"
METRIC_VALUE_TYPE_STRING = "string"
METRIC_VALUE_TYPE_BOOLEAN = "boolean"
METRIC_VALUE_TYPE_JSON = "json"
METRIC_VALUE_TYPE_TIMESTAMP = "timestamp"

METRIC_VALUE_TYPES = [
    METRIC_VALUE_TYPE_NUMERIC,
    METRIC_VALUE_TYPE_STRING,
    METRIC_VALUE_TYPE_BOOLEAN,
    METRIC_VALUE_TYPE_JSON,
    METRIC_VALUE_TYPE_TIMESTAMP
]

# Metric types
METRIC_TYPE_FINANCIAL = "financial"
METRIC_TYPE_RISK = "risk"
METRIC_TYPE_OPERATIONAL = "operational"
METRIC_TYPE_BEHAVIORAL = "behavioral"
METRIC_TYPE_COMPLIANCE = "compliance"
METRIC_TYPE_PERFORMANCE = "performance"

METRIC_TYPES = [
    METRIC_TYPE_FINANCIAL,
    METRIC_TYPE_RISK,
    METRIC_TYPE_OPERATIONAL,
    METRIC_TYPE_BEHAVIORAL,
    METRIC_TYPE_COMPLIANCE,
    METRIC_TYPE_PERFORMANCE
]

# Metric priorities
METRIC_PRIORITY_LOW = "low"
METRIC_PRIORITY_MEDIUM = "medium"
METRIC_PRIORITY_HIGH = "high"
METRIC_PRIORITY_CRITICAL = "critical"

METRIC_PRIORITIES = [
    METRIC_PRIORITY_LOW,
    METRIC_PRIORITY_MEDIUM,
    METRIC_PRIORITY_HIGH,
    METRIC_PRIORITY_CRITICAL
]

# Metric frequencies
METRIC_FREQUENCY_REAL_TIME = "real_time"
METRIC_FREQUENCY_HOURLY = "hourly"
METRIC_FREQUENCY_DAILY = "daily"
METRIC_FREQUENCY_WEEKLY = "weekly"
METRIC_FREQUENCY_MONTHLY = "monthly"
METRIC_FREQUENCY_QUARTERLY = "quarterly"
METRIC_FREQUENCY_YEARLY = "yearly"

METRIC_FREQUENCIES = [
    METRIC_FREQUENCY_REAL_TIME,
    METRIC_FREQUENCY_HOURLY,
    METRIC_FREQUENCY_DAILY,
    METRIC_FREQUENCY_WEEKLY,
    METRIC_FREQUENCY_MONTHLY,
    METRIC_FREQUENCY_QUARTERLY,
    METRIC_FREQUENCY_YEARLY
]

# Risk levels
RISK_LEVEL_LOW = "low"
RISK_LEVEL_MEDIUM = "medium"
RISK_LEVEL_HIGH = "high"
RISK_LEVEL_CRITICAL = "critical"

RISK_LEVELS = [
    RISK_LEVEL_LOW,
    RISK_LEVEL_MEDIUM,
    RISK_LEVEL_HIGH,
    RISK_LEVEL_CRITICAL
]

# Metric table names
METRIC_TABLE_MERCHANT = "merchant_metrics"
METRIC_TABLE_TRANSACTION = "transaction_metrics"
METRIC_TABLE_CUSTOMER = "customer_metrics"
METRIC_TABLE_FINANCIAL = "financial_metrics"
METRIC_TABLE_RISK = "risk_metrics"
METRIC_TABLE_WEBSITE = "website_metrics"

METRIC_TABLES = [
    METRIC_TABLE_MERCHANT,
    METRIC_TABLE_TRANSACTION,
    METRIC_TABLE_CUSTOMER,
    METRIC_TABLE_FINANCIAL,
    METRIC_TABLE_RISK,
    METRIC_TABLE_WEBSITE
]

# Default values
DEFAULT_METRIC_STATUS = METRIC_STATUS_PENDING
DEFAULT_METRIC_PRIORITY = METRIC_PRIORITY_MEDIUM
DEFAULT_METRIC_FREQUENCY = METRIC_FREQUENCY_DAILY
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100

# Query limits
MAX_QUERY_LENGTH = 10000
MAX_DESCRIPTION_LENGTH = 1000
MAX_NAME_LENGTH = 255

# GPT related constants
GPT_MAX_PROMPT_LENGTH = 4000
GPT_DEFAULT_TEMPERATURE = 0.7
GPT_MAX_TOKENS = 1000