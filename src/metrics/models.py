# metrics models - SQLAlchemy models for metrics module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Date, DECIMAL, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Core Metric Store
class MetricStore(Base):
    __tablename__ = 'metric_store'
    __table_args__ = (
        Index('idx_metric_store_metric_code', 'metric_code', unique=True),
        {'schema': 'public'}
    )

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    status = Column(String(50), default='pending')
    type = Column(String(50), nullable=True)
    priority = Column(String(50), nullable=True)
    query = Column(Text, nullable=True)
    prompt = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    is_active = Column(Boolean, default=True)
    schedule = Column(String(100), nullable=True)
    metric_code = Column(String(100), nullable=True, unique=True, index=True)
    metric_table = Column(String(100), nullable=True)
    source_fetching_query = Column(String(1000), nullable=True)
    metric_value_type = Column(String(50), nullable=False)
    query_analysis = Column(JSONB, nullable=True)
    frequency = Column(String(50), nullable=True)
    tags = Column(ARRAY(String), nullable=True)

# Generic Metrics Tables
class merchant_metrics(Base):
    __tablename__ = 'merchant_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(JSONB)
    year = Column(Integer)
    financials_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class transaction_metrics(Base):
    __tablename__ = 'transaction_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transaction_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(JSONB)
    year = Column(Integer)
    financials_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class customer_metrics(Base):
    __tablename__ = 'customer_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    customer_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(JSONB)
    year = Column(Integer)
    financials_date = Column(Date)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Typed Metrics
class Metrics_numeric(Base):
    __tablename__ = 'metrics_numeric'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    metric_name = Column(String)
    metric_description = Column(String)
    metric_status = Column(Boolean)
    metric_value = Column(Float)
    metric_operation = Column(String)
    rule_code = Column(String)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

class Metrics_string(Base):
    __tablename__ = 'metrics_string'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    metric_name = Column(String)
    metric_description = Column(String)
    metric_status = Column(Boolean)
    metric_value = Column(String)
    metric_operation = Column(String)
    rule_code = Column(String)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

class Metrics_boolean(Base):
    __tablename__ = 'metrics_boolean'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    metric_name = Column(String)
    metric_description = Column(String)
    metric_status = Column(Boolean)
    metric_value = Column(Boolean)
    metric_operation = Column(String)
    rule_code = Column(String)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

# LLM Metrics
class LLM_metrics(Base):
    __tablename__ = 'LLM_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), default=uuid.uuid4, primary_key=True)
    metric_name = Column(String)
    metric_description = Column(String)
    severity = Column(String)
    active_status = Column(Boolean)
    last_updated = Column(DateTime)
    last_updated_by = Column(String)

# Merchant Metric Values
class merchant_mertric_results(Base):
    __tablename__ = 'merchant_mertric_results'
    __table_args__ = {'schema': 'public'}
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_result = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

class merchant_metric_value_numeric(Base):
    __tablename__ = 'merchant_metric_value_numeric'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

class merchant_metric_value_string(Base):
    __tablename__ = 'merchant_metric_value_string'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)

class merchant_metric_value_boolean(Base):
    __tablename__ = 'merchant_metric_value_boolean'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    metric_type = Column(String(255))
    metric_value = Column(Boolean)
    created_at = Column(DateTime, default=datetime.now)

# Financial Metrics
class financial_metrics(Base):
    __tablename__ = 'financial_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    monthlyVolume = Column(DECIMAL(15,2))
    averageTicketSize = Column(DECIMAL(10,2))
    successRate = Column(DECIMAL(5,2))
    refundRate = Column(DECIMAL(5,2))
    chargebackRate = Column(DECIMAL(5,2))
    disputeRate = Column(DECIMAL(5,2))
    accountNumber = Column(String(50))
    ifsc = Column(String(20))
    bankName = Column(String(100))
    accountType = Column(String(50))
    verificationStatus = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class processing_metrics(Base):
    __tablename__ = 'processing_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    monthly_volume = Column(DECIMAL(15,2))
    average_ticket_size = Column(DECIMAL(10,2))
    success_rate = Column(DECIMAL(5,2))
    refund_rate = Column(DECIMAL(5,2))
    chargeback_rate = Column(DECIMAL(5,2))
    dispute_rate = Column(DECIMAL(5,2))
    created_at = Column(DateTime, default=datetime.now)

# Risk Metrics
class risk_metrics(Base):
    __tablename__ = 'risk_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    overall_score = Column(Integer)
    indicator_type = Column(String(100))
    frequency = Column(String(50))
    severity = Column(String(50))
    status = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

# Website Metrics
class website_metrics(Base):
    __tablename__ = 'website_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    domain = Column(String(255))
    age = Column(String(50))
    monthly_traffic = Column(String(50))
    traffic_trend_value = Column(String(50))
    traffic_trend_positive = Column(Boolean)
    trust_score = Column(String(20))
    security = Column(JSONB)
    last_updated = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

# Temporal Metrics
class merchant_temporal_metrics(Base):
    __tablename__ = 'merchant_temporal_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    data_type = Column(String(50))
    data_value = Column(String(50))
    data_timestamp = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)