# metrics exceptions - module-specific errors for metrics
from fastapi import HTTPException, status

class MetricNotFoundError(HTTPException):
    def __init__(self, metric_id: str = None):
        detail = f"Metric {metric_id} not found" if metric_id else "Metric not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class MetricAlreadyExistsError(HTTPException):
    def __init__(self, metric_code: str = None):
        detail = f"Metric with code '{metric_code}' already exists" if metric_code else "Metric already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidMetricDataError(HTTPException):
    def __init__(self, detail: str = "Invalid metric data"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class MetricCalculationError(HTTPException):
    def __init__(self, detail: str = "Failed to calculate metric"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class MetricGenerationError(HTTPException):
    def __init__(self, detail: str = "Failed to generate metric"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidMetricStatusError(HTTPException):
    def __init__(self, status: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid metric status: {status}"
        )

class InvalidMetricTypeError(HTTPException):
    def __init__(self, metric_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid metric type: {metric_type}"
        )

class InvalidMetricValueTypeError(HTTPException):
    def __init__(self, value_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid metric value type: {value_type}"
        )

class MetricQueryError(HTTPException):
    def __init__(self, detail: str = "Invalid metric query"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class MetricExecutionError(HTTPException):
    def __init__(self, detail: str = "Failed to execute metric"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class MetricAccessDeniedError(HTTPException):
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to metric data"
        )

class MetricValidationError(HTTPException):
    def __init__(self, detail: str = "Metric validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class MetricsGPTError(HTTPException):
    def __init__(self, detail: str = "MetricsGPT service error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class MetricStorageError(HTTPException):
    def __init__(self, detail: str = "Failed to store metric data"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class MetricRetrievalError(HTTPException):
    def __init__(self, detail: str = "Failed to retrieve metric data"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)