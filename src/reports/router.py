# reports router - report generation and management endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    ReportCreate, ReportUpdate, ReportResponse, ReportWithComponentsResponse,
    ReportComponentCreate, ReportComponentResponse, ReportComponentListCreate,
    ReportSummaryResponse, ReportStatistics, ReportExportCreate, ReportExportResponse,
    ReportListResponse, LegacyReportResponse, LegacyReportListResponse
)
from .service import ReportsService

router = APIRouter()

def get_reports_service(db: Session = Depends(get_db)) -> ReportsService:
    return ReportsService(db)

# Core Report Management Endpoints
@router.post("/", response_model=ReportResponse, status_code=201)
async def create_report(
    report: ReportCreate,
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new report."""
    return reports_service.create_report(report, current_user.email)

@router.get("/", response_model=ReportListResponse)
async def get_reports(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    investigator: Optional[str] = Query(None, description="Filter by investigator email"),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of reports with optional investigator filter."""
    pagination = PaginationParams(page=page, size=size)
    result = reports_service.get_all_reports(pagination, investigator)
    return {"data": result.items}

@router.get("/{report_id}", response_model=ReportWithComponentsResponse)
async def get_report(
    report_id: UUID = Path(..., description="The report ID"),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Get report by ID with all components."""
    # Track view for analytics
    reports_service.track_report_view(report_id, current_user.email)
    return reports_service.get_report_by_id(report_id)

@router.put("/{report_id}", response_model=ReportResponse)
async def update_report(
    report_id: UUID = Path(..., description="The report ID"),
    report_data: ReportUpdate = None,
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Update an existing report."""
    return reports_service.update_report(report_id, report_data, current_user.email)

@router.delete("/{report_id}")
async def delete_report(
    report_id: UUID = Path(..., description="The report ID"),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Delete a report and all its components."""
    return reports_service.delete_report(report_id)

# Report Component Management Endpoints
@router.post("/{report_id}/components", response_model=ReportComponentResponse, status_code=201)
async def add_report_component(
    report_id: UUID = Path(..., description="The report ID"),
    component: ReportComponentCreate = None,
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Add a component to a report."""
    return reports_service.add_report_component(report_id, component)

@router.put("/{report_id}/components", status_code=200)
async def update_report_components(
    report_id: UUID = Path(..., description="The report ID"),
    components: ReportComponentListCreate = None,
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Update all components for a report (replaces existing components)."""
    return reports_service.update_report_components(report_id, components, current_user.email)

@router.get("/{report_id}/components")
async def get_report_components(
    report_id: UUID = Path(..., description="The report ID"),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Get all components for a report."""
    components = reports_service.get_report_components(report_id)
    return {"data": components}

@router.delete("/components/{component_id}")
async def delete_report_component(
    component_id: UUID = Path(..., description="The component ID"),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Delete a specific report component."""
    return reports_service.delete_report_component(component_id)

# AI-Powered Report Analysis Endpoints
@router.get("/{report_id}/summary", response_model=ReportSummaryResponse)
async def get_report_summary(
    report_id: UUID = Path(..., description="The report ID"),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Generate AI-powered summary of a report."""
    return reports_service.generate_report_summary(report_id)

# Report Export Endpoints
@router.post("/{report_id}/export", response_model=ReportExportResponse, status_code=201)
async def export_report(
    report_id: UUID = Path(..., description="The report ID"),
    export_request: ReportExportCreate = None,
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Export a report in the specified format."""
    return reports_service.export_report(report_id, export_request.export_format, current_user.email)

# Analytics Endpoints
@router.get("/analytics/statistics", response_model=ReportStatistics)
async def get_report_statistics(
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive report statistics."""
    return reports_service.get_report_statistics()

@router.get("/{report_id}/analytics")
async def get_report_analytics(
    report_id: UUID = Path(..., description="The report ID"),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Get analytics for a specific report."""
    analytics = reports_service.get_report_analytics(report_id)
    return {"data": analytics} if analytics else {"data": None}

# Investigator-Specific Endpoints
@router.get("/investigators/{investigator_email}/reports")
async def get_investigator_reports(
    investigator_email: str = Path(..., description="The investigator email"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Get reports for a specific investigator."""
    pagination = PaginationParams(page=page, size=size)
    result = reports_service.get_reports_by_investigator(investigator_email, pagination)
    return {"data": result.items}

# Cross-Module Integration Endpoints
@router.post("/generate/merchant/{merchant_id}")
async def generate_merchant_report(
    merchant_id: UUID = Path(..., description="The merchant ID"),
    template_type: str = Query("merchant_analysis", description="Report template type"),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Generate a comprehensive merchant analysis report."""
    report_id = reports_service.generate_merchant_report(merchant_id, template_type)
    return {"report_id": report_id, "status": "generated"}

@router.post("/generate/investigation/{investigation_id}")
async def generate_investigation_report(
    investigation_id: str = Path(..., description="The investigation ID"),
    reports_service: ReportsService = Depends(get_reports_service),
    current_user: User = Depends(get_current_user)
):
    """Generate a comprehensive investigation report."""
    report_id = reports_service.generate_investigation_report(investigation_id)
    return {"report_id": report_id, "status": "generated"}

# Legacy Endpoints (for backward compatibility)
@router.get("/added-new-report", include_in_schema=False)
async def add_new_report_legacy(
    investigator_email: str,
    report_title: str,
    reports_service: ReportsService = Depends(get_reports_service)
):
    """Legacy endpoint for creating reports."""
    return reports_service.create_legacy_report(investigator_email, report_title)

@router.get("/{investigator_email}/get-report-ids", include_in_schema=False)
async def get_report_ids_legacy(
    investigator_email: str = Path(..., description="The investigator email"),
    reports_service: ReportsService = Depends(get_reports_service)
):
    """Legacy endpoint for getting report IDs by investigator."""
    return reports_service.get_legacy_report_list(investigator_email)

@router.post("/{report_id}/update-report-components", include_in_schema=False)
async def update_report_components_legacy(
    report_id: UUID = Path(..., description="The report ID"),
    report_gen: ReportComponentListCreate = None,
    reports_service: ReportsService = Depends(get_reports_service)
):
    """Legacy endpoint for updating report components."""
    try:
        result = reports_service.update_report_components(report_id, report_gen, "legacy_user")
        return result
    except HTTPException as e:
        if e.status_code == 404:
            return {"message": "No report to update"}
        raise

@router.get("/{report_id}/get-report", include_in_schema=False)
async def get_report_legacy(
    report_id: UUID = Path(..., description="The report ID"),
    reports_service: ReportsService = Depends(get_reports_service)
):
    """Legacy endpoint for getting report details."""
    try:
        report_data = reports_service.get_report_by_id(report_id)

        # Format for legacy compatibility
        legacy_response = {
            "response": {
                "report_title": report_data.report_title,
                "investigator_email": report_data.investigator_email,
                "last_updated": report_data.last_updated,
                "created_at": report_data.created_at,
                "components": report_data.components
            }
        }

        return legacy_response
    except HTTPException:
        raise

@router.get("/{report_id}/report-gen-summary", include_in_schema=False)
async def get_report_gen_summary_legacy(
    report_id: UUID = Path(..., description="The report ID"),
    reports_service: ReportsService = Depends(get_reports_service)
):
    """Legacy endpoint for getting AI-generated report summary."""
    try:
        summary = reports_service.generate_report_summary(report_id)

        # Format for legacy compatibility
        return {
            "summary": summary.summary,
            "key_insights": summary.key_insights,
            "recommendations": summary.recommendations,
            "generated_at": summary.generated_at.isoformat()
        }
    except HTTPException:
        raise