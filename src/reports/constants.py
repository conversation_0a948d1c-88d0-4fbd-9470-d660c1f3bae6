# reports constants - module-specific constants for reports

# Report component types
COMPONENT_TYPE_CHART = "chart"
COMPONENT_TYPE_TABLE = "table"
COMPONENT_TYPE_TEXT = "text"
COMPONENT_TYPE_IMAGE = "image"
COMPONENT_TYPE_METRIC = "metric"
COMPONENT_TYPE_SUMMARY = "summary"
COMPONENT_TYPE_TIMELINE = "timeline"
COMPONENT_TYPE_MAP = "map"
COMPONENT_TYPE_GRAPH = "graph"
COMPONENT_TYPE_DASHBOARD = "dashboard"

COMPONENT_TYPES = [
    COMPONENT_TYPE_CHART,
    COMPONENT_TYPE_TABLE,
    COMPONENT_TYPE_TEXT,
    COMPONENT_TYPE_IMAGE,
    COMPONENT_TYPE_METRIC,
    COMPONENT_TYPE_SUMMARY,
    COMPONENT_TYPE_TIMELINE,
    COMPONENT_TYPE_MAP,
    COMPONENT_TYPE_GRAPH,
    COMPONENT_TYPE_DASHBOARD
]

# Report template types
TEMPLATE_TYPE_INVESTIGATION = "investigation"
TEMPLATE_TYPE_MERCHANT_ANALYSIS = "merchant_analysis"
TEMPLATE_TYPE_RISK_ASSESSMENT = "risk_assessment"
TEMPLATE_TYPE_COMPLIANCE = "compliance"
TEMPLATE_TYPE_FINANCIAL = "financial"
TEMPLATE_TYPE_OPERATIONAL = "operational"
TEMPLATE_TYPE_CUSTOM = "custom"

TEMPLATE_TYPES = [
    TEMPLATE_TYPE_INVESTIGATION,
    TEMPLATE_TYPE_MERCHANT_ANALYSIS,
    TEMPLATE_TYPE_RISK_ASSESSMENT,
    TEMPLATE_TYPE_COMPLIANCE,
    TEMPLATE_TYPE_FINANCIAL,
    TEMPLATE_TYPE_OPERATIONAL,
    TEMPLATE_TYPE_CUSTOM
]

# Export formats
EXPORT_FORMAT_PDF = "pdf"
EXPORT_FORMAT_EXCEL = "excel"
EXPORT_FORMAT_CSV = "csv"
EXPORT_FORMAT_JSON = "json"
EXPORT_FORMAT_HTML = "html"
EXPORT_FORMAT_WORD = "word"

EXPORT_FORMATS = [
    EXPORT_FORMAT_PDF,
    EXPORT_FORMAT_EXCEL,
    EXPORT_FORMAT_CSV,
    EXPORT_FORMAT_JSON,
    EXPORT_FORMAT_HTML,
    EXPORT_FORMAT_WORD
]

# Export statuses
EXPORT_STATUS_PENDING = "pending"
EXPORT_STATUS_PROCESSING = "processing"
EXPORT_STATUS_COMPLETED = "completed"
EXPORT_STATUS_FAILED = "failed"
EXPORT_STATUS_CANCELLED = "cancelled"

EXPORT_STATUSES = [
    EXPORT_STATUS_PENDING,
    EXPORT_STATUS_PROCESSING,
    EXPORT_STATUS_COMPLETED,
    EXPORT_STATUS_FAILED,
    EXPORT_STATUS_CANCELLED
]

# Permission levels for report sharing
PERMISSION_LEVEL_VIEW = "view"
PERMISSION_LEVEL_EDIT = "edit"
PERMISSION_LEVEL_ADMIN = "admin"

PERMISSION_LEVELS = [
    PERMISSION_LEVEL_VIEW,
    PERMISSION_LEVEL_EDIT,
    PERMISSION_LEVEL_ADMIN
]

# Comment types
COMMENT_TYPE_GENERAL = "general"
COMMENT_TYPE_ANNOTATION = "annotation"
COMMENT_TYPE_REVIEW = "review"
COMMENT_TYPE_APPROVAL = "approval"
COMMENT_TYPE_FEEDBACK = "feedback"
COMMENT_TYPE_QUESTION = "question"

COMMENT_TYPES = [
    COMMENT_TYPE_GENERAL,
    COMMENT_TYPE_ANNOTATION,
    COMMENT_TYPE_REVIEW,
    COMMENT_TYPE_APPROVAL,
    COMMENT_TYPE_FEEDBACK,
    COMMENT_TYPE_QUESTION
]

# Schedule frequencies
SCHEDULE_FREQUENCY_DAILY = "daily"
SCHEDULE_FREQUENCY_WEEKLY = "weekly"
SCHEDULE_FREQUENCY_MONTHLY = "monthly"
SCHEDULE_FREQUENCY_QUARTERLY = "quarterly"
SCHEDULE_FREQUENCY_YEARLY = "yearly"
SCHEDULE_FREQUENCY_CUSTOM = "custom"

SCHEDULE_FREQUENCIES = [
    SCHEDULE_FREQUENCY_DAILY,
    SCHEDULE_FREQUENCY_WEEKLY,
    SCHEDULE_FREQUENCY_MONTHLY,
    SCHEDULE_FREQUENCY_QUARTERLY,
    SCHEDULE_FREQUENCY_YEARLY,
    SCHEDULE_FREQUENCY_CUSTOM
]

# Data source types
DATA_SOURCE_MERCHANTS = "merchants"
DATA_SOURCE_TRANSACTIONS = "transactions"
DATA_SOURCE_RED_FLAGS = "red_flags"
DATA_SOURCE_RULES = "rules"
DATA_SOURCE_METRICS = "metrics"
DATA_SOURCE_INVESTIGATIONS = "investigations"
DATA_SOURCE_CUSTOMERS = "customers"
DATA_SOURCE_EXTERNAL = "external"

DATA_SOURCE_TYPES = [
    DATA_SOURCE_MERCHANTS,
    DATA_SOURCE_TRANSACTIONS,
    DATA_SOURCE_RED_FLAGS,
    DATA_SOURCE_RULES,
    DATA_SOURCE_METRICS,
    DATA_SOURCE_INVESTIGATIONS,
    DATA_SOURCE_CUSTOMERS,
    DATA_SOURCE_EXTERNAL
]

# Default values
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100
DEFAULT_EXPORT_FORMAT = EXPORT_FORMAT_PDF
DEFAULT_PERMISSION_LEVEL = PERMISSION_LEVEL_VIEW
DEFAULT_TEMPLATE_TYPE = TEMPLATE_TYPE_CUSTOM

# Validation limits
MAX_REPORT_TITLE_LENGTH = 255
MAX_COMPONENT_ID_LENGTH = 50
MAX_COMPONENT_TYPE_LENGTH = 50
MAX_COMMENT_LENGTH = 5000
MAX_TEMPLATE_NAME_LENGTH = 255
MAX_SCHEDULE_NAME_LENGTH = 255
MAX_FILE_PATH_LENGTH = 500

# Report generation limits
MAX_COMPONENTS_PER_REPORT = 50
MAX_REPORTS_PER_INVESTIGATOR = 100
MAX_EXPORT_FILE_SIZE_MB = 100
MAX_SUMMARY_LENGTH = 5000

# AI configuration
AI_SUMMARY_MAX_TOKENS = 1000
AI_INSIGHTS_MAX_TOKENS = 500
AI_RECOMMENDATIONS_MAX_TOKENS = 500
AI_TEMPERATURE = 0.3
AI_MODEL = "llama3-8b-8192"

# Analytics configuration
ANALYTICS_RETENTION_DAYS = 365
VIEW_TRACKING_ENABLED = True
EXPORT_TRACKING_ENABLED = True

# File storage configuration
EXPORT_STORAGE_PATH = "/exports"
TEMP_STORAGE_PATH = "/tmp/reports"
ALLOWED_FILE_EXTENSIONS = ['.pdf', '.xlsx', '.csv', '.json', '.html', '.docx']

# Cache configuration
REPORT_CACHE_TTL_SECONDS = 3600  # 1 hour
ANALYTICS_CACHE_TTL_SECONDS = 1800  # 30 minutes
SUMMARY_CACHE_TTL_SECONDS = 7200  # 2 hours

# Notification settings
NOTIFICATION_EXPORT_COMPLETE = True
NOTIFICATION_REPORT_SHARED = True
NOTIFICATION_COMMENT_ADDED = True
NOTIFICATION_SCHEDULE_FAILED = True

# Security settings
REQUIRE_AUTHENTICATION = True
ENABLE_AUDIT_LOGGING = True
MASK_SENSITIVE_DATA = True
ENCRYPTION_ENABLED = True

# Performance settings
ASYNC_EXPORT_ENABLED = True
BATCH_PROCESSING_SIZE = 100
MAX_CONCURRENT_EXPORTS = 5
EXPORT_TIMEOUT_MINUTES = 30

# Integration settings
CROSS_MODULE_INTEGRATION_ENABLED = True
AUTO_REPORT_GENERATION_ENABLED = True
REAL_TIME_UPDATES_ENABLED = True

# Legacy compatibility
LEGACY_ENDPOINT_SUPPORT = True
LEGACY_FORMAT_CONVERSION = True
BACKWARD_COMPATIBILITY_MODE = True