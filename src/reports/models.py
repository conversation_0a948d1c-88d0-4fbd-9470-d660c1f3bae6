# reports models - SQLAlchemy models for reports module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Core Report Generation Models
class report_gen(Base):
    __tablename__ = 'report_gen'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), unique=True, index=True)
    investigator_email = Column(String(255), index=True)
    report_title = Column(String(255), nullable=False)
    last_updated = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    created_at = Column(DateTime, default=datetime.now)

class entity_report_gen(Base):
    __tablename__ = 'entity_report_gen'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), index=True)
    frontend_component_id = Column(String(50))
    component_type = Column(String(50))
    data = Column(JSONB)
    created_at = Column(DateTime, default=datetime.now)

# Report Templates
class report_templates(Base):
    __tablename__ = 'report_templates'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_name = Column(String(255), nullable=False)
    template_type = Column(String(50))  # investigation, merchant_analysis, risk_assessment
    description = Column(Text)
    template_structure = Column(JSONB)  # JSON structure defining the template
    default_components = Column(JSONB)  # Default components for this template
    is_active = Column(Boolean, default=True)
    created_by = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Report Sharing and Permissions
class report_sharing(Base):
    __tablename__ = 'report_sharing'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), index=True)
    shared_with_email = Column(String(255), index=True)
    shared_by_email = Column(String(255))
    permission_level = Column(String(50))  # view, edit, admin
    shared_at = Column(DateTime, default=datetime.now)
    expires_at = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)

# Report Export History
class report_exports(Base):
    __tablename__ = 'report_exports'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), index=True)
    exported_by = Column(String(255))
    export_format = Column(String(50))  # pdf, excel, csv, json
    export_status = Column(String(50))  # pending, completed, failed
    file_path = Column(String(500), nullable=True)
    file_size_bytes = Column(Integer, nullable=True)
    exported_at = Column(DateTime, default=datetime.now)
    download_count = Column(Integer, default=0)
    last_downloaded_at = Column(DateTime, nullable=True)

# Report Comments and Annotations
class report_comments(Base):
    __tablename__ = 'report_comments'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), index=True)
    component_id = Column(String(50), nullable=True)  # Specific component if applicable
    comment_text = Column(Text, nullable=False)
    comment_type = Column(String(50))  # general, annotation, review, approval
    created_by = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    is_resolved = Column(Boolean, default=False)
    resolved_by = Column(String(255), nullable=True)
    resolved_at = Column(DateTime, nullable=True)

# Report Versions and History
class report_versions(Base):
    __tablename__ = 'report_versions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), index=True)
    version_number = Column(Integer, nullable=False)
    version_name = Column(String(255), nullable=True)
    changes_summary = Column(Text, nullable=True)
    report_data = Column(JSONB)  # Snapshot of report at this version
    created_by = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)
    is_current = Column(Boolean, default=False)

# Report Analytics and Metrics
class report_analytics(Base):
    __tablename__ = 'report_analytics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), index=True)
    view_count = Column(Integer, default=0)
    unique_viewers = Column(Integer, default=0)
    total_time_spent_minutes = Column(Float, default=0.0)
    last_viewed_at = Column(DateTime, nullable=True)
    most_viewed_component = Column(String(50), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Report Schedules (for automated report generation)
class report_schedules(Base):
    __tablename__ = 'report_schedules'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    schedule_name = Column(String(255), nullable=False)
    template_id = Column(UUID(as_uuid=True), nullable=True)
    frequency = Column(String(50))  # daily, weekly, monthly, quarterly
    schedule_config = Column(JSONB)  # Cron expression or schedule details
    recipients = Column(JSONB)  # List of email addresses
    is_active = Column(Boolean, default=True)
    last_run_at = Column(DateTime, nullable=True)
    next_run_at = Column(DateTime, nullable=True)
    created_by = Column(String(255))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Report Data Sources (for tracking data lineage)
class report_data_sources(Base):
    __tablename__ = 'report_data_sources'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), index=True)
    component_id = Column(String(50))
    data_source_type = Column(String(100))  # merchants, transactions, red_flags, etc.
    data_source_table = Column(String(100))
    query_used = Column(Text, nullable=True)
    data_timestamp = Column(DateTime, nullable=True)
    record_count = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.now)