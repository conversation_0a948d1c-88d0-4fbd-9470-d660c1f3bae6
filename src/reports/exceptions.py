# reports exceptions - module-specific errors for reports
from fastapi import HTTPException, status

class ReportNotFoundError(HTTPException):
    def __init__(self, report_id: str = None):
        detail = f"Report {report_id} not found" if report_id else "Report not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class ReportAlreadyExistsError(HTTPException):
    def __init__(self, report_id: str = None):
        detail = f"Report {report_id} already exists" if report_id else "Report already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidReportDataError(HTTPException):
    def __init__(self, detail: str = "Invalid report data"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class ReportValidationError(HTTPException):
    def __init__(self, detail: str = "Report validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class ReportCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create report"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update report"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete report"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportComponentNotFoundError(HTTPException):
    def __init__(self, component_id: str = None):
        detail = f"Report component {component_id} not found" if component_id else "Report component not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class ReportComponentCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create report component"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidComponentTypeError(HTTPException):
    def __init__(self, component_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid component type: {component_type}"
        )

class ReportTemplateNotFoundError(HTTPException):
    def __init__(self, template_id: str = None):
        detail = f"Report template {template_id} not found" if template_id else "Report template not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class ReportTemplateCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create report template"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidTemplateTypeError(HTTPException):
    def __init__(self, template_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid template type: {template_type}"
        )

class ReportExportError(HTTPException):
    def __init__(self, detail: str = "Failed to export report"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidExportFormatError(HTTPException):
    def __init__(self, export_format: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid export format: {export_format}"
        )

class ExportNotFoundError(HTTPException):
    def __init__(self, export_id: str = None):
        detail = f"Export {export_id} not found" if export_id else "Export not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class ExportTimeoutError(HTTPException):
    def __init__(self, detail: str = "Export operation timed out"):
        super().__init__(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=detail)

class ReportSharingError(HTTPException):
    def __init__(self, detail: str = "Failed to share report"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidPermissionLevelError(HTTPException):
    def __init__(self, permission_level: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid permission level: {permission_level}"
        )

class ReportAccessDeniedError(HTTPException):
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to report"
        )

class ReportCommentNotFoundError(HTTPException):
    def __init__(self, comment_id: str = None):
        detail = f"Report comment {comment_id} not found" if comment_id else "Report comment not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class ReportCommentCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create report comment"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidCommentTypeError(HTTPException):
    def __init__(self, comment_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid comment type: {comment_type}"
        )

class ReportVersionNotFoundError(HTTPException):
    def __init__(self, version_id: str = None):
        detail = f"Report version {version_id} not found" if version_id else "Report version not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class ReportVersionCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create report version"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportAnalyticsError(HTTPException):
    def __init__(self, detail: str = "Failed to get report analytics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportStatisticsError(HTTPException):
    def __init__(self, detail: str = "Failed to generate report statistics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportScheduleNotFoundError(HTTPException):
    def __init__(self, schedule_id: str = None):
        detail = f"Report schedule {schedule_id} not found" if schedule_id else "Report schedule not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class ReportScheduleCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create report schedule"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidScheduleFrequencyError(HTTPException):
    def __init__(self, frequency: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid schedule frequency: {frequency}"
        )

class ReportSummaryGenerationError(HTTPException):
    def __init__(self, detail: str = "Failed to generate report summary"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class AIServiceUnavailableError(HTTPException):
    def __init__(self, detail: str = "AI service is currently unavailable"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class InvalidDataSourceError(HTTPException):
    def __init__(self, data_source: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid data source: {data_source}"
        )

class ReportGenerationError(HTTPException):
    def __init__(self, detail: str = "Failed to generate report"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportConfigurationError(HTTPException):
    def __init__(self, detail: str = "Report configuration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportStorageError(HTTPException):
    def __init__(self, detail: str = "Report storage error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportFileSizeExceededError(HTTPException):
    def __init__(self, max_size_mb: int = 100):
        super().__init__(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"Report file size exceeds maximum allowed size of {max_size_mb}MB"
        )

class ReportLimitExceededError(HTTPException):
    def __init__(self, limit_type: str = "reports", limit: int = 100):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Maximum {limit_type} limit of {limit} exceeded"
        )

class ReportCacheError(HTTPException):
    def __init__(self, detail: str = "Report cache error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class ReportIntegrationError(HTTPException):
    def __init__(self, detail: str = "Report integration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)