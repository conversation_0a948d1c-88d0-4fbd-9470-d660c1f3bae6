# reports service - business logic for reports module
from sqlalchemy.orm import Session
from sqlalchemy import select, text, and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4
from fastapi import HTTPException, status
from datetime import datetime
import logging
import os
from groq import Groq

from .models import (
    report_gen, entity_report_gen, report_templates, report_sharing,
    report_exports, report_comments, report_versions, report_analytics,
    report_schedules, report_data_sources
)
from .schemas import (
    ReportCreate, ReportUpdate, ReportResponse, ReportWithComponentsResponse,
    ReportComponentCreate, ReportComponentResponse, ReportComponentListCreate,
    ReportTemplateCreate, ReportTemplateUpdate, ReportTemplateResponse,
    ReportSharingCreate, ReportSharingUpdate, ReportSharingResponse,
    ReportExportCreate, ReportExportResponse, ReportCommentCreate,
    ReportCommentUpdate, ReportCommentResponse, ReportVersionCreate,
    ReportVersionResponse, ReportAnalyticsResponse, ReportScheduleCreate,
    ReportScheduleUpdate, ReportScheduleResponse, ReportSummaryResponse,
    ReportStatistics, LegacyReportResponse
)
from ..pagination import PaginationParams, PaginatedResponse, paginate_query

logger = logging.getLogger(__name__)

class ReportsService:
    def __init__(self, db: Session):
        self.db = db

    def generate_report_id(self) -> UUID:
        """Generate a unique report ID."""
        return uuid4()

    # Core Report Management
    def create_report(self, report_data: ReportCreate, created_by: str) -> ReportResponse:
        """Create a new report."""
        try:
            report_id = report_data.report_id or self.generate_report_id()

            # Check for duplicate report_id
            existing = self.db.query(report_gen).filter(
                report_gen.report_id == report_id
            ).first()

            if existing:
                raise HTTPException(
                    status_code=400,
                    detail="Report ID already exists"
                )

            db_report = report_gen(
                report_id=report_id,
                report_title=report_data.report_title,
                investigator_email=report_data.investigator_email,
                last_updated=datetime.now()
            )

            self.db.add(db_report)
            self.db.commit()
            self.db.refresh(db_report)

            return ReportResponse.from_orm(db_report)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create report: {str(e)}")

    def get_report_by_id(self, report_id: UUID) -> ReportWithComponentsResponse:
        """Get report by ID with components."""
        try:
            report = self.db.query(report_gen).filter(
                report_gen.report_id == report_id
            ).first()

            if not report:
                raise HTTPException(status_code=404, detail="Report not found")

            components = self.db.query(entity_report_gen).filter(
                entity_report_gen.report_id == report_id
            ).all()

            components_data = []
            for component in components:
                components_data.append({
                    "frontend_component_id": component.frontend_component_id,
                    "component_type": component.component_type,
                    "data": component.data,
                    "uploaded_at": component.created_at
                })

            return ReportWithComponentsResponse(
                report_title=report.report_title,
                investigator_email=report.investigator_email,
                report_id=report.report_id,
                last_updated=report.last_updated,
                created_at=report.created_at,
                components=components_data
            )
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get report: {str(e)}")

    def get_reports_by_investigator(self, investigator_email: str, pagination: PaginationParams) -> PaginatedResponse[Dict[str, Any]]:
        """Get reports for a specific investigator."""
        try:
            query = self.db.query(report_gen).filter(
                report_gen.investigator_email == investigator_email
            ).order_by(report_gen.last_updated.desc())

            reports_list, total = paginate_query(query, pagination)

            reports_data = []
            for report in reports_list:
                reports_data.append({
                    "id": str(report.report_id),
                    "report_title": report.report_title,
                    "last_updated": report.last_updated.isoformat()
                })

            return PaginatedResponse.create(reports_data, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get reports: {str(e)}")

    def get_all_reports(self, pagination: PaginationParams, investigator_filter: Optional[str] = None) -> PaginatedResponse[ReportResponse]:
        """Get paginated list of all reports with optional investigator filter."""
        try:
            query = self.db.query(report_gen).order_by(report_gen.last_updated.desc())

            if investigator_filter:
                query = query.filter(report_gen.investigator_email == investigator_filter)

            reports_list, total = paginate_query(query, pagination)

            report_responses = [ReportResponse.from_orm(report) for report in reports_list]

            return PaginatedResponse.create(report_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get reports: {str(e)}")

    def update_report(self, report_id: UUID, report_data: ReportUpdate, updated_by: str) -> ReportResponse:
        """Update an existing report."""
        try:
            report = self.db.query(report_gen).filter(
                report_gen.report_id == report_id
            ).first()

            if not report:
                raise HTTPException(status_code=404, detail="Report not found")

            update_data = report_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(report, field, value)

            report.last_updated = datetime.now()

            self.db.commit()
            self.db.refresh(report)

            return ReportResponse.from_orm(report)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update report: {str(e)}")

    def delete_report(self, report_id: UUID) -> Dict[str, str]:
        """Delete a report and all its components."""
        try:
            report = self.db.query(report_gen).filter(
                report_gen.report_id == report_id
            ).first()

            if not report:
                raise HTTPException(status_code=404, detail="Report not found")

            # Delete all components
            self.db.query(entity_report_gen).filter(
                entity_report_gen.report_id == report_id
            ).delete()

            # Delete related data
            self.db.query(report_comments).filter(
                report_comments.report_id == report_id
            ).delete()

            self.db.query(report_versions).filter(
                report_versions.report_id == report_id
            ).delete()

            self.db.query(report_sharing).filter(
                report_sharing.report_id == report_id
            ).delete()

            self.db.query(report_exports).filter(
                report_exports.report_id == report_id
            ).delete()

            self.db.query(report_analytics).filter(
                report_analytics.report_id == report_id
            ).delete()

            self.db.query(report_data_sources).filter(
                report_data_sources.report_id == report_id
            ).delete()

            # Delete the report
            self.db.delete(report)
            self.db.commit()

            return {"status": "success", "message": "Report deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete report: {str(e)}")

    # Report Component Management
    def update_report_components(self, report_id: UUID, components_data: ReportComponentListCreate, updated_by: str) -> Dict[str, str]:
        """Update report components (replace all existing components)."""
        try:
            # Verify report exists
            report = self.db.query(report_gen).filter(
                report_gen.report_id == report_id
            ).first()

            if not report:
                raise HTTPException(status_code=404, detail="Report not found")

            # Delete existing components
            self.db.query(entity_report_gen).filter(
                entity_report_gen.report_id == report_id
            ).delete()

            # Add new components
            for component in components_data.report_gen:
                db_component = entity_report_gen(
                    report_id=report_id,
                    frontend_component_id=component.frontend_component_id,
                    component_type=component.component_type,
                    data=component.data
                )
                self.db.add(db_component)

            # Update report timestamp
            report.last_updated = datetime.now()

            self.db.commit()

            return {"status": "success", "message": "Report components updated successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update report components: {str(e)}")

    def add_report_component(self, report_id: UUID, component_data: ReportComponentCreate) -> ReportComponentResponse:
        """Add a single component to a report."""
        try:
            # Verify report exists
            report = self.db.query(report_gen).filter(
                report_gen.report_id == report_id
            ).first()

            if not report:
                raise HTTPException(status_code=404, detail="Report not found")

            db_component = entity_report_gen(
                report_id=report_id,
                frontend_component_id=component_data.frontend_component_id,
                component_type=component_data.component_type,
                data=component_data.data
            )

            self.db.add(db_component)

            # Update report timestamp
            report.last_updated = datetime.now()

            self.db.commit()
            self.db.refresh(db_component)

            return ReportComponentResponse.from_orm(db_component)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to add report component: {str(e)}")

    def get_report_components(self, report_id: UUID) -> List[ReportComponentResponse]:
        """Get all components for a report."""
        try:
            components = self.db.query(entity_report_gen).filter(
                entity_report_gen.report_id == report_id
            ).order_by(entity_report_gen.created_at.asc()).all()

            return [ReportComponentResponse.from_orm(component) for component in components]
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get report components: {str(e)}")

    def delete_report_component(self, component_id: UUID) -> Dict[str, str]:
        """Delete a specific report component."""
        try:
            component = self.db.query(entity_report_gen).filter(
                entity_report_gen.id == component_id
            ).first()

            if not component:
                raise HTTPException(status_code=404, detail="Component not found")

            report_id = component.report_id

            self.db.delete(component)

            # Update report timestamp
            report = self.db.query(report_gen).filter(
                report_gen.report_id == report_id
            ).first()

            if report:
                report.last_updated = datetime.now()

            self.db.commit()

            return {"status": "success", "message": "Component deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete component: {str(e)}")

    # AI-Powered Report Summarization
    def generate_report_summary(self, report_id: UUID) -> ReportSummaryResponse:
        """Generate AI-powered summary of a report."""
        try:
            # Get report details
            report = self.db.query(report_gen).filter(
                report_gen.report_id == report_id
            ).first()

            if not report:
                raise HTTPException(status_code=404, detail="Report not found")

            components = self.db.query(entity_report_gen).filter(
                entity_report_gen.report_id == report_id
            ).all()

            # Prepare data for AI summarization
            report_data = {
                "title": report.report_title,
                "investigator": report.investigator_email,
                "components": []
            }

            for component in components:
                report_data["components"].append({
                    "type": component.component_type,
                    "data": component.data
                })

            # Generate summary using AI
            summary_text = self._generate_ai_summary(report_data)
            key_insights = self._extract_key_insights(report_data)
            recommendations = self._generate_recommendations(report_data)

            return ReportSummaryResponse(
                report_id=report_id,
                summary=summary_text,
                key_insights=key_insights,
                recommendations=recommendations,
                generated_at=datetime.now()
            )
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to generate report summary: {str(e)}")

    def _generate_ai_summary(self, report_data: Dict[str, Any]) -> str:
        """Generate AI summary using Groq."""
        try:
            groq_api_key = os.getenv("GROQ_API_KEY")
            if not groq_api_key:
                return "AI summarization unavailable - API key not configured"

            client = Groq(api_key=groq_api_key)

            # Prepare prompt for summarization
            prompt = f"""
            Please provide a comprehensive summary of this investigation report:

            Report Title: {report_data['title']}
            Investigator: {report_data['investigator']}

            Components:
            """

            for component in report_data['components']:
                prompt += f"\n- {component['type']}: {str(component['data'])[:500]}..."

            prompt += "\n\nPlease provide a concise but thorough summary highlighting the key findings and important details."

            response = client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are an expert analyst specializing in investigation report summarization."},
                    {"role": "user", "content": prompt}
                ],
                model="llama3-8b-8192",
                temperature=0.3,
                max_tokens=1000
            )

            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"AI summarization failed: {str(e)}")
            return f"Summary generation failed: {str(e)}"

    def _extract_key_insights(self, report_data: Dict[str, Any]) -> List[str]:
        """Extract key insights from report data."""
        try:
            groq_api_key = os.getenv("GROQ_API_KEY")
            if not groq_api_key:
                return ["AI insights unavailable - API key not configured"]

            client = Groq(api_key=groq_api_key)

            prompt = f"""
            Based on this investigation report, identify the top 5 key insights:

            Report: {report_data['title']}
            Components: {len(report_data['components'])} data components

            Please provide exactly 5 bullet points with the most important insights.
            """

            response = client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are an expert analyst. Provide exactly 5 key insights as bullet points."},
                    {"role": "user", "content": prompt}
                ],
                model="llama3-8b-8192",
                temperature=0.3,
                max_tokens=500
            )

            insights_text = response.choices[0].message.content
            insights = [insight.strip() for insight in insights_text.split('\n') if insight.strip() and ('•' in insight or '-' in insight)]

            return insights[:5] if insights else ["No specific insights generated"]
        except Exception as e:
            logger.error(f"Key insights extraction failed: {str(e)}")
            return [f"Insights extraction failed: {str(e)}"]

    def _generate_recommendations(self, report_data: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on report data."""
        try:
            groq_api_key = os.getenv("GROQ_API_KEY")
            if not groq_api_key:
                return ["AI recommendations unavailable - API key not configured"]

            client = Groq(api_key=groq_api_key)

            prompt = f"""
            Based on this investigation report, provide 3-5 actionable recommendations:

            Report: {report_data['title']}

            Please provide specific, actionable recommendations for next steps or improvements.
            """

            response = client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are an expert consultant. Provide actionable recommendations."},
                    {"role": "user", "content": prompt}
                ],
                model="llama3-8b-8192",
                temperature=0.3,
                max_tokens=500
            )

            recommendations_text = response.choices[0].message.content
            recommendations = [rec.strip() for rec in recommendations_text.split('\n') if rec.strip() and ('•' in rec or '-' in rec)]

            return recommendations[:5] if recommendations else ["No specific recommendations generated"]
        except Exception as e:
            logger.error(f"Recommendations generation failed: {str(e)}")
            return [f"Recommendations generation failed: {str(e)}"]

    # Report Analytics and Statistics
    def get_report_statistics(self) -> ReportStatistics:
        """Get comprehensive report statistics."""
        try:
            # Total reports
            total_reports = self.db.query(func.count(report_gen.id)).scalar()

            # Reports by investigator
            investigator_counts = self.db.query(
                report_gen.investigator_email,
                func.count(report_gen.id)
            ).group_by(report_gen.investigator_email).all()

            reports_by_investigator = {email: count for email, count in investigator_counts}

            # Component type analysis
            component_counts = self.db.query(
                entity_report_gen.component_type,
                func.count(entity_report_gen.id)
            ).group_by(entity_report_gen.component_type).all()

            most_used_components = {comp_type: count for comp_type, count in component_counts}

            # Average components per report
            total_components = self.db.query(func.count(entity_report_gen.id)).scalar()
            avg_components = total_components / total_reports if total_reports > 0 else 0

            # Export statistics
            total_exports = self.db.query(func.count(report_exports.id)).scalar()

            export_format_counts = self.db.query(
                report_exports.export_format,
                func.count(report_exports.id)
            ).group_by(report_exports.export_format).all()

            exports_by_format = {format_type: count for format_type, count in export_format_counts}

            return ReportStatistics(
                total_reports=total_reports,
                reports_by_investigator=reports_by_investigator,
                reports_by_type={},  # Could be enhanced with template types
                most_used_components=most_used_components,
                average_components_per_report=round(avg_components, 2),
                total_exports=total_exports,
                exports_by_format=exports_by_format
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get report statistics: {str(e)}")

    def track_report_view(self, report_id: UUID, viewer_email: str) -> None:
        """Track report view for analytics."""
        try:
            # Get or create analytics record
            analytics = self.db.query(report_analytics).filter(
                report_analytics.report_id == report_id
            ).first()

            if not analytics:
                analytics = report_analytics(
                    report_id=report_id,
                    view_count=0,
                    unique_viewers=0,
                    total_time_spent_minutes=0.0
                )
                self.db.add(analytics)

            # Update view count
            analytics.view_count += 1
            analytics.last_viewed_at = datetime.now()
            analytics.updated_at = datetime.now()

            self.db.commit()
        except Exception as e:
            logger.error(f"Failed to track report view: {str(e)}")
            # Don't raise exception for analytics tracking failures

    def get_report_analytics(self, report_id: UUID) -> Optional[ReportAnalyticsResponse]:
        """Get analytics for a specific report."""
        try:
            analytics = self.db.query(report_analytics).filter(
                report_analytics.report_id == report_id
            ).first()

            if not analytics:
                return None

            return ReportAnalyticsResponse.from_orm(analytics)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get report analytics: {str(e)}")

    # Legacy Support Methods
    def create_legacy_report(self, investigator_email: str, report_title: str) -> LegacyReportResponse:
        """Create report using legacy format for backward compatibility."""
        try:
            report_id = self.generate_report_id()

            db_report = report_gen(
                report_id=report_id,
                investigator_email=investigator_email,
                report_title=report_title,
                last_updated=datetime.now()
            )

            self.db.add(db_report)
            self.db.commit()

            return LegacyReportResponse(
                message="Report added successfully",
                report_id=report_id,
                report_title=report_title
            )
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create legacy report: {str(e)}")

    def get_legacy_report_list(self, investigator_email: str) -> List[Dict[str, Any]]:
        """Get reports in legacy format for backward compatibility."""
        try:
            reports = self.db.query(report_gen).filter(
                report_gen.investigator_email == investigator_email
            ).order_by(report_gen.last_updated.desc()).all()

            return [
                {
                    "id": str(report.report_id),
                    "report_title": report.report_title,
                    "last_updated": report.last_updated.isoformat()
                }
                for report in reports
            ]
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get legacy report list: {str(e)}")

    # Cross-Module Integration Methods
    def generate_merchant_report(self, merchant_id: UUID, template_type: str = "merchant_analysis") -> UUID:
        """Generate a comprehensive merchant report using data from other modules."""
        try:
            # This would integrate with other modules to create comprehensive reports
            report_title = f"Merchant Analysis Report - {merchant_id}"
            investigator_email = "system@automated"

            # Create the report
            report_data = ReportCreate(
                report_title=report_title,
                investigator_email=investigator_email
            )

            report = self.create_report(report_data, "system")

            # Add components from various modules
            # This would be expanded to pull data from:
            # - Merchants module (merchant details, digital footprint)
            # - Red flags module (red flag analysis)
            # - Rules module (rule violations)
            # - Metrics module (calculated metrics)
            # - Investigations module (related investigations)

            components = [
                {
                    "frontend_component_id": "merchant_overview",
                    "component_type": "merchant_details",
                    "data": {"merchant_id": str(merchant_id), "status": "placeholder"}
                },
                {
                    "frontend_component_id": "risk_analysis",
                    "component_type": "risk_assessment",
                    "data": {"risk_level": "medium", "score": 65}
                }
            ]

            # Add components to report
            for component_data in components:
                component = ReportComponentCreate(**component_data)
                self.add_report_component(report.report_id, component)

            return report.report_id
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to generate merchant report: {str(e)}")

    def generate_investigation_report(self, investigation_id: str) -> UUID:
        """Generate a comprehensive investigation report."""
        try:
            report_title = f"Investigation Report - {investigation_id}"
            investigator_email = "system@automated"

            # Create the report
            report_data = ReportCreate(
                report_title=report_title,
                investigator_email=investigator_email
            )

            report = self.create_report(report_data, "system")

            # This would integrate with investigations module to pull:
            # - Investigation details
            # - Case events
            # - Investigation notes
            # - Related merchant data
            # - Red flags
            # - Rule violations

            components = [
                {
                    "frontend_component_id": "investigation_summary",
                    "component_type": "investigation_details",
                    "data": {"investigation_id": investigation_id, "status": "placeholder"}
                }
            ]

            # Add components to report
            for component_data in components:
                component = ReportComponentCreate(**component_data)
                self.add_report_component(report.report_id, component)

            return report.report_id
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to generate investigation report: {str(e)}")

    def export_report(self, report_id: UUID, export_format: str, exported_by: str) -> ReportExportResponse:
        """Export report in specified format."""
        try:
            # Verify report exists
            report = self.db.query(report_gen).filter(
                report_gen.report_id == report_id
            ).first()

            if not report:
                raise HTTPException(status_code=404, detail="Report not found")

            # Create export record
            export_record = report_exports(
                report_id=report_id,
                exported_by=exported_by,
                export_format=export_format,
                export_status="pending"
            )

            self.db.add(export_record)
            self.db.commit()
            self.db.refresh(export_record)

            # In a real implementation, this would trigger async export processing
            # For now, we'll mark it as completed
            export_record.export_status = "completed"
            export_record.file_path = f"/exports/{report_id}_{export_format}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{export_format}"

            self.db.commit()
            self.db.refresh(export_record)

            return ReportExportResponse.from_orm(export_record)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to export report: {str(e)}")