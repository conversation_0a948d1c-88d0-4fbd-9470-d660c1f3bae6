# reports schemas - Pydantic models for reports module
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime

# Report Generation Schemas
class ReportComponentBase(BaseModel):
    frontend_component_id: str = Field(..., max_length=50)
    component_type: str = Field(..., max_length=50)
    data: Dict[str, Any]

class ReportComponentCreate(ReportComponentBase):
    pass

class ReportComponentResponse(ReportComponentBase):
    id: UUID
    report_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class ReportBase(BaseModel):
    report_title: str = Field(..., min_length=1, max_length=255)
    investigator_email: str = Field(..., max_length=255)

class ReportCreate(ReportBase):
    report_id: Optional[UUID] = None

class ReportUpdate(BaseModel):
    report_title: Optional[str] = Field(None, min_length=1, max_length=255)
    investigator_email: Optional[str] = Field(None, max_length=255)

class ReportResponse(ReportBase):
    id: UUID
    report_id: UUID
    last_updated: datetime
    created_at: datetime
    components: List[ReportComponentResponse] = []

    model_config = ConfigDict(from_attributes=True)

class ReportWithComponentsResponse(ReportBase):
    report_id: UUID
    last_updated: datetime
    created_at: datetime
    components: List[Dict[str, Any]] = []

# Bulk Operations
class ReportComponentListCreate(BaseModel):
    report_gen: List[ReportComponentCreate]

class ReportComponentListUpdate(BaseModel):
    components: List[ReportComponentCreate]

# Report Template Schemas
class ReportTemplateBase(BaseModel):
    template_name: str = Field(..., min_length=1, max_length=255)
    template_type: str = Field(..., max_length=50)
    description: Optional[str] = None
    template_structure: Optional[Dict[str, Any]] = None
    default_components: Optional[Dict[str, Any]] = None

class ReportTemplateCreate(ReportTemplateBase):
    is_active: bool = True

class ReportTemplateUpdate(BaseModel):
    template_name: Optional[str] = Field(None, min_length=1, max_length=255)
    template_type: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    template_structure: Optional[Dict[str, Any]] = None
    default_components: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class ReportTemplateResponse(ReportTemplateBase):
    id: UUID
    is_active: bool
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Report Sharing Schemas
class ReportSharingBase(BaseModel):
    report_id: UUID
    shared_with_email: str = Field(..., max_length=255)
    permission_level: str = Field(..., max_length=50)
    expires_at: Optional[datetime] = None

class ReportSharingCreate(ReportSharingBase):
    pass

class ReportSharingUpdate(BaseModel):
    permission_level: Optional[str] = Field(None, max_length=50)
    expires_at: Optional[datetime] = None
    is_active: Optional[bool] = None

class ReportSharingResponse(ReportSharingBase):
    id: UUID
    shared_by_email: Optional[str] = None
    shared_at: datetime
    is_active: bool

    model_config = ConfigDict(from_attributes=True)

# Report Export Schemas
class ReportExportBase(BaseModel):
    report_id: UUID
    export_format: str = Field(..., max_length=50)

class ReportExportCreate(ReportExportBase):
    pass

class ReportExportResponse(ReportExportBase):
    id: UUID
    exported_by: Optional[str] = None
    export_status: str
    file_path: Optional[str] = None
    file_size_bytes: Optional[int] = None
    exported_at: datetime
    download_count: int
    last_downloaded_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Report Comment Schemas
class ReportCommentBase(BaseModel):
    report_id: UUID
    component_id: Optional[str] = Field(None, max_length=50)
    comment_text: str = Field(..., min_length=1)
    comment_type: str = Field(..., max_length=50)

class ReportCommentCreate(ReportCommentBase):
    pass

class ReportCommentUpdate(BaseModel):
    comment_text: Optional[str] = Field(None, min_length=1)
    comment_type: Optional[str] = Field(None, max_length=50)
    is_resolved: Optional[bool] = None

class ReportCommentResponse(ReportCommentBase):
    id: UUID
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    is_resolved: bool
    resolved_by: Optional[str] = None
    resolved_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Report Version Schemas
class ReportVersionBase(BaseModel):
    report_id: UUID
    version_number: int = Field(..., ge=1)
    version_name: Optional[str] = Field(None, max_length=255)
    changes_summary: Optional[str] = None
    report_data: Dict[str, Any]

class ReportVersionCreate(ReportVersionBase):
    pass

class ReportVersionResponse(ReportVersionBase):
    id: UUID
    created_by: Optional[str] = None
    created_at: datetime
    is_current: bool

    model_config = ConfigDict(from_attributes=True)

# Report Analytics Schemas
class ReportAnalyticsResponse(BaseModel):
    id: UUID
    report_id: UUID
    view_count: int
    unique_viewers: int
    total_time_spent_minutes: float
    last_viewed_at: Optional[datetime] = None
    most_viewed_component: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Report Schedule Schemas
class ReportScheduleBase(BaseModel):
    schedule_name: str = Field(..., min_length=1, max_length=255)
    template_id: Optional[UUID] = None
    frequency: str = Field(..., max_length=50)
    schedule_config: Dict[str, Any]
    recipients: List[str]

class ReportScheduleCreate(ReportScheduleBase):
    is_active: bool = True

class ReportScheduleUpdate(BaseModel):
    schedule_name: Optional[str] = Field(None, min_length=1, max_length=255)
    template_id: Optional[UUID] = None
    frequency: Optional[str] = Field(None, max_length=50)
    schedule_config: Optional[Dict[str, Any]] = None
    recipients: Optional[List[str]] = None
    is_active: Optional[bool] = None

class ReportScheduleResponse(ReportScheduleBase):
    id: UUID
    is_active: bool
    last_run_at: Optional[datetime] = None
    next_run_at: Optional[datetime] = None
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Report Summary Schemas
class ReportSummaryRequest(BaseModel):
    report_id: UUID

class ReportSummaryResponse(BaseModel):
    report_id: UUID
    summary: str
    key_insights: List[str]
    recommendations: List[str]
    generated_at: datetime

# Response List Schemas
class ReportListResponse(BaseModel):
    data: List[ReportResponse]

class ReportTemplateListResponse(BaseModel):
    data: List[ReportTemplateResponse]

class ReportSharingListResponse(BaseModel):
    data: List[ReportSharingResponse]

class ReportExportListResponse(BaseModel):
    data: List[ReportExportResponse]

class ReportCommentListResponse(BaseModel):
    data: List[ReportCommentResponse]

class ReportVersionListResponse(BaseModel):
    data: List[ReportVersionResponse]

class ReportScheduleListResponse(BaseModel):
    data: List[ReportScheduleResponse]

# Report Statistics Schema
class ReportStatistics(BaseModel):
    total_reports: int
    reports_by_investigator: Dict[str, int]
    reports_by_type: Dict[str, int]
    most_used_components: Dict[str, int]
    average_components_per_report: float
    total_exports: int
    exports_by_format: Dict[str, int]

# Legacy Compatibility Schemas (for backward compatibility)
class LegacyReportCreate(BaseModel):
    investigator_email: str
    report_title: str

class LegacyReportResponse(BaseModel):
    message: str
    report_id: UUID
    report_title: str

class LegacyReportListResponse(BaseModel):
    data: List[Dict[str, Any]]