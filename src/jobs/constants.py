# jobs constants - module-specific constants for jobs

# Job Types
JOB_TYPE_SCHEDULED = "scheduled"
JOB_TYPE_IMMEDIATE = "immediate"
JOB_TYPE_RECURRING = "recurring"
JOB_TYPE_BACKGROUND = "background"
JOB_TYPE_BATCH = "batch"
JOB_TYPE_STREAMING = "streaming"
JOB_TYPE_ETL = "etl"
JOB_TYPE_MAINTENANCE = "maintenance"
JOB_TYPE_MONITORING = "monitoring"
JOB_TYPE_CLEANUP = "cleanup"

JOB_TYPES = [
    JOB_TYPE_SCHEDULED,
    JOB_TYPE_IMMEDIATE,
    JOB_TYPE_RECURRING,
    JOB_TYPE_BACKGROUND,
    JOB_TYPE_BATCH,
    JOB_TYPE_STREAMING,
    JOB_TYPE_ETL,
    JOB_TYPE_MAINTENANCE,
    JOB_TYPE_MONITORING,
    JOB_TYPE_CLEANUP
]

# Job Categories
JOB_CATEGORY_DATA_PROCESSING = "data_processing"
JOB_CATEGORY_ANALYTICS = "analytics"
JOB_CATEGORY_MAINTENANCE = "maintenance"
JOB_CATEGORY_REPORTING = "reporting"
JOB_CATEGORY_BACKUP = "backup"
JOB_CATEGORY_SYNC = "sync"
JOB_CATEGORY_NOTIFICATION = "notification"
JOB_CATEGORY_MONITORING = "monitoring"
JOB_CATEGORY_CLEANUP = "cleanup"
JOB_CATEGORY_MIGRATION = "migration"
JOB_CATEGORY_VALIDATION = "validation"
JOB_CATEGORY_TRANSFORMATION = "transformation"

JOB_CATEGORIES = [
    JOB_CATEGORY_DATA_PROCESSING,
    JOB_CATEGORY_ANALYTICS,
    JOB_CATEGORY_MAINTENANCE,
    JOB_CATEGORY_REPORTING,
    JOB_CATEGORY_BACKUP,
    JOB_CATEGORY_SYNC,
    JOB_CATEGORY_NOTIFICATION,
    JOB_CATEGORY_MONITORING,
    JOB_CATEGORY_CLEANUP,
    JOB_CATEGORY_MIGRATION,
    JOB_CATEGORY_VALIDATION,
    JOB_CATEGORY_TRANSFORMATION
]

# Job Statuses
JOB_STATUS_PENDING = "pending"
JOB_STATUS_RUNNING = "running"
JOB_STATUS_COMPLETED = "completed"
JOB_STATUS_FAILED = "failed"
JOB_STATUS_CANCELLED = "cancelled"
JOB_STATUS_PAUSED = "paused"
JOB_STATUS_RETRYING = "retrying"
JOB_STATUS_TIMEOUT = "timeout"
JOB_STATUS_SKIPPED = "skipped"

JOB_STATUSES = [
    JOB_STATUS_PENDING,
    JOB_STATUS_RUNNING,
    JOB_STATUS_COMPLETED,
    JOB_STATUS_FAILED,
    JOB_STATUS_CANCELLED,
    JOB_STATUS_PAUSED,
    JOB_STATUS_RETRYING,
    JOB_STATUS_TIMEOUT,
    JOB_STATUS_SKIPPED
]

# Job Priorities
JOB_PRIORITY_LOW = "low"
JOB_PRIORITY_MEDIUM = "medium"
JOB_PRIORITY_HIGH = "high"
JOB_PRIORITY_CRITICAL = "critical"
JOB_PRIORITY_URGENT = "urgent"

JOB_PRIORITIES = [
    JOB_PRIORITY_LOW,
    JOB_PRIORITY_MEDIUM,
    JOB_PRIORITY_HIGH,
    JOB_PRIORITY_CRITICAL,
    JOB_PRIORITY_URGENT
]

# Schedule Types
SCHEDULE_TYPE_CRON = "cron"
SCHEDULE_TYPE_INTERVAL = "interval"
SCHEDULE_TYPE_ONCE = "once"
SCHEDULE_TYPE_MANUAL = "manual"
SCHEDULE_TYPE_IMMEDIATE = "immediate"

SCHEDULE_TYPES = [
    SCHEDULE_TYPE_CRON,
    SCHEDULE_TYPE_INTERVAL,
    SCHEDULE_TYPE_ONCE,
    SCHEDULE_TYPE_MANUAL,
    SCHEDULE_TYPE_IMMEDIATE
]

# Queue Types
QUEUE_TYPE_FIFO = "fifo"
QUEUE_TYPE_PRIORITY = "priority"
QUEUE_TYPE_DELAYED = "delayed"
QUEUE_TYPE_BATCH = "batch"
QUEUE_TYPE_ROUND_ROBIN = "round_robin"

QUEUE_TYPES = [
    QUEUE_TYPE_FIFO,
    QUEUE_TYPE_PRIORITY,
    QUEUE_TYPE_DELAYED,
    QUEUE_TYPE_BATCH,
    QUEUE_TYPE_ROUND_ROBIN
]

# Worker Types
WORKER_TYPE_LOCAL = "local"
WORKER_TYPE_REMOTE = "remote"
WORKER_TYPE_CONTAINER = "container"
WORKER_TYPE_SERVERLESS = "serverless"
WORKER_TYPE_KUBERNETES = "kubernetes"
WORKER_TYPE_DOCKER = "docker"

WORKER_TYPES = [
    WORKER_TYPE_LOCAL,
    WORKER_TYPE_REMOTE,
    WORKER_TYPE_CONTAINER,
    WORKER_TYPE_SERVERLESS,
    WORKER_TYPE_KUBERNETES,
    WORKER_TYPE_DOCKER
]

# Worker Statuses
WORKER_STATUS_IDLE = "idle"
WORKER_STATUS_BUSY = "busy"
WORKER_STATUS_OFFLINE = "offline"
WORKER_STATUS_ERROR = "error"
WORKER_STATUS_MAINTENANCE = "maintenance"

WORKER_STATUSES = [
    WORKER_STATUS_IDLE,
    WORKER_STATUS_BUSY,
    WORKER_STATUS_OFFLINE,
    WORKER_STATUS_ERROR,
    WORKER_STATUS_MAINTENANCE
]

# Execution Types
EXECUTION_TYPE_SCHEDULED = "scheduled"
EXECUTION_TYPE_MANUAL = "manual"
EXECUTION_TYPE_RETRY = "retry"
EXECUTION_TYPE_DEPENDENCY = "dependency"
EXECUTION_TYPE_TRIGGER = "trigger"

EXECUTION_TYPES = [
    EXECUTION_TYPE_SCHEDULED,
    EXECUTION_TYPE_MANUAL,
    EXECUTION_TYPE_RETRY,
    EXECUTION_TYPE_DEPENDENCY,
    EXECUTION_TYPE_TRIGGER
]

# Trigger Sources
TRIGGER_SOURCE_SCHEDULER = "scheduler"
TRIGGER_SOURCE_API = "api"
TRIGGER_SOURCE_DEPENDENCY = "dependency"
TRIGGER_SOURCE_MANUAL = "manual"
TRIGGER_SOURCE_WEBHOOK = "webhook"
TRIGGER_SOURCE_EVENT = "event"

TRIGGER_SOURCES = [
    TRIGGER_SOURCE_SCHEDULER,
    TRIGGER_SOURCE_API,
    TRIGGER_SOURCE_DEPENDENCY,
    TRIGGER_SOURCE_MANUAL,
    TRIGGER_SOURCE_WEBHOOK,
    TRIGGER_SOURCE_EVENT
]

# Dependency Types
DEPENDENCY_TYPE_SUCCESS = "success"
DEPENDENCY_TYPE_COMPLETION = "completion"
DEPENDENCY_TYPE_FAILURE = "failure"
DEPENDENCY_TYPE_ALWAYS = "always"
DEPENDENCY_TYPE_CONDITIONAL = "conditional"

DEPENDENCY_TYPES = [
    DEPENDENCY_TYPE_SUCCESS,
    DEPENDENCY_TYPE_COMPLETION,
    DEPENDENCY_TYPE_FAILURE,
    DEPENDENCY_TYPE_ALWAYS,
    DEPENDENCY_TYPE_CONDITIONAL
]

# Notification Types
NOTIFICATION_TYPE_SUCCESS = "success"
NOTIFICATION_TYPE_FAILURE = "failure"
NOTIFICATION_TYPE_WARNING = "warning"
NOTIFICATION_TYPE_INFO = "info"
NOTIFICATION_TYPE_ERROR = "error"

NOTIFICATION_TYPES = [
    NOTIFICATION_TYPE_SUCCESS,
    NOTIFICATION_TYPE_FAILURE,
    NOTIFICATION_TYPE_WARNING,
    NOTIFICATION_TYPE_INFO,
    NOTIFICATION_TYPE_ERROR
]

# Notification Events
NOTIFICATION_EVENT_JOB_STARTED = "job_started"
NOTIFICATION_EVENT_JOB_COMPLETED = "job_completed"
NOTIFICATION_EVENT_JOB_FAILED = "job_failed"
NOTIFICATION_EVENT_JOB_CANCELLED = "job_cancelled"
NOTIFICATION_EVENT_JOB_TIMEOUT = "job_timeout"
NOTIFICATION_EVENT_JOB_RETRY = "job_retry"
NOTIFICATION_EVENT_QUEUE_FULL = "queue_full"
NOTIFICATION_EVENT_WORKER_DOWN = "worker_down"

NOTIFICATION_EVENTS = [
    NOTIFICATION_EVENT_JOB_STARTED,
    NOTIFICATION_EVENT_JOB_COMPLETED,
    NOTIFICATION_EVENT_JOB_FAILED,
    NOTIFICATION_EVENT_JOB_CANCELLED,
    NOTIFICATION_EVENT_JOB_TIMEOUT,
    NOTIFICATION_EVENT_JOB_RETRY,
    NOTIFICATION_EVENT_QUEUE_FULL,
    NOTIFICATION_EVENT_WORKER_DOWN
]

# Recipient Types
RECIPIENT_TYPE_EMAIL = "email"
RECIPIENT_TYPE_WEBHOOK = "webhook"
RECIPIENT_TYPE_SLACK = "slack"
RECIPIENT_TYPE_TEAMS = "teams"
RECIPIENT_TYPE_SMS = "sms"
RECIPIENT_TYPE_PUSH = "push"

RECIPIENT_TYPES = [
    RECIPIENT_TYPE_EMAIL,
    RECIPIENT_TYPE_WEBHOOK,
    RECIPIENT_TYPE_SLACK,
    RECIPIENT_TYPE_TEAMS,
    RECIPIENT_TYPE_SMS,
    RECIPIENT_TYPE_PUSH
]

# Notification Statuses
NOTIFICATION_STATUS_PENDING = "pending"
NOTIFICATION_STATUS_SENT = "sent"
NOTIFICATION_STATUS_FAILED = "failed"
NOTIFICATION_STATUS_DELIVERED = "delivered"
NOTIFICATION_STATUS_READ = "read"

NOTIFICATION_STATUSES = [
    NOTIFICATION_STATUS_PENDING,
    NOTIFICATION_STATUS_SENT,
    NOTIFICATION_STATUS_FAILED,
    NOTIFICATION_STATUS_DELIVERED,
    NOTIFICATION_STATUS_READ
]

# Alert Types
ALERT_TYPE_THRESHOLD = "threshold"
ALERT_TYPE_ANOMALY = "anomaly"
ALERT_TYPE_PATTERN = "pattern"
ALERT_TYPE_CUSTOM = "custom"
ALERT_TYPE_PERFORMANCE = "performance"
ALERT_TYPE_AVAILABILITY = "availability"

ALERT_TYPES = [
    ALERT_TYPE_THRESHOLD,
    ALERT_TYPE_ANOMALY,
    ALERT_TYPE_PATTERN,
    ALERT_TYPE_CUSTOM,
    ALERT_TYPE_PERFORMANCE,
    ALERT_TYPE_AVAILABILITY
]

# Alert Severities
ALERT_SEVERITY_LOW = "low"
ALERT_SEVERITY_MEDIUM = "medium"
ALERT_SEVERITY_HIGH = "high"
ALERT_SEVERITY_CRITICAL = "critical"

ALERT_SEVERITIES = [
    ALERT_SEVERITY_LOW,
    ALERT_SEVERITY_MEDIUM,
    ALERT_SEVERITY_HIGH,
    ALERT_SEVERITY_CRITICAL
]

# Log Levels
LOG_LEVEL_DEBUG = "DEBUG"
LOG_LEVEL_INFO = "INFO"
LOG_LEVEL_WARNING = "WARNING"
LOG_LEVEL_ERROR = "ERROR"
LOG_LEVEL_CRITICAL = "CRITICAL"

LOG_LEVELS = [
    LOG_LEVEL_DEBUG,
    LOG_LEVEL_INFO,
    LOG_LEVEL_WARNING,
    LOG_LEVEL_ERROR,
    LOG_LEVEL_CRITICAL
]

# Log Sources
LOG_SOURCE_APPLICATION = "application"
LOG_SOURCE_SYSTEM = "system"
LOG_SOURCE_WORKER = "worker"
LOG_SOURCE_SCHEDULER = "scheduler"
LOG_SOURCE_QUEUE = "queue"

LOG_SOURCES = [
    LOG_SOURCE_APPLICATION,
    LOG_SOURCE_SYSTEM,
    LOG_SOURCE_WORKER,
    LOG_SOURCE_SCHEDULER,
    LOG_SOURCE_QUEUE
]

# Metric Types
METRIC_TYPE_COUNTER = "counter"
METRIC_TYPE_GAUGE = "gauge"
METRIC_TYPE_HISTOGRAM = "histogram"
METRIC_TYPE_TIMER = "timer"
METRIC_TYPE_SUMMARY = "summary"

METRIC_TYPES = [
    METRIC_TYPE_COUNTER,
    METRIC_TYPE_GAUGE,
    METRIC_TYPE_HISTOGRAM,
    METRIC_TYPE_TIMER,
    METRIC_TYPE_SUMMARY
]

# Configuration Types
CONFIG_TYPE_GLOBAL = "global"
CONFIG_TYPE_QUEUE = "queue"
CONFIG_TYPE_WORKER = "worker"
CONFIG_TYPE_JOB_TYPE = "job_type"
CONFIG_TYPE_ENVIRONMENT = "environment"

CONFIG_TYPES = [
    CONFIG_TYPE_GLOBAL,
    CONFIG_TYPE_QUEUE,
    CONFIG_TYPE_WORKER,
    CONFIG_TYPE_JOB_TYPE,
    CONFIG_TYPE_ENVIRONMENT
]

# Default values
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100
DEFAULT_JOB_STATUS = JOB_STATUS_PENDING
DEFAULT_JOB_PRIORITY = JOB_PRIORITY_MEDIUM
DEFAULT_JOB_TYPE = JOB_TYPE_IMMEDIATE
DEFAULT_SCHEDULE_TYPE = SCHEDULE_TYPE_MANUAL
DEFAULT_QUEUE_TYPE = QUEUE_TYPE_FIFO
DEFAULT_WORKER_TYPE = WORKER_TYPE_LOCAL
DEFAULT_EXECUTION_TYPE = EXECUTION_TYPE_MANUAL
DEFAULT_NOTIFICATION_TYPE = NOTIFICATION_TYPE_INFO
DEFAULT_ALERT_SEVERITY = ALERT_SEVERITY_MEDIUM
DEFAULT_LOG_LEVEL = LOG_LEVEL_INFO

# Validation limits
MAX_JOB_NAME_LENGTH = 255
MAX_JOB_DESCRIPTION_LENGTH = 2000
MAX_FUNCTION_NAME_LENGTH = 255
MAX_MODULE_PATH_LENGTH = 500
MAX_QUEUE_NAME_LENGTH = 255
MAX_WORKER_NAME_LENGTH = 255
MAX_SCHEDULE_NAME_LENGTH = 255
MAX_TEMPLATE_NAME_LENGTH = 255
MAX_ALERT_NAME_LENGTH = 255
MAX_CONFIG_NAME_LENGTH = 255

# Timeout settings (in seconds)
DEFAULT_JOB_TIMEOUT = 3600  # 1 hour
MAX_JOB_TIMEOUT = 86400  # 24 hours
DEFAULT_EXECUTION_TIMEOUT = 1800  # 30 minutes
DEFAULT_HEARTBEAT_TIMEOUT = 300  # 5 minutes
DEFAULT_NOTIFICATION_TIMEOUT = 60  # 1 minute

# Retry settings
DEFAULT_MAX_RETRIES = 3
MAX_RETRY_ATTEMPTS = 10
DEFAULT_RETRY_DELAY_SECONDS = 300  # 5 minutes
MAX_RETRY_DELAY_SECONDS = 3600  # 1 hour

# Resource limits
DEFAULT_MEMORY_LIMIT_MB = 512
MAX_MEMORY_LIMIT_MB = 8192  # 8GB
DEFAULT_CPU_LIMIT_PERCENT = 50.0
MAX_CPU_LIMIT_PERCENT = 100.0

# Queue settings
DEFAULT_MAX_CONCURRENT_JOBS = 5
MAX_CONCURRENT_JOBS = 100
DEFAULT_MAX_QUEUE_SIZE = 1000
MAX_QUEUE_SIZE = 10000

# Worker settings
DEFAULT_MAX_WORKER_JOBS = 1
MAX_WORKER_JOBS = 50
DEFAULT_WORKER_HEARTBEAT_INTERVAL = 60  # 1 minute
MAX_WORKER_HEARTBEAT_INTERVAL = 300  # 5 minutes

# Notification settings
DEFAULT_MAX_NOTIFICATION_ATTEMPTS = 3
MAX_NOTIFICATION_ATTEMPTS = 10
DEFAULT_NOTIFICATION_RETRY_DELAY = 60  # 1 minute

# Alert settings
DEFAULT_ALERT_CHECK_INTERVAL = 300  # 5 minutes
MAX_ALERT_TRIGGERS_PER_HOUR = 100

# Metric settings
DEFAULT_METRIC_RETENTION_DAYS = 30
MAX_METRIC_RETENTION_DAYS = 365

# Log settings
DEFAULT_LOG_RETENTION_DAYS = 7
MAX_LOG_RETENTION_DAYS = 90
MAX_LOG_MESSAGE_LENGTH = 5000

# System limits
MAX_JOBS_PER_USER = 1000
MAX_QUEUES_PER_USER = 50
MAX_WORKERS_PER_USER = 20
MAX_SCHEDULES_PER_USER = 100
MAX_TEMPLATES_PER_USER = 50
MAX_ALERTS_PER_USER = 100

# Performance settings
JOB_SEARCH_LIMIT = 1000
BULK_OPERATION_LIMIT = 100
EXPORT_LIMIT = 10000
METRICS_BATCH_SIZE = 1000
LOG_BATCH_SIZE = 500

# Cron expressions for common schedules
CRON_EVERY_MINUTE = "* * * * *"
CRON_EVERY_HOUR = "0 * * * *"
CRON_EVERY_DAY = "0 0 * * *"
CRON_EVERY_WEEK = "0 0 * * 0"
CRON_EVERY_MONTH = "0 0 1 * *"

COMMON_CRON_EXPRESSIONS = {
    "every_minute": CRON_EVERY_MINUTE,
    "every_hour": CRON_EVERY_HOUR,
    "every_day": CRON_EVERY_DAY,
    "every_week": CRON_EVERY_WEEK,
    "every_month": CRON_EVERY_MONTH
}

# Interval presets (in seconds)
INTERVAL_1_MINUTE = 60
INTERVAL_5_MINUTES = 300
INTERVAL_15_MINUTES = 900
INTERVAL_30_MINUTES = 1800
INTERVAL_1_HOUR = 3600
INTERVAL_6_HOURS = 21600
INTERVAL_12_HOURS = 43200
INTERVAL_24_HOURS = 86400

COMMON_INTERVALS = {
    "1_minute": INTERVAL_1_MINUTE,
    "5_minutes": INTERVAL_5_MINUTES,
    "15_minutes": INTERVAL_15_MINUTES,
    "30_minutes": INTERVAL_30_MINUTES,
    "1_hour": INTERVAL_1_HOUR,
    "6_hours": INTERVAL_6_HOURS,
    "12_hours": INTERVAL_12_HOURS,
    "24_hours": INTERVAL_24_HOURS
}

# Environment settings
ENVIRONMENT_DEVELOPMENT = "development"
ENVIRONMENT_STAGING = "staging"
ENVIRONMENT_PRODUCTION = "production"
ENVIRONMENT_TEST = "test"

ENVIRONMENTS = [
    ENVIRONMENT_DEVELOPMENT,
    ENVIRONMENT_STAGING,
    ENVIRONMENT_PRODUCTION,
    ENVIRONMENT_TEST
]

# Module metadata
JOBS_MODULE_VERSION = "1.0.0"
JOBS_MODULE_NAME = "jobs"
JOBS_API_VERSION = "v1"
JOBS_BUILD_DATE = "2024-01-01"

# Feature flags
FEATURE_JOB_SCHEDULING = True
FEATURE_JOB_DEPENDENCIES = True
FEATURE_JOB_NOTIFICATIONS = True
FEATURE_JOB_METRICS = True
FEATURE_JOB_ALERTS = True
FEATURE_JOB_TEMPLATES = True
FEATURE_WORKER_MANAGEMENT = True
FEATURE_QUEUE_MANAGEMENT = True
FEATURE_BULK_OPERATIONS = True
FEATURE_JOB_EXPORT = True

# Security settings
ENABLE_JOB_ENCRYPTION = False
ENABLE_AUDIT_LOGGING = True
ENABLE_ACCESS_CONTROL = True
ENABLE_RATE_LIMITING = True

# Rate limiting
RATE_LIMIT_JOB_CREATION = 100  # per hour
RATE_LIMIT_JOB_EXECUTION = 1000  # per hour
RATE_LIMIT_NOTIFICATION_SENDING = 500  # per hour

# Cache settings
CACHE_TTL_JOB_DATA = 300  # 5 minutes
CACHE_TTL_QUEUE_DATA = 180  # 3 minutes
CACHE_TTL_WORKER_DATA = 120  # 2 minutes
CACHE_TTL_METRICS_DATA = 600  # 10 minutes

# File storage settings
JOB_LOG_PATH = "/logs/jobs"
JOB_EXPORT_PATH = "/exports/jobs"
JOB_BACKUP_PATH = "/backups/jobs"
JOB_TEMP_PATH = "/tmp/jobs"

# Legacy compatibility
LEGACY_ENDPOINT_SUPPORT = True
LEGACY_FORMAT_CONVERSION = True
BACKWARD_COMPATIBILITY_MODE = True