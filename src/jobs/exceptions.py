# jobs exceptions - module-specific errors for jobs
from fastapi import HTTPException, status

class JobNotFoundError(HTTPException):
    def __init__(self, job_id: str = None):
        detail = f"Job '{job_id}' not found" if job_id else "Job not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class JobAlreadyExistsError(HTTPException):
    def __init__(self, job_id: str = None):
        detail = f"Job '{job_id}' already exists" if job_id else "Job already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidJobError(HTTPException):
    def __init__(self, detail: str = "Invalid job"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobValidationError(HTTPException):
    def __init__(self, detail: str = "Job validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create job"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update job"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete job"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobExecutionError(HTTPException):
    def __init__(self, detail: str = "Job execution failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobTimeoutError(HTTPException):
    def __init__(self, detail: str = "Job execution timed out"):
        super().__init__(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=detail)

class JobDependencyError(HTTPException):
    def __init__(self, detail: str = "Job dependency error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobQueueNotFoundError(HTTPException):
    def __init__(self, queue_name: str = None):
        detail = f"Job queue '{queue_name}' not found" if queue_name else "Job queue not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class JobQueueError(HTTPException):
    def __init__(self, detail: str = "Job queue error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobQueueFullError(HTTPException):
    def __init__(self, detail: str = "Job queue is full"):
        super().__init__(status_code=status.HTTP_429_TOO_MANY_REQUESTS, detail=detail)

class JobTemplateNotFoundError(HTTPException):
    def __init__(self, template_id: str = None):
        detail = f"Job template '{template_id}' not found" if template_id else "Job template not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class JobTemplateError(HTTPException):
    def __init__(self, detail: str = "Job template error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobWorkerNotFoundError(HTTPException):
    def __init__(self, worker_id: str = None):
        detail = f"Job worker '{worker_id}' not found" if worker_id else "Job worker not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class JobWorkerError(HTTPException):
    def __init__(self, detail: str = "Job worker error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobWorkerUnavailableError(HTTPException):
    def __init__(self, detail: str = "Job worker unavailable"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class JobScheduleNotFoundError(HTTPException):
    def __init__(self, schedule_id: str = None):
        detail = f"Job schedule '{schedule_id}' not found" if schedule_id else "Job schedule not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class JobScheduleError(HTTPException):
    def __init__(self, detail: str = "Job schedule error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidCronExpressionError(HTTPException):
    def __init__(self, expression: str = None):
        detail = f"Invalid cron expression: {expression}" if expression else "Invalid cron expression"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobNotificationError(HTTPException):
    def __init__(self, detail: str = "Job notification error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobAlertError(HTTPException):
    def __init__(self, detail: str = "Job alert error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobMetricsError(HTTPException):
    def __init__(self, detail: str = "Job metrics error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobLoggingError(HTTPException):
    def __init__(self, detail: str = "Job logging error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobConfigurationError(HTTPException):
    def __init__(self, detail: str = "Job configuration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobPermissionError(HTTPException):
    def __init__(self, detail: str = "Insufficient permissions for job operation"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class JobAccessDeniedError(HTTPException):
    def __init__(self, job_id: str = None):
        detail = f"Access denied to job '{job_id}'" if job_id else "Job access denied"
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class JobResourceLimitError(HTTPException):
    def __init__(self, detail: str = "Job resource limit exceeded"):
        super().__init__(status_code=status.HTTP_429_TOO_MANY_REQUESTS, detail=detail)

class JobStatusTransitionError(HTTPException):
    def __init__(self, detail: str = "Invalid job status transition"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobPriorityError(HTTPException):
    def __init__(self, detail: str = "Invalid job priority"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobTypeError(HTTPException):
    def __init__(self, detail: str = "Invalid job type"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobCategoryError(HTTPException):
    def __init__(self, detail: str = "Invalid job category"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobSearchError(HTTPException):
    def __init__(self, detail: str = "Job search error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobBulkOperationError(HTTPException):
    def __init__(self, detail: str = "Job bulk operation error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobStatisticsError(HTTPException):
    def __init__(self, detail: str = "Failed to calculate job statistics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobMonitoringError(HTTPException):
    def __init__(self, detail: str = "Job monitoring error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobSchedulerError(HTTPException):
    def __init__(self, detail: str = "Job scheduler error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobRetryLimitExceededError(HTTPException):
    def __init__(self, detail: str = "Job retry limit exceeded"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobCancellationError(HTTPException):
    def __init__(self, detail: str = "Job cancellation error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobImportError(HTTPException):
    def __init__(self, detail: str = "Job function import error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobParameterError(HTTPException):
    def __init__(self, detail: str = "Job parameter error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class JobEnvironmentError(HTTPException):
    def __init__(self, detail: str = "Job environment error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobDataIntegrityError(HTTPException):
    def __init__(self, detail: str = "Job data integrity error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobConcurrencyError(HTTPException):
    def __init__(self, detail: str = "Job concurrency error"):
        super().__init__(status_code=status.HTTP_409_CONFLICT, detail=detail)

class JobLockError(HTTPException):
    def __init__(self, detail: str = "Job lock error"):
        super().__init__(status_code=status.HTTP_409_CONFLICT, detail=detail)

class JobMaintenanceError(HTTPException):
    def __init__(self, detail: str = "Job system is under maintenance"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class JobCapacityError(HTTPException):
    def __init__(self, detail: str = "Job system capacity exceeded"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class JobNetworkError(HTTPException):
    def __init__(self, detail: str = "Job network error"):
        super().__init__(status_code=status.HTTP_502_BAD_GATEWAY, detail=detail)

class JobStorageError(HTTPException):
    def __init__(self, detail: str = "Job storage error"):
        super().__init__(status_code=status.HTTP_507_INSUFFICIENT_STORAGE, detail=detail)

class LegacyJobError(HTTPException):
    def __init__(self, detail: str = "Legacy job operation failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class JobFeatureNotAvailableError(HTTPException):
    def __init__(self, feature: str):
        super().__init__(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail=f"Job management feature '{feature}' is not available"
        )