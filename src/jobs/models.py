# jobs models - SQLAlchemy models for jobs module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Job Management Models
class Job(Base):
    __tablename__ = 'jobs'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(String(100), unique=True, nullable=False, index=True)
    job_name = Column(String(255), nullable=False)
    job_description = Column(Text, nullable=True)
    job_type = Column(String(50), nullable=False)  # scheduled, immediate, recurring, background
    job_category = Column(String(100), nullable=True)  # data_processing, analytics, maintenance

    # Job execution details
    function_name = Column(String(255), nullable=False)
    module_path = Column(String(500), nullable=True)
    parameters = Column(JSONB, nullable=True)
    environment_variables = Column(JSONB, nullable=True)

    # Job status and progress
    status = Column(String(50), nullable=False, default='pending')  # pending, running, completed, failed, cancelled
    priority = Column(String(50), nullable=False, default='medium')  # low, medium, high, critical
    progress_percentage = Column(Float, default=0.0)
    current_step = Column(String(255), nullable=True)
    total_steps = Column(Integer, nullable=True)

    # Scheduling information
    schedule_type = Column(String(50), nullable=True)  # cron, interval, once, manual
    cron_expression = Column(String(100), nullable=True)
    interval_seconds = Column(Integer, nullable=True)
    scheduled_at = Column(DateTime, nullable=True)
    next_run_at = Column(DateTime, nullable=True)
    last_run_at = Column(DateTime, nullable=True)

    # Execution tracking
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    execution_time_seconds = Column(Float, nullable=True)
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)

    # Job configuration
    timeout_seconds = Column(Integer, nullable=True)
    memory_limit_mb = Column(Integer, nullable=True)
    cpu_limit_percent = Column(Float, nullable=True)
    is_active = Column(Boolean, default=True)
    is_recurring = Column(Boolean, default=False)

    # Dependencies and relationships
    parent_job_id = Column(String(100), nullable=True)
    depends_on_jobs = Column(ARRAY(String), nullable=True)
    tags = Column(ARRAY(String), nullable=True)

    # Results and logging
    result_data = Column(JSONB, nullable=True)
    error_message = Column(Text, nullable=True)
    error_details = Column(JSONB, nullable=True)
    log_data = Column(JSONB, nullable=True)

    # Metadata
    created_by = Column(String(255), nullable=False)
    updated_by = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Job Queues
class job_queues(Base):
    __tablename__ = 'job_queues'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    queue_name = Column(String(255), nullable=False, unique=True)
    queue_description = Column(Text, nullable=True)
    queue_type = Column(String(50), nullable=False)  # fifo, priority, delayed, batch

    # Queue configuration
    max_concurrent_jobs = Column(Integer, default=5)
    max_queue_size = Column(Integer, default=1000)
    default_priority = Column(String(50), default='medium')
    retry_policy = Column(JSONB, nullable=True)

    # Queue status
    is_active = Column(Boolean, default=True)
    is_paused = Column(Boolean, default=False)
    current_job_count = Column(Integer, default=0)
    pending_job_count = Column(Integer, default=0)

    # Queue metrics
    total_jobs_processed = Column(Integer, default=0)
    total_jobs_failed = Column(Integer, default=0)
    average_execution_time = Column(Float, nullable=True)

    # Queue settings
    worker_settings = Column(JSONB, nullable=True)
    notification_settings = Column(JSONB, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Job Executions (History)
class job_executions(Base):
    __tablename__ = 'job_executions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    execution_id = Column(String(100), unique=True, nullable=False, index=True)
    job_id = Column(String(100), nullable=False, index=True)
    queue_name = Column(String(255), nullable=True)

    # Execution details
    execution_type = Column(String(50), nullable=False)  # scheduled, manual, retry, dependency
    trigger_source = Column(String(100), nullable=True)  # scheduler, api, dependency, manual
    worker_id = Column(String(100), nullable=True)
    worker_node = Column(String(255), nullable=True)

    # Execution status
    status = Column(String(50), nullable=False)  # pending, running, completed, failed, cancelled
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    execution_time_seconds = Column(Float, nullable=True)

    # Execution results
    exit_code = Column(Integer, nullable=True)
    result_data = Column(JSONB, nullable=True)
    error_message = Column(Text, nullable=True)
    error_details = Column(JSONB, nullable=True)

    # Resource usage
    memory_used_mb = Column(Float, nullable=True)
    cpu_used_percent = Column(Float, nullable=True)
    disk_io_mb = Column(Float, nullable=True)
    network_io_mb = Column(Float, nullable=True)

    # Execution logs
    stdout_log = Column(Text, nullable=True)
    stderr_log = Column(Text, nullable=True)
    execution_log = Column(JSONB, nullable=True)

    created_at = Column(DateTime, default=datetime.now)

# Job Templates
class job_templates(Base):
    __tablename__ = 'job_templates'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_id = Column(String(100), unique=True, nullable=False, index=True)
    template_name = Column(String(255), nullable=False)
    template_description = Column(Text, nullable=True)
    template_category = Column(String(100), nullable=True)

    # Template configuration
    job_type = Column(String(50), nullable=False)
    function_name = Column(String(255), nullable=False)
    module_path = Column(String(500), nullable=True)
    default_parameters = Column(JSONB, nullable=True)
    parameter_schema = Column(JSONB, nullable=True)  # JSON schema for validation

    # Default settings
    default_priority = Column(String(50), default='medium')
    default_timeout_seconds = Column(Integer, nullable=True)
    default_max_retries = Column(Integer, default=3)
    default_schedule_type = Column(String(50), nullable=True)
    default_queue_name = Column(String(255), nullable=True)

    # Template metadata
    version = Column(String(50), nullable=True)
    is_active = Column(Boolean, default=True)
    is_public = Column(Boolean, default=False)
    usage_count = Column(Integer, default=0)

    # Template settings
    required_permissions = Column(ARRAY(String), nullable=True)
    environment_requirements = Column(JSONB, nullable=True)
    resource_requirements = Column(JSONB, nullable=True)

    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Job Dependencies
class job_dependencies(Base):
    __tablename__ = 'job_dependencies'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(String(100), nullable=False, index=True)
    depends_on_job_id = Column(String(100), nullable=False, index=True)
    dependency_type = Column(String(50), nullable=False)  # success, completion, failure, always
    dependency_condition = Column(JSONB, nullable=True)  # Additional conditions

    # Dependency status
    is_satisfied = Column(Boolean, default=False)
    satisfied_at = Column(DateTime, nullable=True)

    # Dependency metadata
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Job Schedules
class job_schedules(Base):
    __tablename__ = 'job_schedules'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    schedule_id = Column(String(100), unique=True, nullable=False, index=True)
    schedule_name = Column(String(255), nullable=False)
    schedule_description = Column(Text, nullable=True)

    # Schedule configuration
    schedule_type = Column(String(50), nullable=False)  # cron, interval, once
    cron_expression = Column(String(100), nullable=True)
    interval_seconds = Column(Integer, nullable=True)
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    timezone = Column(String(50), default='UTC')

    # Schedule status
    is_active = Column(Boolean, default=True)
    is_paused = Column(Boolean, default=False)
    next_run_at = Column(DateTime, nullable=True)
    last_run_at = Column(DateTime, nullable=True)

    # Schedule metadata
    total_runs = Column(Integer, default=0)
    successful_runs = Column(Integer, default=0)
    failed_runs = Column(Integer, default=0)

    # Associated jobs
    job_template_id = Column(String(100), nullable=True)
    job_parameters = Column(JSONB, nullable=True)

    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Job Workers
class job_workers(Base):
    __tablename__ = 'job_workers'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    worker_id = Column(String(100), unique=True, nullable=False, index=True)
    worker_name = Column(String(255), nullable=False)
    worker_type = Column(String(50), nullable=False)  # local, remote, container, serverless

    # Worker configuration
    host_name = Column(String(255), nullable=True)
    ip_address = Column(String(45), nullable=True)
    port = Column(Integer, nullable=True)
    max_concurrent_jobs = Column(Integer, default=1)
    supported_job_types = Column(ARRAY(String), nullable=True)
    supported_queues = Column(ARRAY(String), nullable=True)

    # Worker status
    status = Column(String(50), nullable=False, default='idle')  # idle, busy, offline, error
    is_active = Column(Boolean, default=True)
    is_healthy = Column(Boolean, default=True)
    last_heartbeat = Column(DateTime, nullable=True)

    # Worker metrics
    current_job_count = Column(Integer, default=0)
    total_jobs_processed = Column(Integer, default=0)
    total_jobs_failed = Column(Integer, default=0)
    average_execution_time = Column(Float, nullable=True)

    # Resource information
    cpu_cores = Column(Integer, nullable=True)
    memory_total_mb = Column(Integer, nullable=True)
    memory_available_mb = Column(Integer, nullable=True)
    disk_space_gb = Column(Float, nullable=True)

    # Worker metadata
    version = Column(String(50), nullable=True)
    capabilities = Column(JSONB, nullable=True)
    configuration = Column(JSONB, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Job Notifications
class job_notifications(Base):
    __tablename__ = 'job_notifications'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    notification_id = Column(String(100), unique=True, nullable=False, index=True)
    job_id = Column(String(100), nullable=False, index=True)

    # Notification details
    notification_type = Column(String(50), nullable=False)  # success, failure, warning, info
    notification_event = Column(String(100), nullable=False)  # job_started, job_completed, job_failed
    message = Column(Text, nullable=False)
    details = Column(JSONB, nullable=True)

    # Recipients
    recipient_type = Column(String(50), nullable=False)  # email, webhook, slack, teams
    recipient_address = Column(String(500), nullable=False)

    # Notification status
    status = Column(String(50), nullable=False, default='pending')  # pending, sent, failed
    sent_at = Column(DateTime, nullable=True)
    delivery_attempts = Column(Integer, default=0)
    max_attempts = Column(Integer, default=3)

    # Notification metadata
    priority = Column(String(50), default='medium')
    template_used = Column(String(255), nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Job Metrics
class job_metrics(Base):
    __tablename__ = 'job_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    metric_id = Column(String(100), unique=True, nullable=False, index=True)
    job_id = Column(String(100), nullable=False, index=True)
    execution_id = Column(String(100), nullable=True, index=True)

    # Metric details
    metric_name = Column(String(255), nullable=False)
    metric_type = Column(String(50), nullable=False)  # counter, gauge, histogram, timer
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(50), nullable=True)

    # Metric metadata
    tags = Column(JSONB, nullable=True)
    labels = Column(JSONB, nullable=True)
    dimensions = Column(JSONB, nullable=True)

    # Timing information
    timestamp = Column(DateTime, nullable=False, default=datetime.now)
    created_at = Column(DateTime, default=datetime.now)

# Job Logs
class job_logs(Base):
    __tablename__ = 'job_logs'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    log_id = Column(String(100), unique=True, nullable=False, index=True)
    job_id = Column(String(100), nullable=False, index=True)
    execution_id = Column(String(100), nullable=True, index=True)

    # Log details
    log_level = Column(String(20), nullable=False)  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    log_source = Column(String(100), nullable=True)  # application, system, worker
    log_message = Column(Text, nullable=False)
    log_data = Column(JSONB, nullable=True)

    # Log metadata
    logger_name = Column(String(255), nullable=True)
    module_name = Column(String(255), nullable=True)
    function_name = Column(String(255), nullable=True)
    line_number = Column(Integer, nullable=True)

    # Timing information
    timestamp = Column(DateTime, nullable=False, default=datetime.now)
    created_at = Column(DateTime, default=datetime.now)

# Job Alerts
class job_alerts(Base):
    __tablename__ = 'job_alerts'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    alert_id = Column(String(100), unique=True, nullable=False, index=True)
    alert_name = Column(String(255), nullable=False)
    alert_description = Column(Text, nullable=True)

    # Alert configuration
    alert_type = Column(String(50), nullable=False)  # threshold, anomaly, pattern, custom
    alert_condition = Column(JSONB, nullable=False)  # Alert condition configuration
    alert_severity = Column(String(50), nullable=False)  # low, medium, high, critical

    # Alert scope
    job_filter = Column(JSONB, nullable=True)  # Filter criteria for jobs
    queue_filter = Column(JSONB, nullable=True)  # Filter criteria for queues
    worker_filter = Column(JSONB, nullable=True)  # Filter criteria for workers

    # Alert status
    is_active = Column(Boolean, default=True)
    is_triggered = Column(Boolean, default=False)
    last_triggered_at = Column(DateTime, nullable=True)
    trigger_count = Column(Integer, default=0)

    # Alert actions
    notification_channels = Column(JSONB, nullable=True)  # Notification configuration
    escalation_policy = Column(JSONB, nullable=True)  # Escalation configuration

    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Job Configurations
class job_configurations(Base):
    __tablename__ = 'job_configurations'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    config_id = Column(String(100), unique=True, nullable=False, index=True)
    config_name = Column(String(255), nullable=False)
    config_description = Column(Text, nullable=True)
    config_type = Column(String(50), nullable=False)  # global, queue, worker, job_type

    # Configuration data
    config_data = Column(JSONB, nullable=False)
    config_schema = Column(JSONB, nullable=True)  # JSON schema for validation

    # Configuration scope
    scope = Column(String(100), nullable=True)  # Which entity this config applies to
    environment = Column(String(50), nullable=True)  # dev, staging, prod

    # Configuration status
    is_active = Column(Boolean, default=True)
    version = Column(String(50), nullable=True)

    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Add indexes for performance
Index('idx_jobs_status_priority', Job.status, Job.priority)
Index('idx_jobs_type_category', Job.job_type, Job.job_category)
Index('idx_jobs_scheduled_next_run', Job.scheduled_at, Job.next_run_at)
Index('idx_jobs_created_date', Job.created_at.desc())
Index('idx_job_executions_job_status', job_executions.job_id, job_executions.status)
Index('idx_job_executions_started_date', job_executions.started_at.desc())
Index('idx_job_dependencies_job_satisfied', job_dependencies.job_id, job_dependencies.is_satisfied)
Index('idx_job_workers_status_active', job_workers.status, job_workers.is_active)
Index('idx_job_notifications_job_status', job_notifications.job_id, job_notifications.status)
Index('idx_job_metrics_job_timestamp', job_metrics.job_id, job_metrics.timestamp.desc())
Index('idx_job_logs_job_level_timestamp', job_logs.job_id, job_logs.log_level, job_logs.timestamp.desc())