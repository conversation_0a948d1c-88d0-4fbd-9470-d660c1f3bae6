# jobs schemas - Pydantic models for jobs module
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime

# Job Base Schemas
class JobBase(BaseModel):
    job_name: str = Field(..., min_length=1, max_length=255)
    job_description: Optional[str] = None
    job_type: str = Field(..., max_length=50)
    job_category: Optional[str] = Field(None, max_length=100)
    function_name: str = Field(..., max_length=255)
    module_path: Optional[str] = Field(None, max_length=500)
    parameters: Optional[Dict[str, Any]] = None
    environment_variables: Optional[Dict[str, Any]] = None
    priority: str = Field(default='medium', max_length=50)
    schedule_type: Optional[str] = Field(None, max_length=50)
    cron_expression: Optional[str] = Field(None, max_length=100)
    interval_seconds: Optional[int] = None
    scheduled_at: Optional[datetime] = None
    timeout_seconds: Optional[int] = None
    memory_limit_mb: Optional[int] = None
    cpu_limit_percent: Optional[float] = None
    max_retries: int = 3
    is_active: bool = True
    is_recurring: bool = False
    parent_job_id: Optional[str] = Field(None, max_length=100)
    depends_on_jobs: Optional[List[str]] = None
    tags: Optional[List[str]] = None

class JobCreate(JobBase):
    job_id: Optional[str] = Field(None, max_length=100)

class JobUpdate(BaseModel):
    job_name: Optional[str] = Field(None, min_length=1, max_length=255)
    job_description: Optional[str] = None
    job_type: Optional[str] = Field(None, max_length=50)
    job_category: Optional[str] = Field(None, max_length=100)
    function_name: Optional[str] = Field(None, max_length=255)
    module_path: Optional[str] = Field(None, max_length=500)
    parameters: Optional[Dict[str, Any]] = None
    environment_variables: Optional[Dict[str, Any]] = None
    priority: Optional[str] = Field(None, max_length=50)
    schedule_type: Optional[str] = Field(None, max_length=50)
    cron_expression: Optional[str] = Field(None, max_length=100)
    interval_seconds: Optional[int] = None
    scheduled_at: Optional[datetime] = None
    timeout_seconds: Optional[int] = None
    memory_limit_mb: Optional[int] = None
    cpu_limit_percent: Optional[float] = None
    max_retries: Optional[int] = None
    is_active: Optional[bool] = None
    is_recurring: Optional[bool] = None
    parent_job_id: Optional[str] = Field(None, max_length=100)
    depends_on_jobs: Optional[List[str]] = None
    tags: Optional[List[str]] = None

class JobResponse(JobBase):
    id: UUID
    job_id: str
    status: str
    progress_percentage: float
    current_step: Optional[str] = None
    total_steps: Optional[int] = None
    next_run_at: Optional[datetime] = None
    last_run_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time_seconds: Optional[float] = None
    retry_count: int
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    log_data: Optional[Dict[str, Any]] = None
    created_by: str
    updated_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Job Queue Schemas
class JobQueueBase(BaseModel):
    queue_name: str = Field(..., min_length=1, max_length=255)
    queue_description: Optional[str] = None
    queue_type: str = Field(..., max_length=50)
    max_concurrent_jobs: int = 5
    max_queue_size: int = 1000
    default_priority: str = Field(default='medium', max_length=50)
    retry_policy: Optional[Dict[str, Any]] = None
    is_active: bool = True
    is_paused: bool = False
    worker_settings: Optional[Dict[str, Any]] = None
    notification_settings: Optional[Dict[str, Any]] = None

class JobQueueCreate(JobQueueBase):
    pass

class JobQueueUpdate(BaseModel):
    queue_name: Optional[str] = Field(None, min_length=1, max_length=255)
    queue_description: Optional[str] = None
    queue_type: Optional[str] = Field(None, max_length=50)
    max_concurrent_jobs: Optional[int] = None
    max_queue_size: Optional[int] = None
    default_priority: Optional[str] = Field(None, max_length=50)
    retry_policy: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_paused: Optional[bool] = None
    worker_settings: Optional[Dict[str, Any]] = None
    notification_settings: Optional[Dict[str, Any]] = None

class JobQueueResponse(JobQueueBase):
    id: UUID
    current_job_count: int
    pending_job_count: int
    total_jobs_processed: int
    total_jobs_failed: int
    average_execution_time: Optional[float] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Job Execution Schemas
class JobExecutionBase(BaseModel):
    job_id: str = Field(..., max_length=100)
    queue_name: Optional[str] = Field(None, max_length=255)
    execution_type: str = Field(..., max_length=50)
    trigger_source: Optional[str] = Field(None, max_length=100)
    worker_id: Optional[str] = Field(None, max_length=100)
    worker_node: Optional[str] = Field(None, max_length=255)

class JobExecutionCreate(JobExecutionBase):
    execution_id: Optional[str] = Field(None, max_length=100)

class JobExecutionUpdate(BaseModel):
    status: Optional[str] = Field(None, max_length=50)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time_seconds: Optional[float] = None
    exit_code: Optional[int] = None
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    memory_used_mb: Optional[float] = None
    cpu_used_percent: Optional[float] = None
    disk_io_mb: Optional[float] = None
    network_io_mb: Optional[float] = None
    stdout_log: Optional[str] = None
    stderr_log: Optional[str] = None
    execution_log: Optional[Dict[str, Any]] = None

class JobExecutionResponse(JobExecutionBase):
    id: UUID
    execution_id: str
    status: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time_seconds: Optional[float] = None
    exit_code: Optional[int] = None
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    memory_used_mb: Optional[float] = None
    cpu_used_percent: Optional[float] = None
    disk_io_mb: Optional[float] = None
    network_io_mb: Optional[float] = None
    stdout_log: Optional[str] = None
    stderr_log: Optional[str] = None
    execution_log: Optional[Dict[str, Any]] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Job Template Schemas
class JobTemplateBase(BaseModel):
    template_name: str = Field(..., min_length=1, max_length=255)
    template_description: Optional[str] = None
    template_category: Optional[str] = Field(None, max_length=100)
    job_type: str = Field(..., max_length=50)
    function_name: str = Field(..., max_length=255)
    module_path: Optional[str] = Field(None, max_length=500)
    default_parameters: Optional[Dict[str, Any]] = None
    parameter_schema: Optional[Dict[str, Any]] = None
    default_priority: str = Field(default='medium', max_length=50)
    default_timeout_seconds: Optional[int] = None
    default_max_retries: int = 3
    default_schedule_type: Optional[str] = Field(None, max_length=50)
    default_queue_name: Optional[str] = Field(None, max_length=255)
    version: Optional[str] = Field(None, max_length=50)
    is_active: bool = True
    is_public: bool = False
    required_permissions: Optional[List[str]] = None
    environment_requirements: Optional[Dict[str, Any]] = None
    resource_requirements: Optional[Dict[str, Any]] = None

class JobTemplateCreate(JobTemplateBase):
    template_id: Optional[str] = Field(None, max_length=100)

class JobTemplateUpdate(BaseModel):
    template_name: Optional[str] = Field(None, min_length=1, max_length=255)
    template_description: Optional[str] = None
    template_category: Optional[str] = Field(None, max_length=100)
    job_type: Optional[str] = Field(None, max_length=50)
    function_name: Optional[str] = Field(None, max_length=255)
    module_path: Optional[str] = Field(None, max_length=500)
    default_parameters: Optional[Dict[str, Any]] = None
    parameter_schema: Optional[Dict[str, Any]] = None
    default_priority: Optional[str] = Field(None, max_length=50)
    default_timeout_seconds: Optional[int] = None
    default_max_retries: Optional[int] = None
    default_schedule_type: Optional[str] = Field(None, max_length=50)
    default_queue_name: Optional[str] = Field(None, max_length=255)
    version: Optional[str] = Field(None, max_length=50)
    is_active: Optional[bool] = None
    is_public: Optional[bool] = None
    required_permissions: Optional[List[str]] = None
    environment_requirements: Optional[Dict[str, Any]] = None
    resource_requirements: Optional[Dict[str, Any]] = None

class JobTemplateResponse(JobTemplateBase):
    id: UUID
    template_id: str
    usage_count: int
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Job Worker Schemas
class JobWorkerBase(BaseModel):
    worker_name: str = Field(..., min_length=1, max_length=255)
    worker_type: str = Field(..., max_length=50)
    host_name: Optional[str] = Field(None, max_length=255)
    ip_address: Optional[str] = Field(None, max_length=45)
    port: Optional[int] = None
    max_concurrent_jobs: int = 1
    supported_job_types: Optional[List[str]] = None
    supported_queues: Optional[List[str]] = None
    is_active: bool = True
    cpu_cores: Optional[int] = None
    memory_total_mb: Optional[int] = None
    disk_space_gb: Optional[float] = None
    version: Optional[str] = Field(None, max_length=50)
    capabilities: Optional[Dict[str, Any]] = None
    configuration: Optional[Dict[str, Any]] = None

class JobWorkerCreate(JobWorkerBase):
    worker_id: Optional[str] = Field(None, max_length=100)

class JobWorkerUpdate(BaseModel):
    worker_name: Optional[str] = Field(None, min_length=1, max_length=255)
    worker_type: Optional[str] = Field(None, max_length=50)
    host_name: Optional[str] = Field(None, max_length=255)
    ip_address: Optional[str] = Field(None, max_length=45)
    port: Optional[int] = None
    max_concurrent_jobs: Optional[int] = None
    supported_job_types: Optional[List[str]] = None
    supported_queues: Optional[List[str]] = None
    is_active: Optional[bool] = None
    cpu_cores: Optional[int] = None
    memory_total_mb: Optional[int] = None
    disk_space_gb: Optional[float] = None
    version: Optional[str] = Field(None, max_length=50)
    capabilities: Optional[Dict[str, Any]] = None
    configuration: Optional[Dict[str, Any]] = None

class JobWorkerResponse(JobWorkerBase):
    id: UUID
    worker_id: str
    status: str
    is_healthy: bool
    last_heartbeat: Optional[datetime] = None
    current_job_count: int
    total_jobs_processed: int
    total_jobs_failed: int
    average_execution_time: Optional[float] = None
    memory_available_mb: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Job Schedule Schemas
class JobScheduleBase(BaseModel):
    schedule_name: str = Field(..., min_length=1, max_length=255)
    schedule_description: Optional[str] = None
    schedule_type: str = Field(..., max_length=50)
    cron_expression: Optional[str] = Field(None, max_length=100)
    interval_seconds: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    timezone: str = Field(default='UTC', max_length=50)
    is_active: bool = True
    is_paused: bool = False
    job_template_id: Optional[str] = Field(None, max_length=100)
    job_parameters: Optional[Dict[str, Any]] = None

class JobScheduleCreate(JobScheduleBase):
    schedule_id: Optional[str] = Field(None, max_length=100)

class JobScheduleUpdate(BaseModel):
    schedule_name: Optional[str] = Field(None, min_length=1, max_length=255)
    schedule_description: Optional[str] = None
    schedule_type: Optional[str] = Field(None, max_length=50)
    cron_expression: Optional[str] = Field(None, max_length=100)
    interval_seconds: Optional[int] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    timezone: Optional[str] = Field(None, max_length=50)
    is_active: Optional[bool] = None
    is_paused: Optional[bool] = None
    job_template_id: Optional[str] = Field(None, max_length=100)
    job_parameters: Optional[Dict[str, Any]] = None

class JobScheduleResponse(JobScheduleBase):
    id: UUID
    schedule_id: str
    next_run_at: Optional[datetime] = None
    last_run_at: Optional[datetime] = None
    total_runs: int
    successful_runs: int
    failed_runs: int
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Job Dependency Schemas
class JobDependencyBase(BaseModel):
    job_id: str = Field(..., max_length=100)
    depends_on_job_id: str = Field(..., max_length=100)
    dependency_type: str = Field(..., max_length=50)
    dependency_condition: Optional[Dict[str, Any]] = None

class JobDependencyCreate(JobDependencyBase):
    pass

class JobDependencyUpdate(BaseModel):
    dependency_type: Optional[str] = Field(None, max_length=50)
    dependency_condition: Optional[Dict[str, Any]] = None
    is_satisfied: Optional[bool] = None

class JobDependencyResponse(JobDependencyBase):
    id: UUID
    is_satisfied: bool
    satisfied_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Job Notification Schemas
class JobNotificationBase(BaseModel):
    job_id: str = Field(..., max_length=100)
    notification_type: str = Field(..., max_length=50)
    notification_event: str = Field(..., max_length=100)
    message: str = Field(..., min_length=1)
    details: Optional[Dict[str, Any]] = None
    recipient_type: str = Field(..., max_length=50)
    recipient_address: str = Field(..., max_length=500)
    priority: str = Field(default='medium', max_length=50)
    max_attempts: int = 3
    template_used: Optional[str] = Field(None, max_length=255)

class JobNotificationCreate(JobNotificationBase):
    notification_id: Optional[str] = Field(None, max_length=100)

class JobNotificationUpdate(BaseModel):
    status: Optional[str] = Field(None, max_length=50)
    sent_at: Optional[datetime] = None
    delivery_attempts: Optional[int] = None

class JobNotificationResponse(JobNotificationBase):
    id: UUID
    notification_id: str
    status: str
    sent_at: Optional[datetime] = None
    delivery_attempts: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Job Alert Schemas
class JobAlertBase(BaseModel):
    alert_name: str = Field(..., min_length=1, max_length=255)
    alert_description: Optional[str] = None
    alert_type: str = Field(..., max_length=50)
    alert_condition: Dict[str, Any] = Field(..., description="Alert condition configuration")
    alert_severity: str = Field(..., max_length=50)
    job_filter: Optional[Dict[str, Any]] = None
    queue_filter: Optional[Dict[str, Any]] = None
    worker_filter: Optional[Dict[str, Any]] = None
    is_active: bool = True
    notification_channels: Optional[Dict[str, Any]] = None
    escalation_policy: Optional[Dict[str, Any]] = None

class JobAlertCreate(JobAlertBase):
    alert_id: Optional[str] = Field(None, max_length=100)

class JobAlertUpdate(BaseModel):
    alert_name: Optional[str] = Field(None, min_length=1, max_length=255)
    alert_description: Optional[str] = None
    alert_type: Optional[str] = Field(None, max_length=50)
    alert_condition: Optional[Dict[str, Any]] = None
    alert_severity: Optional[str] = Field(None, max_length=50)
    job_filter: Optional[Dict[str, Any]] = None
    queue_filter: Optional[Dict[str, Any]] = None
    worker_filter: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    notification_channels: Optional[Dict[str, Any]] = None
    escalation_policy: Optional[Dict[str, Any]] = None

class JobAlertResponse(JobAlertBase):
    id: UUID
    alert_id: str
    is_triggered: bool
    last_triggered_at: Optional[datetime] = None
    trigger_count: int
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Utility and Request Schemas
class PaginationParams(BaseModel):
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=100)

class JobFilterParams(BaseModel):
    job_type: Optional[str] = None
    job_category: Optional[str] = None
    status: Optional[str] = None
    priority: Optional[str] = None
    queue_name: Optional[str] = None
    worker_id: Optional[str] = None
    created_by: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    tags: Optional[List[str]] = None

class JobSearchParams(BaseModel):
    query: str = Field(..., min_length=1)
    search_fields: Optional[List[str]] = None
    job_types: Optional[List[str]] = None
    statuses: Optional[List[str]] = None

class JobExecutionRequest(BaseModel):
    job_id: str = Field(..., max_length=100)
    execution_type: str = Field(default='manual', max_length=50)
    parameters: Optional[Dict[str, Any]] = None
    priority: Optional[str] = Field(None, max_length=50)
    queue_name: Optional[str] = Field(None, max_length=255)

class JobStatusUpdateRequest(BaseModel):
    status: str = Field(..., max_length=50)
    progress_percentage: Optional[float] = Field(None, ge=0.0, le=100.0)
    current_step: Optional[str] = Field(None, max_length=255)
    error_message: Optional[str] = None
    result_data: Optional[Dict[str, Any]] = None

class BulkJobOperation(BaseModel):
    job_ids: List[str] = Field(..., min_items=1)
    operation: str = Field(..., max_length=50)
    parameters: Optional[Dict[str, Any]] = None

class JobStatistics(BaseModel):
    total_jobs: int
    pending_jobs: int
    running_jobs: int
    completed_jobs: int
    failed_jobs: int
    cancelled_jobs: int
    jobs_by_type: Dict[str, int]
    jobs_by_priority: Dict[str, int]
    jobs_by_queue: Dict[str, int]
    average_execution_time: Optional[float] = None
    success_rate_percentage: Optional[float] = None

class WorkerStatistics(BaseModel):
    total_workers: int
    active_workers: int
    idle_workers: int
    busy_workers: int
    offline_workers: int
    workers_by_type: Dict[str, int]
    total_capacity: int
    used_capacity: int
    capacity_utilization_percentage: Optional[float] = None

# Response List Schemas
class JobListResponse(BaseModel):
    items: List[JobResponse]
    total: int
    page: int
    size: int
    pages: int

class JobQueueListResponse(BaseModel):
    items: List[JobQueueResponse]
    total: int
    page: int
    size: int
    pages: int

class JobExecutionListResponse(BaseModel):
    items: List[JobExecutionResponse]
    total: int
    page: int
    size: int
    pages: int

class JobTemplateListResponse(BaseModel):
    items: List[JobTemplateResponse]
    total: int
    page: int
    size: int
    pages: int

class JobWorkerListResponse(BaseModel):
    items: List[JobWorkerResponse]
    total: int
    page: int
    size: int
    pages: int

class JobScheduleListResponse(BaseModel):
    items: List[JobScheduleResponse]
    total: int
    page: int
    size: int
    pages: int

# Legacy Compatibility Schemas
class LegacyJobResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class LegacyJobCreate(BaseModel):
    job_name: str
    function_name: str
    parameters: Optional[Dict[str, Any]] = None
    priority: Optional[str] = "medium"

class LegacyJobUpdate(BaseModel):
    status: Optional[str] = None
    progress: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# Custom Response Schemas
class CustomJSONResponse(BaseModel):
    data: Optional[Any] = None
    status: str = "success"
    message: str = "Operation completed successfully"
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None