# jobs service - business logic for jobs module
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func, text
from fastapi import HTTPException
from typing import Optional, List, Dict, Any
from uuid import uuid4
from datetime import datetime, timedelta
import math
import asyncio
import importlib
import traceback

from .models import (
    Job, job_queues, job_executions, job_templates, job_dependencies,
    job_schedules, job_workers, job_notifications, job_metrics,
    job_logs, job_alerts, job_configurations
)
from .schemas import (
    JobCreate, JobUpdate, JobResponse, JobListResponse,
    JobQueueCreate, JobQueueUpdate, JobQueueResponse, JobQueueListResponse,
    JobExecutionCreate, JobExecutionUpdate, JobExecutionResponse, JobExecutionListResponse,
    JobTemplateCreate, JobTemplateUpdate, JobTemplateResponse, JobTemplateListResponse,
    JobWorkerCreate, JobWorkerUpdate, JobWorkerResponse, JobWorkerListResponse,
    JobScheduleCreate, JobScheduleUpdate, JobScheduleResponse, JobScheduleListResponse,
    JobDependencyCreate, JobDependencyUpdate, JobDependencyResponse,
    JobNotificationCreate, JobNotificationUpdate, JobNotificationResponse,
    JobAlertCreate, JobAlertUpdate, JobAlertResponse,
    PaginationParams, JobFilterParams, JobSearchParams,
    JobExecutionRequest, JobStatusUpdateRequest, BulkJobOperation,
    JobStatistics, WorkerStatistics
)
from .exceptions import (
    JobNotFoundError, JobAlreadyExistsError, InvalidJobError,
    JobCreationError, JobUpdateError, JobDeletionError,
    JobExecutionError, JobTimeoutError, JobDependencyError,
    JobQueueNotFoundError, JobTemplateNotFoundError,
    JobWorkerNotFoundError, JobScheduleNotFoundError
)

class JobManagementService:
    def __init__(self, db: Session):
        self.db = db

    # Job Management
    def create_job(self, job_data: JobCreate, created_by: str) -> JobResponse:
        """Create a new job."""
        try:
            # Generate job ID if not provided
            job_id = job_data.job_id or self.generate_job_id()

            # Check for duplicates
            existing = self.db.query(Job).filter(Job.job_id == job_id).first()
            if existing:
                raise JobAlreadyExistsError(f"Job with ID {job_id} already exists")

            # Validate dependencies
            if job_data.depends_on_jobs:
                self._validate_job_dependencies(job_data.depends_on_jobs)

            # Create job
            db_job = Job(
                job_id=job_id,
                **job_data.dict(exclude={'job_id'}),
                created_by=created_by
            )

            # Set next run time for scheduled jobs
            if job_data.schedule_type and job_data.schedule_type != 'manual':
                db_job.next_run_at = self._calculate_next_run_time(
                    job_data.schedule_type,
                    job_data.cron_expression,
                    job_data.interval_seconds,
                    job_data.scheduled_at
                )

            self.db.add(db_job)
            self.db.commit()
            self.db.refresh(db_job)

            # Create dependencies if specified
            if job_data.depends_on_jobs:
                self._create_job_dependencies(job_id, job_data.depends_on_jobs)

            # Log job creation
            self._log_job_event(job_id, "INFO", "Job created", {"created_by": created_by})

            return JobResponse.from_orm(db_job)
        except Exception as e:
            self.db.rollback()
            if isinstance(e, HTTPException):
                raise
            raise JobCreationError(f"Failed to create job: {str(e)}")

    def get_jobs(self, pagination: PaginationParams, filters: Optional[JobFilterParams] = None) -> JobListResponse:
        """Get paginated list of jobs with optional filtering."""
        try:
            query = self.db.query(Job)

            # Apply filters
            if filters:
                if filters.job_type:
                    query = query.filter(Job.job_type == filters.job_type)
                if filters.job_category:
                    query = query.filter(Job.job_category == filters.job_category)
                if filters.status:
                    query = query.filter(Job.status == filters.status)
                if filters.priority:
                    query = query.filter(Job.priority == filters.priority)
                if filters.queue_name:
                    # Join with executions to filter by queue
                    query = query.join(job_executions).filter(job_executions.queue_name == filters.queue_name)
                if filters.worker_id:
                    # Join with executions to filter by worker
                    query = query.join(job_executions).filter(job_executions.worker_id == filters.worker_id)
                if filters.created_by:
                    query = query.filter(Job.created_by == filters.created_by)
                if filters.date_from:
                    query = query.filter(Job.created_at >= filters.date_from)
                if filters.date_to:
                    query = query.filter(Job.created_at <= filters.date_to)
                if filters.tags:
                    query = query.filter(Job.tags.overlap(filters.tags))

            # Get total count
            total = query.count()

            # Apply pagination
            offset = (pagination.page - 1) * pagination.size
            jobs = query.order_by(desc(Job.created_at)).offset(offset).limit(pagination.size).all()

            # Calculate pages
            pages = math.ceil(total / pagination.size) if total > 0 else 1

            return JobListResponse(
                items=[JobResponse.from_orm(job) for job in jobs],
                total=total,
                page=pagination.page,
                size=pagination.size,
                pages=pages
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get jobs: {str(e)}")

    def get_job_by_id(self, job_id: str) -> JobResponse:
        """Get a specific job by ID."""
        job = self.db.query(Job).filter(Job.job_id == job_id).first()
        if not job:
            raise JobNotFoundError(job_id)
        return JobResponse.from_orm(job)

    def update_job(self, job_id: str, job_data: JobUpdate, updated_by: str) -> JobResponse:
        """Update an existing job."""
        try:
            job = self.db.query(Job).filter(Job.job_id == job_id).first()
            if not job:
                raise JobNotFoundError(job_id)

            # Store old values for logging
            old_values = {}
            new_values = {}

            # Update fields
            update_data = job_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(job, field):
                    old_value = getattr(job, field)
                    if old_value != value:
                        old_values[field] = old_value
                        new_values[field] = value
                        setattr(job, field, value)

            job.updated_by = updated_by

            # Recalculate next run time if schedule changed
            if any(field in update_data for field in ['schedule_type', 'cron_expression', 'interval_seconds']):
                job.next_run_at = self._calculate_next_run_time(
                    job.schedule_type,
                    job.cron_expression,
                    job.interval_seconds,
                    job.scheduled_at
                )

            self.db.commit()
            self.db.refresh(job)

            # Log job update
            if old_values:
                self._log_job_event(
                    job_id, "INFO", "Job updated",
                    {"updated_by": updated_by, "changes": {"old": old_values, "new": new_values}}
                )

            return JobResponse.from_orm(job)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise JobUpdateError(f"Failed to update job: {str(e)}")

    def delete_job(self, job_id: str, deleted_by: str) -> Dict[str, str]:
        """Delete a job and all related data."""
        try:
            job = self.db.query(Job).filter(Job.job_id == job_id).first()
            if not job:
                raise JobNotFoundError(job_id)

            # Check if job is currently running
            if job.status == 'running':
                raise InvalidJobError("Cannot delete a running job")

            # Log job deletion
            self._log_job_event(job_id, "INFO", "Job deleted", {"deleted_by": deleted_by})

            # Delete related data (cascade)
            self.db.query(job_executions).filter(job_executions.job_id == job_id).delete()
            self.db.query(job_dependencies).filter(
                or_(
                    job_dependencies.job_id == job_id,
                    job_dependencies.depends_on_job_id == job_id
                )
            ).delete()
            self.db.query(job_notifications).filter(job_notifications.job_id == job_id).delete()
            self.db.query(job_metrics).filter(job_metrics.job_id == job_id).delete()
            self.db.query(job_logs).filter(job_logs.job_id == job_id).delete()

            # Delete the job
            self.db.delete(job)
            self.db.commit()

            return {"message": f"Job {job_id} deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise JobDeletionError(f"Failed to delete job: {str(e)}")

    # Job Execution
    async def execute_job(self, job_id: str, execution_request: JobExecutionRequest) -> JobExecutionResponse:
        """Execute a job."""
        try:
            job = self.db.query(Job).filter(Job.job_id == job_id).first()
            if not job:
                raise JobNotFoundError(job_id)

            if not job.is_active:
                raise InvalidJobError("Job is not active")

            # Check dependencies
            if not self._check_job_dependencies(job_id):
                raise JobDependencyError("Job dependencies not satisfied")

            # Create execution record
            execution_id = self.generate_execution_id()
            execution = job_executions(
                execution_id=execution_id,
                job_id=job_id,
                queue_name=execution_request.queue_name,
                execution_type=execution_request.execution_type,
                trigger_source="api",
                status="pending"
            )
            self.db.add(execution)
            self.db.commit()

            # Update job status
            job.status = "running"
            job.started_at = datetime.now()
            job.retry_count += 1
            self.db.commit()

            # Log execution start
            self._log_job_event(job_id, "INFO", "Job execution started", {"execution_id": execution_id})

            try:
                # Execute the job function
                result = await self._execute_job_function(job, execution_request.parameters or job.parameters)

                # Update execution with success
                execution.status = "completed"
                execution.completed_at = datetime.now()
                execution.execution_time_seconds = (execution.completed_at - execution.started_at).total_seconds()
                execution.result_data = result
                execution.exit_code = 0

                # Update job status
                job.status = "completed"
                job.completed_at = datetime.now()
                job.execution_time_seconds = execution.execution_time_seconds
                job.result_data = result
                job.last_run_at = datetime.now()
                job.progress_percentage = 100.0

                # Calculate next run time for recurring jobs
                if job.is_recurring and job.schedule_type:
                    job.next_run_at = self._calculate_next_run_time(
                        job.schedule_type,
                        job.cron_expression,
                        job.interval_seconds
                    )
                    job.status = "pending"  # Reset for next run

                self.db.commit()

                # Log execution success
                self._log_job_event(job_id, "INFO", "Job execution completed", {"execution_id": execution_id})

            except Exception as exec_error:
                # Update execution with failure
                execution.status = "failed"
                execution.completed_at = datetime.now()
                execution.execution_time_seconds = (execution.completed_at - execution.started_at).total_seconds()
                execution.error_message = str(exec_error)
                execution.error_details = {"traceback": traceback.format_exc()}
                execution.exit_code = 1

                # Update job status
                job.status = "failed"
                job.completed_at = datetime.now()
                job.error_message = str(exec_error)
                job.error_details = {"traceback": traceback.format_exc()}

                # Check if retry is needed
                if job.retry_count < job.max_retries:
                    job.status = "pending"  # Will be retried
                    job.next_run_at = datetime.now() + timedelta(minutes=5)  # Retry in 5 minutes

                self.db.commit()

                # Log execution failure
                self._log_job_event(job_id, "ERROR", "Job execution failed", {
                    "execution_id": execution_id,
                    "error": str(exec_error)
                })

                if job.retry_count >= job.max_retries:
                    raise JobExecutionError(f"Job execution failed: {str(exec_error)}")

            self.db.refresh(execution)
            return JobExecutionResponse.from_orm(execution)

        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise JobExecutionError(f"Failed to execute job: {str(e)}")

    # Helper methods
    def generate_job_id(self) -> str:
        """Generate a unique job ID."""
        return f"JOB-{uuid4().hex[:8].upper()}"

    def generate_execution_id(self) -> str:
        """Generate a unique execution ID."""
        return f"EXEC-{uuid4().hex[:8].upper()}"

    def _validate_job_dependencies(self, depends_on_jobs: List[str]) -> None:
        """Validate that dependency jobs exist."""
        for dep_job_id in depends_on_jobs:
            dep_job = self.db.query(Job).filter(Job.job_id == dep_job_id).first()
            if not dep_job:
                raise JobDependencyError(f"Dependency job {dep_job_id} not found")

    def _create_job_dependencies(self, job_id: str, depends_on_jobs: List[str]) -> None:
        """Create job dependency records."""
        for dep_job_id in depends_on_jobs:
            dependency = job_dependencies(
                job_id=job_id,
                depends_on_job_id=dep_job_id,
                dependency_type="success"
            )
            self.db.add(dependency)

    def _check_job_dependencies(self, job_id: str) -> bool:
        """Check if all job dependencies are satisfied."""
        dependencies = self.db.query(job_dependencies).filter(
            job_dependencies.job_id == job_id
        ).all()

        for dep in dependencies:
            dep_job = self.db.query(Job).filter(Job.job_id == dep.depends_on_job_id).first()
            if not dep_job or dep_job.status != "completed":
                return False

            # Update dependency as satisfied
            dep.is_satisfied = True
            dep.satisfied_at = datetime.now()

        return True

    def _calculate_next_run_time(self, schedule_type: str, cron_expression: Optional[str] = None,
                                interval_seconds: Optional[int] = None,
                                scheduled_at: Optional[datetime] = None) -> Optional[datetime]:
        """Calculate the next run time for a scheduled job."""
        if schedule_type == "once":
            return scheduled_at
        elif schedule_type == "interval" and interval_seconds:
            return datetime.now() + timedelta(seconds=interval_seconds)
        elif schedule_type == "cron" and cron_expression:
            # Simple cron parsing - in production, use a proper cron library
            return datetime.now() + timedelta(hours=1)  # Placeholder
        return None

    async def _execute_job_function(self, job: Job, parameters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute the actual job function."""
        try:
            # Import the module and function
            if job.module_path:
                module = importlib.import_module(job.module_path)
                func = getattr(module, job.function_name)
            else:
                # Assume function is in global scope or built-in
                func = globals().get(job.function_name)
                if not func:
                    raise ImportError(f"Function {job.function_name} not found")

            # Execute the function
            if asyncio.iscoroutinefunction(func):
                result = await func(**(parameters or {}))
            else:
                result = func(**(parameters or {}))

            return {"result": result, "status": "success"}

        except Exception as e:
            raise JobExecutionError(f"Function execution failed: {str(e)}")

    def _log_job_event(self, job_id: str, level: str, message: str, data: Optional[Dict[str, Any]] = None) -> None:
        """Log a job event."""
        log_entry = job_logs(
            log_id=f"LOG-{uuid4().hex[:8].upper()}",
            job_id=job_id,
            log_level=level,
            log_source="job_service",
            log_message=message,
            log_data=data
        )
        self.db.add(log_entry)