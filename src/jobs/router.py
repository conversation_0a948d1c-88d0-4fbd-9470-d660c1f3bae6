# jobs router - job management and scheduling endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import datetime

from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    JobCreate, JobUpdate, JobResponse, JobListResponse,
    JobExecutionCreate, JobExecutionUpdate, JobExecutionResponse,
    JobScheduleCreate, JobScheduleUpdate, JobScheduleResponse,
    JobLogResponse, JobStatistics, JobSearchParams,
    JobBulkOperation, JobTriggerRequest, JobRetryRequest,
    LegacyJobCreate, LegacyJobResponse, CustomJSONResponse
)
from .service import JobManagementService
from .exceptions import JobNotFoundError

router = APIRouter()

def get_job_service(db: Session = Depends(get_db)) -> JobManagementService:
    return JobManagementService(db)

# Job Management Endpoints
@router.post("/", response_model=JobResponse, status_code=201)
async def create_job(
    job: JobCreate,
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new job."""
    return job_service.create_job(job, current_user.email)

@router.get("/", response_model=JobListResponse)
async def get_jobs(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by job status"),
    job_type: Optional[str] = Query(None, description="Filter by job type"),
    priority: Optional[str] = Query(None, description="Filter by priority"),
    created_from: Optional[str] = Query(None, description="Filter by creation date from"),
    created_to: Optional[str] = Query(None, description="Filter by creation date to"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of jobs with filtering."""
    pagination = PaginationParams(page=page, size=size)
    filters = {
        "status": status,
        "job_type": job_type,
        "priority": priority,
        "created_from": created_from,
        "created_to": created_to
    }
    return job_service.get_jobs(pagination, filters)

@router.get("/{job_id}", response_model=JobResponse)
async def get_job(
    job_id: str = Path(..., description="The job ID"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get job by ID."""
    return job_service.get_job_by_id(job_id)

@router.put("/{job_id}", response_model=JobResponse)
async def update_job(
    job_id: str = Path(..., description="The job ID"),
    job_data: JobUpdate = ...,
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Update job information."""
    return job_service.update_job(job_id, job_data, current_user.email)

@router.delete("/{job_id}")
async def delete_job(
    job_id: str = Path(..., description="The job ID"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Delete job."""
    job_service.delete_job(job_id, current_user.email)
    return {"message": "Job deleted successfully"}

# Job Execution Endpoints
@router.post("/{job_id}/execute", response_model=JobExecutionResponse)
async def execute_job(
    job_id: str = Path(..., description="The job ID"),
    background_tasks: BackgroundTasks,
    trigger_request: Optional[JobTriggerRequest] = None,
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Execute job immediately."""
    return job_service.execute_job(job_id, background_tasks, trigger_request, current_user.email)

@router.get("/{job_id}/executions", response_model=List[JobExecutionResponse])
async def get_job_executions(
    job_id: str = Path(..., description="The job ID"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by execution status"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get job execution history."""
    pagination = PaginationParams(page=page, size=size)
    return job_service.get_job_executions(job_id, pagination, status)

@router.get("/executions/{execution_id}", response_model=JobExecutionResponse)
async def get_job_execution(
    execution_id: str = Path(..., description="The execution ID"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get job execution by ID."""
    return job_service.get_job_execution_by_id(execution_id)

@router.post("/executions/{execution_id}/retry", response_model=JobExecutionResponse)
async def retry_job_execution(
    execution_id: str = Path(..., description="The execution ID"),
    background_tasks: BackgroundTasks,
    retry_request: Optional[JobRetryRequest] = None,
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Retry failed job execution."""
    return job_service.retry_job_execution(execution_id, background_tasks, retry_request, current_user.email)

@router.post("/executions/{execution_id}/cancel")
async def cancel_job_execution(
    execution_id: str = Path(..., description="The execution ID"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Cancel running job execution."""
    job_service.cancel_job_execution(execution_id, current_user.email)
    return {"message": "Job execution cancelled successfully"}

# Job Scheduling Endpoints
@router.post("/{job_id}/schedule", response_model=JobScheduleResponse)
async def create_job_schedule(
    job_id: str = Path(..., description="The job ID"),
    schedule: JobScheduleCreate = ...,
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Create job schedule."""
    return job_service.create_job_schedule(job_id, schedule, current_user.email)

@router.get("/{job_id}/schedules", response_model=List[JobScheduleResponse])
async def get_job_schedules(
    job_id: str = Path(..., description="The job ID"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get job schedules."""
    return job_service.get_job_schedules(job_id)

@router.put("/schedules/{schedule_id}", response_model=JobScheduleResponse)
async def update_job_schedule(
    schedule_id: str = Path(..., description="The schedule ID"),
    schedule_data: JobScheduleUpdate = ...,
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Update job schedule."""
    return job_service.update_job_schedule(schedule_id, schedule_data, current_user.email)

@router.delete("/schedules/{schedule_id}")
async def delete_job_schedule(
    schedule_id: str = Path(..., description="The schedule ID"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Delete job schedule."""
    job_service.delete_job_schedule(schedule_id, current_user.email)
    return {"message": "Job schedule deleted successfully"}

@router.post("/schedules/{schedule_id}/enable")
async def enable_job_schedule(
    schedule_id: str = Path(..., description="The schedule ID"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Enable job schedule."""
    job_service.enable_job_schedule(schedule_id, current_user.email)
    return {"message": "Job schedule enabled successfully"}

@router.post("/schedules/{schedule_id}/disable")
async def disable_job_schedule(
    schedule_id: str = Path(..., description="The schedule ID"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Disable job schedule."""
    job_service.disable_job_schedule(schedule_id, current_user.email)
    return {"message": "Job schedule disabled successfully"}

# Job Logs and Monitoring
@router.get("/{job_id}/logs", response_model=List[JobLogResponse])
async def get_job_logs(
    job_id: str = Path(..., description="The job ID"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    level: Optional[str] = Query(None, description="Filter by log level"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get job logs."""
    pagination = PaginationParams(page=page, size=size)
    return job_service.get_job_logs(job_id, pagination, level)

@router.get("/executions/{execution_id}/logs", response_model=List[JobLogResponse])
async def get_execution_logs(
    execution_id: str = Path(..., description="The execution ID"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    level: Optional[str] = Query(None, description="Filter by log level"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get execution logs."""
    pagination = PaginationParams(page=page, size=size)
    return job_service.get_execution_logs(execution_id, pagination, level)

# Job Analytics and Statistics
@router.get("/statistics", response_model=JobStatistics)
async def get_job_statistics(
    period: str = Query("monthly", description="Statistics period"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive job statistics."""
    return job_service.get_job_statistics(period)

@router.get("/analytics", response_model=CustomJSONResponse)
async def get_jobs_analytics(
    period: str = Query("monthly", description="Analytics period"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get overall job analytics."""
    return job_service.get_jobs_analytics(period)

@router.get("/performance", response_model=CustomJSONResponse)
async def get_job_performance_metrics(
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get job performance metrics."""
    return job_service.get_job_performance_metrics()

# Search and Bulk Operations
@router.post("/search", response_model=JobListResponse)
async def search_jobs(
    search_params: JobSearchParams = ...,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Search jobs with advanced criteria."""
    pagination = PaginationParams(page=page, size=size)
    return job_service.search_jobs(search_params, pagination)

@router.post("/bulk-operations", response_model=CustomJSONResponse)
async def perform_bulk_operation(
    operation: JobBulkOperation = ...,
    background_tasks: BackgroundTasks,
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Perform bulk job operations."""
    return job_service.perform_bulk_operation(operation, background_tasks, current_user.email)

@router.get("/export")
async def export_jobs(
    format: str = Query("csv", description="Export format"),
    status: Optional[str] = Query(None, description="Filter by status"),
    job_type: Optional[str] = Query(None, description="Filter by job type"),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Export job data."""
    filters = {"status": status, "job_type": job_type}
    return job_service.export_jobs(format, filters, current_user.email)

# Job Queue Management
@router.get("/queue/status")
async def get_queue_status(
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get job queue status."""
    return job_service.get_queue_status()

@router.get("/queue/pending", response_model=JobListResponse)
async def get_pending_jobs(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get pending jobs in queue."""
    pagination = PaginationParams(page=page, size=size)
    return job_service.get_pending_jobs(pagination)

@router.get("/queue/running", response_model=JobListResponse)
async def get_running_jobs(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get currently running jobs."""
    pagination = PaginationParams(page=page, size=size)
    return job_service.get_running_jobs(pagination)

@router.post("/queue/clear")
async def clear_job_queue(
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Clear job queue (admin only)."""
    job_service.clear_job_queue(current_user.email)
    return {"message": "Job queue cleared successfully"}

@router.post("/queue/pause")
async def pause_job_queue(
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Pause job queue processing."""
    job_service.pause_job_queue(current_user.email)
    return {"message": "Job queue paused successfully"}

@router.post("/queue/resume")
async def resume_job_queue(
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Resume job queue processing."""
    job_service.resume_job_queue(current_user.email)
    return {"message": "Job queue resumed successfully"}

# Job Templates and Types
@router.get("/types")
async def get_job_types(
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get available job types."""
    return job_service.get_job_types()

@router.get("/templates")
async def get_job_templates(
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Get job templates."""
    return job_service.get_job_templates()

@router.post("/templates/{template_id}/create", response_model=JobResponse)
async def create_job_from_template(
    template_id: str = Path(..., description="The template ID"),
    parameters: dict = {},
    job_service: JobManagementService = Depends(get_job_service),
    current_user: User = Depends(get_current_user)
):
    """Create job from template."""
    return job_service.create_job_from_template(template_id, parameters, current_user.email)

# Legacy Compatibility Endpoints
@router.post("/legacy/create", response_model=LegacyJobResponse)
async def create_job_legacy(
    job: LegacyJobCreate = ...,
    job_service: JobManagementService = Depends(get_job_service)
):
    """Legacy job creation endpoint."""
    try:
        job_data = JobCreate(
            name=job.name,
            job_type=job.job_type or "generic",
            description=job.description,
            priority=job.priority or "medium",
            parameters=job.parameters or {}
        )
        result = job_service.create_job(job_data, "legacy_user")
        return LegacyJobResponse(
            success=True,
            message="Job created successfully",
            data={"job_id": result.job_id}
        )
    except Exception as e:
        return LegacyJobResponse(
            success=False,
            message=f"Failed to create job: {str(e)}"
        )

# Health Check
@router.get("/health", response_model=CustomJSONResponse)
async def health_check():
    """Job management module health check."""
    return CustomJSONResponse(
        data={"status": "healthy", "module": "jobs"},
        message="Job management module is operational"
    )