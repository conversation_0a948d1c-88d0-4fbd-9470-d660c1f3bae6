# dashboard exceptions - module-specific errors for dashboard
from fastapi import HTTPException, status

class DashboardNotFoundError(HTTPException):
    def __init__(self, dashboard_id: str = None):
        detail = f"Dashboard '{dashboard_id}' not found" if dashboard_id else "Dashboard not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class DashboardAlreadyExistsError(HTTPException):
    def __init__(self, dashboard_name: str = None):
        detail = f"Dashboard '{dashboard_name}' already exists" if dashboard_name else "Dashboard already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidDashboardError(HTTPException):
    def __init__(self, detail: str = "Invalid dashboard"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardValidationError(HTTPException):
    def __init__(self, detail: str = "Dashboard validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create dashboard"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update dashboard"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete dashboard"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class VisualizationNotFoundError(HTTPException):
    def __init__(self, visualization_id: str = None):
        detail = f"Visualization '{visualization_id}' not found" if visualization_id else "Visualization not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class VisualizationAlreadyExistsError(HTTPException):
    def __init__(self, visualization_title: str = None):
        detail = f"Visualization '{visualization_title}' already exists" if visualization_title else "Visualization already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidVisualizationError(HTTPException):
    def __init__(self, detail: str = "Invalid visualization"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class VisualizationValidationError(HTTPException):
    def __init__(self, detail: str = "Visualization validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class VisualizationCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create visualization"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class VisualizationUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update visualization"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class VisualizationDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete visualization"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class VisualizationExecutionError(HTTPException):
    def __init__(self, detail: str = "Failed to execute visualization"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidVisualizationCodeError(HTTPException):
    def __init__(self, code_type: str = None):
        detail = f"Invalid {code_type} code in visualization" if code_type else "Invalid code in visualization"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class VisualizationTimeoutError(HTTPException):
    def __init__(self, detail: str = "Visualization execution timed out"):
        super().__init__(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=detail)

class DashboardTemplateNotFoundError(HTTPException):
    def __init__(self, template_id: str = None):
        detail = f"Dashboard template '{template_id}' not found" if template_id else "Dashboard template not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class DashboardTemplateAlreadyExistsError(HTTPException):
    def __init__(self, template_name: str = None):
        detail = f"Dashboard template '{template_name}' already exists" if template_name else "Dashboard template already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidDashboardTemplateError(HTTPException):
    def __init__(self, detail: str = "Invalid dashboard template"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardTemplateValidationError(HTTPException):
    def __init__(self, detail: str = "Dashboard template validation failed"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardTemplateCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create dashboard template"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardTemplateUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update dashboard template"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardTemplateUsageError(HTTPException):
    def __init__(self, detail: str = "Failed to create dashboard from template"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardShareNotFoundError(HTTPException):
    def __init__(self, share_id: str = None):
        detail = f"Dashboard share '{share_id}' not found" if share_id else "Dashboard share not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class DashboardShareCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create dashboard share"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidDashboardShareError(HTTPException):
    def __init__(self, detail: str = "Invalid dashboard share"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardSharePermissionError(HTTPException):
    def __init__(self, detail: str = "Insufficient permissions for dashboard share"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class DashboardShareExpiredError(HTTPException):
    def __init__(self, detail: str = "Dashboard share has expired"):
        super().__init__(status_code=status.HTTP_410_GONE, detail=detail)

class DashboardAnalyticsError(HTTPException):
    def __init__(self, detail: str = "Failed to record dashboard analytics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardStatisticsError(HTTPException):
    def __init__(self, detail: str = "Failed to get dashboard statistics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardUsageAnalyticsError(HTTPException):
    def __init__(self, detail: str = "Failed to get dashboard usage analytics"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardAlertNotFoundError(HTTPException):
    def __init__(self, alert_id: str = None):
        detail = f"Dashboard alert '{alert_id}' not found" if alert_id else "Dashboard alert not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class DashboardAlertCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create dashboard alert"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidDashboardAlertError(HTTPException):
    def __init__(self, detail: str = "Invalid dashboard alert"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardAlertTriggerError(HTTPException):
    def __init__(self, detail: str = "Failed to trigger dashboard alert"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardExportNotFoundError(HTTPException):
    def __init__(self, export_id: str = None):
        detail = f"Dashboard export '{export_id}' not found" if export_id else "Dashboard export not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class DashboardExportCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create dashboard export"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidDashboardExportError(HTTPException):
    def __init__(self, detail: str = "Invalid dashboard export"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardExportProcessingError(HTTPException):
    def __init__(self, detail: str = "Failed to process dashboard export"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardExportExpiredError(HTTPException):
    def __init__(self, detail: str = "Dashboard export has expired"):
        super().__init__(status_code=status.HTTP_410_GONE, detail=detail)

class InvalidExportFormatError(HTTPException):
    def __init__(self, format_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid export format: {format_type}"
        )

class DashboardFilterNotFoundError(HTTPException):
    def __init__(self, filter_id: str = None):
        detail = f"Dashboard filter '{filter_id}' not found" if filter_id else "Dashboard filter not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class DashboardFilterCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create dashboard filter"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidDashboardFilterError(HTTPException):
    def __init__(self, detail: str = "Invalid dashboard filter"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidFilterTypeError(HTTPException):
    def __init__(self, filter_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid filter type: {filter_type}"
        )

class DashboardCommentNotFoundError(HTTPException):
    def __init__(self, comment_id: str = None):
        detail = f"Dashboard comment '{comment_id}' not found" if comment_id else "Dashboard comment not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class DashboardCommentCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create dashboard comment"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidDashboardCommentError(HTTPException):
    def __init__(self, detail: str = "Invalid dashboard comment"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardVersionNotFoundError(HTTPException):
    def __init__(self, version_id: str = None):
        detail = f"Dashboard version '{version_id}' not found" if version_id else "Dashboard version not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class DashboardVersionCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create dashboard version"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidDashboardVersionError(HTTPException):
    def __init__(self, detail: str = "Invalid dashboard version"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardVersionRestoreError(HTTPException):
    def __init__(self, detail: str = "Failed to restore dashboard version"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class InvalidWidgetTypeError(HTTPException):
    def __init__(self, widget_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid widget type: {widget_type}"
        )

class InvalidCodeTypeError(HTTPException):
    def __init__(self, code_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid code type: {code_type}"
        )

class InvalidDashboardTypeError(HTTPException):
    def __init__(self, dashboard_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid dashboard type: {dashboard_type}"
        )

class DashboardAccessDeniedError(HTTPException):
    def __init__(self, dashboard_id: str = None):
        detail = f"Access denied to dashboard '{dashboard_id}'" if dashboard_id else "Dashboard access denied"
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class DashboardQuotaExceededError(HTTPException):
    def __init__(self, quota_type: str = "dashboard"):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"{quota_type.title()} quota exceeded"
        )

class DashboardRateLimitExceededError(HTTPException):
    def __init__(self, operation: str = "dashboard operation"):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Rate limit exceeded for {operation}"
        )

class DashboardDataSourceError(HTTPException):
    def __init__(self, detail: str = "Dashboard data source error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardConfigurationError(HTTPException):
    def __init__(self, detail: str = "Dashboard configuration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardLayoutError(HTTPException):
    def __init__(self, detail: str = "Dashboard layout error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardThemeError(HTTPException):
    def __init__(self, detail: str = "Dashboard theme error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class DashboardCacheError(HTTPException):
    def __init__(self, detail: str = "Dashboard cache error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardSyncError(HTTPException):
    def __init__(self, detail: str = "Dashboard synchronization error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardBackupError(HTTPException):
    def __init__(self, detail: str = "Dashboard backup error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardRestoreError(HTTPException):
    def __init__(self, detail: str = "Dashboard restore error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardMigrationError(HTTPException):
    def __init__(self, detail: str = "Dashboard migration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardIntegrityError(HTTPException):
    def __init__(self, detail: str = "Dashboard data integrity error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardPerformanceError(HTTPException):
    def __init__(self, detail: str = "Dashboard performance issue"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class DashboardMaintenanceError(HTTPException):
    def __init__(self, detail: str = "Dashboard is under maintenance"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class LegacyDashboardError(HTTPException):
    def __init__(self, detail: str = "Legacy dashboard operation failed"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DashboardFeatureNotAvailableError(HTTPException):
    def __init__(self, feature: str):
        super().__init__(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail=f"Dashboard feature '{feature}' is not available"
        )