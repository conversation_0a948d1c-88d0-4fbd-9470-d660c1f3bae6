# dashboard schemas - Pydantic models for dashboard module
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Dict, Any, Union
from uuid import UUID
from datetime import datetime
from decimal import Decimal

# Dashboard Base Schemas
class DashboardBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    dash_type: str = Field(default='user', max_length=50)
    layout_config: Optional[Dict[str, Any]] = None
    theme_config: Optional[Dict[str, Any]] = None
    filters_config: Optional[Dict[str, Any]] = None
    refresh_interval: Optional[int] = Field(None, ge=0)
    is_public: bool = False
    is_favorite: bool = False
    tags: Optional[List[str]] = None

class DashboardCreate(DashboardBase):
    customer_id: Optional[str] = Field(None, max_length=255)
    organization_id: Optional[str] = Field(None, max_length=255)

class DashboardUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    dash_type: Optional[str] = Field(None, max_length=50)
    layout_config: Optional[Dict[str, Any]] = None
    theme_config: Optional[Dict[str, Any]] = None
    filters_config: Optional[Dict[str, Any]] = None
    refresh_interval: Optional[int] = Field(None, ge=0)
    is_public: Optional[bool] = None
    is_favorite: Optional[bool] = None
    tags: Optional[List[str]] = None

class DashboardResponse(DashboardBase):
    dashboard_id: str
    customer_id: Optional[str] = None
    organization_id: Optional[str] = None
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None
    visualization_count: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)

# Visualization/Widget Schemas
class VisualizationBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    widget_type: str = Field(default='chart', max_length=50)
    config: Dict[str, Any] = Field(..., description="Visualization configuration")
    code_type: str = Field(..., max_length=50)
    code: Optional[str] = None
    query: Optional[str] = None
    data_source: Optional[str] = Field(None, max_length=100)
    refresh_interval: Optional[int] = Field(None, ge=0)
    position_x: Optional[int] = None
    position_y: Optional[int] = None
    width: Optional[int] = Field(None, ge=1)
    height: Optional[int] = Field(None, ge=1)
    is_visible: bool = True

class VisualizationCreate(VisualizationBase):
    dashboard_id: Optional[str] = Field(None, max_length=255)
    chat_id: Optional[str] = Field(None, max_length=255)

class VisualizationUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    dashboard_id: Optional[str] = Field(None, max_length=255)
    widget_type: Optional[str] = Field(None, max_length=50)
    config: Optional[Dict[str, Any]] = None
    code_type: Optional[str] = Field(None, max_length=50)
    code: Optional[str] = None
    query: Optional[str] = None
    data_source: Optional[str] = Field(None, max_length=100)
    refresh_interval: Optional[int] = Field(None, ge=0)
    position_x: Optional[int] = None
    position_y: Optional[int] = None
    width: Optional[int] = Field(None, ge=1)
    height: Optional[int] = Field(None, ge=1)
    is_visible: Optional[bool] = None

class VisualizationResponse(VisualizationBase):
    visualization_id: str
    dashboard_id: Optional[str] = None
    chat_id: Optional[str] = None
    created_by: str
    created_at: datetime
    updated_by: str
    updated_at: datetime
    data: Optional[Any] = None  # Actual visualization data

    model_config = ConfigDict(from_attributes=True)

# Dashboard Template Schemas
class DashboardTemplateBase(BaseModel):
    template_name: str = Field(..., min_length=1, max_length=255)
    template_description: Optional[str] = None
    template_category: Optional[str] = Field(None, max_length=100)
    template_config: Dict[str, Any] = Field(..., description="Template configuration")
    default_widgets: Optional[Dict[str, Any]] = None
    layout_config: Optional[Dict[str, Any]] = None
    theme_config: Optional[Dict[str, Any]] = None
    is_system_template: bool = False
    is_active: bool = True

class DashboardTemplateCreate(DashboardTemplateBase):
    pass

class DashboardTemplateUpdate(BaseModel):
    template_name: Optional[str] = Field(None, min_length=1, max_length=255)
    template_description: Optional[str] = None
    template_category: Optional[str] = Field(None, max_length=100)
    template_config: Optional[Dict[str, Any]] = None
    default_widgets: Optional[Dict[str, Any]] = None
    layout_config: Optional[Dict[str, Any]] = None
    theme_config: Optional[Dict[str, Any]] = None
    is_system_template: Optional[bool] = None
    is_active: Optional[bool] = None

class DashboardTemplateResponse(DashboardTemplateBase):
    id: UUID
    usage_count: int
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Dashboard Sharing Schemas
class DashboardShareBase(BaseModel):
    dashboard_id: str = Field(..., max_length=255)
    shared_with_user: Optional[str] = Field(None, max_length=255)
    shared_with_role: Optional[str] = Field(None, max_length=100)
    permission_level: str = Field(..., max_length=50)
    is_public_link: bool = False
    expires_at: Optional[datetime] = None

class DashboardShareCreate(DashboardShareBase):
    pass

class DashboardShareResponse(DashboardShareBase):
    id: UUID
    public_token: Optional[str] = None
    created_by: str
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Dashboard Analytics Schemas
class DashboardAnalyticsBase(BaseModel):
    dashboard_id: str = Field(..., max_length=255)
    action_type: str = Field(..., max_length=50)
    session_id: Optional[str] = Field(None, max_length=255)
    ip_address: Optional[str] = Field(None, max_length=45)
    user_agent: Optional[str] = None
    duration_seconds: Optional[float] = Field(None, ge=0)
    widgets_interacted: Optional[List[str]] = None
    filters_applied: Optional[Dict[str, Any]] = None
    export_format: Optional[str] = Field(None, max_length=50)
    metadata: Optional[Dict[str, Any]] = None

class DashboardAnalyticsCreate(DashboardAnalyticsBase):
    user_id: Optional[str] = Field(None, max_length=255)

class DashboardAnalyticsResponse(DashboardAnalyticsBase):
    id: UUID
    user_id: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Dashboard Alert Schemas
class DashboardAlertBase(BaseModel):
    dashboard_id: str = Field(..., max_length=255)
    widget_id: Optional[str] = Field(None, max_length=255)
    alert_name: str = Field(..., min_length=1, max_length=255)
    alert_description: Optional[str] = None
    alert_type: str = Field(..., max_length=50)
    condition_config: Dict[str, Any] = Field(..., description="Alert condition configuration")
    notification_config: Optional[Dict[str, Any]] = None
    is_active: bool = True

class DashboardAlertCreate(DashboardAlertBase):
    pass

class DashboardAlertUpdate(BaseModel):
    alert_name: Optional[str] = Field(None, min_length=1, max_length=255)
    alert_description: Optional[str] = None
    alert_type: Optional[str] = Field(None, max_length=50)
    condition_config: Optional[Dict[str, Any]] = None
    notification_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None

class DashboardAlertResponse(DashboardAlertBase):
    id: UUID
    last_triggered: Optional[datetime] = None
    trigger_count: int
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Dashboard Export Schemas
class DashboardExportBase(BaseModel):
    dashboard_id: str = Field(..., max_length=255)
    export_name: str = Field(..., min_length=1, max_length=255)
    export_format: str = Field(..., max_length=50)
    export_config: Optional[Dict[str, Any]] = None

class DashboardExportCreate(DashboardExportBase):
    pass

class DashboardExportResponse(DashboardExportBase):
    id: UUID
    file_path: Optional[str] = None
    file_size_bytes: Optional[int] = None
    export_status: str
    error_message: Optional[str] = None
    exported_by: str
    exported_at: datetime
    expires_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Dashboard Filter Schemas
class DashboardFilterBase(BaseModel):
    dashboard_id: str = Field(..., max_length=255)
    filter_name: str = Field(..., min_length=1, max_length=255)
    filter_type: str = Field(..., max_length=50)
    filter_config: Dict[str, Any] = Field(..., description="Filter configuration")
    default_value: Optional[Dict[str, Any]] = None
    is_required: bool = False
    is_global: bool = True
    display_order: Optional[int] = None

class DashboardFilterCreate(DashboardFilterBase):
    pass

class DashboardFilterUpdate(BaseModel):
    filter_name: Optional[str] = Field(None, min_length=1, max_length=255)
    filter_type: Optional[str] = Field(None, max_length=50)
    filter_config: Optional[Dict[str, Any]] = None
    default_value: Optional[Dict[str, Any]] = None
    is_required: Optional[bool] = None
    is_global: Optional[bool] = None
    display_order: Optional[int] = None

class DashboardFilterResponse(DashboardFilterBase):
    id: UUID
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Dashboard Comment Schemas
class DashboardCommentBase(BaseModel):
    dashboard_id: str = Field(..., max_length=255)
    widget_id: Optional[str] = Field(None, max_length=255)
    comment_text: str = Field(..., min_length=1)
    comment_type: str = Field(default='general', max_length=50)
    position_x: Optional[float] = None
    position_y: Optional[float] = None
    parent_comment_id: Optional[UUID] = None

class DashboardCommentCreate(DashboardCommentBase):
    pass

class DashboardCommentUpdate(BaseModel):
    comment_text: Optional[str] = Field(None, min_length=1)
    comment_type: Optional[str] = Field(None, max_length=50)
    position_x: Optional[float] = None
    position_y: Optional[float] = None
    is_resolved: Optional[bool] = None

class DashboardCommentResponse(DashboardCommentBase):
    id: UUID
    is_resolved: bool
    created_by: str
    created_at: datetime
    updated_by: Optional[str] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

# Dashboard Version Schemas
class DashboardVersionBase(BaseModel):
    dashboard_id: str = Field(..., max_length=255)
    version_number: int = Field(..., ge=1)
    version_name: Optional[str] = Field(None, max_length=255)
    version_description: Optional[str] = None
    dashboard_config: Dict[str, Any] = Field(..., description="Complete dashboard configuration")
    widgets_config: Dict[str, Any] = Field(..., description="All widgets configuration")
    change_summary: Optional[str] = None

class DashboardVersionCreate(DashboardVersionBase):
    pass

class DashboardVersionResponse(DashboardVersionBase):
    id: UUID
    is_current: bool
    created_by: str
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Request/Response Schemas for API Operations
class VisualizationDashboardRequest(BaseModel):
    visualization_id: str = Field(..., max_length=255)
    dashboard_id: str = Field(..., max_length=255)

class DashboardDataRequest(BaseModel):
    dashboard_id: str = Field(..., max_length=255)
    filters: Optional[Dict[str, Any]] = None
    refresh: bool = False

class DashboardDataResponse(BaseModel):
    dashboard: DashboardResponse
    visualizations: List[VisualizationResponse]
    filters: List[DashboardFilterResponse]
    metadata: Optional[Dict[str, Any]] = None

class VisualizationDataResponse(BaseModel):
    visualization: VisualizationResponse
    data: Any
    metadata: Optional[Dict[str, Any]] = None

# Dashboard Statistics Schemas
class DashboardStatistics(BaseModel):
    total_dashboards: int
    dashboards_by_type: Dict[str, int]
    dashboards_by_user: Dict[str, int]
    most_viewed_dashboards: List[Dict[str, Any]]
    most_used_widgets: Dict[str, int]
    average_widgets_per_dashboard: float
    total_visualizations: int
    total_exports: int
    exports_by_format: Dict[str, int]
    active_users_last_30_days: int
    dashboard_views_last_30_days: int

class DashboardUsageAnalytics(BaseModel):
    dashboard_id: str
    total_views: int
    unique_viewers: int
    average_session_duration: float
    most_interacted_widgets: List[str]
    common_filters: Dict[str, Any]
    export_count: int
    share_count: int
    last_accessed: Optional[datetime] = None

# Bulk Operations Schemas
class BulkDashboardOperation(BaseModel):
    dashboard_ids: List[str] = Field(..., min_items=1)
    operation: str = Field(..., max_length=50)  # delete, export, share, etc.
    parameters: Optional[Dict[str, Any]] = None

class BulkVisualizationOperation(BaseModel):
    visualization_ids: List[str] = Field(..., min_items=1)
    operation: str = Field(..., max_length=50)
    parameters: Optional[Dict[str, Any]] = None

# Response List Schemas
class DashboardListResponse(BaseModel):
    data: List[DashboardResponse]

class VisualizationListResponse(BaseModel):
    data: List[VisualizationResponse]

class DashboardTemplateListResponse(BaseModel):
    data: List[DashboardTemplateResponse]

class DashboardShareListResponse(BaseModel):
    data: List[DashboardShareResponse]

class DashboardAnalyticsListResponse(BaseModel):
    data: List[DashboardAnalyticsResponse]

class DashboardAlertListResponse(BaseModel):
    data: List[DashboardAlertResponse]

class DashboardExportListResponse(BaseModel):
    data: List[DashboardExportResponse]

class DashboardFilterListResponse(BaseModel):
    data: List[DashboardFilterResponse]

class DashboardCommentListResponse(BaseModel):
    data: List[DashboardCommentResponse]

class DashboardVersionListResponse(BaseModel):
    data: List[DashboardVersionResponse]

# Legacy Compatibility Schemas
class LegacyDashboardCreate(BaseModel):
    name: str
    description: Optional[str] = None

class LegacyVisualizationUpdate(BaseModel):
    visualization_id: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    dashboard_id: Optional[str] = None
    config: Dict[str, Any]
    code_type: Optional[str] = None
    code: Optional[str] = None
    query: Optional[str] = None
    chat_id: Optional[str] = None

class LegacyDashboardResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

# Custom JSON Encoder Support
class CustomJSONResponse(BaseModel):
    data: Any
    status: str = "success"
    message: Optional[str] = None

# Dashboard Configuration Schemas
class DashboardLayoutConfig(BaseModel):
    grid_size: Optional[int] = 12
    row_height: Optional[int] = 100
    margin: Optional[List[int]] = [10, 10]
    container_padding: Optional[List[int]] = [10, 10]
    breakpoints: Optional[Dict[str, int]] = None
    cols: Optional[Dict[str, int]] = None

class DashboardThemeConfig(BaseModel):
    primary_color: Optional[str] = "#1976d2"
    secondary_color: Optional[str] = "#dc004e"
    background_color: Optional[str] = "#ffffff"
    text_color: Optional[str] = "#000000"
    font_family: Optional[str] = "Roboto, sans-serif"
    font_size: Optional[str] = "14px"
    border_radius: Optional[str] = "4px"
    shadow: Optional[str] = "0 2px 4px rgba(0,0,0,0.1)"

class WidgetConfig(BaseModel):
    chart_type: Optional[str] = None
    x_axis: Optional[str] = None
    y_axis: Optional[str] = None
    color_scheme: Optional[List[str]] = None
    show_legend: Optional[bool] = True
    show_grid: Optional[bool] = True
    animation: Optional[bool] = True
    responsive: Optional[bool] = True
    custom_options: Optional[Dict[str, Any]] = None