# dashboard service - business logic for dashboard module
from sqlalchemy.orm import Session
from sqlalchemy import select, text, and_, or_, func
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4
from fastapi import HTTPException, status
from datetime import datetime, timedelta
import logging
import json
from decimal import Decimal

from .models import (
    Dashboard, GraphSpecs, dashboard_templates, dashboard_shares,
    dashboard_analytics, dashboard_alerts, dashboard_exports,
    dashboard_filters, dashboard_comments, dashboard_versions
)
from .schemas import (
    DashboardCreate, DashboardUpdate, DashboardResponse,
    VisualizationCreate, VisualizationUpdate, VisualizationResponse,
    DashboardTemplateCreate, DashboardTemplateUpdate, DashboardTemplateResponse,
    DashboardShareCreate, DashboardShareResponse,
    DashboardAnalyticsCreate, DashboardAnalyticsResponse,
    DashboardAlertCreate, DashboardAlertUpdate, DashboardAlertResponse,
    DashboardExportCreate, DashboardExportResponse,
    DashboardFilterCreate, DashboardFilterUpdate, DashboardFilterResponse,
    DashboardCommentCreate, DashboardCommentUpdate, DashboardCommentResponse,
    DashboardVersionCreate, DashboardVersionResponse,
    DashboardDataResponse, VisualizationDataResponse,
    DashboardStatistics, DashboardUsageAnalytics,
    LegacyDashboardResponse, CustomJSONResponse
)
from ..pagination import PaginationParams, PaginatedResponse, paginate_query

logger = logging.getLogger(__name__)

class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for handling special types."""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        if isinstance(obj, (datetime)):
            return obj.isoformat()
        return super().default(obj)

class DashboardService:
    def __init__(self, db: Session):
        self.db = db

    # Dashboard Management
    def create_dashboard(self, dashboard_data: DashboardCreate, created_by: str) -> DashboardResponse:
        """Create a new dashboard."""
        try:
            dashboard_id = str(uuid4())

            db_dashboard = Dashboard(
                dashboard_id=dashboard_id,
                **dashboard_data.dict(),
                created_by=created_by
            )

            self.db.add(db_dashboard)
            self.db.commit()
            self.db.refresh(db_dashboard)

            # Log analytics
            self._log_dashboard_analytics(
                dashboard_id=dashboard_id,
                user_id=created_by,
                action_type="create"
            )

            return DashboardResponse.from_orm(db_dashboard)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create dashboard: {str(e)}")

    def get_dashboards(self, pagination: PaginationParams, dash_type: Optional[str] = None,
                      created_by: Optional[str] = None, tags: Optional[List[str]] = None) -> PaginatedResponse[DashboardResponse]:
        """Get paginated list of dashboards."""
        try:
            query = self.db.query(Dashboard).order_by(Dashboard.created_at.desc())

            if dash_type:
                query = query.filter(Dashboard.dash_type == dash_type)
            if created_by:
                query = query.filter(Dashboard.created_by == created_by)
            if tags:
                query = query.filter(Dashboard.tags.overlap(tags))

            dashboards_list, total = paginate_query(query, pagination)

            # Add visualization count for each dashboard
            dashboard_responses = []
            for dashboard in dashboards_list:
                dashboard_dict = dashboard.__dict__.copy()

                # Get visualization count
                viz_count = self.db.query(func.count(GraphSpecs.visualization_id)).filter(
                    GraphSpecs.dashboard_id == dashboard.dashboard_id
                ).scalar() or 0

                dashboard_dict['visualization_count'] = viz_count
                dashboard_responses.append(DashboardResponse(**dashboard_dict))

            return PaginatedResponse.create(dashboard_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get dashboards: {str(e)}")

    def get_dashboard_by_id(self, dashboard_id: str, user_id: str = None) -> DashboardResponse:
        """Get dashboard by ID."""
        try:
            dashboard = self.db.query(Dashboard).filter(
                Dashboard.dashboard_id == dashboard_id
            ).first()

            if not dashboard:
                raise HTTPException(status_code=404, detail="Dashboard not found")

            # Log analytics
            if user_id:
                self._log_dashboard_analytics(
                    dashboard_id=dashboard_id,
                    user_id=user_id,
                    action_type="view"
                )

            # Get visualization count
            viz_count = self.db.query(func.count(GraphSpecs.visualization_id)).filter(
                GraphSpecs.dashboard_id == dashboard_id
            ).scalar() or 0

            dashboard_dict = dashboard.__dict__.copy()
            dashboard_dict['visualization_count'] = viz_count

            return DashboardResponse(**dashboard_dict)
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get dashboard: {str(e)}")

    def update_dashboard(self, dashboard_id: str, dashboard_data: DashboardUpdate, updated_by: str) -> DashboardResponse:
        """Update an existing dashboard."""
        try:
            dashboard = self.db.query(Dashboard).filter(
                Dashboard.dashboard_id == dashboard_id
            ).first()

            if not dashboard:
                raise HTTPException(status_code=404, detail="Dashboard not found")

            update_data = dashboard_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(dashboard, field, value)

            dashboard.updated_by = updated_by
            dashboard.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(dashboard)

            # Log analytics
            self._log_dashboard_analytics(
                dashboard_id=dashboard_id,
                user_id=updated_by,
                action_type="edit"
            )

            return DashboardResponse.from_orm(dashboard)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update dashboard: {str(e)}")

    def delete_dashboard(self, dashboard_id: str, deleted_by: str) -> Dict[str, str]:
        """Delete a dashboard and all its visualizations."""
        try:
            dashboard = self.db.query(Dashboard).filter(
                Dashboard.dashboard_id == dashboard_id
            ).first()

            if not dashboard:
                raise HTTPException(status_code=404, detail="Dashboard not found")

            # Delete all visualizations associated with this dashboard
            self.db.query(GraphSpecs).filter(
                GraphSpecs.dashboard_id == dashboard_id
            ).delete(synchronize_session=False)

            # Delete dashboard shares
            self.db.query(dashboard_shares).filter(
                dashboard_shares.dashboard_id == dashboard_id
            ).delete(synchronize_session=False)

            # Delete dashboard analytics
            self.db.query(dashboard_analytics).filter(
                dashboard_analytics.dashboard_id == dashboard_id
            ).delete(synchronize_session=False)

            # Delete dashboard alerts
            self.db.query(dashboard_alerts).filter(
                dashboard_alerts.dashboard_id == dashboard_id
            ).delete(synchronize_session=False)

            # Delete dashboard exports
            self.db.query(dashboard_exports).filter(
                dashboard_exports.dashboard_id == dashboard_id
            ).delete(synchronize_session=False)

            # Delete dashboard filters
            self.db.query(dashboard_filters).filter(
                dashboard_filters.dashboard_id == dashboard_id
            ).delete(synchronize_session=False)

            # Delete dashboard comments
            self.db.query(dashboard_comments).filter(
                dashboard_comments.dashboard_id == dashboard_id
            ).delete(synchronize_session=False)

            # Delete dashboard versions
            self.db.query(dashboard_versions).filter(
                dashboard_versions.dashboard_id == dashboard_id
            ).delete(synchronize_session=False)

            # Delete the dashboard
            self.db.delete(dashboard)
            self.db.commit()

            return {"status": "success", "message": "Dashboard deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete dashboard: {str(e)}")

    def _log_dashboard_analytics(self, dashboard_id: str, user_id: str, action_type: str,
                               session_id: str = None, ip_address: str = None,
                               user_agent: str = None, duration_seconds: float = None,
                               widgets_interacted: List[str] = None,
                               filters_applied: Dict[str, Any] = None,
                               export_format: str = None, metadata: Dict[str, Any] = None) -> None:
        """Log dashboard analytics."""
        try:
            analytics = dashboard_analytics(
                dashboard_id=dashboard_id,
                user_id=user_id,
                action_type=action_type,
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                duration_seconds=duration_seconds,
                widgets_interacted=widgets_interacted,
                filters_applied=filters_applied,
                export_format=export_format,
                metadata=metadata
            )

            self.db.add(analytics)
            self.db.commit()
        except Exception as e:
            logger.error(f"Failed to log dashboard analytics: {str(e)}")
            # Don't raise exception for analytics logging failures

    # Visualization Management
    def create_visualization(self, viz_data: VisualizationCreate, created_by: str) -> VisualizationResponse:
        """Create a new visualization."""
        try:
            visualization_id = str(uuid4())

            db_viz = GraphSpecs(
                visualization_id=visualization_id,
                **viz_data.dict(),
                created_by=created_by,
                updated_by=created_by
            )

            self.db.add(db_viz)
            self.db.commit()
            self.db.refresh(db_viz)

            return VisualizationResponse.from_orm(db_viz)
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create visualization: {str(e)}")

    def get_visualizations(self, pagination: PaginationParams, dashboard_id: Optional[str] = None,
                          widget_type: Optional[str] = None, created_by: Optional[str] = None) -> PaginatedResponse[VisualizationResponse]:
        """Get paginated list of visualizations."""
        try:
            query = self.db.query(GraphSpecs).order_by(GraphSpecs.created_at.desc())

            if dashboard_id:
                query = query.filter(GraphSpecs.dashboard_id == dashboard_id)
            if widget_type:
                query = query.filter(GraphSpecs.widget_type == widget_type)
            if created_by:
                query = query.filter(GraphSpecs.created_by == created_by)

            visualizations_list, total = paginate_query(query, pagination)

            viz_responses = [VisualizationResponse.from_orm(viz) for viz in visualizations_list]

            return PaginatedResponse.create(viz_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get visualizations: {str(e)}")

    def get_visualization_by_id(self, visualization_id: str) -> VisualizationResponse:
        """Get visualization by ID."""
        try:
            visualization = self.db.query(GraphSpecs).filter(
                GraphSpecs.visualization_id == visualization_id
            ).first()

            if not visualization:
                raise HTTPException(status_code=404, detail="Visualization not found")

            return VisualizationResponse.from_orm(visualization)
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get visualization: {str(e)}")

    def update_visualization(self, visualization_id: str, viz_data: VisualizationUpdate, updated_by: str) -> VisualizationResponse:
        """Update an existing visualization."""
        try:
            visualization = self.db.query(GraphSpecs).filter(
                GraphSpecs.visualization_id == visualization_id
            ).first()

            if not visualization:
                raise HTTPException(status_code=404, detail="Visualization not found")

            update_data = viz_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(visualization, field, value)

            visualization.updated_by = updated_by
            visualization.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(visualization)

            return VisualizationResponse.from_orm(visualization)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update visualization: {str(e)}")

    def delete_visualization(self, visualization_id: str, deleted_by: str) -> Dict[str, str]:
        """Delete a visualization."""
        try:
            visualization = self.db.query(GraphSpecs).filter(
                GraphSpecs.visualization_id == visualization_id
            ).first()

            if not visualization:
                raise HTTPException(status_code=404, detail="Visualization not found")

            self.db.delete(visualization)
            self.db.commit()

            return {"status": "success", "message": "Visualization deleted successfully"}
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to delete visualization: {str(e)}")

    def execute_visualization(self, visualization_id: str, filters: Dict[str, Any] = None) -> VisualizationDataResponse:
        """Execute visualization and return data."""
        try:
            visualization = self.db.query(GraphSpecs).filter(
                GraphSpecs.visualization_id == visualization_id
            ).first()

            if not visualization:
                raise HTTPException(status_code=404, detail="Visualization not found")

            # Execute the visualization based on code_type
            if visualization.code_type == "sql" and visualization.query:
                data = self._execute_sql_query(visualization.query, filters)
            elif visualization.code_type == "python" and visualization.code:
                data = self._execute_python_code(visualization.code, filters)
            else:
                data = {"message": "No executable code found"}

            viz_response = VisualizationResponse.from_orm(visualization)

            return VisualizationDataResponse(
                visualization=viz_response,
                data=data,
                metadata={"executed_at": datetime.now().isoformat(), "filters_applied": filters}
            )
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to execute visualization: {str(e)}")

    def _execute_sql_query(self, query: str, filters: Dict[str, Any] = None) -> Any:
        """Execute SQL query and return results."""
        try:
            # Apply filters to query if provided
            if filters:
                # This is a simplified filter application
                # In a real implementation, you'd want proper SQL injection protection
                for key, value in filters.items():
                    if isinstance(value, str):
                        query = query.replace(f":{key}", f"'{value}'")
                    else:
                        query = query.replace(f":{key}", str(value))

            result = self.db.execute(text(query))

            # Convert result to list of dictionaries
            columns = result.keys()
            data = [dict(zip(columns, row)) for row in result.fetchall()]

            return {
                "data": data,
                "columns": list(columns),
                "row_count": len(data)
            }
        except Exception as e:
            logger.error(f"Failed to execute SQL query: {str(e)}")
            return {"error": f"Query execution failed: {str(e)}"}

    def _execute_python_code(self, code: str, filters: Dict[str, Any] = None) -> Any:
        """Execute Python code and return results."""
        try:
            # This is a simplified implementation
            # In a real system, you'd want to use a sandboxed environment
            local_vars = {"filters": filters or {}, "db": self.db}

            # Execute the code
            exec(code, {"__builtins__": {}}, local_vars)

            # Return the result if it exists
            return local_vars.get("result", {"message": "Code executed successfully"})
        except Exception as e:
            logger.error(f"Failed to execute Python code: {str(e)}")
            return {"error": f"Code execution failed: {str(e)}"}

    def add_visualization_to_dashboard(self, visualization_id: str, dashboard_id: str, updated_by: str) -> VisualizationResponse:
        """Add an existing visualization to a dashboard."""
        try:
            visualization = self.db.query(GraphSpecs).filter(
                GraphSpecs.visualization_id == visualization_id
            ).first()

            if not visualization:
                raise HTTPException(status_code=404, detail="Visualization not found")

            # Check if dashboard exists
            dashboard = self.db.query(Dashboard).filter(
                Dashboard.dashboard_id == dashboard_id
            ).first()

            if not dashboard:
                raise HTTPException(status_code=404, detail="Dashboard not found")

            visualization.dashboard_id = dashboard_id
            visualization.updated_by = updated_by
            visualization.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(visualization)

            return VisualizationResponse.from_orm(visualization)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to add visualization to dashboard: {str(e)}")

    def remove_visualization_from_dashboard(self, visualization_id: str, updated_by: str) -> VisualizationResponse:
        """Remove a visualization from its dashboard."""
        try:
            visualization = self.db.query(GraphSpecs).filter(
                GraphSpecs.visualization_id == visualization_id
            ).first()

            if not visualization:
                raise HTTPException(status_code=404, detail="Visualization not found")

            visualization.dashboard_id = None
            visualization.updated_by = updated_by
            visualization.updated_at = datetime.now()

            self.db.commit()
            self.db.refresh(visualization)

            return VisualizationResponse.from_orm(visualization)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to remove visualization from dashboard: {str(e)}")

    # Dashboard Templates Management
    def create_dashboard_template(self, template_data: DashboardTemplateCreate, created_by: str) -> DashboardTemplateResponse:
        """Create a new dashboard template."""
        try:
            # Check if template name already exists
            existing = self.db.query(dashboard_templates).filter(
                dashboard_templates.template_name == template_data.template_name
            ).first()

            if existing:
                raise HTTPException(
                    status_code=400,
                    detail=f"Template '{template_data.template_name}' already exists"
                )

            db_template = dashboard_templates(
                **template_data.dict(),
                created_by=created_by
            )

            self.db.add(db_template)
            self.db.commit()
            self.db.refresh(db_template)

            return DashboardTemplateResponse.from_orm(db_template)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create dashboard template: {str(e)}")

    def get_dashboard_templates(self, pagination: PaginationParams, category: Optional[str] = None,
                               is_system_template: Optional[bool] = None) -> PaginatedResponse[DashboardTemplateResponse]:
        """Get paginated list of dashboard templates."""
        try:
            query = self.db.query(dashboard_templates).filter(
                dashboard_templates.is_active == True
            ).order_by(dashboard_templates.usage_count.desc())

            if category:
                query = query.filter(dashboard_templates.template_category == category)
            if is_system_template is not None:
                query = query.filter(dashboard_templates.is_system_template == is_system_template)

            templates_list, total = paginate_query(query, pagination)

            template_responses = [DashboardTemplateResponse.from_orm(template) for template in templates_list]

            return PaginatedResponse.create(template_responses, total, pagination.page, pagination.size)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get dashboard templates: {str(e)}")

    def get_dashboard_template_by_id(self, template_id: UUID) -> DashboardTemplateResponse:
        """Get dashboard template by ID."""
        try:
            template = self.db.query(dashboard_templates).filter(
                dashboard_templates.id == template_id
            ).first()

            if not template:
                raise HTTPException(status_code=404, detail="Dashboard template not found")

            return DashboardTemplateResponse.from_orm(template)
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get dashboard template: {str(e)}")

    def create_dashboard_from_template(self, template_id: UUID, dashboard_name: str, created_by: str) -> DashboardResponse:
        """Create a new dashboard from a template."""
        try:
            template = self.db.query(dashboard_templates).filter(
                dashboard_templates.id == template_id
            ).first()

            if not template:
                raise HTTPException(status_code=404, detail="Dashboard template not found")

            # Create dashboard from template
            dashboard_id = str(uuid4())

            db_dashboard = Dashboard(
                dashboard_id=dashboard_id,
                name=dashboard_name,
                description=f"Created from template: {template.template_name}",
                dash_type="user",
                layout_config=template.layout_config,
                theme_config=template.theme_config,
                created_by=created_by
            )

            self.db.add(db_dashboard)

            # Create default widgets if specified in template
            if template.default_widgets:
                for widget_config in template.default_widgets.get("widgets", []):
                    viz_id = str(uuid4())
                    db_viz = GraphSpecs(
                        visualization_id=viz_id,
                        dashboard_id=dashboard_id,
                        title=widget_config.get("title", "Widget"),
                        widget_type=widget_config.get("widget_type", "chart"),
                        config=widget_config.get("config", {}),
                        code_type=widget_config.get("code_type", "sql"),
                        query=widget_config.get("query", ""),
                        created_by=created_by,
                        updated_by=created_by
                    )
                    self.db.add(db_viz)

            # Increment template usage count
            template.usage_count += 1

            self.db.commit()
            self.db.refresh(db_dashboard)

            return DashboardResponse.from_orm(db_dashboard)
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create dashboard from template: {str(e)}")

    # Dashboard Analytics and Statistics
    def get_dashboard_statistics(self) -> DashboardStatistics:
        """Get comprehensive dashboard statistics."""
        try:
            # Total dashboards
            total_dashboards = self.db.query(func.count(Dashboard.dashboard_id)).scalar() or 0

            # Dashboards by type
            type_stats = self.db.query(
                Dashboard.dash_type,
                func.count(Dashboard.dashboard_id)
            ).group_by(Dashboard.dash_type).all()
            dashboards_by_type = {dash_type: count for dash_type, count in type_stats}

            # Dashboards by user (top 10)
            user_stats = self.db.query(
                Dashboard.created_by,
                func.count(Dashboard.dashboard_id)
            ).group_by(Dashboard.created_by).order_by(
                func.count(Dashboard.dashboard_id).desc()
            ).limit(10).all()
            dashboards_by_user = {user: count for user, count in user_stats}

            # Most viewed dashboards (based on analytics)
            most_viewed = self.db.query(
                dashboard_analytics.dashboard_id,
                func.count(dashboard_analytics.id).label('view_count')
            ).filter(
                dashboard_analytics.action_type == 'view'
            ).group_by(
                dashboard_analytics.dashboard_id
            ).order_by(
                func.count(dashboard_analytics.id).desc()
            ).limit(10).all()

            most_viewed_dashboards = []
            for dashboard_id, view_count in most_viewed:
                dashboard = self.db.query(Dashboard).filter(
                    Dashboard.dashboard_id == dashboard_id
                ).first()
                if dashboard:
                    most_viewed_dashboards.append({
                        "dashboard_id": dashboard_id,
                        "name": dashboard.name,
                        "view_count": view_count
                    })

            # Most used widget types
            widget_stats = self.db.query(
                GraphSpecs.widget_type,
                func.count(GraphSpecs.visualization_id)
            ).group_by(GraphSpecs.widget_type).all()
            most_used_widgets = {widget_type: count for widget_type, count in widget_stats}

            # Average widgets per dashboard
            total_visualizations = self.db.query(func.count(GraphSpecs.visualization_id)).scalar() or 0
            avg_widgets = total_visualizations / total_dashboards if total_dashboards > 0 else 0

            # Export statistics
            total_exports = self.db.query(func.count(dashboard_exports.id)).scalar() or 0
            export_stats = self.db.query(
                dashboard_exports.export_format,
                func.count(dashboard_exports.id)
            ).group_by(dashboard_exports.export_format).all()
            exports_by_format = {format_type: count for format_type, count in export_stats}

            # Active users and views in last 30 days
            thirty_days_ago = datetime.now() - timedelta(days=30)
            active_users = self.db.query(func.count(func.distinct(dashboard_analytics.user_id))).filter(
                dashboard_analytics.created_at >= thirty_days_ago
            ).scalar() or 0

            dashboard_views = self.db.query(func.count(dashboard_analytics.id)).filter(
                dashboard_analytics.action_type == 'view',
                dashboard_analytics.created_at >= thirty_days_ago
            ).scalar() or 0

            return DashboardStatistics(
                total_dashboards=total_dashboards,
                dashboards_by_type=dashboards_by_type,
                dashboards_by_user=dashboards_by_user,
                most_viewed_dashboards=most_viewed_dashboards,
                most_used_widgets=most_used_widgets,
                average_widgets_per_dashboard=avg_widgets,
                total_visualizations=total_visualizations,
                total_exports=total_exports,
                exports_by_format=exports_by_format,
                active_users_last_30_days=active_users,
                dashboard_views_last_30_days=dashboard_views
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get dashboard statistics: {str(e)}")

    def get_dashboard_usage_analytics(self, dashboard_id: str) -> DashboardUsageAnalytics:
        """Get usage analytics for a specific dashboard."""
        try:
            # Check if dashboard exists
            dashboard = self.db.query(Dashboard).filter(
                Dashboard.dashboard_id == dashboard_id
            ).first()

            if not dashboard:
                raise HTTPException(status_code=404, detail="Dashboard not found")

            # Total views
            total_views = self.db.query(func.count(dashboard_analytics.id)).filter(
                dashboard_analytics.dashboard_id == dashboard_id,
                dashboard_analytics.action_type == 'view'
            ).scalar() or 0

            # Unique viewers
            unique_viewers = self.db.query(func.count(func.distinct(dashboard_analytics.user_id))).filter(
                dashboard_analytics.dashboard_id == dashboard_id,
                dashboard_analytics.action_type == 'view'
            ).scalar() or 0

            # Average session duration
            avg_duration = self.db.query(func.avg(dashboard_analytics.duration_seconds)).filter(
                dashboard_analytics.dashboard_id == dashboard_id,
                dashboard_analytics.duration_seconds.isnot(None)
            ).scalar() or 0.0

            # Most interacted widgets
            widget_interactions = self.db.query(dashboard_analytics.widgets_interacted).filter(
                dashboard_analytics.dashboard_id == dashboard_id,
                dashboard_analytics.widgets_interacted.isnot(None)
            ).all()

            all_widgets = []
            for interaction in widget_interactions:
                if interaction[0]:  # widgets_interacted is not None
                    all_widgets.extend(interaction[0])

            most_interacted_widgets = list(set(all_widgets))[:10]  # Top 10 unique widgets

            # Common filters (simplified)
            common_filters = {}

            # Export count
            export_count = self.db.query(func.count(dashboard_exports.id)).filter(
                dashboard_exports.dashboard_id == dashboard_id
            ).scalar() or 0

            # Share count
            share_count = self.db.query(func.count(dashboard_shares.id)).filter(
                dashboard_shares.dashboard_id == dashboard_id
            ).scalar() or 0

            # Last accessed
            last_access = self.db.query(func.max(dashboard_analytics.created_at)).filter(
                dashboard_analytics.dashboard_id == dashboard_id
            ).scalar()

            return DashboardUsageAnalytics(
                dashboard_id=dashboard_id,
                total_views=total_views,
                unique_viewers=unique_viewers,
                average_session_duration=float(avg_duration),
                most_interacted_widgets=most_interacted_widgets,
                common_filters=common_filters,
                export_count=export_count,
                share_count=share_count,
                last_accessed=last_access
            )
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get dashboard usage analytics: {str(e)}")