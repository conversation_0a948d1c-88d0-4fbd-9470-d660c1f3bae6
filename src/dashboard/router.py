# dashboard router - dashboard management and visualization endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime
import os

from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    DashboardCreate, DashboardUpdate, DashboardResponse,
    VisualizationCreate, VisualizationUpdate, VisualizationResponse,
    DashboardTemplateCreate, DashboardTemplateUpdate, DashboardTemplateResponse,
    DashboardShareCreate, DashboardShareResponse,
    DashboardAnalyticsCreate, DashboardAnalyticsResponse,
    DashboardAlertCreate, DashboardAlertUpdate, DashboardAlertResponse,
    DashboardExportCreate, DashboardExportResponse,
    DashboardFilterCreate, DashboardFilterUpdate, DashboardFilterResponse,
    DashboardCommentCreate, DashboardCommentUpdate, DashboardCommentResponse,
    DashboardVersionCreate, DashboardVersionResponse,
    DashboardDataResponse, VisualizationDataResponse,
    DashboardStatistics, DashboardUsageAnalytics,
    DashboardListResponse, VisualizationListResponse, DashboardTemplateListResponse,
    LegacyDashboardResponse, CustomJSONResponse, LegacyDashboardCreate,
    LegacyVisualizationUpdate, VisualizationDashboardRequest
)
from .service import DashboardService

router = APIRouter()

def get_dashboard_service(db: Session = Depends(get_db)) -> DashboardService:
    return DashboardService(db)

# Dashboard Management Endpoints
@router.post("/dashboards", response_model=DashboardResponse, status_code=201)
async def create_dashboard(
    dashboard: DashboardCreate,
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new dashboard."""
    return dashboard_service.create_dashboard(dashboard, current_user.email)

@router.get("/dashboards", response_model=DashboardListResponse)
async def get_dashboards(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    dash_type: Optional[str] = Query(None, description="Filter by dashboard type"),
    created_by: Optional[str] = Query(None, description="Filter by creator"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of dashboards."""
    pagination = PaginationParams(page=page, size=size)
    result = dashboard_service.get_dashboards(pagination, dash_type, created_by, tags)
    return {"data": result.items}

@router.get("/dashboards/{dashboard_id}", response_model=DashboardResponse)
async def get_dashboard(
    dashboard_id: str = Path(..., description="The dashboard ID"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Get dashboard by ID."""
    return dashboard_service.get_dashboard_by_id(dashboard_id, current_user.email)

@router.put("/dashboards/{dashboard_id}", response_model=DashboardResponse)
async def update_dashboard(
    dashboard_id: str = Path(..., description="The dashboard ID"),
    dashboard_data: DashboardUpdate = None,
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Update an existing dashboard."""
    return dashboard_service.update_dashboard(dashboard_id, dashboard_data, current_user.email)

@router.delete("/dashboards/{dashboard_id}")
async def delete_dashboard(
    dashboard_id: str = Path(..., description="The dashboard ID"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Delete a dashboard."""
    return dashboard_service.delete_dashboard(dashboard_id, current_user.email)

# Visualization Management Endpoints
@router.post("/visualizations", response_model=VisualizationResponse, status_code=201)
async def create_visualization(
    visualization: VisualizationCreate,
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new visualization."""
    return dashboard_service.create_visualization(visualization, current_user.email)

@router.get("/visualizations", response_model=VisualizationListResponse)
async def get_visualizations(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    dashboard_id: Optional[str] = Query(None, description="Filter by dashboard ID"),
    widget_type: Optional[str] = Query(None, description="Filter by widget type"),
    created_by: Optional[str] = Query(None, description="Filter by creator"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of visualizations."""
    pagination = PaginationParams(page=page, size=size)
    result = dashboard_service.get_visualizations(pagination, dashboard_id, widget_type, created_by)
    return {"data": result.items}

@router.get("/visualizations/{visualization_id}", response_model=VisualizationResponse)
async def get_visualization(
    visualization_id: str = Path(..., description="The visualization ID"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Get visualization by ID."""
    return dashboard_service.get_visualization_by_id(visualization_id)

@router.put("/visualizations/{visualization_id}", response_model=VisualizationResponse)
async def update_visualization(
    visualization_id: str = Path(..., description="The visualization ID"),
    visualization_data: VisualizationUpdate = None,
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Update an existing visualization."""
    return dashboard_service.update_visualization(visualization_id, visualization_data, current_user.email)

@router.delete("/visualizations/{visualization_id}")
async def delete_visualization(
    visualization_id: str = Path(..., description="The visualization ID"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Delete a visualization."""
    return dashboard_service.delete_visualization(visualization_id, current_user.email)

@router.post("/visualizations/{visualization_id}/execute", response_model=VisualizationDataResponse)
async def execute_visualization(
    visualization_id: str = Path(..., description="The visualization ID"),
    filters: Optional[Dict[str, Any]] = None,
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Execute visualization and return data."""
    return dashboard_service.execute_visualization(visualization_id, filters)

@router.post("/visualizations/{visualization_id}/dashboard", response_model=VisualizationResponse)
async def add_visualization_to_dashboard(
    visualization_id: str = Path(..., description="The visualization ID"),
    request: VisualizationDashboardRequest = None,
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Add visualization to dashboard."""
    return dashboard_service.add_visualization_to_dashboard(
        visualization_id, request.dashboard_id, current_user.email
    )

@router.delete("/visualizations/{visualization_id}/dashboard", response_model=VisualizationResponse)
async def remove_visualization_from_dashboard(
    visualization_id: str = Path(..., description="The visualization ID"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Remove visualization from dashboard."""
    return dashboard_service.remove_visualization_from_dashboard(visualization_id, current_user.email)

# Dashboard Templates Endpoints
@router.post("/templates", response_model=DashboardTemplateResponse, status_code=201)
async def create_dashboard_template(
    template: DashboardTemplateCreate,
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new dashboard template."""
    return dashboard_service.create_dashboard_template(template, current_user.email)

@router.get("/templates", response_model=DashboardTemplateListResponse)
async def get_dashboard_templates(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    category: Optional[str] = Query(None, description="Filter by category"),
    is_system_template: Optional[bool] = Query(None, description="Filter by system template"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of dashboard templates."""
    pagination = PaginationParams(page=page, size=size)
    result = dashboard_service.get_dashboard_templates(pagination, category, is_system_template)
    return {"data": result.items}

@router.get("/templates/{template_id}", response_model=DashboardTemplateResponse)
async def get_dashboard_template(
    template_id: UUID = Path(..., description="The template ID"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Get dashboard template by ID."""
    return dashboard_service.get_dashboard_template_by_id(template_id)

@router.post("/templates/{template_id}/create-dashboard", response_model=DashboardResponse)
async def create_dashboard_from_template(
    template_id: UUID = Path(..., description="The template ID"),
    dashboard_name: str = Query(..., description="Name for the new dashboard"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new dashboard from a template."""
    return dashboard_service.create_dashboard_from_template(template_id, dashboard_name, current_user.email)

# Analytics and Statistics Endpoints
@router.get("/statistics", response_model=DashboardStatistics)
async def get_dashboard_statistics(
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive dashboard statistics."""
    return dashboard_service.get_dashboard_statistics()

@router.get("/dashboards/{dashboard_id}/analytics", response_model=DashboardUsageAnalytics)
async def get_dashboard_usage_analytics(
    dashboard_id: str = Path(..., description="The dashboard ID"),
    dashboard_service: DashboardService = Depends(get_dashboard_service),
    current_user: User = Depends(get_current_user)
):
    """Get usage analytics for a specific dashboard."""
    return dashboard_service.get_dashboard_usage_analytics(dashboard_id)

# Legacy Endpoints (for backward compatibility)
@router.post("/create-dashboard", response_model=LegacyDashboardResponse, include_in_schema=False)
async def create_dashboard_legacy(
    dashboard: LegacyDashboardCreate,
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """Legacy endpoint for creating dashboard."""
    try:
        dashboard_data = DashboardCreate(
            name=dashboard.name,
            description=dashboard.description
        )
        result = dashboard_service.create_dashboard(dashboard_data, "legacy_user")
        return LegacyDashboardResponse(
            success=True,
            message="Dashboard created successfully",
            data={"dashboard_id": result.dashboard_id}
        )
    except Exception as e:
        return LegacyDashboardResponse(
            success=False,
            message=f"Failed to create dashboard: {str(e)}"
        )

@router.post("/update-visualization", response_model=LegacyDashboardResponse, include_in_schema=False)
async def update_visualization_legacy(
    visualization: LegacyVisualizationUpdate,
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """Legacy endpoint for updating visualization."""
    try:
        if visualization.visualization_id:
            # Update existing visualization
            viz_data = VisualizationUpdate(
                title=visualization.title,
                description=visualization.description,
                dashboard_id=visualization.dashboard_id,
                config=visualization.config,
                code_type=visualization.code_type,
                code=visualization.code,
                query=visualization.query
            )
            result = dashboard_service.update_visualization(
                visualization.visualization_id, viz_data, "legacy_user"
            )
            return LegacyDashboardResponse(
                success=True,
                message="Visualization updated successfully",
                data={"visualization_id": result.visualization_id}
            )
        else:
            # Create new visualization
            viz_data = VisualizationCreate(
                title=visualization.title or "Untitled Visualization",
                description=visualization.description,
                dashboard_id=visualization.dashboard_id,
                chat_id=visualization.chat_id,
                config=visualization.config,
                code_type=visualization.code_type or "sql",
                code=visualization.code,
                query=visualization.query
            )
            result = dashboard_service.create_visualization(viz_data, "legacy_user")
            return LegacyDashboardResponse(
                success=True,
                message="Visualization created successfully",
                data={"visualization_id": result.visualization_id}
            )
    except Exception as e:
        return LegacyDashboardResponse(
            success=False,
            message=f"Failed to update visualization: {str(e)}"
        )

@router.get("/get-dashboard/{dashboard_id}", response_model=CustomJSONResponse, include_in_schema=False)
async def get_dashboard_legacy(
    dashboard_id: str = Path(..., description="The dashboard ID"),
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """Legacy endpoint for getting dashboard."""
    try:
        dashboard = dashboard_service.get_dashboard_by_id(dashboard_id)
        return CustomJSONResponse(
            data=dashboard.dict(),
            status="success",
            message="Dashboard retrieved successfully"
        )
    except Exception as e:
        return CustomJSONResponse(
            data=None,
            status="error",
            message=f"Failed to get dashboard: {str(e)}"
        )

@router.get("/get-visualizations/{dashboard_id}", response_model=CustomJSONResponse, include_in_schema=False)
async def get_dashboard_visualizations_legacy(
    dashboard_id: str = Path(..., description="The dashboard ID"),
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """Legacy endpoint for getting dashboard visualizations."""
    try:
        pagination = PaginationParams(page=1, size=100)  # Get all visualizations
        result = dashboard_service.get_visualizations(pagination, dashboard_id=dashboard_id)
        return CustomJSONResponse(
            data=[viz.dict() for viz in result.items],
            status="success",
            message="Visualizations retrieved successfully"
        )
    except Exception as e:
        return CustomJSONResponse(
            data=[],
            status="error",
            message=f"Failed to get visualizations: {str(e)}"
        )

@router.post("/execute-visualization/{visualization_id}", response_model=CustomJSONResponse, include_in_schema=False)
async def execute_visualization_legacy(
    visualization_id: str = Path(..., description="The visualization ID"),
    filters: Optional[Dict[str, Any]] = None,
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """Legacy endpoint for executing visualization."""
    try:
        result = dashboard_service.execute_visualization(visualization_id, filters)
        return CustomJSONResponse(
            data=result.dict(),
            status="success",
            message="Visualization executed successfully"
        )
    except Exception as e:
        return CustomJSONResponse(
            data=None,
            status="error",
            message=f"Failed to execute visualization: {str(e)}"
        )

# Health Check Endpoints
@router.get("/ping")
async def ping():
    """Simple health check endpoint."""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@router.get("/version")
async def get_version():
    """Get dashboard module version information."""
    return {
        "module": "dashboard",
        "version": "1.0.0",
        "environment": os.getenv("ENVIRONMENT", "development"),
        "features": [
            "dashboard_management",
            "visualization_engine",
            "template_system",
            "analytics_tracking",
            "legacy_compatibility"
        ]
    }

@router.get("/health-check")
async def health_check(
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """Comprehensive health check for dashboard module."""
    try:
        # Test database connectivity
        dashboard_service.db.execute(text("SELECT 1"))

        return {
            "status": "healthy",
            "module": "dashboard",
            "database": "connected",
            "features": {
                "dashboard_crud": "operational",
                "visualization_engine": "operational",
                "template_system": "operational",
                "analytics": "operational"
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "module": "dashboard",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }