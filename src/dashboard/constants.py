# dashboard constants - module-specific constants for dashboard

# Dashboard types
DASHBOARD_TYPE_USER = "user"
DASHBOARD_TYPE_SYSTEM = "system"
DASHBOARD_TYPE_TEMPLATE = "template"
DASHBOARD_TYPE_SHARED = "shared"

DASHBOARD_TYPES = [
    DASHBOARD_TYPE_USER,
    DASHBOARD_TYPE_SYSTEM,
    DASHBOARD_TYPE_TEMPLATE,
    DASHBOARD_TYPE_SHARED
]

# Widget/Visualization types
WIDGET_TYPE_CHART = "chart"
WIDGET_TYPE_TABLE = "table"
WIDGET_TYPE_METRIC = "metric"
WIDGET_TYPE_TEXT = "text"
WIDGET_TYPE_MAP = "map"
WIDGET_TYPE_GAUGE = "gauge"
WIDGET_TYPE_HEATMAP = "heatmap"
WIDGET_TYPE_SCATTER = "scatter"
WIDGET_TYPE_LINE = "line"
WIDGET_TYPE_BAR = "bar"
WIDGET_TYPE_PIE = "pie"
WIDGET_TYPE_AREA = "area"
WIDGET_TYPE_HISTOGRAM = "histogram"
WIDGET_TYPE_BOX_PLOT = "box_plot"
WIDGET_TYPE_TREEMAP = "treemap"
WIDGET_TYPE_SANKEY = "sankey"
WIDGET_TYPE_FUNNEL = "funnel"
WIDGET_TYPE_WATERFALL = "waterfall"

WIDGET_TYPES = [
    WIDGET_TYPE_CHART,
    WIDGET_TYPE_TABLE,
    WIDGET_TYPE_METRIC,
    WIDGET_TYPE_TEXT,
    WIDGET_TYPE_MAP,
    WIDGET_TYPE_GAUGE,
    WIDGET_TYPE_HEATMAP,
    WIDGET_TYPE_SCATTER,
    WIDGET_TYPE_LINE,
    WIDGET_TYPE_BAR,
    WIDGET_TYPE_PIE,
    WIDGET_TYPE_AREA,
    WIDGET_TYPE_HISTOGRAM,
    WIDGET_TYPE_BOX_PLOT,
    WIDGET_TYPE_TREEMAP,
    WIDGET_TYPE_SANKEY,
    WIDGET_TYPE_FUNNEL,
    WIDGET_TYPE_WATERFALL
]

# Code types for visualizations
CODE_TYPE_SQL = "sql"
CODE_TYPE_PYTHON = "python"
CODE_TYPE_QUERY = "query"
CODE_TYPE_JSON = "json"
CODE_TYPE_JAVASCRIPT = "javascript"

CODE_TYPES = [
    CODE_TYPE_SQL,
    CODE_TYPE_PYTHON,
    CODE_TYPE_QUERY,
    CODE_TYPE_JSON,
    CODE_TYPE_JAVASCRIPT
]

# Dashboard template categories
TEMPLATE_CATEGORY_ANALYTICS = "analytics"
TEMPLATE_CATEGORY_MONITORING = "monitoring"
TEMPLATE_CATEGORY_REPORTING = "reporting"
TEMPLATE_CATEGORY_BUSINESS = "business"
TEMPLATE_CATEGORY_FINANCIAL = "financial"
TEMPLATE_CATEGORY_OPERATIONAL = "operational"
TEMPLATE_CATEGORY_MARKETING = "marketing"
TEMPLATE_CATEGORY_SALES = "sales"
TEMPLATE_CATEGORY_CUSTOM = "custom"

TEMPLATE_CATEGORIES = [
    TEMPLATE_CATEGORY_ANALYTICS,
    TEMPLATE_CATEGORY_MONITORING,
    TEMPLATE_CATEGORY_REPORTING,
    TEMPLATE_CATEGORY_BUSINESS,
    TEMPLATE_CATEGORY_FINANCIAL,
    TEMPLATE_CATEGORY_OPERATIONAL,
    TEMPLATE_CATEGORY_MARKETING,
    TEMPLATE_CATEGORY_SALES,
    TEMPLATE_CATEGORY_CUSTOM
]

# Dashboard sharing permission levels
PERMISSION_LEVEL_VIEW = "view"
PERMISSION_LEVEL_EDIT = "edit"
PERMISSION_LEVEL_ADMIN = "admin"
PERMISSION_LEVEL_COMMENT = "comment"

PERMISSION_LEVELS = [
    PERMISSION_LEVEL_VIEW,
    PERMISSION_LEVEL_EDIT,
    PERMISSION_LEVEL_ADMIN,
    PERMISSION_LEVEL_COMMENT
]

# Dashboard analytics action types
ANALYTICS_ACTION_VIEW = "view"
ANALYTICS_ACTION_EDIT = "edit"
ANALYTICS_ACTION_EXPORT = "export"
ANALYTICS_ACTION_SHARE = "share"
ANALYTICS_ACTION_CREATE = "create"
ANALYTICS_ACTION_DELETE = "delete"
ANALYTICS_ACTION_DUPLICATE = "duplicate"
ANALYTICS_ACTION_FILTER = "filter"
ANALYTICS_ACTION_REFRESH = "refresh"
ANALYTICS_ACTION_DOWNLOAD = "download"

ANALYTICS_ACTIONS = [
    ANALYTICS_ACTION_VIEW,
    ANALYTICS_ACTION_EDIT,
    ANALYTICS_ACTION_EXPORT,
    ANALYTICS_ACTION_SHARE,
    ANALYTICS_ACTION_CREATE,
    ANALYTICS_ACTION_DELETE,
    ANALYTICS_ACTION_DUPLICATE,
    ANALYTICS_ACTION_FILTER,
    ANALYTICS_ACTION_REFRESH,
    ANALYTICS_ACTION_DOWNLOAD
]

# Dashboard alert types
ALERT_TYPE_THRESHOLD = "threshold"
ALERT_TYPE_ANOMALY = "anomaly"
ALERT_TYPE_DATA_QUALITY = "data_quality"
ALERT_TYPE_PERFORMANCE = "performance"
ALERT_TYPE_AVAILABILITY = "availability"

ALERT_TYPES = [
    ALERT_TYPE_THRESHOLD,
    ALERT_TYPE_ANOMALY,
    ALERT_TYPE_DATA_QUALITY,
    ALERT_TYPE_PERFORMANCE,
    ALERT_TYPE_AVAILABILITY
]

# Export formats
EXPORT_FORMAT_PDF = "pdf"
EXPORT_FORMAT_PNG = "png"
EXPORT_FORMAT_JPG = "jpg"
EXPORT_FORMAT_SVG = "svg"
EXPORT_FORMAT_CSV = "csv"
EXPORT_FORMAT_EXCEL = "excel"
EXPORT_FORMAT_JSON = "json"
EXPORT_FORMAT_HTML = "html"

EXPORT_FORMATS = [
    EXPORT_FORMAT_PDF,
    EXPORT_FORMAT_PNG,
    EXPORT_FORMAT_JPG,
    EXPORT_FORMAT_SVG,
    EXPORT_FORMAT_CSV,
    EXPORT_FORMAT_EXCEL,
    EXPORT_FORMAT_JSON,
    EXPORT_FORMAT_HTML
]

# Export statuses
EXPORT_STATUS_PENDING = "pending"
EXPORT_STATUS_PROCESSING = "processing"
EXPORT_STATUS_COMPLETED = "completed"
EXPORT_STATUS_FAILED = "failed"
EXPORT_STATUS_EXPIRED = "expired"

EXPORT_STATUSES = [
    EXPORT_STATUS_PENDING,
    EXPORT_STATUS_PROCESSING,
    EXPORT_STATUS_COMPLETED,
    EXPORT_STATUS_FAILED,
    EXPORT_STATUS_EXPIRED
]

# Filter types
FILTER_TYPE_DATE_RANGE = "date_range"
FILTER_TYPE_DROPDOWN = "dropdown"
FILTER_TYPE_MULTI_SELECT = "multi_select"
FILTER_TYPE_TEXT = "text"
FILTER_TYPE_NUMBER = "number"
FILTER_TYPE_BOOLEAN = "boolean"
FILTER_TYPE_SLIDER = "slider"
FILTER_TYPE_AUTOCOMPLETE = "autocomplete"

FILTER_TYPES = [
    FILTER_TYPE_DATE_RANGE,
    FILTER_TYPE_DROPDOWN,
    FILTER_TYPE_MULTI_SELECT,
    FILTER_TYPE_TEXT,
    FILTER_TYPE_NUMBER,
    FILTER_TYPE_BOOLEAN,
    FILTER_TYPE_SLIDER,
    FILTER_TYPE_AUTOCOMPLETE
]

# Comment types
COMMENT_TYPE_GENERAL = "general"
COMMENT_TYPE_ANNOTATION = "annotation"
COMMENT_TYPE_FEEDBACK = "feedback"
COMMENT_TYPE_QUESTION = "question"
COMMENT_TYPE_SUGGESTION = "suggestion"

COMMENT_TYPES = [
    COMMENT_TYPE_GENERAL,
    COMMENT_TYPE_ANNOTATION,
    COMMENT_TYPE_FEEDBACK,
    COMMENT_TYPE_QUESTION,
    COMMENT_TYPE_SUGGESTION
]

# Data sources
DATA_SOURCE_DATABASE = "database"
DATA_SOURCE_API = "api"
DATA_SOURCE_FILE = "file"
DATA_SOURCE_STREAM = "stream"
DATA_SOURCE_CACHE = "cache"
DATA_SOURCE_EXTERNAL = "external"

DATA_SOURCES = [
    DATA_SOURCE_DATABASE,
    DATA_SOURCE_API,
    DATA_SOURCE_FILE,
    DATA_SOURCE_STREAM,
    DATA_SOURCE_CACHE,
    DATA_SOURCE_EXTERNAL
]

# Default values
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100
DEFAULT_DASHBOARD_TYPE = DASHBOARD_TYPE_USER
DEFAULT_WIDGET_TYPE = WIDGET_TYPE_CHART
DEFAULT_CODE_TYPE = CODE_TYPE_SQL
DEFAULT_PERMISSION_LEVEL = PERMISSION_LEVEL_VIEW
DEFAULT_EXPORT_FORMAT = EXPORT_FORMAT_PDF
DEFAULT_FILTER_TYPE = FILTER_TYPE_TEXT
DEFAULT_COMMENT_TYPE = COMMENT_TYPE_GENERAL

# Validation limits
MAX_DASHBOARD_NAME_LENGTH = 255
MAX_DASHBOARD_DESCRIPTION_LENGTH = 1000
MAX_WIDGET_TITLE_LENGTH = 255
MAX_WIDGET_DESCRIPTION_LENGTH = 500
MAX_TEMPLATE_NAME_LENGTH = 255
MAX_TEMPLATE_DESCRIPTION_LENGTH = 1000
MAX_COMMENT_TEXT_LENGTH = 2000
MAX_FILTER_NAME_LENGTH = 255
MAX_ALERT_NAME_LENGTH = 255
MAX_EXPORT_NAME_LENGTH = 255

# Grid and layout settings
DEFAULT_GRID_SIZE = 12
DEFAULT_ROW_HEIGHT = 100
DEFAULT_MARGIN = [10, 10]
DEFAULT_CONTAINER_PADDING = [10, 10]
MIN_WIDGET_WIDTH = 1
MIN_WIDGET_HEIGHT = 1
MAX_WIDGET_WIDTH = 12
MAX_WIDGET_HEIGHT = 20

# Refresh intervals (in seconds)
REFRESH_INTERVAL_NEVER = 0
REFRESH_INTERVAL_30_SECONDS = 30
REFRESH_INTERVAL_1_MINUTE = 60
REFRESH_INTERVAL_5_MINUTES = 300
REFRESH_INTERVAL_15_MINUTES = 900
REFRESH_INTERVAL_30_MINUTES = 1800
REFRESH_INTERVAL_1_HOUR = 3600
REFRESH_INTERVAL_6_HOURS = 21600
REFRESH_INTERVAL_12_HOURS = 43200
REFRESH_INTERVAL_24_HOURS = 86400

REFRESH_INTERVALS = [
    REFRESH_INTERVAL_NEVER,
    REFRESH_INTERVAL_30_SECONDS,
    REFRESH_INTERVAL_1_MINUTE,
    REFRESH_INTERVAL_5_MINUTES,
    REFRESH_INTERVAL_15_MINUTES,
    REFRESH_INTERVAL_30_MINUTES,
    REFRESH_INTERVAL_1_HOUR,
    REFRESH_INTERVAL_6_HOURS,
    REFRESH_INTERVAL_12_HOURS,
    REFRESH_INTERVAL_24_HOURS
]

# Color schemes
COLOR_SCHEME_DEFAULT = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"]
COLOR_SCHEME_VIRIDIS = ["#440154", "#482777", "#3f4a8a", "#31678e", "#26838f", "#1f9d8a", "#6cce5a", "#b6de2b", "#fee825"]
COLOR_SCHEME_PLASMA = ["#0d0887", "#5302a3", "#8b0aa5", "#b83289", "#db5c68", "#f48849", "#febd2a", "#f0f921"]
COLOR_SCHEME_BLUES = ["#f7fbff", "#deebf7", "#c6dbef", "#9ecae1", "#6baed6", "#4292c6", "#2171b5", "#08519c", "#08306b"]
COLOR_SCHEME_REDS = ["#fff5f0", "#fee0d2", "#fcbba1", "#fc9272", "#fb6a4a", "#ef3b2c", "#cb181d", "#a50f15", "#67000d"]

COLOR_SCHEMES = {
    "default": COLOR_SCHEME_DEFAULT,
    "viridis": COLOR_SCHEME_VIRIDIS,
    "plasma": COLOR_SCHEME_PLASMA,
    "blues": COLOR_SCHEME_BLUES,
    "reds": COLOR_SCHEME_REDS
}

# Theme settings
THEME_LIGHT = "light"
THEME_DARK = "dark"
THEME_AUTO = "auto"

THEMES = [THEME_LIGHT, THEME_DARK, THEME_AUTO]

# Performance settings
MAX_QUERY_EXECUTION_TIME_SECONDS = 300  # 5 minutes
MAX_PYTHON_EXECUTION_TIME_SECONDS = 60  # 1 minute
MAX_DATA_POINTS_PER_CHART = 10000
MAX_ROWS_PER_TABLE = 1000
MAX_EXPORT_FILE_SIZE_MB = 100

# Cache settings
CACHE_TTL_DASHBOARD_DATA = 300  # 5 minutes
CACHE_TTL_VISUALIZATION_DATA = 180  # 3 minutes
CACHE_TTL_TEMPLATE_DATA = 3600  # 1 hour
CACHE_TTL_ANALYTICS_DATA = 900  # 15 minutes

# File storage settings
DASHBOARD_EXPORT_PATH = "/exports/dashboards"
DASHBOARD_BACKUP_PATH = "/backups/dashboards"
DASHBOARD_TEMP_PATH = "/tmp/dashboards"

# Security settings
ENABLE_PUBLIC_DASHBOARDS = True
ENABLE_DASHBOARD_SHARING = True
ENABLE_DASHBOARD_EXPORTS = True
ENABLE_DASHBOARD_COMMENTS = True
ENABLE_DASHBOARD_VERSIONING = True
ENABLE_ANALYTICS_TRACKING = True

# Rate limiting
RATE_LIMIT_DASHBOARD_CREATION = 10  # per hour
RATE_LIMIT_VISUALIZATION_EXECUTION = 100  # per hour
RATE_LIMIT_EXPORT_GENERATION = 5  # per hour

# Legacy compatibility
LEGACY_ENDPOINT_SUPPORT = True
LEGACY_FORMAT_CONVERSION = True
BACKWARD_COMPATIBILITY_MODE = True

# System limits
MAX_DASHBOARDS_PER_USER = 100
MAX_VISUALIZATIONS_PER_DASHBOARD = 50
MAX_TEMPLATES_PER_USER = 20
MAX_SHARES_PER_DASHBOARD = 100
MAX_COMMENTS_PER_DASHBOARD = 500
MAX_VERSIONS_PER_DASHBOARD = 10

# Analytics retention
ANALYTICS_RETENTION_DAYS = 90
EXPORT_RETENTION_DAYS = 30
COMMENT_RETENTION_DAYS = 365
VERSION_RETENTION_DAYS = 180

# Default chart configurations
DEFAULT_CHART_CONFIG = {
    "responsive": True,
    "animation": True,
    "show_legend": True,
    "show_grid": True,
    "show_tooltip": True,
    "color_scheme": "default"
}

DEFAULT_TABLE_CONFIG = {
    "pagination": True,
    "sorting": True,
    "filtering": True,
    "row_selection": False,
    "export_enabled": True
}

DEFAULT_METRIC_CONFIG = {
    "show_trend": True,
    "show_comparison": True,
    "format": "number",
    "precision": 2
}

# Breakpoints for responsive design
BREAKPOINTS = {
    "xs": 480,
    "sm": 768,
    "md": 1024,
    "lg": 1200,
    "xl": 1600
}

# Grid columns for different breakpoints
GRID_COLS = {
    "xs": 1,
    "sm": 2,
    "md": 6,
    "lg": 12,
    "xl": 12
}

# Module version and metadata
DASHBOARD_MODULE_VERSION = "1.0.0"
DASHBOARD_MODULE_NAME = "dashboard"
DASHBOARD_API_VERSION = "v1"
DASHBOARD_BUILD_DATE = "2024-01-01"

# Feature flags
FEATURE_ADVANCED_ANALYTICS = True
FEATURE_REAL_TIME_UPDATES = True
FEATURE_COLLABORATIVE_EDITING = True
FEATURE_AI_INSIGHTS = False
FEATURE_CUSTOM_THEMES = True
FEATURE_MOBILE_OPTIMIZATION = True