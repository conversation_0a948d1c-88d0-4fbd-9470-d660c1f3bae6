# dashboard models - SQLAlchemy models for dashboard module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Dashboard Models
class Dashboard(Base):
    __tablename__ = 'dashboards'
    __table_args__ = {'schema': 'public'}

    dashboard_id = Column(String(255), primary_key=True)
    customer_id = Column(String(255), nullable=True, index=True)
    organization_id = Column(String(255), nullable=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    dash_type = Column(String(50), nullable=False, default='user')  # user, system, template
    layout_config = Column(JSONB, nullable=True)  # Dashboard layout configuration
    theme_config = Column(JSONB, nullable=True)  # Theme and styling configuration
    filters_config = Column(JSONB, nullable=True)  # Global dashboard filters
    refresh_interval = Column(Integer, nullable=True)  # Auto-refresh interval in seconds
    is_public = Column(Boolean, default=False)  # Public dashboard access
    is_favorite = Column(Boolean, default=False)  # User favorite dashboard
    tags = Column(ARRAY(String), nullable=True)  # Dashboard tags for categorization
    created_by = Column(String(255), nullable=False, index=True)
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.now)

# Visualization/Widget Models
class GraphSpecs(Base):
    __tablename__ = 'visualizations'
    __table_args__ = {'schema': 'public'}

    visualization_id = Column(String(255), primary_key=True)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    dashboard_id = Column(String(255), nullable=True, index=True)
    chat_id = Column(String(255), nullable=True, index=True)
    widget_type = Column(String(50), nullable=False, default='chart')  # chart, table, metric, text
    config = Column(JSONB, nullable=False)  # Visualization configuration
    code_type = Column(String(50), nullable=False)  # python, sql, query
    code = Column(Text, nullable=True)  # Python code for the visualization
    query = Column(Text, nullable=True)  # SQL query for the visualization
    data_source = Column(String(100), nullable=True)  # Data source identifier
    refresh_interval = Column(Integer, nullable=True)  # Widget refresh interval
    position_x = Column(Integer, nullable=True)  # Widget position X
    position_y = Column(Integer, nullable=True)  # Widget position Y
    width = Column(Integer, nullable=True)  # Widget width
    height = Column(Integer, nullable=True)  # Widget height
    is_visible = Column(Boolean, default=True)  # Widget visibility
    created_by = Column(String(255), nullable=False, index=True)
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_by = Column(String(255), nullable=False)
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

# Dashboard Templates
class dashboard_templates(Base):
    __tablename__ = 'dashboard_templates'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    template_name = Column(String(255), nullable=False, unique=True)
    template_description = Column(Text, nullable=True)
    template_category = Column(String(100), nullable=True)  # analytics, monitoring, reporting
    template_config = Column(JSONB, nullable=False)  # Template configuration
    default_widgets = Column(JSONB, nullable=True)  # Default widgets for template
    layout_config = Column(JSONB, nullable=True)  # Default layout configuration
    theme_config = Column(JSONB, nullable=True)  # Default theme configuration
    is_system_template = Column(Boolean, default=False)  # System vs user template
    is_active = Column(Boolean, default=True)
    usage_count = Column(Integer, default=0)  # Template usage tracking
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.now)

# Dashboard Sharing and Permissions
class dashboard_shares(Base):
    __tablename__ = 'dashboard_shares'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dashboard_id = Column(String(255), nullable=False, index=True)
    shared_with_user = Column(String(255), nullable=True, index=True)  # Specific user
    shared_with_role = Column(String(100), nullable=True)  # Role-based sharing
    permission_level = Column(String(50), nullable=False)  # view, edit, admin
    is_public_link = Column(Boolean, default=False)  # Public link sharing
    public_token = Column(String(255), nullable=True, unique=True)  # Public access token
    expires_at = Column(DateTime, nullable=True)  # Share expiration
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)

# Dashboard Analytics and Usage
class dashboard_analytics(Base):
    __tablename__ = 'dashboard_analytics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dashboard_id = Column(String(255), nullable=False, index=True)
    user_id = Column(String(255), nullable=True, index=True)
    action_type = Column(String(50), nullable=False)  # view, edit, export, share
    session_id = Column(String(255), nullable=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    duration_seconds = Column(Float, nullable=True)  # Time spent on dashboard
    widgets_interacted = Column(ARRAY(String), nullable=True)  # Widgets user interacted with
    filters_applied = Column(JSONB, nullable=True)  # Filters applied during session
    export_format = Column(String(50), nullable=True)  # Export format if applicable
    metadata = Column(JSONB, nullable=True)  # Additional analytics metadata
    created_at = Column(DateTime, default=datetime.now, index=True)

# Dashboard Alerts and Notifications
class dashboard_alerts(Base):
    __tablename__ = 'dashboard_alerts'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dashboard_id = Column(String(255), nullable=False, index=True)
    widget_id = Column(String(255), nullable=True, index=True)  # Specific widget alert
    alert_name = Column(String(255), nullable=False)
    alert_description = Column(Text, nullable=True)
    alert_type = Column(String(50), nullable=False)  # threshold, anomaly, data_quality
    condition_config = Column(JSONB, nullable=False)  # Alert condition configuration
    notification_config = Column(JSONB, nullable=True)  # Notification settings
    is_active = Column(Boolean, default=True)
    last_triggered = Column(DateTime, nullable=True)
    trigger_count = Column(Integer, default=0)
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.now)

# Dashboard Exports
class dashboard_exports(Base):
    __tablename__ = 'dashboard_exports'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dashboard_id = Column(String(255), nullable=False, index=True)
    export_name = Column(String(255), nullable=False)
    export_format = Column(String(50), nullable=False)  # pdf, png, csv, excel
    export_config = Column(JSONB, nullable=True)  # Export configuration
    file_path = Column(String(500), nullable=True)
    file_size_bytes = Column(Integer, nullable=True)
    export_status = Column(String(50), nullable=False, default='pending')  # pending, completed, failed
    error_message = Column(Text, nullable=True)
    exported_by = Column(String(255), nullable=False)
    exported_at = Column(DateTime, default=datetime.now)
    expires_at = Column(DateTime, nullable=True)  # Export file expiration

# Dashboard Filters and Parameters
class dashboard_filters(Base):
    __tablename__ = 'dashboard_filters'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dashboard_id = Column(String(255), nullable=False, index=True)
    filter_name = Column(String(255), nullable=False)
    filter_type = Column(String(50), nullable=False)  # date_range, dropdown, multi_select, text
    filter_config = Column(JSONB, nullable=False)  # Filter configuration
    default_value = Column(JSONB, nullable=True)  # Default filter value
    is_required = Column(Boolean, default=False)
    is_global = Column(Boolean, default=True)  # Global vs widget-specific filter
    display_order = Column(Integer, nullable=True)
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.now)

# Dashboard Comments and Annotations
class dashboard_comments(Base):
    __tablename__ = 'dashboard_comments'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dashboard_id = Column(String(255), nullable=False, index=True)
    widget_id = Column(String(255), nullable=True, index=True)  # Widget-specific comment
    comment_text = Column(Text, nullable=False)
    comment_type = Column(String(50), nullable=False, default='general')  # general, annotation, feedback
    position_x = Column(Float, nullable=True)  # Comment position for annotations
    position_y = Column(Float, nullable=True)
    is_resolved = Column(Boolean, default=False)
    parent_comment_id = Column(UUID(as_uuid=True), nullable=True)  # For threaded comments
    created_by = Column(String(255), nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_by = Column(String(255), nullable=True)
    updated_at = Column(DateTime, nullable=True, onupdate=datetime.now)

# Dashboard Versions and History
class dashboard_versions(Base):
    __tablename__ = 'dashboard_versions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dashboard_id = Column(String(255), nullable=False, index=True)
    version_number = Column(Integer, nullable=False)
    version_name = Column(String(255), nullable=True)
    version_description = Column(Text, nullable=True)
    dashboard_config = Column(JSONB, nullable=False)  # Complete dashboard configuration
    widgets_config = Column(JSONB, nullable=False)  # All widgets configuration
    change_summary = Column(Text, nullable=True)  # Summary of changes
    is_current = Column(Boolean, default=False)
    created_by = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now)

# Add indexes for performance
Index('idx_dashboards_created_by_type', Dashboard.created_by, Dashboard.dash_type)
Index('idx_dashboards_customer_org', Dashboard.customer_id, Dashboard.organization_id)
Index('idx_visualizations_dashboard_type', GraphSpecs.dashboard_id, GraphSpecs.widget_type)
Index('idx_visualizations_created_by', GraphSpecs.created_by, GraphSpecs.created_at)
Index('idx_dashboard_analytics_user_action', dashboard_analytics.user_id, dashboard_analytics.action_type)
Index('idx_dashboard_analytics_dashboard_time', dashboard_analytics.dashboard_id, dashboard_analytics.created_at)
Index('idx_dashboard_shares_dashboard_user', dashboard_shares.dashboard_id, dashboard_shares.shared_with_user)
Index('idx_dashboard_alerts_dashboard_active', dashboard_alerts.dashboard_id, dashboard_alerts.is_active)