# credit router - API endpoints for credit module
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from .service import CreditAssessmentService
from .schemas import (
    CreditAssessmentCreate, CreditAssessmentUpdate, CreditAssessmentResponse,
    CreditAssessmentListResponse, CreditScoreResponse, RiskAssessmentResponse,
    CreditAssessmentRequest, CreditScoringRequest, RiskAssessmentRequest,
    CreditDecisionRequest, CreditMonitoringRequest, BulkCreditOperation,
    CreditStatistics, CreditPortfolioAnalytics, CreditAssessmentResult,
    CreditScoringResult, RiskAssessmentResult, CreditDecisionResult,
    CreditMonitoringResult, PaginationParams, CreditFilterParams,
    CustomJSONResponse, LegacyCreditResponse
)
from .dependencies import get_credit_service, get_current_user
from .exceptions import CreditAssessmentNotFoundError

router = APIRouter(prefix="/credit", tags=["credit"])

# Credit Assessment Endpoints
@router.post("/assessments", response_model=CreditAssessmentResponse)
async def create_credit_assessment(
    assessment_data: CreditAssessmentCreate,
    service: CreditAssessmentService = Depends(get_credit_service),
    current_user: dict = Depends(get_current_user)
):
    """Create a new credit assessment."""
    return service.create_credit_assessment(assessment_data, current_user.get("username", "system"))

@router.get("/assessments", response_model=CreditAssessmentListResponse)
async def get_credit_assessments(
    filters: CreditFilterParams = Depends(),
    pagination: PaginationParams = Depends(),
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Get credit assessments with filtering and pagination."""
    assessments, total = service.get_credit_assessments(filters, pagination)

    return CreditAssessmentListResponse(
        items=assessments,
        total=total,
        page=pagination.page,
        size=pagination.size,
        pages=(total + pagination.size - 1) // pagination.size
    )

@router.get("/assessments/{assessment_id}", response_model=CreditAssessmentResponse)
async def get_credit_assessment(
    assessment_id: str = Path(..., description="Credit assessment ID"),
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Get a credit assessment by ID."""
    return service.get_credit_assessment_by_id(assessment_id)

@router.put("/assessments/{assessment_id}", response_model=CreditAssessmentResponse)
async def update_credit_assessment(
    assessment_id: str = Path(..., description="Credit assessment ID"),
    assessment_data: CreditAssessmentUpdate = ...,
    service: CreditAssessmentService = Depends(get_credit_service),
    current_user: dict = Depends(get_current_user)
):
    """Update a credit assessment."""
    return service.update_credit_assessment(assessment_id, assessment_data, current_user.get("username", "system"))

@router.post("/assess", response_model=CreditAssessmentResult)
async def perform_credit_assessment(
    request: CreditAssessmentRequest,
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Perform a comprehensive credit assessment."""
    return service.perform_credit_assessment(request)

@router.post("/score", response_model=CreditScoringResult)
async def calculate_credit_score(
    request: CreditScoringRequest,
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Calculate credit score for a merchant/customer."""
    return service.calculate_credit_score(request)

@router.post("/risk-assessment", response_model=RiskAssessmentResult)
async def perform_risk_assessment(
    request: RiskAssessmentRequest,
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Perform risk assessment."""
    # Implementation would be added to service
    raise HTTPException(status_code=501, detail="Risk assessment endpoint not implemented")

@router.post("/decision", response_model=CreditDecisionResult)
async def make_credit_decision(
    request: CreditDecisionRequest,
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Make a credit decision."""
    # Implementation would be added to service
    raise HTTPException(status_code=501, detail="Credit decision endpoint not implemented")

@router.post("/monitoring", response_model=CreditMonitoringResult)
async def setup_credit_monitoring(
    request: CreditMonitoringRequest,
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Setup credit monitoring."""
    # Implementation would be added to service
    raise HTTPException(status_code=501, detail="Credit monitoring endpoint not implemented")

# Bulk Operations
@router.post("/bulk-operations", response_model=CustomJSONResponse)
async def perform_bulk_operation(
    operation: BulkCreditOperation,
    service: CreditAssessmentService = Depends(get_credit_service),
    current_user: dict = Depends(get_current_user)
):
    """Perform bulk credit operations."""
    # Implementation would be added to service
    raise HTTPException(status_code=501, detail="Bulk operations endpoint not implemented")

# Analytics and Statistics
@router.get("/statistics", response_model=CreditStatistics)
async def get_credit_statistics(
    merchant_id: Optional[UUID] = Query(None, description="Filter by merchant ID"),
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Get credit statistics."""
    # Implementation would be added to service
    raise HTTPException(status_code=501, detail="Credit statistics endpoint not implemented")

@router.get("/portfolio-analytics", response_model=CreditPortfolioAnalytics)
async def get_portfolio_analytics(
    merchant_id: Optional[UUID] = Query(None, description="Filter by merchant ID"),
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Get credit portfolio analytics."""
    # Implementation would be added to service
    raise HTTPException(status_code=501, detail="Portfolio analytics endpoint not implemented")

# Legacy Compatibility Endpoints
@router.post("/legacy/assess", response_model=LegacyCreditResponse)
async def legacy_credit_assessment(
    merchant_id: str,
    assessment_type: str = "initial",
    service: CreditAssessmentService = Depends(get_credit_service)
):
    """Legacy credit assessment endpoint for backward compatibility."""
    try:
        request = CreditAssessmentRequest(
            merchant_id=UUID(merchant_id),
            assessment_type=assessment_type,
            assessment_purpose="loan",
            assessment_method="automated"
        )
        result = service.perform_credit_assessment(request)

        return LegacyCreditResponse(
            success=True,
            message="Credit assessment completed successfully",
            data={
                "assessment_id": result.assessment_id,
                "credit_score": result.overall_credit_score,
                "risk_level": result.risk_level,
                "decision": result.credit_decision
            }
        )
    except Exception as e:
        return LegacyCreditResponse(
            success=False,
            message=f"Credit assessment failed: {str(e)}",
            data=None
        )

# Health Check
@router.get("/health", response_model=CustomJSONResponse)
async def health_check():
    """Credit module health check."""
    return CustomJSONResponse(
        data={"status": "healthy", "module": "credit"},
        message="Credit module is operational"
    )