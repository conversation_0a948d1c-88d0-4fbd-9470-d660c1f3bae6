# credit models - SQLAlchemy models for credit module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Index, Date
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY, DECIMAL, NUMERIC
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

# Credit Assessment Models
class CreditAssessment(Base):
    __tablename__ = 'credit_assessments'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    assessment_id = Column(String(100), unique=True, nullable=False, index=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(String(100), nullable=True, index=True)

    # Assessment details
    assessment_type = Column(String(50), nullable=False)  # initial, periodic, ad_hoc, renewal
    assessment_purpose = Column(String(100), nullable=False)  # loan, credit_line, monitoring, compliance
    assessment_method = Column(String(50), nullable=False)  # automated, manual, hybrid

    # Credit scores
    overall_credit_score = Column(Float, nullable=True)
    financial_score = Column(Float, nullable=True)
    behavioral_score = Column(Float, nullable=True)
    risk_score = Column(Float, nullable=True)
    fraud_score = Column(Float, nullable=True)

    # Risk assessment
    risk_level = Column(String(20), nullable=True)  # low, medium, high, critical
    risk_category = Column(String(50), nullable=True)
    probability_of_default = Column(Float, nullable=True)
    loss_given_default = Column(Float, nullable=True)
    exposure_at_default = Column(Float, nullable=True)
    expected_loss = Column(Float, nullable=True)

    # Credit decision
    credit_decision = Column(String(50), nullable=True)  # approved, rejected, conditional, pending
    recommended_limit = Column(DECIMAL(15,2), nullable=True)
    approved_limit = Column(DECIMAL(15,2), nullable=True)
    interest_rate = Column(Float, nullable=True)
    tenure_months = Column(Integer, nullable=True)

    # Assessment data
    input_data = Column(JSONB, nullable=True)
    score_breakdown = Column(JSONB, nullable=True)
    risk_factors = Column(JSONB, nullable=True)
    mitigating_factors = Column(JSONB, nullable=True)

    # Assessment status
    status = Column(String(50), default='pending')  # pending, in_progress, completed, failed
    confidence_level = Column(Float, nullable=True)
    data_quality_score = Column(Float, nullable=True)

    # Assessment metadata
    model_version = Column(String(50), nullable=True)
    algorithm_used = Column(String(100), nullable=True)
    assessment_date = Column(DateTime, default=datetime.now)
    valid_until = Column(DateTime, nullable=True)

    # Approval workflow
    assessed_by = Column(String(255), nullable=True)
    reviewed_by = Column(String(255), nullable=True)
    approved_by = Column(String(255), nullable=True)
    assessment_notes = Column(Text, nullable=True)
    reviewer_notes = Column(Text, nullable=True)
    approval_notes = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Credit Scores
class credit_scores(Base):
    __tablename__ = 'credit_scores'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    score_id = Column(String(100), unique=True, nullable=False, index=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(String(100), nullable=True, index=True)

    # Score details
    score_type = Column(String(50), nullable=False)  # credit, risk, fraud, behavioral, financial
    score_model = Column(String(100), nullable=False)
    score_version = Column(String(20), nullable=True)

    # Score values
    score_value = Column(Float, nullable=False)
    score_range_min = Column(Float, nullable=True)
    score_range_max = Column(Float, nullable=True)
    score_percentile = Column(Float, nullable=True)
    score_grade = Column(String(10), nullable=True)  # A+, A, B+, B, C+, C, D

    # Score components
    score_components = Column(JSONB, nullable=True)
    component_weights = Column(JSONB, nullable=True)
    score_factors = Column(JSONB, nullable=True)

    # Score metadata
    calculation_method = Column(String(100), nullable=True)
    data_sources = Column(ARRAY(String), nullable=True)
    confidence_level = Column(Float, nullable=True)
    data_completeness = Column(Float, nullable=True)

    # Score validity
    calculated_at = Column(DateTime, default=datetime.now)
    valid_from = Column(DateTime, default=datetime.now)
    valid_until = Column(DateTime, nullable=True)
    is_active = Column(Boolean, default=True)

    # Score history
    previous_score = Column(Float, nullable=True)
    score_change = Column(Float, nullable=True)
    score_trend = Column(String(20), nullable=True)  # improving, stable, declining

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Risk Assessments
class risk_assessments(Base):
    __tablename__ = 'risk_assessments'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Risk scoring
    percentile = Column(String(50), nullable=True)
    percentile_business_category = Column(String(50), nullable=True)
    risk_level = Column(String(50), nullable=True)
    description = Column(Text, nullable=True)
    risk_score = Column(Integer, nullable=True)

    # Risk rankings
    risk_score_025_rank = Column(Integer, nullable=True)
    risk_score_050_rank = Column(Integer, nullable=True)
    risk_score_075_rank = Column(Integer, nullable=True)
    risk_score_business_category_025_rank = Column(Integer, nullable=True)
    risk_score_business_category_050_rank = Column(Integer, nullable=True)
    risk_score_business_category_075_rank = Column(Integer, nullable=True)
    risk_score_max = Column(Integer, nullable=True)
    risk_score_max_business_category = Column(Integer, nullable=True)

    created_at = Column(DateTime, default=datetime.now)

# Risk Categories
class risk_categories(Base):
    __tablename__ = 'risk_categories'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    category = Column(String(50), nullable=False)
    score = Column(Integer, nullable=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.now)

# Risk Indicators
class risk_indicators(Base):
    __tablename__ = 'risk_indicators'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    category = Column(String(50), nullable=False)
    indicator_label = Column(String(50), nullable=False)
    indicator_value = Column(String(50), nullable=True)
    severity = Column(String(50), nullable=True)
    created_at = Column(DateTime, default=datetime.now)

# Risk Metrics
class risk_metrics(Base):
    __tablename__ = 'risk_metrics'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    overall_score = Column(Integer, nullable=True)
    indicator_type = Column(String(100), nullable=True)
    frequency = Column(String(50), nullable=True)
    severity = Column(String(50), nullable=True)
    status = Column(String(50), nullable=True)
    created_at = Column(DateTime, default=datetime.now)

# Credit Ratings
class credit_ratings(Base):
    __tablename__ = 'credit_ratings'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    rating_id = Column(String(100), unique=True, nullable=False, index=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(String(100), nullable=True, index=True)

    # Rating details
    rating_agency = Column(String(100), nullable=False)
    rating_type = Column(String(50), nullable=False)  # long_term, short_term, issuer, issue
    rating_scale = Column(String(50), nullable=False)  # AAA_to_D, numerical, custom

    # Rating values
    current_rating = Column(String(20), nullable=False)
    previous_rating = Column(String(20), nullable=True)
    rating_outlook = Column(String(20), nullable=True)  # positive, stable, negative, developing
    rating_watch = Column(String(20), nullable=True)  # positive, negative, none

    # Rating details
    rating_date = Column(Date, nullable=False)
    effective_date = Column(Date, nullable=True)
    review_date = Column(Date, nullable=True)
    expiry_date = Column(Date, nullable=True)

    # Rating context
    currency = Column(String(10), nullable=True)
    amount = Column(DECIMAL(15,2), nullable=True)
    type_of_loan = Column(String(100), nullable=True)
    rating_details = Column(JSONB, nullable=True)

    # Rating rationale
    rating_rationale = Column(Text, nullable=True)
    key_strengths = Column(ARRAY(String), nullable=True)
    key_weaknesses = Column(ARRAY(String), nullable=True)
    rating_factors = Column(JSONB, nullable=True)

    # Rating status
    is_active = Column(Boolean, default=True)
    is_solicited = Column(Boolean, default=True)
    is_public = Column(Boolean, default=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Credit Decisions
class credit_decisions(Base):
    __tablename__ = 'credit_decisions'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    decision_id = Column(String(100), unique=True, nullable=False, index=True)
    assessment_id = Column(String(100), nullable=False, index=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(String(100), nullable=True, index=True)

    # Decision details
    decision_type = Column(String(50), nullable=False)  # approval, rejection, conditional, review
    decision_method = Column(String(50), nullable=False)  # automated, manual, hybrid
    decision_engine = Column(String(100), nullable=True)

    # Credit terms
    approved_amount = Column(DECIMAL(15,2), nullable=True)
    requested_amount = Column(DECIMAL(15,2), nullable=True)
    credit_limit = Column(DECIMAL(15,2), nullable=True)
    interest_rate = Column(Float, nullable=True)
    tenure_months = Column(Integer, nullable=True)

    # Decision factors
    decision_score = Column(Float, nullable=True)
    decision_factors = Column(JSONB, nullable=True)
    approval_conditions = Column(JSONB, nullable=True)
    rejection_reasons = Column(JSONB, nullable=True)

    # Decision workflow
    decision_date = Column(DateTime, default=datetime.now)
    decision_maker = Column(String(255), nullable=True)
    reviewer = Column(String(255), nullable=True)
    approver = Column(String(255), nullable=True)

    # Decision status
    status = Column(String(50), default='pending')  # pending, approved, rejected, expired
    is_final = Column(Boolean, default=False)
    appeal_status = Column(String(50), nullable=True)

    # Decision notes
    decision_notes = Column(Text, nullable=True)
    internal_notes = Column(Text, nullable=True)
    customer_communication = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Credit Monitoring
class credit_monitoring(Base):
    __tablename__ = 'credit_monitoring'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    monitoring_id = Column(String(100), unique=True, nullable=False, index=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(String(100), nullable=True, index=True)

    # Monitoring details
    monitoring_type = Column(String(50), nullable=False)  # continuous, periodic, event_driven
    monitoring_frequency = Column(String(50), nullable=True)  # daily, weekly, monthly, quarterly
    monitoring_scope = Column(String(100), nullable=True)  # full, limited, specific

    # Monitoring metrics
    current_exposure = Column(DECIMAL(15,2), nullable=True)
    utilization_rate = Column(Float, nullable=True)
    payment_behavior_score = Column(Float, nullable=True)
    early_warning_score = Column(Float, nullable=True)

    # Alert thresholds
    exposure_threshold = Column(DECIMAL(15,2), nullable=True)
    utilization_threshold = Column(Float, nullable=True)
    score_threshold = Column(Float, nullable=True)

    # Monitoring status
    monitoring_status = Column(String(50), default='active')  # active, paused, stopped
    last_check_date = Column(DateTime, nullable=True)
    next_check_date = Column(DateTime, nullable=True)

    # Alert information
    alert_count = Column(Integer, default=0)
    last_alert_date = Column(DateTime, nullable=True)
    alert_severity = Column(String(20), nullable=True)

    # Monitoring data
    monitoring_data = Column(JSONB, nullable=True)
    trend_analysis = Column(JSONB, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Credit Alerts
class credit_alerts(Base):
    __tablename__ = 'credit_alerts'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    alert_id = Column(String(100), unique=True, nullable=False, index=True)
    monitoring_id = Column(String(100), nullable=False, index=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(String(100), nullable=True, index=True)

    # Alert details
    alert_type = Column(String(50), nullable=False)  # threshold, trend, anomaly, event
    alert_category = Column(String(50), nullable=False)  # exposure, payment, score, behavior
    alert_severity = Column(String(20), nullable=False)  # low, medium, high, critical

    # Alert trigger
    trigger_condition = Column(String(255), nullable=False)
    trigger_value = Column(Float, nullable=True)
    threshold_value = Column(Float, nullable=True)

    # Alert message
    alert_title = Column(String(255), nullable=False)
    alert_message = Column(Text, nullable=False)
    alert_data = Column(JSONB, nullable=True)

    # Alert status
    status = Column(String(50), default='active')  # active, acknowledged, resolved, dismissed
    acknowledged_by = Column(String(255), nullable=True)
    acknowledged_at = Column(DateTime, nullable=True)
    resolved_by = Column(String(255), nullable=True)
    resolved_at = Column(DateTime, nullable=True)

    # Alert actions
    recommended_actions = Column(JSONB, nullable=True)
    actions_taken = Column(JSONB, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Credit Models
class credit_models(Base):
    __tablename__ = 'credit_models'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    model_id = Column(String(100), unique=True, nullable=False, index=True)
    model_name = Column(String(255), nullable=False)
    model_description = Column(Text, nullable=True)

    # Model details
    model_type = Column(String(50), nullable=False)  # scorecard, ml, statistical, hybrid
    model_purpose = Column(String(100), nullable=False)  # credit_scoring, risk_assessment, pd_estimation
    model_algorithm = Column(String(100), nullable=True)

    # Model version
    version = Column(String(20), nullable=False)
    previous_version = Column(String(20), nullable=True)

    # Model configuration
    model_config = Column(JSONB, nullable=False)
    feature_list = Column(JSONB, nullable=True)
    weight_matrix = Column(JSONB, nullable=True)

    # Model performance
    accuracy_score = Column(Float, nullable=True)
    precision_score = Column(Float, nullable=True)
    recall_score = Column(Float, nullable=True)
    f1_score = Column(Float, nullable=True)
    auc_score = Column(Float, nullable=True)
    gini_coefficient = Column(Float, nullable=True)

    # Model validation
    validation_date = Column(DateTime, nullable=True)
    validation_results = Column(JSONB, nullable=True)
    backtesting_results = Column(JSONB, nullable=True)

    # Model status
    status = Column(String(50), default='development')  # development, testing, production, retired
    is_active = Column(Boolean, default=False)
    deployment_date = Column(DateTime, nullable=True)
    retirement_date = Column(DateTime, nullable=True)

    # Model governance
    developed_by = Column(String(255), nullable=False)
    validated_by = Column(String(255), nullable=True)
    approved_by = Column(String(255), nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Credit Bureau Data
class credit_bureau_data(Base):
    __tablename__ = 'credit_bureau_data'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    bureau_id = Column(String(100), unique=True, nullable=False, index=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(String(100), nullable=True, index=True)

    # Bureau details
    bureau_name = Column(String(100), nullable=False)
    bureau_report_id = Column(String(100), nullable=True)
    report_type = Column(String(50), nullable=False)  # individual, commercial, consolidated

    # Credit information
    credit_score = Column(Integer, nullable=True)
    credit_grade = Column(String(10), nullable=True)
    total_accounts = Column(Integer, nullable=True)
    active_accounts = Column(Integer, nullable=True)
    closed_accounts = Column(Integer, nullable=True)

    # Credit utilization
    total_credit_limit = Column(DECIMAL(15,2), nullable=True)
    total_outstanding = Column(DECIMAL(15,2), nullable=True)
    utilization_ratio = Column(Float, nullable=True)

    # Payment history
    payment_history_months = Column(Integer, nullable=True)
    on_time_payments = Column(Integer, nullable=True)
    late_payments = Column(Integer, nullable=True)
    defaults = Column(Integer, nullable=True)

    # Enquiries
    hard_enquiries_6m = Column(Integer, nullable=True)
    hard_enquiries_12m = Column(Integer, nullable=True)
    soft_enquiries_6m = Column(Integer, nullable=True)

    # Bureau data
    bureau_data = Column(JSONB, nullable=False)
    raw_report = Column(JSONB, nullable=True)

    # Report metadata
    report_date = Column(Date, nullable=False)
    data_as_of_date = Column(Date, nullable=True)
    report_version = Column(String(20), nullable=True)

    # Data quality
    data_completeness = Column(Float, nullable=True)
    data_accuracy = Column(Float, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Financial Scores
class financial_scores(Base):
    __tablename__ = 'financial_scores'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # Overall scores
    overall_financial_score = Column(NUMERIC, nullable=True)
    growth_score = Column(NUMERIC, nullable=True)
    profitability_score = Column(NUMERIC, nullable=True)
    liquidity_score = Column(NUMERIC, nullable=True)
    solvency_score = Column(NUMERIC, nullable=True)
    efficiency_score = Column(NUMERIC, nullable=True)

    # Score metadata
    score_date = Column(Date, default=datetime.now().date())
    score_period = Column(String(20), nullable=True)  # annual, quarterly, monthly
    score_methodology = Column(String(100), nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Credit Policies
class credit_policies(Base):
    __tablename__ = 'credit_policies'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    policy_id = Column(String(100), unique=True, nullable=False, index=True)
    policy_name = Column(String(255), nullable=False)
    policy_description = Column(Text, nullable=True)

    # Policy details
    policy_type = Column(String(50), nullable=False)  # underwriting, pricing, monitoring, collection
    policy_category = Column(String(50), nullable=True)  # individual, sme, corporate

    # Policy rules
    policy_rules = Column(JSONB, nullable=False)
    eligibility_criteria = Column(JSONB, nullable=True)
    scoring_criteria = Column(JSONB, nullable=True)
    approval_matrix = Column(JSONB, nullable=True)

    # Policy limits
    minimum_score = Column(Float, nullable=True)
    maximum_exposure = Column(DECIMAL(15,2), nullable=True)
    maximum_tenure = Column(Integer, nullable=True)

    # Policy status
    status = Column(String(50), default='draft')  # draft, active, inactive, archived
    effective_date = Column(Date, nullable=True)
    expiry_date = Column(Date, nullable=True)

    # Policy governance
    created_by = Column(String(255), nullable=False)
    approved_by = Column(String(255), nullable=True)
    approval_date = Column(Date, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Credit Limits
class credit_limits(Base):
    __tablename__ = 'credit_limits'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    limit_id = Column(String(100), unique=True, nullable=False, index=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(String(100), nullable=True, index=True)

    # Limit details
    limit_type = Column(String(50), nullable=False)  # credit_line, term_loan, overdraft
    limit_category = Column(String(50), nullable=True)  # secured, unsecured, guaranteed

    # Limit amounts
    approved_limit = Column(DECIMAL(15,2), nullable=False)
    available_limit = Column(DECIMAL(15,2), nullable=False)
    utilized_amount = Column(DECIMAL(15,2), default=0)
    blocked_amount = Column(DECIMAL(15,2), default=0)

    # Limit terms
    interest_rate = Column(Float, nullable=True)
    tenure_months = Column(Integer, nullable=True)
    currency = Column(String(10), default='USD')

    # Limit status
    status = Column(String(50), default='active')  # active, suspended, closed, expired
    approval_date = Column(Date, nullable=False)
    effective_date = Column(Date, nullable=False)
    expiry_date = Column(Date, nullable=True)

    # Limit conditions
    conditions = Column(JSONB, nullable=True)
    covenants = Column(JSONB, nullable=True)
    security_details = Column(JSONB, nullable=True)

    # Limit review
    last_review_date = Column(Date, nullable=True)
    next_review_date = Column(Date, nullable=True)
    review_frequency = Column(String(20), nullable=True)  # annual, semi_annual, quarterly

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Credit Applications
class credit_applications(Base):
    __tablename__ = 'credit_applications'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    application_id = Column(String(100), unique=True, nullable=False, index=True)
    merchant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    customer_id = Column(String(100), nullable=True, index=True)

    # Application details
    application_type = Column(String(50), nullable=False)  # new, renewal, enhancement, restructure
    product_type = Column(String(50), nullable=False)  # credit_line, term_loan, overdraft
    requested_amount = Column(DECIMAL(15,2), nullable=False)
    requested_tenure = Column(Integer, nullable=True)

    # Application data
    application_data = Column(JSONB, nullable=False)
    supporting_documents = Column(JSONB, nullable=True)

    # Application status
    status = Column(String(50), default='submitted')  # submitted, under_review, approved, rejected, withdrawn
    stage = Column(String(50), nullable=True)  # initial_review, credit_assessment, approval, documentation

    # Application workflow
    submitted_date = Column(Date, default=datetime.now().date())
    review_start_date = Column(Date, nullable=True)
    decision_date = Column(Date, nullable=True)
    disbursement_date = Column(Date, nullable=True)

    # Application team
    relationship_manager = Column(String(255), nullable=True)
    credit_analyst = Column(String(255), nullable=True)
    approving_authority = Column(String(255), nullable=True)

    # Application notes
    application_notes = Column(Text, nullable=True)
    internal_notes = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

# Add indexes for performance
Index('idx_credit_assessments_merchant_type', CreditAssessment.merchant_id, CreditAssessment.assessment_type)
Index('idx_credit_assessments_status_date', CreditAssessment.status, CreditAssessment.assessment_date.desc())
Index('idx_credit_scores_merchant_type', credit_scores.merchant_id, credit_scores.score_type)
Index('idx_credit_scores_active_date', credit_scores.is_active, credit_scores.calculated_at.desc())
Index('idx_risk_assessments_merchant_score', risk_assessments.merchant_id, risk_assessments.risk_score.desc())
Index('idx_credit_ratings_merchant_active', credit_ratings.merchant_id, credit_ratings.is_active)
Index('idx_credit_decisions_assessment_status', credit_decisions.assessment_id, credit_decisions.status)
Index('idx_credit_monitoring_merchant_status', credit_monitoring.merchant_id, credit_monitoring.monitoring_status)
Index('idx_credit_alerts_merchant_severity', credit_alerts.merchant_id, credit_alerts.alert_severity, credit_alerts.status)
Index('idx_credit_models_type_status', credit_models.model_type, credit_models.status)
Index('idx_credit_bureau_merchant_date', credit_bureau_data.merchant_id, credit_bureau_data.report_date.desc())
Index('idx_financial_scores_merchant_date', financial_scores.merchant_id, financial_scores.score_date.desc())
Index('idx_credit_policies_type_status', credit_policies.policy_type, credit_policies.status)
Index('idx_credit_limits_merchant_status', credit_limits.merchant_id, credit_limits.status)
Index('idx_credit_applications_merchant_status', credit_applications.merchant_id, credit_applications.status)