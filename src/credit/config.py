# credit config - configuration for credit module
import os
from typing import Dict, Any, List
from pydantic import BaseSettings, Field

class CreditSettings(BaseSettings):
    """Credit module configuration settings."""

    # Assessment Configuration
    max_assessment_age_days: int = Field(default=30, description="Maximum age of assessments in days")
    default_assessment_method: str = Field(default="automated", description="Default assessment method")
    enable_auto_assessments: bool = Field(default=True, description="Enable automatic assessments")

    # Scoring Configuration
    default_score_model: str = Field(default="internal", description="Default scoring model")
    min_credit_score: int = Field(default=300, description="Minimum credit score")
    max_credit_score: int = Field(default=850, description="Maximum credit score")
    score_cache_ttl_minutes: int = Field(default=60, description="Score cache TTL in minutes")

    # Risk Configuration
    risk_threshold_low: float = Field(default=20.0, description="Low risk threshold")
    risk_threshold_medium: float = Field(default=40.0, description="Medium risk threshold")
    risk_threshold_high: float = Field(default=70.0, description="High risk threshold")

    # Decision Configuration
    enable_auto_decisions: bool = Field(default=True, description="Enable automatic decisions")
    auto_approval_threshold: float = Field(default=700.0, description="Auto approval score threshold")
    auto_rejection_threshold: float = Field(default=500.0, description="Auto rejection score threshold")

    # Limit Configuration
    min_credit_limit: float = Field(default=1000.0, description="Minimum credit limit")
    max_credit_limit: float = Field(default=1000000.0, description="Maximum credit limit")
    default_limit_multiplier: float = Field(default=2.0, description="Default limit multiplier")

    # Monitoring Configuration
    enable_monitoring: bool = Field(default=True, description="Enable credit monitoring")
    monitoring_frequency_hours: int = Field(default=24, description="Monitoring frequency in hours")
    alert_threshold_utilization: float = Field(default=0.8, description="Alert threshold for utilization")

    # Bureau Integration
    enable_bureau_integration: bool = Field(default=True, description="Enable bureau integration")
    bureau_timeout_seconds: int = Field(default=30, description="Bureau API timeout")
    bureau_retry_attempts: int = Field(default=3, description="Bureau API retry attempts")

    # ML Models
    enable_ml_models: bool = Field(default=False, description="Enable ML models")
    ml_model_endpoint: str = Field(default="", description="ML model service endpoint")
    ml_model_timeout_seconds: int = Field(default=10, description="ML model timeout")

    # Performance
    enable_caching: bool = Field(default=True, description="Enable caching")
    cache_ttl_minutes: int = Field(default=30, description="Cache TTL in minutes")
    max_concurrent_assessments: int = Field(default=100, description="Max concurrent assessments")

    # Security
    enable_audit_logging: bool = Field(default=True, description="Enable audit logging")
    mask_sensitive_data: bool = Field(default=True, description="Mask sensitive data in logs")
    require_approval_for_high_limits: bool = Field(default=True, description="Require approval for high limits")

    # Feature Flags
    feature_advanced_scoring: bool = Field(default=True, description="Advanced scoring features")
    feature_real_time_monitoring: bool = Field(default=False, description="Real-time monitoring")
    feature_predictive_analytics: bool = Field(default=False, description="Predictive analytics")
    feature_automated_underwriting: bool = Field(default=True, description="Automated underwriting")

    class Config:
        env_prefix = "CREDIT_"
        case_sensitive = False

# Global settings instance
credit_settings = CreditSettings()

# Score Model Configuration
SCORE_MODELS_CONFIG = {
    "internal": {
        "name": "Internal Scoring Model",
        "version": "1.0",
        "weights": {
            "financial_strength": 0.4,
            "payment_history": 0.3,
            "business_stability": 0.2,
            "credit_utilization": 0.1
        },
        "score_range": (300, 850)
    },
    "fico": {
        "name": "FICO Score",
        "version": "9.0",
        "score_range": (300, 850),
        "api_endpoint": os.getenv("FICO_API_ENDPOINT", "")
    },
    "vantage": {
        "name": "VantageScore",
        "version": "4.0",
        "score_range": (300, 850),
        "api_endpoint": os.getenv("VANTAGE_API_ENDPOINT", "")
    }
}

def get_credit_config() -> Dict[str, Any]:
    """Get complete credit configuration."""
    return {
        "settings": credit_settings.dict(),
        "score_models": SCORE_MODELS_CONFIG
    }