# credit constants - module-specific constants for credit

# Assessment Types
ASSESSMENT_TYPE_INITIAL = "initial"
ASSESSMENT_TYPE_PERIODIC = "periodic"
ASSESSMENT_TYPE_AD_HOC = "ad_hoc"
ASSESSMENT_TYPE_RENEWAL = "renewal"

ASSESSMENT_TYPES = [
    ASSESSMENT_TYPE_INITIAL,
    ASSESSMENT_TYPE_PERIODIC,
    ASSESSMENT_TYPE_AD_HOC,
    ASSESSMENT_TYPE_RENEWAL
]

# Assessment Purposes
ASSESSMENT_PURPOSE_LOAN = "loan"
ASSESSMENT_PURPOSE_CREDIT_LINE = "credit_line"
ASSESSMENT_PURPOSE_MONITORING = "monitoring"
ASSESSMENT_PURPOSE_COMPLIANCE = "compliance"
ASSESSMENT_PURPOSE_UNDERWRITING = "underwriting"
ASSESSMENT_PURPOSE_PORTFOLIO_REVIEW = "portfolio_review"

ASSESSMENT_PURPOSES = [
    ASSESSMENT_PURPOSE_LOAN,
    ASSESSMENT_PURPOSE_CREDIT_LINE,
    ASSESSMENT_PURPOSE_MONITORING,
    ASSESSMENT_PURPOSE_COMPLIANCE,
    ASSESSMENT_PURPOSE_UNDERWRITING,
    ASSESSMENT_PURPOSE_PORTFOLIO_REVIEW
]

# Assessment Methods
ASSESSMENT_METHOD_AUTOMATED = "automated"
ASSESSMENT_METHOD_MANUAL = "manual"
ASSESSMENT_METHOD_HYBRID = "hybrid"

ASSESSMENT_METHODS = [
    ASSESSMENT_METHOD_AUTOMATED,
    ASSESSMENT_METHOD_MANUAL,
    ASSESSMENT_METHOD_HYBRID
]

# Assessment Statuses
ASSESSMENT_STATUS_PENDING = "pending"
ASSESSMENT_STATUS_IN_PROGRESS = "in_progress"
ASSESSMENT_STATUS_COMPLETED = "completed"
ASSESSMENT_STATUS_FAILED = "failed"
ASSESSMENT_STATUS_CANCELLED = "cancelled"

ASSESSMENT_STATUSES = [
    ASSESSMENT_STATUS_PENDING,
    ASSESSMENT_STATUS_IN_PROGRESS,
    ASSESSMENT_STATUS_COMPLETED,
    ASSESSMENT_STATUS_FAILED,
    ASSESSMENT_STATUS_CANCELLED
]

# Score Types
SCORE_TYPE_CREDIT = "credit"
SCORE_TYPE_RISK = "risk"
SCORE_TYPE_FRAUD = "fraud"
SCORE_TYPE_BEHAVIORAL = "behavioral"
SCORE_TYPE_FINANCIAL = "financial"
SCORE_TYPE_PAYMENT = "payment"

SCORE_TYPES = [
    SCORE_TYPE_CREDIT,
    SCORE_TYPE_RISK,
    SCORE_TYPE_FRAUD,
    SCORE_TYPE_BEHAVIORAL,
    SCORE_TYPE_FINANCIAL,
    SCORE_TYPE_PAYMENT
]

# Score Models
SCORE_MODEL_INTERNAL = "internal"
SCORE_MODEL_FICO = "fico"
SCORE_MODEL_VANTAGE = "vantage"
SCORE_MODEL_EXPERIAN = "experian"
SCORE_MODEL_EQUIFAX = "equifax"
SCORE_MODEL_TRANSUNION = "transunion"
SCORE_MODEL_CUSTOM = "custom"

SCORE_MODELS = [
    SCORE_MODEL_INTERNAL,
    SCORE_MODEL_FICO,
    SCORE_MODEL_VANTAGE,
    SCORE_MODEL_EXPERIAN,
    SCORE_MODEL_EQUIFAX,
    SCORE_MODEL_TRANSUNION,
    SCORE_MODEL_CUSTOM
]

# Score Grades
SCORE_GRADE_A_PLUS = "A+"
SCORE_GRADE_A = "A"
SCORE_GRADE_B_PLUS = "B+"
SCORE_GRADE_B = "B"
SCORE_GRADE_C_PLUS = "C+"
SCORE_GRADE_C = "C"
SCORE_GRADE_D = "D"
SCORE_GRADE_F = "F"

SCORE_GRADES = [
    SCORE_GRADE_A_PLUS,
    SCORE_GRADE_A,
    SCORE_GRADE_B_PLUS,
    SCORE_GRADE_B,
    SCORE_GRADE_C_PLUS,
    SCORE_GRADE_C,
    SCORE_GRADE_D,
    SCORE_GRADE_F
]

# Risk Levels
RISK_LEVEL_LOW = "low"
RISK_LEVEL_MEDIUM = "medium"
RISK_LEVEL_HIGH = "high"
RISK_LEVEL_CRITICAL = "critical"

RISK_LEVELS = [
    RISK_LEVEL_LOW,
    RISK_LEVEL_MEDIUM,
    RISK_LEVEL_HIGH,
    RISK_LEVEL_CRITICAL
]

# Credit Decisions
CREDIT_DECISION_APPROVED = "approved"
CREDIT_DECISION_REJECTED = "rejected"
CREDIT_DECISION_CONDITIONAL = "conditional"
CREDIT_DECISION_PENDING = "pending"
CREDIT_DECISION_REVIEW = "review"

CREDIT_DECISIONS = [
    CREDIT_DECISION_APPROVED,
    CREDIT_DECISION_REJECTED,
    CREDIT_DECISION_CONDITIONAL,
    CREDIT_DECISION_PENDING,
    CREDIT_DECISION_REVIEW
]

# Decision Types
DECISION_TYPE_APPROVAL = "approval"
DECISION_TYPE_REJECTION = "rejection"
DECISION_TYPE_CONDITIONAL = "conditional"
DECISION_TYPE_REVIEW = "review"
DECISION_TYPE_AMENDMENT = "amendment"

DECISION_TYPES = [
    DECISION_TYPE_APPROVAL,
    DECISION_TYPE_REJECTION,
    DECISION_TYPE_CONDITIONAL,
    DECISION_TYPE_REVIEW,
    DECISION_TYPE_AMENDMENT
]

# Decision Methods
DECISION_METHOD_AUTOMATED = "automated"
DECISION_METHOD_MANUAL = "manual"
DECISION_METHOD_HYBRID = "hybrid"

DECISION_METHODS = [
    DECISION_METHOD_AUTOMATED,
    DECISION_METHOD_MANUAL,
    DECISION_METHOD_HYBRID
]

# Monitoring Types
MONITORING_TYPE_CONTINUOUS = "continuous"
MONITORING_TYPE_PERIODIC = "periodic"
MONITORING_TYPE_EVENT_DRIVEN = "event_driven"
MONITORING_TYPE_THRESHOLD_BASED = "threshold_based"

MONITORING_TYPES = [
    MONITORING_TYPE_CONTINUOUS,
    MONITORING_TYPE_PERIODIC,
    MONITORING_TYPE_EVENT_DRIVEN,
    MONITORING_TYPE_THRESHOLD_BASED
]