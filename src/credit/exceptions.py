# credit exceptions - module-specific errors for credit
from fastapi import HTTPException, status

class CreditAssessmentNotFoundError(HTTPException):
    def __init__(self, assessment_id: str = None):
        detail = f"Credit assessment '{assessment_id}' not found" if assessment_id else "Credit assessment not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CreditAssessmentCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create credit assessment"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditAssessmentError(HTTPException):
    def __init__(self, detail: str = "Credit assessment error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditScoreNotFoundError(HTTPException):
    def __init__(self, score_id: str = None):
        detail = f"Credit score '{score_id}' not found" if score_id else "Credit score not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class CreditScoringError(HTTPException):
    def __init__(self, detail: str = "Credit scoring error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class RiskAssessmentError(HTTPException):
    def __init__(self, detail: str = "Risk assessment error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditDecisionError(HTTPException):
    def __init__(self, detail: str = "Credit decision error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditMonitoringError(HTTPException):
    def __init__(self, detail: str = "Credit monitoring error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditModelError(HTTPException):
    def __init__(self, detail: str = "Credit model error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditBureauError(HTTPException):
    def __init__(self, detail: str = "Credit bureau error"):
        super().__init__(status_code=status.HTTP_502_BAD_GATEWAY, detail=detail)

class CreditPolicyError(HTTPException):
    def __init__(self, detail: str = "Credit policy error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditLimitError(HTTPException):
    def __init__(self, detail: str = "Credit limit error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditApplicationError(HTTPException):
    def __init__(self, detail: str = "Credit application error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditValidationError(HTTPException):
    def __init__(self, detail: str = "Credit validation error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditCalculationError(HTTPException):
    def __init__(self, detail: str = "Credit calculation error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditRatingError(HTTPException):
    def __init__(self, detail: str = "Credit rating error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditAlertError(HTTPException):
    def __init__(self, detail: str = "Credit alert error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditReportError(HTTPException):
    def __init__(self, detail: str = "Credit report error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditDataError(HTTPException):
    def __init__(self, detail: str = "Credit data error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditConfigurationError(HTTPException):
    def __init__(self, detail: str = "Credit configuration error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditPermissionError(HTTPException):
    def __init__(self, detail: str = "Insufficient permissions for credit operation"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class CreditAccessDeniedError(HTTPException):
    def __init__(self, detail: str = "Credit access denied"):
        super().__init__(status_code=status.HTTP_403_FORBIDDEN, detail=detail)

class CreditServiceUnavailableError(HTTPException):
    def __init__(self, detail: str = "Credit service temporarily unavailable"):
        super().__init__(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=detail)

class CreditTimeoutError(HTTPException):
    def __init__(self, detail: str = "Credit operation timeout"):
        super().__init__(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=detail)

class CreditIntegrationError(HTTPException):
    def __init__(self, detail: str = "Credit integration error"):
        super().__init__(status_code=status.HTTP_502_BAD_GATEWAY, detail=detail)

class CreditComplianceError(HTTPException):
    def __init__(self, detail: str = "Credit compliance error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditRegulatoryError(HTTPException):
    def __init__(self, detail: str = "Credit regulatory error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditAuditError(HTTPException):
    def __init__(self, detail: str = "Credit audit error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditWorkflowError(HTTPException):
    def __init__(self, detail: str = "Credit workflow error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditApprovalError(HTTPException):
    def __init__(self, detail: str = "Credit approval error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditRejectionError(HTTPException):
    def __init__(self, detail: str = "Credit rejection error"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class CreditUnderwritingError(HTTPException):
    def __init__(self, detail: str = "Credit underwriting error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditPortfolioError(HTTPException):
    def __init__(self, detail: str = "Credit portfolio error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditAnalyticsError(HTTPException):
    def __init__(self, detail: str = "Credit analytics error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditMLModelError(HTTPException):
    def __init__(self, detail: str = "Credit ML model error"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class CreditFeatureNotAvailableError(HTTPException):
    def __init__(self, feature: str):
        super().__init__(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail=f"Credit feature '{feature}' is not available"
        )