# credit utils - utility functions for credit module
from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, date
import hashlib
import json
import re

def calculate_score_percentile(score: float, score_range: tuple = (300, 850)) -> float:
    """Calculate percentile for a credit score."""
    min_score, max_score = score_range
    if score <= min_score:
        return 0.0
    elif score >= max_score:
        return 100.0
    else:
        return ((score - min_score) / (max_score - min_score)) * 100

def determine_score_grade(score: float) -> str:
    """Determine letter grade for a credit score."""
    if score >= 800:
        return "A+"
    elif score >= 750:
        return "A"
    elif score >= 700:
        return "B+"
    elif score >= 650:
        return "B"
    elif score >= 600:
        return "C+"
    elif score >= 550:
        return "C"
    elif score >= 500:
        return "D"
    else:
        return "F"

def normalize_score(score: float, current_range: tuple, target_range: tuple = (0, 100)) -> float:
    """Normalize a score from one range to another."""
    current_min, current_max = current_range
    target_min, target_max = target_range

    if current_max == current_min:
        return target_min

    normalized = ((score - current_min) / (current_max - current_min)) * (target_max - target_min) + target_min
    return max(target_min, min(target_max, normalized))

def calculate_weighted_score(components: Dict[str, float], weights: Dict[str, float]) -> float:
    """Calculate weighted score from components."""
    total_weight = sum(weights.values())
    if total_weight == 0:
        return 0.0

    weighted_sum = sum(components.get(key, 0) * weight for key, weight in weights.items())
    return weighted_sum / total_weight

def format_currency(amount: Decimal, currency: str = "USD") -> str:
    """Format currency amount."""
    if currency == "USD":
        return f"${amount:,.2f}"
    else:
        return f"{amount:,.2f} {currency}"

def validate_merchant_id(merchant_id: str) -> bool:
    """Validate merchant ID format."""
    # UUID format validation
    uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
    return bool(re.match(uuid_pattern, merchant_id, re.IGNORECASE))

def generate_assessment_hash(data: Dict[str, Any]) -> str:
    """Generate hash for assessment data."""
    # Sort keys for consistent hashing
    sorted_data = json.dumps(data, sort_keys=True, default=str)
    return hashlib.sha256(sorted_data.encode()).hexdigest()[:16]

def calculate_days_between(start_date: date, end_date: date) -> int:
    """Calculate days between two dates."""
    return (end_date - start_date).days

def is_business_day(check_date: date) -> bool:
    """Check if a date is a business day (Monday-Friday)."""
    return check_date.weekday() < 5

def get_next_business_day(from_date: date) -> date:
    """Get the next business day."""
    next_day = from_date
    while not is_business_day(next_day):
        next_day = date.fromordinal(next_day.toordinal() + 1)
    return next_day

def sanitize_input_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Sanitize input data for security."""
    sanitized = {}
    for key, value in data.items():
        if isinstance(value, str):
            # Remove potentially dangerous characters
            sanitized[key] = re.sub(r'[<>"\']', '', value)
        elif isinstance(value, (int, float, bool)):
            sanitized[key] = value
        elif isinstance(value, dict):
            sanitized[key] = sanitize_input_data(value)
        elif isinstance(value, list):
            sanitized[key] = [sanitize_input_data(item) if isinstance(item, dict) else item for item in value]
    return sanitized

def calculate_confidence_interval(score: float, confidence_level: float = 0.95) -> tuple:
    """Calculate confidence interval for a score."""
    # Simplified confidence interval calculation
    margin = (1 - confidence_level) * 50  # Simplified margin calculation
    return (max(0, score - margin), min(100, score + margin))

def format_risk_description(risk_level: str, risk_score: float) -> str:
    """Format risk description based on level and score."""
    descriptions = {
        "low": f"Low risk profile with score {risk_score:.1f}. Minimal credit risk expected.",
        "medium": f"Medium risk profile with score {risk_score:.1f}. Moderate credit risk management required.",
        "high": f"High risk profile with score {risk_score:.1f}. Enhanced monitoring and controls recommended.",
        "critical": f"Critical risk profile with score {risk_score:.1f}. Immediate attention and strict controls required."
    }
    return descriptions.get(risk_level, f"Risk level {risk_level} with score {risk_score:.1f}")

def extract_key_factors(factors_data: Dict[str, Any], limit: int = 5) -> List[str]:
    """Extract key factors from factors data."""
    if not factors_data:
        return []

    # Sort factors by impact/weight if available
    sorted_factors = sorted(
        factors_data.items(),
        key=lambda x: x[1] if isinstance(x[1], (int, float)) else 0,
        reverse=True
    )

    return [factor[0] for factor in sorted_factors[:limit]]

def calculate_trend(current_value: float, previous_value: float) -> str:
    """Calculate trend direction."""
    if previous_value == 0:
        return "stable"

    change_percent = ((current_value - previous_value) / previous_value) * 100

    if change_percent > 5:
        return "improving"
    elif change_percent < -5:
        return "declining"
    else:
        return "stable"

def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
    """Mask sensitive data showing only last few characters."""
    if len(data) <= visible_chars:
        return mask_char * len(data)

    return mask_char * (len(data) - visible_chars) + data[-visible_chars:]

def validate_score_range(score: float, min_score: float = 0, max_score: float = 100) -> bool:
    """Validate if score is within acceptable range."""
    return min_score <= score <= max_score from app/utils/