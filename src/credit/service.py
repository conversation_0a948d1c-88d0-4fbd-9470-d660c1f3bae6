# credit service - business logic for credit module
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from typing import Optional, List, Dict, Any, Tuple
from uuid import UUID, uuid4
from datetime import datetime, date, timedelta
from decimal import Decimal
import json
import math

from .models import (
    CreditAssessment, credit_scores, risk_assessments, risk_categories,
    risk_indicators, risk_metrics, credit_ratings, credit_decisions,
    credit_monitoring, credit_alerts, credit_models, credit_bureau_data,
    financial_scores, credit_policies, credit_limits, credit_applications
)
from .schemas import (
    CreditAssessmentCreate, CreditAssessmentUpdate, CreditAssessmentResponse,
    CreditScoreCreate, CreditScoreUpdate, CreditScoreResponse,
    RiskAssessmentCreate, RiskAssessmentUpdate, RiskAssessmentResponse,
    CreditAssessmentRequest, CreditScoringRequest, RiskAssessmentRequest,
    CreditDecisionRequest, CreditMonitoringRequest, BulkCreditOperation,
    CreditStatistics, CreditPortfolioAnalytics, CreditAssessmentResult,
    CreditScoringResult, RiskAssessmentResult, CreditDecisionResult,
    CreditMonitoringResult, CreditBureauResult, FinancialScoreResult,
    CreditModelPerformance, PaginationParams, CreditFilterParams
)
from .exceptions import (
    CreditAssessmentNotFoundError, CreditAssessmentCreationError,
    CreditScoreNotFoundError, CreditScoringError, RiskAssessmentError,
    CreditDecisionError, CreditMonitoringError, CreditModelError,
    CreditBureauError, CreditPolicyError, CreditLimitError,
    CreditApplicationError, CreditValidationError, CreditCalculationError
)

class CreditAssessmentService:
    """Service class for credit assessment operations."""

    def __init__(self, db: Session):
        self.db = db

    # Credit Assessment Management
    def create_credit_assessment(self, assessment_data: CreditAssessmentCreate, created_by: str) -> CreditAssessmentResponse:
        """Create a new credit assessment."""
        try:
            # Generate assessment ID if not provided
            assessment_id = assessment_data.assessment_id or self.generate_assessment_id()

            # Check for existing assessment
            existing = self.db.query(CreditAssessment).filter(
                CreditAssessment.assessment_id == assessment_id
            ).first()

            if existing:
                raise CreditAssessmentCreationError(f"Assessment with ID {assessment_id} already exists")

            # Validate assessment data
            self._validate_assessment_data(assessment_data)

            # Create assessment
            db_assessment = CreditAssessment(
                assessment_id=assessment_id,
                merchant_id=assessment_data.merchant_id,
                customer_id=assessment_data.customer_id,
                assessment_type=assessment_data.assessment_type,
                assessment_purpose=assessment_data.assessment_purpose,
                assessment_method=assessment_data.assessment_method,
                input_data=assessment_data.input_data,
                assessment_notes=assessment_data.assessment_notes,
                assessed_by=created_by,
                status='pending'
            )

            self.db.add(db_assessment)
            self.db.commit()
            self.db.refresh(db_assessment)

            # Trigger assessment calculation if automated
            if assessment_data.assessment_method == 'automated':
                self._calculate_assessment_scores(db_assessment)

            return CreditAssessmentResponse.model_validate(db_assessment)

        except Exception as e:
            self.db.rollback()
            raise CreditAssessmentCreationError(f"Failed to create credit assessment: {str(e)}")

    def get_credit_assessments(
        self,
        filters: CreditFilterParams,
        pagination: PaginationParams
    ) -> Tuple[List[CreditAssessmentResponse], int]:
        """Get credit assessments with filtering and pagination."""
        try:
            query = self.db.query(CreditAssessment)

            # Apply filters
            if filters.merchant_id:
                query = query.filter(CreditAssessment.merchant_id == filters.merchant_id)
            if filters.customer_id:
                query = query.filter(CreditAssessment.customer_id == filters.customer_id)
            if filters.assessment_type:
                query = query.filter(CreditAssessment.assessment_type == filters.assessment_type)
            if filters.assessment_purpose:
                query = query.filter(CreditAssessment.assessment_purpose == filters.assessment_purpose)
            if filters.assessment_method:
                query = query.filter(CreditAssessment.assessment_method == filters.assessment_method)
            if filters.status:
                query = query.filter(CreditAssessment.status == filters.status)
            if filters.risk_level:
                query = query.filter(CreditAssessment.risk_level == filters.risk_level)
            if filters.credit_decision:
                query = query.filter(CreditAssessment.credit_decision == filters.credit_decision)
            if filters.assessment_date_from:
                query = query.filter(CreditAssessment.assessment_date >= filters.assessment_date_from)
            if filters.assessment_date_to:
                query = query.filter(CreditAssessment.assessment_date <= filters.assessment_date_to)

            # Get total count
            total = query.count()

            # Apply pagination
            offset = (pagination.page - 1) * pagination.size
            assessments = query.order_by(desc(CreditAssessment.assessment_date))\
                              .offset(offset)\
                              .limit(pagination.size)\
                              .all()

            return [CreditAssessmentResponse.model_validate(assessment) for assessment in assessments], total

        except Exception as e:
            raise CreditAssessmentNotFoundError(f"Failed to retrieve credit assessments: {str(e)}")

    def get_credit_assessment_by_id(self, assessment_id: str) -> CreditAssessmentResponse:
        """Get a credit assessment by ID."""
        assessment = self.db.query(CreditAssessment).filter(
            CreditAssessment.assessment_id == assessment_id
        ).first()

        if not assessment:
            raise CreditAssessmentNotFoundError(f"Credit assessment '{assessment_id}' not found")

        return CreditAssessmentResponse.model_validate(assessment)

    def update_credit_assessment(
        self,
        assessment_id: str,
        assessment_data: CreditAssessmentUpdate,
        updated_by: str
    ) -> CreditAssessmentResponse:
        """Update a credit assessment."""
        try:
            assessment = self.db.query(CreditAssessment).filter(
                CreditAssessment.assessment_id == assessment_id
            ).first()

            if not assessment:
                raise CreditAssessmentNotFoundError(f"Credit assessment '{assessment_id}' not found")

            # Update fields
            update_data = assessment_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(assessment, field, value)

            assessment.updated_at = datetime.now()
            if updated_by:
                assessment.reviewed_by = updated_by

            self.db.commit()
            self.db.refresh(assessment)

            return CreditAssessmentResponse.model_validate(assessment)

        except Exception as e:
            self.db.rollback()
            raise CreditAssessmentCreationError(f"Failed to update credit assessment: {str(e)}")

    def perform_credit_assessment(self, request: CreditAssessmentRequest) -> CreditAssessmentResult:
        """Perform a comprehensive credit assessment."""
        try:
            # Check for existing assessment
            existing_assessment = None
            if not request.force_recalculation:
                existing_assessment = self.db.query(CreditAssessment).filter(
                    and_(
                        CreditAssessment.merchant_id == request.merchant_id,
                        CreditAssessment.customer_id == request.customer_id,
                        CreditAssessment.assessment_type == request.assessment_type,
                        CreditAssessment.status == 'completed',
                        CreditAssessment.assessment_date >= datetime.now() - timedelta(days=30)
                    )
                ).first()

            if existing_assessment:
                return self._convert_to_assessment_result(existing_assessment)

            # Create new assessment
            assessment_data = CreditAssessmentCreate(
                merchant_id=request.merchant_id,
                customer_id=request.customer_id,
                assessment_type=request.assessment_type,
                assessment_purpose=request.assessment_purpose,
                assessment_method=request.assessment_method,
                input_data=request.input_data
            )

            assessment = self.create_credit_assessment(assessment_data, "system")

            # Perform comprehensive assessment
            result = self._perform_comprehensive_assessment(assessment.assessment_id, request.input_data)

            return result

        except Exception as e:
            raise CreditAssessmentError(f"Failed to perform credit assessment: {str(e)}")

    # Credit Scoring
    def calculate_credit_score(self, request: CreditScoringRequest) -> CreditScoringResult:
        """Calculate credit score for a merchant/customer."""
        try:
            # Generate score ID
            score_id = self.generate_score_id()

            # Get input data for scoring
            scoring_data = self._prepare_scoring_data(request.merchant_id, request.customer_id, request.input_data)

            # Calculate score based on model
            score_result = self._calculate_score_by_model(request.score_model, scoring_data)

            # Create score record
            score_data = CreditScoreCreate(
                score_id=score_id,
                merchant_id=request.merchant_id,
                customer_id=request.customer_id,
                score_type=request.score_type,
                score_model=request.score_model,
                score_value=score_result['score_value'],
                score_range_min=score_result.get('score_range_min'),
                score_range_max=score_result.get('score_range_max'),
                score_percentile=score_result.get('score_percentile'),
                score_grade=score_result.get('score_grade'),
                score_components=score_result.get('score_components'),
                component_weights=score_result.get('component_weights'),
                score_factors=score_result.get('score_factors'),
                calculation_method=request.calculation_method,
                confidence_level=score_result.get('confidence_level'),
                data_completeness=score_result.get('data_completeness')
            )

            db_score = credit_scores(
                score_id=score_id,
                merchant_id=score_data.merchant_id,
                customer_id=score_data.customer_id,
                score_type=score_data.score_type,
                score_model=score_data.score_model,
                score_version=score_data.score_version,
                score_value=score_data.score_value,
                score_range_min=score_data.score_range_min,
                score_range_max=score_data.score_range_max,
                score_percentile=score_data.score_percentile,
                score_grade=score_data.score_grade,
                score_components=score_data.score_components,
                component_weights=score_data.component_weights,
                score_factors=score_data.score_factors,
                calculation_method=score_data.calculation_method,
                data_sources=score_data.data_sources,
                confidence_level=score_data.confidence_level,
                data_completeness=score_data.data_completeness
            )

            self.db.add(db_score)
            self.db.commit()
            self.db.refresh(db_score)

            return CreditScoringResult(
                score_id=score_id,
                merchant_id=request.merchant_id,
                customer_id=request.customer_id,
                score_type=request.score_type,
                score_value=score_result['score_value'],
                score_grade=score_result.get('score_grade', 'N/A'),
                score_percentile=score_result.get('score_percentile', 0.0),
                score_components=score_result.get('score_components', {}),
                score_factors=score_result.get('score_factors', []),
                confidence_level=score_result.get('confidence_level', 0.0),
                calculation_timestamp=datetime.now(),
                valid_until=score_result.get('valid_until')
            )

        except Exception as e:
            self.db.rollback()
            raise CreditScoringError(f"Failed to calculate credit score: {str(e)}")

    # Helper methods
    def generate_assessment_id(self) -> str:
        """Generate a unique assessment ID."""
        return f"ASSESS-{uuid4().hex[:8].upper()}"

    def generate_score_id(self) -> str:
        """Generate a unique score ID."""
        return f"SCORE-{uuid4().hex[:8].upper()}"

    def _validate_assessment_data(self, assessment_data: CreditAssessmentCreate) -> None:
        """Validate assessment data."""
        if not assessment_data.merchant_id:
            raise CreditValidationError("Merchant ID is required")

        if assessment_data.assessment_type not in ['initial', 'periodic', 'ad_hoc', 'renewal']:
            raise CreditValidationError("Invalid assessment type")

        if assessment_data.assessment_method not in ['automated', 'manual', 'hybrid']:
            raise CreditValidationError("Invalid assessment method")

    def _calculate_assessment_scores(self, assessment: CreditAssessment) -> None:
        """Calculate scores for an assessment."""
        try:
            # Get merchant data for scoring
            scoring_data = self._prepare_scoring_data(assessment.merchant_id, assessment.customer_id, assessment.input_data)

            # Calculate various scores
            credit_score = self._calculate_credit_score_internal(scoring_data)
            risk_score = self._calculate_risk_score_internal(scoring_data)
            financial_score = self._calculate_financial_score_internal(scoring_data)
            behavioral_score = self._calculate_behavioral_score_internal(scoring_data)
            fraud_score = self._calculate_fraud_score_internal(scoring_data)

            # Calculate risk metrics
            pd = self._calculate_probability_of_default(scoring_data)
            lgd = self._calculate_loss_given_default(scoring_data)
            ead = self._calculate_exposure_at_default(scoring_data)
            el = pd * lgd * ead if all([pd, lgd, ead]) else None

            # Determine risk level
            risk_level = self._determine_risk_level(risk_score)

            # Make credit decision
            credit_decision = self._make_credit_decision(credit_score, risk_score, risk_level)

            # Calculate recommended limit
            recommended_limit = self._calculate_recommended_limit(scoring_data, credit_score, risk_score)

            # Update assessment
            assessment.overall_credit_score = credit_score
            assessment.financial_score = financial_score
            assessment.behavioral_score = behavioral_score
            assessment.risk_score = risk_score
            assessment.fraud_score = fraud_score
            assessment.probability_of_default = pd
            assessment.loss_given_default = lgd
            assessment.exposure_at_default = ead
            assessment.expected_loss = el
            assessment.risk_level = risk_level
            assessment.credit_decision = credit_decision
            assessment.recommended_limit = recommended_limit
            assessment.status = 'completed'
            assessment.confidence_level = self._calculate_confidence_level(scoring_data)
            assessment.data_quality_score = self._calculate_data_quality_score(scoring_data)

            # Store score breakdown
            assessment.score_breakdown = {
                'credit_score': credit_score,
                'risk_score': risk_score,
                'financial_score': financial_score,
                'behavioral_score': behavioral_score,
                'fraud_score': fraud_score
            }

            # Store risk factors
            assessment.risk_factors = self._identify_risk_factors(scoring_data)
            assessment.mitigating_factors = self._identify_mitigating_factors(scoring_data)

            self.db.commit()

        except Exception as e:
            raise CreditCalculationError(f"Failed to calculate assessment scores: {str(e)}")

    def _prepare_scoring_data(self, merchant_id: UUID, customer_id: Optional[str], input_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Prepare data for scoring calculations."""
        scoring_data = {
            'merchant_id': str(merchant_id),
            'customer_id': customer_id,
            'input_data': input_data or {}
        }

        # Add merchant financial data
        financial_data = self._get_merchant_financial_data(merchant_id)
        scoring_data.update(financial_data)

        # Add transaction data
        transaction_data = self._get_merchant_transaction_data(merchant_id)
        scoring_data.update(transaction_data)

        # Add risk indicators
        risk_data = self._get_merchant_risk_data(merchant_id)
        scoring_data.update(risk_data)

        # Add bureau data if available
        bureau_data = self._get_bureau_data(merchant_id, customer_id)
        scoring_data.update(bureau_data)

        return scoring_data

    def _calculate_credit_score_internal(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate internal credit score."""
        # Simplified credit scoring algorithm
        base_score = 500.0

        # Financial strength (40% weight)
        financial_score = scoring_data.get('financial_score', 0) * 0.4

        # Payment history (30% weight)
        payment_score = scoring_data.get('payment_behavior_score', 0) * 0.3

        # Business stability (20% weight)
        stability_score = scoring_data.get('business_stability_score', 0) * 0.2

        # Credit utilization (10% weight)
        utilization_score = (100 - scoring_data.get('credit_utilization', 0)) * 0.1

        total_score = base_score + financial_score + payment_score + stability_score + utilization_score

        # Normalize to 300-850 range
        return max(300, min(850, total_score))

    def _calculate_risk_score_internal(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate internal risk score."""
        # Risk factors
        risk_factors = []

        # Financial risk
        if scoring_data.get('debt_to_equity', 0) > 2.0:
            risk_factors.append(('high_leverage', 20))

        # Operational risk
        if scoring_data.get('transaction_volatility', 0) > 0.5:
            risk_factors.append(('high_volatility', 15))

        # Industry risk
        if scoring_data.get('industry_risk_level') == 'high':
            risk_factors.append(('high_industry_risk', 25))

        # Calculate total risk score
        base_risk = 10.0
        additional_risk = sum(weight for _, weight in risk_factors)

        return min(100, base_risk + additional_risk)

    def _calculate_financial_score_internal(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate financial strength score."""
        # Financial ratios
        profitability = scoring_data.get('profitability_ratio', 0) * 30
        liquidity = scoring_data.get('liquidity_ratio', 0) * 25
        solvency = scoring_data.get('solvency_ratio', 0) * 25
        efficiency = scoring_data.get('efficiency_ratio', 0) * 20

        return min(100, profitability + liquidity + solvency + efficiency)

    def _calculate_behavioral_score_internal(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate behavioral score."""
        # Payment behavior
        on_time_payments = scoring_data.get('on_time_payment_rate', 0) * 40

        # Account management
        account_stability = scoring_data.get('account_stability_score', 0) * 30

        # Transaction patterns
        transaction_consistency = scoring_data.get('transaction_consistency', 0) * 30

        return min(100, on_time_payments + account_stability + transaction_consistency)

    def _calculate_fraud_score_internal(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate fraud risk score."""
        fraud_indicators = []

        # Velocity checks
        if scoring_data.get('transaction_velocity_anomaly', False):
            fraud_indicators.append(10)

        # Pattern analysis
        if scoring_data.get('unusual_transaction_patterns', False):
            fraud_indicators.append(15)

        # Identity verification
        if scoring_data.get('identity_verification_issues', False):
            fraud_indicators.append(20)

        return min(100, sum(fraud_indicators))

    def _calculate_probability_of_default(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate probability of default."""
        # Simplified PD calculation based on credit score
        credit_score = scoring_data.get('credit_score', 500)

        if credit_score >= 750:
            return 0.01  # 1%
        elif credit_score >= 700:
            return 0.03  # 3%
        elif credit_score >= 650:
            return 0.08  # 8%
        elif credit_score >= 600:
            return 0.15  # 15%
        else:
            return 0.25  # 25%

    def _calculate_loss_given_default(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate loss given default."""
        # Base LGD
        base_lgd = 0.45  # 45%

        # Adjust based on collateral
        if scoring_data.get('has_collateral', False):
            base_lgd *= 0.7  # Reduce by 30%

        # Adjust based on guarantees
        if scoring_data.get('has_guarantees', False):
            base_lgd *= 0.8  # Reduce by 20%

        return min(1.0, base_lgd)

    def _calculate_exposure_at_default(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate exposure at default."""
        # Current exposure
        current_exposure = scoring_data.get('current_exposure', 0)

        # Credit utilization factor
        utilization_rate = scoring_data.get('credit_utilization', 0) / 100

        # EAD calculation
        ead = current_exposure * (1 + utilization_rate * 0.5)

        return ead

    def _determine_risk_level(self, risk_score: float) -> str:
        """Determine risk level based on risk score."""
        if risk_score <= 20:
            return 'low'
        elif risk_score <= 40:
            return 'medium'
        elif risk_score <= 70:
            return 'high'
        else:
            return 'critical'

    def _make_credit_decision(self, credit_score: float, risk_score: float, risk_level: str) -> str:
        """Make automated credit decision."""
        if credit_score >= 700 and risk_level in ['low', 'medium']:
            return 'approved'
        elif credit_score >= 650 and risk_level == 'low':
            return 'approved'
        elif credit_score >= 600 and risk_level == 'low':
            return 'conditional'
        elif credit_score < 500 or risk_level == 'critical':
            return 'rejected'
        else:
            return 'pending'

    def _calculate_recommended_limit(self, scoring_data: Dict[str, Any], credit_score: float, risk_score: float) -> Decimal:
        """Calculate recommended credit limit."""
        # Base limit calculation
        monthly_revenue = scoring_data.get('monthly_revenue', 0)
        base_limit = monthly_revenue * 2  # 2x monthly revenue

        # Adjust based on credit score
        if credit_score >= 750:
            multiplier = 1.5
        elif credit_score >= 700:
            multiplier = 1.2
        elif credit_score >= 650:
            multiplier = 1.0
        elif credit_score >= 600:
            multiplier = 0.8
        else:
            multiplier = 0.5

        # Adjust based on risk level
        risk_multiplier = {
            'low': 1.0,
            'medium': 0.8,
            'high': 0.6,
            'critical': 0.3
        }.get(self._determine_risk_level(risk_score), 0.5)

        recommended_limit = base_limit * multiplier * risk_multiplier

        return Decimal(str(max(1000, min(1000000, recommended_limit))))  # Min $1K, Max $1M

    def _calculate_confidence_level(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate confidence level for the assessment."""
        data_completeness = self._calculate_data_quality_score(scoring_data)
        model_accuracy = 0.85  # Assumed model accuracy
        return min(1.0, data_completeness * model_accuracy)

    def _calculate_data_quality_score(self, scoring_data: Dict[str, Any]) -> float:
        """Calculate data quality score."""
        required_fields = ['monthly_revenue', 'financial_score', 'payment_behavior_score']
        available_fields = sum(1 for field in required_fields if scoring_data.get(field) is not None)
        return available_fields / len(required_fields)

    def _identify_risk_factors(self, scoring_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify risk factors."""
        risk_factors = {}

        if scoring_data.get('debt_to_equity', 0) > 2.0:
            risk_factors['high_leverage'] = 'Debt-to-equity ratio exceeds 2.0'

        if scoring_data.get('transaction_volatility', 0) > 0.5:
            risk_factors['high_volatility'] = 'High transaction volatility detected'

        return risk_factors

    def _identify_mitigating_factors(self, scoring_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify mitigating factors."""
        mitigating_factors = {}

        if scoring_data.get('has_collateral', False):
            mitigating_factors['collateral'] = 'Secured with collateral'

        if scoring_data.get('has_guarantees', False):
            mitigating_factors['guarantees'] = 'Personal or corporate guarantees provided'

        return mitigating_factors

    def _get_merchant_financial_data(self, merchant_id: UUID) -> Dict[str, Any]:
        """Get merchant financial data."""
        # Placeholder - would integrate with financial data sources
        return {
            'monthly_revenue': 50000,
            'profitability_ratio': 0.15,
            'liquidity_ratio': 1.2,
            'solvency_ratio': 0.8,
            'efficiency_ratio': 0.9,
            'debt_to_equity': 1.5
        }

    def _get_merchant_transaction_data(self, merchant_id: UUID) -> Dict[str, Any]:
        """Get merchant transaction data."""
        # Placeholder - would integrate with transaction data sources
        return {
            'transaction_volatility': 0.3,
            'payment_behavior_score': 85,
            'on_time_payment_rate': 95,
            'transaction_consistency': 80
        }

    def _get_merchant_risk_data(self, merchant_id: UUID) -> Dict[str, Any]:
        """Get merchant risk data."""
        # Placeholder - would integrate with risk data sources
        return {
            'industry_risk_level': 'medium',
            'business_stability_score': 75,
            'account_stability_score': 80
        }

    def _get_bureau_data(self, merchant_id: UUID, customer_id: Optional[str]) -> Dict[str, Any]:
        """Get credit bureau data."""
        # Placeholder - would integrate with credit bureau APIs
        return {
            'credit_utilization': 30,
            'has_collateral': False,
            'has_guarantees': True,
            'current_exposure': 25000
        }