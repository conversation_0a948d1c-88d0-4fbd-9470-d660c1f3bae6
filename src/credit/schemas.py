# credit schemas - Pydantic models for credit module
from pydantic import BaseModel, ConfigDict, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date
from decimal import Decimal

# Credit Assessment Schemas
class CreditAssessmentBase(BaseModel):
    merchant_id: UUID
    customer_id: Optional[str] = Field(None, max_length=100)
    assessment_type: str = Field(..., max_length=50)
    assessment_purpose: str = Field(..., max_length=100)
    assessment_method: str = Field(..., max_length=50)
    input_data: Optional[Dict[str, Any]] = None
    assessment_notes: Optional[str] = None

class CreditAssessmentCreate(CreditAssessmentBase):
    assessment_id: Optional[str] = Field(None, max_length=100)

class CreditAssessmentUpdate(BaseModel):
    assessment_type: Optional[str] = Field(None, max_length=50)
    assessment_purpose: Optional[str] = Field(None, max_length=100)
    assessment_method: Optional[str] = Field(None, max_length=50)
    overall_credit_score: Optional[float] = None
    financial_score: Optional[float] = None
    behavioral_score: Optional[float] = None
    risk_score: Optional[float] = None
    fraud_score: Optional[float] = None
    risk_level: Optional[str] = Field(None, max_length=20)
    risk_category: Optional[str] = Field(None, max_length=50)
    probability_of_default: Optional[float] = None
    loss_given_default: Optional[float] = None
    exposure_at_default: Optional[float] = None
    expected_loss: Optional[float] = None
    credit_decision: Optional[str] = Field(None, max_length=50)
    recommended_limit: Optional[Decimal] = None
    approved_limit: Optional[Decimal] = None
    interest_rate: Optional[float] = None
    tenure_months: Optional[int] = None
    score_breakdown: Optional[Dict[str, Any]] = None
    risk_factors: Optional[Dict[str, Any]] = None
    mitigating_factors: Optional[Dict[str, Any]] = None
    status: Optional[str] = Field(None, max_length=50)
    confidence_level: Optional[float] = None
    data_quality_score: Optional[float] = None
    model_version: Optional[str] = Field(None, max_length=50)
    algorithm_used: Optional[str] = Field(None, max_length=100)
    valid_until: Optional[datetime] = None
    assessed_by: Optional[str] = Field(None, max_length=255)
    reviewed_by: Optional[str] = Field(None, max_length=255)
    approved_by: Optional[str] = Field(None, max_length=255)
    assessment_notes: Optional[str] = None
    reviewer_notes: Optional[str] = None
    approval_notes: Optional[str] = None

class CreditAssessmentResponse(CreditAssessmentBase):
    id: UUID
    assessment_id: str
    overall_credit_score: Optional[float] = None
    financial_score: Optional[float] = None
    behavioral_score: Optional[float] = None
    risk_score: Optional[float] = None
    fraud_score: Optional[float] = None
    risk_level: Optional[str] = None
    risk_category: Optional[str] = None
    probability_of_default: Optional[float] = None
    loss_given_default: Optional[float] = None
    exposure_at_default: Optional[float] = None
    expected_loss: Optional[float] = None
    credit_decision: Optional[str] = None
    recommended_limit: Optional[Decimal] = None
    approved_limit: Optional[Decimal] = None
    interest_rate: Optional[float] = None
    tenure_months: Optional[int] = None
    score_breakdown: Optional[Dict[str, Any]] = None
    risk_factors: Optional[Dict[str, Any]] = None
    mitigating_factors: Optional[Dict[str, Any]] = None
    status: str
    confidence_level: Optional[float] = None
    data_quality_score: Optional[float] = None
    model_version: Optional[str] = None
    algorithm_used: Optional[str] = None
    assessment_date: datetime
    valid_until: Optional[datetime] = None
    assessed_by: Optional[str] = None
    reviewed_by: Optional[str] = None
    approved_by: Optional[str] = None
    reviewer_notes: Optional[str] = None
    approval_notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Credit Score Schemas
class CreditScoreBase(BaseModel):
    merchant_id: UUID
    customer_id: Optional[str] = Field(None, max_length=100)
    score_type: str = Field(..., max_length=50)
    score_model: str = Field(..., max_length=100)
    score_version: Optional[str] = Field(None, max_length=20)
    score_value: float
    score_range_min: Optional[float] = None
    score_range_max: Optional[float] = None
    score_percentile: Optional[float] = None
    score_grade: Optional[str] = Field(None, max_length=10)
    score_components: Optional[Dict[str, Any]] = None
    component_weights: Optional[Dict[str, Any]] = None
    score_factors: Optional[Dict[str, Any]] = None
    calculation_method: Optional[str] = Field(None, max_length=100)
    data_sources: Optional[List[str]] = None
    confidence_level: Optional[float] = None
    data_completeness: Optional[float] = None

class CreditScoreCreate(CreditScoreBase):
    score_id: Optional[str] = Field(None, max_length=100)

class CreditScoreUpdate(BaseModel):
    score_value: Optional[float] = None
    score_range_min: Optional[float] = None
    score_range_max: Optional[float] = None
    score_percentile: Optional[float] = None
    score_grade: Optional[str] = Field(None, max_length=10)
    score_components: Optional[Dict[str, Any]] = None
    component_weights: Optional[Dict[str, Any]] = None
    score_factors: Optional[Dict[str, Any]] = None
    confidence_level: Optional[float] = None
    data_completeness: Optional[float] = None
    valid_until: Optional[datetime] = None
    is_active: Optional[bool] = None
    previous_score: Optional[float] = None
    score_change: Optional[float] = None
    score_trend: Optional[str] = Field(None, max_length=20)

class CreditScoreResponse(CreditScoreBase):
    id: UUID
    score_id: str
    calculated_at: datetime
    valid_from: datetime
    valid_until: Optional[datetime] = None
    is_active: bool
    previous_score: Optional[float] = None
    score_change: Optional[float] = None
    score_trend: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Risk Assessment Schemas
class RiskAssessmentBase(BaseModel):
    merchant_id: UUID
    percentile: Optional[str] = Field(None, max_length=50)
    percentile_business_category: Optional[str] = Field(None, max_length=50)
    risk_level: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    risk_score: Optional[int] = None

class RiskAssessmentCreate(RiskAssessmentBase):
    pass

class RiskAssessmentUpdate(BaseModel):
    percentile: Optional[str] = Field(None, max_length=50)
    percentile_business_category: Optional[str] = Field(None, max_length=50)
    risk_level: Optional[str] = Field(None, max_length=50)
    description: Optional[str] = None
    risk_score: Optional[int] = None
    risk_score_025_rank: Optional[int] = None
    risk_score_050_rank: Optional[int] = None
    risk_score_075_rank: Optional[int] = None
    risk_score_business_category_025_rank: Optional[int] = None
    risk_score_business_category_050_rank: Optional[int] = None
    risk_score_business_category_075_rank: Optional[int] = None
    risk_score_max: Optional[int] = None
    risk_score_max_business_category: Optional[int] = None

class RiskAssessmentResponse(RiskAssessmentBase):
    id: UUID
    risk_score_025_rank: Optional[int] = None
    risk_score_050_rank: Optional[int] = None
    risk_score_075_rank: Optional[int] = None
    risk_score_business_category_025_rank: Optional[int] = None
    risk_score_business_category_050_rank: Optional[int] = None
    risk_score_business_category_075_rank: Optional[int] = None
    risk_score_max: Optional[int] = None
    risk_score_max_business_category: Optional[int] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Utility and Request Schemas
class PaginationParams(BaseModel):
    page: int = Field(default=1, ge=1)
    size: int = Field(default=20, ge=1, le=100)

class CreditFilterParams(BaseModel):
    assessment_type: Optional[str] = None
    assessment_purpose: Optional[str] = None
    assessment_method: Optional[str] = None
    status: Optional[str] = None
    risk_level: Optional[str] = None
    credit_decision: Optional[str] = None
    score_type: Optional[str] = None
    score_model: Optional[str] = None
    risk_score_min: Optional[float] = None
    risk_score_max: Optional[float] = None
    assessment_date_from: Optional[datetime] = None
    assessment_date_to: Optional[datetime] = None
    merchant_id: Optional[UUID] = None
    customer_id: Optional[str] = None

class CreditSearchParams(BaseModel):
    query: str = Field(..., min_length=1)
    search_fields: Optional[List[str]] = None
    assessment_types: Optional[List[str]] = None
    statuses: Optional[List[str]] = None

class CreditAssessmentRequest(BaseModel):
    merchant_id: UUID
    customer_id: Optional[str] = Field(None, max_length=100)
    assessment_type: str = Field(..., max_length=50)
    assessment_purpose: str = Field(..., max_length=100)
    assessment_method: str = Field(default='automated', max_length=50)
    input_data: Optional[Dict[str, Any]] = None
    force_recalculation: bool = False

class CreditScoringRequest(BaseModel):
    merchant_id: UUID
    customer_id: Optional[str] = Field(None, max_length=100)
    score_type: str = Field(..., max_length=50)
    score_model: str = Field(..., max_length=100)
    input_data: Optional[Dict[str, Any]] = None
    calculation_method: Optional[str] = Field(None, max_length=100)

class RiskAssessmentRequest(BaseModel):
    merchant_id: UUID
    assessment_data: Optional[Dict[str, Any]] = None
    include_percentile: bool = True
    include_business_category: bool = True

class CreditDecisionRequest(BaseModel):
    assessment_id: str = Field(..., max_length=100)
    decision_type: str = Field(..., max_length=50)
    decision_method: str = Field(default='automated', max_length=50)
    requested_amount: Optional[Decimal] = None
    requested_tenure: Optional[int] = None
    decision_notes: Optional[str] = None

class CreditMonitoringRequest(BaseModel):
    merchant_id: UUID
    customer_id: Optional[str] = Field(None, max_length=100)
    monitoring_type: str = Field(..., max_length=50)
    monitoring_frequency: Optional[str] = Field(None, max_length=50)
    monitoring_scope: Optional[str] = Field(None, max_length=100)
    alert_thresholds: Optional[Dict[str, Any]] = None

class BulkCreditOperation(BaseModel):
    merchant_ids: Optional[List[UUID]] = None
    customer_ids: Optional[List[str]] = None
    assessment_ids: Optional[List[str]] = None
    operation: str = Field(..., max_length=50)
    parameters: Optional[Dict[str, Any]] = None

class CreditStatistics(BaseModel):
    total_assessments: int
    pending_assessments: int
    completed_assessments: int
    approved_assessments: int
    rejected_assessments: int
    assessments_by_type: Dict[str, int]
    assessments_by_purpose: Dict[str, int]
    assessments_by_risk_level: Dict[str, int]
    average_credit_score: Optional[float] = None
    average_risk_score: Optional[float] = None
    average_processing_time_hours: Optional[float] = None
    approval_rate_percentage: Optional[float] = None
    total_approved_amount: Optional[Decimal] = None
    average_approved_amount: Optional[Decimal] = None

class CreditPortfolioAnalytics(BaseModel):
    total_exposure: Decimal
    utilized_exposure: Decimal
    available_exposure: Decimal
    utilization_rate: float
    number_of_accounts: int
    active_accounts: int
    average_score: float
    risk_distribution: Dict[str, int]
    exposure_by_risk_level: Dict[str, Decimal]
    portfolio_quality_metrics: Dict[str, float]

# Response List Schemas
class CreditAssessmentListResponse(BaseModel):
    items: List[CreditAssessmentResponse]
    total: int
    page: int
    size: int
    pages: int

class CreditScoreListResponse(BaseModel):
    items: List[CreditScoreResponse]
    total: int
    page: int
    size: int
    pages: int

class RiskAssessmentListResponse(BaseModel):
    items: List[RiskAssessmentResponse]
    total: int
    page: int
    size: int
    pages: int

# Legacy Compatibility Schemas
class LegacyCreditResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class LegacyCreditAssessment(BaseModel):
    merchant_id: str
    assessment_type: str = "initial"
    assessment_purpose: str = "loan"

class LegacyCreditScore(BaseModel):
    merchant_id: str
    score_type: str = "credit"
    score_value: float

# Custom Response Schemas
class CustomJSONResponse(BaseModel):
    data: Optional[Any] = None
    status: str = "success"
    message: str = "Operation completed successfully"
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

class CreditAssessmentResult(BaseModel):
    assessment_id: str
    merchant_id: UUID
    customer_id: Optional[str] = None
    overall_credit_score: float
    risk_level: str
    credit_decision: str
    recommended_limit: Optional[Decimal] = None
    interest_rate: Optional[float] = None
    assessment_summary: Dict[str, Any]
    risk_factors: List[str]
    mitigating_factors: List[str]
    next_steps: List[str]
    assessment_timestamp: datetime

class CreditScoringResult(BaseModel):
    score_id: str
    merchant_id: UUID
    customer_id: Optional[str] = None
    score_type: str
    score_value: float
    score_grade: str
    score_percentile: float
    score_components: Dict[str, float]
    score_factors: List[Dict[str, Any]]
    confidence_level: float
    calculation_timestamp: datetime
    valid_until: Optional[datetime] = None

class RiskAssessmentResult(BaseModel):
    merchant_id: UUID
    risk_score: int
    risk_level: str
    percentile: str
    percentile_business_category: str
    risk_ranking: Dict[str, int]
    risk_description: str
    assessment_timestamp: datetime

class CreditDecisionResult(BaseModel):
    decision_id: str
    assessment_id: str
    merchant_id: UUID
    customer_id: Optional[str] = None
    decision_type: str
    decision_outcome: str
    approved_amount: Optional[Decimal] = None
    credit_limit: Optional[Decimal] = None
    interest_rate: Optional[float] = None
    tenure_months: Optional[int] = None
    decision_factors: List[Dict[str, Any]]
    approval_conditions: List[str]
    rejection_reasons: List[str]
    decision_timestamp: datetime
    decision_maker: str

class CreditMonitoringResult(BaseModel):
    monitoring_id: str
    merchant_id: UUID
    customer_id: Optional[str] = None
    monitoring_status: str
    current_exposure: Decimal
    utilization_rate: float
    payment_behavior_score: float
    early_warning_score: float
    alert_count: int
    last_alert_date: Optional[datetime] = None
    monitoring_summary: Dict[str, Any]
    trend_analysis: Dict[str, Any]
    recommendations: List[str]

class CreditBureauResult(BaseModel):
    bureau_id: str
    merchant_id: UUID
    customer_id: Optional[str] = None
    bureau_name: str
    credit_score: int
    credit_grade: str
    total_accounts: int
    utilization_ratio: float
    payment_history_months: int
    on_time_payments: int
    late_payments: int
    defaults: int
    hard_enquiries_6m: int
    bureau_summary: Dict[str, Any]
    report_date: date
    data_quality_score: float

class FinancialScoreResult(BaseModel):
    merchant_id: UUID
    overall_financial_score: float
    growth_score: float
    profitability_score: float
    liquidity_score: float
    solvency_score: float
    efficiency_score: float
    score_breakdown: Dict[str, float]
    score_interpretation: Dict[str, str]
    score_date: date
    score_period: str

class CreditModelPerformance(BaseModel):
    model_id: str
    model_name: str
    model_type: str
    model_version: str
    accuracy_score: float
    precision_score: float
    recall_score: float
    f1_score: float
    auc_score: float
    gini_coefficient: float
    validation_results: Dict[str, Any]
    performance_metrics: Dict[str, float]
    model_status: str
    last_validation_date: datetime