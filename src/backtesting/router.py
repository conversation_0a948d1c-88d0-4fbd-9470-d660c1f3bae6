# Backtesting router - strategy analysis and performance evaluation endpoints
from fastapi import APIRouter, Depends, HTTPException, Query, Path, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    BacktestingAnalysisRequest, BacktestingAnalysisResponse, BacktestingAnalysisListResponse,
    BacktestingAnalysisCreate, BacktestingAnalysisUpdate, BacktestingExecutionRequest,
    BacktestingExecutionResponse, BacktestingStatistics, BacktestingSearchParams,
    BacktestingBulkOperation, CustomJSONResponse, LegacyBacktestingResponse
)
from .service import BacktestingService
from .exceptions import BacktestingAnalysisNotFoundError, BacktestingExecutionError
from .utils.sql_executor import (
    validate_sql_query, get_table_schema, get_available_tables, 
    test_database_connection, format_query_results
)

router = APIRouter()

def get_backtesting_service(db: Session = Depends(get_db)) -> BacktestingService:
    return BacktestingService(db)

# Analysis Management Endpoints
@router.post("/analyses", response_model=BacktestingAnalysisResponse, status_code=201)
async def create_analysis(
    analysis: BacktestingAnalysisRequest,
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new backtesting analysis configuration."""
    return backtesting_service.create_analysis(analysis, current_user.email)

@router.get("/analyses", response_model=BacktestingAnalysisListResponse)
async def get_analyses(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    analysis_type: Optional[str] = Query(None, description="Filter by analysis type (rule/metric)"),
    status: Optional[str] = Query(None, description="Filter by status"),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of backtesting analyses."""
    pagination = PaginationParams(page=page, size=size)
    result = backtesting_service.get_analyses(pagination, analysis_type, status)
    
    return BacktestingAnalysisListResponse(
        data=result.items,
        total=result.total,
        page=result.page,
        size=result.size,
        pages=result.pages
    )

@router.get("/analyses/{analysis_id}", response_model=BacktestingAnalysisResponse)
async def get_analysis(
    analysis_id: str = Path(..., description="The analysis ID"),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Get backtesting analysis by ID."""
    return backtesting_service.get_analysis_by_id(analysis_id)

@router.put("/analyses/{analysis_id}", response_model=BacktestingAnalysisResponse)
async def update_analysis(
    analysis_id: str = Path(..., description="The analysis ID"),
    analysis_data: BacktestingAnalysisUpdate = ...,
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Update backtesting analysis configuration."""
    return backtesting_service.update_analysis(analysis_id, analysis_data, current_user.email)

@router.delete("/analyses/{analysis_id}")
async def delete_analysis(
    analysis_id: str = Path(..., description="The analysis ID"),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Delete backtesting analysis."""
    backtesting_service.delete_analysis(analysis_id, current_user.email)
    return {"message": "Analysis deleted successfully"}

# Analysis Execution Endpoints
@router.post("/analyses/{analysis_id}/execute", response_model=BacktestingExecutionResponse)
async def execute_analysis(
    analysis_id: str = Path(..., description="The analysis ID"),
    execution_request: BacktestingExecutionRequest = BacktestingExecutionRequest(),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Execute backtesting analysis and return results."""
    return await backtesting_service.execute_analysis(analysis_id, execution_request)

@router.get("/analyses/{analysis_id}/sql")
async def get_analysis_sql(
    analysis_id: str = Path(..., description="The analysis ID"),
    query_type: str = Query("combined", description="Type of SQL to retrieve"),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Get generated SQL queries for analysis."""
    analysis = backtesting_service.get_analysis_by_id(analysis_id)
    
    if query_type == "preliminary":
        return {"sql": analysis.preliminary_sql, "type": "preliminary"}
    elif query_type == "pivot":
        return {"sql": analysis.pivot_sql, "type": "pivot"}
    else:
        return {
            "preliminary_sql": analysis.preliminary_sql,
            "pivot_sql": analysis.pivot_sql,
            "type": "combined"
        }

@router.post("/analyses/{analysis_id}/validate")
async def validate_analysis(
    analysis_id: str = Path(..., description="The analysis ID"),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Validate analysis SQL queries without executing them."""
    analysis = backtesting_service.get_analysis_by_id(analysis_id)
    
    validation_results = {
        "analysis_id": analysis_id,
        "preliminary_sql_valid": validate_sql_query(analysis.preliminary_sql) if analysis.preliminary_sql else False,
        "pivot_sql_valid": validate_sql_query(analysis.pivot_sql) if analysis.pivot_sql else False,
        "overall_valid": False
    }
    
    validation_results["overall_valid"] = (
        validation_results["preliminary_sql_valid"] and 
        validation_results["pivot_sql_valid"]
    )
    
    return validation_results

# Rule and Metric Analysis Endpoints
@router.post("/rule-analysis", response_model=BacktestingAnalysisResponse)
async def create_rule_analysis(
    analysis: BacktestingAnalysisRequest,
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Create rule-specific backtesting analysis."""
    # Ensure analysis type is set to rule
    analysis.analysisType = "rule"
    return backtesting_service.create_analysis(analysis, current_user.email)

@router.post("/metric-analysis", response_model=BacktestingAnalysisResponse)
async def create_metric_analysis(
    analysis: BacktestingAnalysisRequest,
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Create metric-specific backtesting analysis."""
    # Ensure analysis type is set to metric
    analysis.analysisType = "metric"
    return backtesting_service.create_analysis(analysis, current_user.email)

@router.get("/rule-analyses", response_model=BacktestingAnalysisListResponse)
async def get_rule_analyses(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by status"),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Get rule-specific backtesting analyses."""
    pagination = PaginationParams(page=page, size=size)
    result = backtesting_service.get_analyses(pagination, "rule", status)
    
    return BacktestingAnalysisListResponse(
        data=result.items,
        total=result.total,
        page=result.page,
        size=result.size,
        pages=result.pages
    )

@router.get("/metric-analyses", response_model=BacktestingAnalysisListResponse)
async def get_metric_analyses(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None, description="Filter by status"),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Get metric-specific backtesting analyses."""
    pagination = PaginationParams(page=page, size=size)
    result = backtesting_service.get_analyses(pagination, "metric", status)
    
    return BacktestingAnalysisListResponse(
        data=result.items,
        total=result.total,
        page=result.page,
        size=result.size,
        pages=result.pages
    )

# Statistics and Analytics
@router.get("/statistics", response_model=BacktestingStatistics)
async def get_backtesting_statistics(
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Get comprehensive backtesting statistics."""
    return backtesting_service.get_statistics()

@router.get("/analytics", response_model=CustomJSONResponse)
async def get_backtesting_analytics(
    period: str = Query("monthly", description="Analytics period"),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Get backtesting analytics and insights."""
    # This would typically involve more complex analytics
    stats = backtesting_service.get_statistics()
    
    analytics_data = {
        "period": period,
        "total_analyses": stats.total_analyses,
        "success_rate": (stats.completed_analyses / stats.total_analyses * 100) if stats.total_analyses > 0 else 0,
        "rule_vs_metric_ratio": {
            "rule_analyses": stats.rule_analyses,
            "metric_analyses": stats.metric_analyses
        },
        "performance_metrics": {
            "avg_execution_time": stats.avg_execution_time,
            "completion_rate": (stats.completed_analyses / stats.total_analyses * 100) if stats.total_analyses > 0 else 0,
            "failure_rate": (stats.failed_analyses / stats.total_analyses * 100) if stats.total_analyses > 0 else 0
        }
    }
    
    return CustomJSONResponse(
        data=analytics_data,
        message="Backtesting analytics retrieved successfully"
    )

# Search and Bulk Operations
@router.post("/search", response_model=BacktestingAnalysisListResponse)
async def search_analyses(
    search_params: BacktestingSearchParams = ...,
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Search backtesting analyses with advanced criteria."""
    pagination = PaginationParams(page=page, size=size)
    result = backtesting_service.search_analyses(search_params, pagination)
    
    return BacktestingAnalysisListResponse(
        data=result.items,
        total=result.total,
        page=result.page,
        size=result.size,
        pages=result.pages
    )

@router.post("/bulk-operations", response_model=CustomJSONResponse)
async def perform_bulk_operation(
    operation: BacktestingBulkOperation = ...,
    backtesting_service: BacktestingService = Depends(get_backtesting_service),
    current_user: User = Depends(get_current_user)
):
    """Perform bulk operations on backtesting analyses."""
    # Implementation would depend on the specific operation
    if operation.operation == "delete":
        deleted_count = 0
        for analysis_id in operation.analysis_ids:
            try:
                backtesting_service.delete_analysis(analysis_id, current_user.email)
                deleted_count += 1
            except Exception:
                continue
        
        return CustomJSONResponse(
            data={"deleted_count": deleted_count, "total_requested": len(operation.analysis_ids)},
            message=f"Bulk delete completed: {deleted_count} analyses deleted"
        )
    
    elif operation.operation == "update_status":
        new_status = operation.parameters.get("status") if operation.parameters else "created"
        updated_count = 0
        
        for analysis_id in operation.analysis_ids:
            try:
                update_data = BacktestingAnalysisUpdate(status=new_status)
                backtesting_service.update_analysis(analysis_id, update_data, current_user.email)
                updated_count += 1
            except Exception:
                continue
        
        return CustomJSONResponse(
            data={"updated_count": updated_count, "total_requested": len(operation.analysis_ids)},
            message=f"Bulk status update completed: {updated_count} analyses updated"
        )
    
    else:
        raise HTTPException(status_code=400, detail="Unsupported bulk operation")

# Database Utilities
@router.get("/database/tables")
async def get_available_tables(
    current_user: User = Depends(get_current_user)
):
    """Get list of available database tables for analysis."""
    try:
        tables = get_available_tables()
        return {"tables": tables}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve tables: {str(e)}")

@router.get("/database/tables/{table_name}/schema")
async def get_table_schema_info(
    table_name: str = Path(..., description="The table name"),
    current_user: User = Depends(get_current_user)
):
    """Get schema information for a specific table."""
    try:
        schema = get_table_schema(table_name)
        return {"table_name": table_name, "schema": schema}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve schema: {str(e)}")

@router.get("/database/health")
async def check_database_health(
    current_user: User = Depends(get_current_user)
):
    """Check database connection health."""
    try:
        is_healthy = await test_database_connection()
        return {"healthy": is_healthy, "timestamp": datetime.now()}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Database health check failed: {str(e)}")

# Legacy Compatibility Endpoints
@router.post("/legacy/create-analysis", response_model=LegacyBacktestingResponse)
async def create_analysis_legacy(
    analysis_config: Dict[str, Any] = ...,
    backtesting_service: BacktestingService = Depends(get_backtesting_service)
):
    """Legacy analysis creation endpoint for backward compatibility."""
    try:
        # Convert legacy format to new format
        analysis_request = BacktestingAnalysisRequest(
            analysisId=analysis_config.get("analysisId", f"legacy_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
            analysisType=analysis_config.get("analysisType", "rule"),
            timestamp=datetime.now().isoformat(),
            dataTable=analysis_config.get("dataTable", {}),
            configuration=analysis_config.get("configuration", {}),
            filters=analysis_config.get("filters", {}),
            analysis=analysis_config.get("analysis", {})
        )
        
        result = backtesting_service.create_analysis(analysis_request, "legacy_user")
        
        return LegacyBacktestingResponse(
            success=True,
            message="Analysis created successfully",
            data={"analysis_id": result.analysis_id, "status": result.status}
        )
    except Exception as e:
        return LegacyBacktestingResponse(
            success=False,
            message=f"Failed to create analysis: {str(e)}"
        )

# Health Check
@router.get("/health", response_model=CustomJSONResponse)
async def health_check():
    """Backtesting module health check."""
    return CustomJSONResponse(
        data={"status": "healthy", "module": "backtesting"},
        message="Backtesting module is operational"
    )
