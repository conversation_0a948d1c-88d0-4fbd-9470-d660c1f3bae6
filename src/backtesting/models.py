# Backtesting models
from sqlalchemy import Column, String, DateTime, Text, UUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

class BacktestingAnalysis(Base):
    """
    Model for storing backtesting analysis configurations and results.
    
    This table stores:
    - Analysis configurations (rules, metrics, filters)
    - Generated SQL queries for data processing
    - Analysis status and metadata
    """
    __tablename__ = 'backtesting_analysis'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    analysis_id = Column(String(100), unique=True, nullable=False, index=True)
    analysis_type = Column(String(20), nullable=False)  # 'rule' or 'metric'
    analysis_config = Column(JSONB, nullable=False)  # Store the entire analysis configuration
    prelim_sql = Column(Text, nullable=True)  # Generated preliminary SQL
    pivot_sql = Column(Text, nullable=True)  # Generated pivot table SQL
    status = Column(String(20), default='created')  # created, processing, completed, failed
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    def __repr__(self):
        return f"<BacktestingAnalysis(id={self.id}, analysis_id={self.analysis_id}, type={self.analysis_type})>"
