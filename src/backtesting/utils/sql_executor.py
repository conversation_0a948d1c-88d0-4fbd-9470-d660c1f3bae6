# SQL Executor for backtesting queries
import asyncio
import asyncpg
import pandas as pd
from typing import List, Dict, Any, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
import logging

logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://user:password@localhost/dbname")

async def execute_sql_query_async(sql_query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """Execute SQL query asynchronously and return results."""
    try:
        # Parse connection string for asyncpg
        if DATABASE_URL.startswith("postgresql://"):
            conn_str = DATABASE_URL.replace("postgresql://", "")
        else:
            conn_str = DATABASE_URL
        
        # Connect to database
        conn = await asyncpg.connect(DATABASE_URL)
        
        try:
            # Execute query
            if parameters:
                rows = await conn.fetch(sql_query, *parameters.values())
            else:
                rows = await conn.fetch(sql_query)
            
            # Convert to list of dictionaries
            results = [dict(row) for row in rows]
            
            logger.info(f"Query executed successfully, returned {len(results)} rows")
            return results
            
        finally:
            await conn.close()
            
    except Exception as e:
        logger.error(f"Error executing SQL query: {str(e)}")
        logger.error(f"Query: {sql_query}")
        raise

def execute_sql_query_sync(sql_query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """Execute SQL query synchronously and return results."""
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)
        
        # Execute query
        with engine.connect() as conn:
            if parameters:
                result = conn.execute(text(sql_query), parameters)
            else:
                result = conn.execute(text(sql_query))
            
            # Convert to list of dictionaries
            columns = result.keys()
            results = [dict(zip(columns, row)) for row in result.fetchall()]
            
            logger.info(f"Query executed successfully, returned {len(results)} rows")
            return results
            
    except Exception as e:
        logger.error(f"Error executing SQL query: {str(e)}")
        logger.error(f"Query: {sql_query}")
        raise

def execute_sql_with_pandas(sql_query: str, parameters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
    """Execute SQL query and return results as pandas DataFrame."""
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)
        
        # Execute query with pandas
        if parameters:
            df = pd.read_sql_query(sql_query, engine, params=parameters)
        else:
            df = pd.read_sql_query(sql_query, engine)
        
        logger.info(f"Query executed successfully, returned {len(df)} rows")
        return df
        
    except Exception as e:
        logger.error(f"Error executing SQL query with pandas: {str(e)}")
        logger.error(f"Query: {sql_query}")
        raise

def validate_sql_query(sql_query: str) -> bool:
    """Validate SQL query syntax without executing it."""
    try:
        # Basic validation - check for dangerous operations
        dangerous_keywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE']
        upper_query = sql_query.upper()
        
        for keyword in dangerous_keywords:
            if keyword in upper_query:
                logger.warning(f"Potentially dangerous SQL keyword detected: {keyword}")
                return False
        
        # Check for basic SQL structure
        if not any(keyword in upper_query for keyword in ['SELECT', 'WITH']):
            logger.warning("Query does not appear to be a SELECT statement")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Error validating SQL query: {str(e)}")
        return False

def format_query_results(results: List[Dict[str, Any]], max_rows: int = 1000) -> Dict[str, Any]:
    """Format query results for API response."""
    try:
        # Limit results if too many
        if len(results) > max_rows:
            limited_results = results[:max_rows]
            truncated = True
        else:
            limited_results = results
            truncated = False
        
        # Convert any datetime objects to strings
        formatted_results = []
        for row in limited_results:
            formatted_row = {}
            for key, value in row.items():
                if hasattr(value, 'isoformat'):  # datetime objects
                    formatted_row[key] = value.isoformat()
                elif isinstance(value, (int, float, str, bool)) or value is None:
                    formatted_row[key] = value
                else:
                    formatted_row[key] = str(value)
            formatted_results.append(formatted_row)
        
        return {
            "data": formatted_results,
            "row_count": len(results),
            "returned_rows": len(limited_results),
            "truncated": truncated
        }
        
    except Exception as e:
        logger.error(f"Error formatting query results: {str(e)}")
        raise

async def test_database_connection() -> bool:
    """Test database connection."""
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        await conn.execute("SELECT 1")
        await conn.close()
        return True
    except Exception as e:
        logger.error(f"Database connection test failed: {str(e)}")
        return False

def get_table_schema(table_name: str) -> List[Dict[str, Any]]:
    """Get table schema information."""
    try:
        engine = create_engine(DATABASE_URL)
        
        schema_query = """
        SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default
        FROM information_schema.columns 
        WHERE table_name = :table_name
        ORDER BY ordinal_position
        """
        
        with engine.connect() as conn:
            result = conn.execute(text(schema_query), {"table_name": table_name})
            columns = result.keys()
            schema_info = [dict(zip(columns, row)) for row in result.fetchall()]
        
        return schema_info
        
    except Exception as e:
        logger.error(f"Error getting table schema for {table_name}: {str(e)}")
        raise

def get_available_tables() -> List[str]:
    """Get list of available tables."""
    try:
        engine = create_engine(DATABASE_URL)
        
        tables_query = """
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
        """
        
        with engine.connect() as conn:
            result = conn.execute(text(tables_query))
            tables = [row[0] for row in result.fetchall()]
        
        return tables
        
    except Exception as e:
        logger.error(f"Error getting available tables: {str(e)}")
        raise
