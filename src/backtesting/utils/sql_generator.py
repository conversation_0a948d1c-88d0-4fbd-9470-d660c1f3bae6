# SQL Generator for backtesting analysis
from typing import Dict, Any, List
import json

def process_analysis_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """Process and validate analysis configuration."""
    # Extract main components
    data_table = config.get("dataTable", {})
    configuration = config.get("configuration", {})
    filters = config.get("filters", {})
    analysis = config.get("analysis", {})
    
    return {
        "analysis_type": config.get("analysisType", "rule"),
        "data_table": data_table,
        "configuration": configuration,
        "filters": filters,
        "analysis": analysis
    }

def generate_filter_conditions(filters: Dict[str, Any], column_mappings: Dict[str, Any]) -> str:
    """Generate WHERE clause conditions from filters."""
    conditions = []
    
    # Timeframe filter
    timeframe = filters.get("timeframeFilter", {})
    if timeframe.get("startDate") and timeframe.get("endDate"):
        datetime_col = column_mappings.get("datetime", {}).get("mappedTo", "txn_datetime")
        conditions.append(f"{datetime_col} BETWEEN '{timeframe['startDate']}' AND '{timeframe['endDate']}'")
    
    # Population filters
    population_filters = filters.get("populationFilters", [])
    for pop_filter in population_filters:
        field = pop_filter.get("field")
        operator = pop_filter.get("operator")
        value = pop_filter.get("value")
        
        if field and operator and value is not None:
            if operator == "equals":
                conditions.append(f"{field} = '{value}'")
            elif operator == "not_equals":
                conditions.append(f"{field} != '{value}'")
            elif operator == "greater_than":
                conditions.append(f"{field} > {value}")
            elif operator == "less_than":
                conditions.append(f"{field} < {value}")
            elif operator == "in":
                if isinstance(value, list):
                    value_str = "', '".join(str(v) for v in value)
                    conditions.append(f"{field} IN ('{value_str}')")
    
    return " AND ".join(conditions) if conditions else "1=1"

def generate_bucket_case_statements(bucketing: Dict[str, Any]) -> List[str]:
    """Generate CASE statements for bucketing."""
    if not bucketing:
        return []
    
    bucket_column = bucketing.get("bucketingColumn")
    buckets = bucketing.get("buckets", [])
    
    if not bucket_column or not buckets:
        return []
    
    case_conditions = []
    for bucket in buckets:
        bucket_name = bucket.get("bucketName")
        bucket_range = bucket.get("bucketRange", {})
        min_val = bucket_range.get("min")
        max_val = bucket_range.get("max")
        
        if min_val is not None and max_val is not None:
            case_conditions.append(f"WHEN {bucket_column} BETWEEN {min_val} AND {max_val} THEN '{bucket_name}'")
        elif min_val is not None:
            case_conditions.append(f"WHEN {bucket_column} >= {min_val} THEN '{bucket_name}'")
        elif max_val is not None:
            case_conditions.append(f"WHEN {bucket_column} <= {max_val} THEN '{bucket_name}'")
    
    if case_conditions:
        case_statement = f"CASE {' '.join(case_conditions)} ELSE 'Other' END AS {bucket_column}_bucket"
        return [case_statement]
    
    return []

def generate_rule_conditions(rule: Dict[str, Any]) -> str:
    """Generate rule condition SQL."""
    if not rule:
        return "1=1"
    
    equation = rule.get("equation", {})
    conditions = equation.get("conditions", [])
    operator = equation.get("operator", "AND")
    
    condition_parts = []
    for condition in conditions:
        field = condition.get("field")
        op = condition.get("operator")
        value = condition.get("value")
        
        if field and op and value is not None:
            if op == "equals":
                condition_parts.append(f"{field} = '{value}'")
            elif op == "not_equals":
                condition_parts.append(f"{field} != '{value}'")
            elif op == "greater_than":
                condition_parts.append(f"{field} > {value}")
            elif op == "less_than":
                condition_parts.append(f"{field} < {value}")
            elif op == "greater_than_or_equal":
                condition_parts.append(f"{field} >= {value}")
            elif op == "less_than_or_equal":
                condition_parts.append(f"{field} <= {value}")
            elif op == "contains":
                condition_parts.append(f"{field} ILIKE '%{value}%'")
            elif op == "in":
                if isinstance(value, list):
                    value_str = "', '".join(str(v) for v in value)
                    condition_parts.append(f"{field} IN ('{value_str}')")
    
    if condition_parts:
        return f" {operator} ".join(condition_parts)
    
    return "1=1"

def generate_preliminary_sql(config: Dict[str, Any]) -> str:
    """Generate preliminary data processing SQL."""
    data_table = config.get("data_table", {})
    configuration = config.get("configuration", {})
    filters = config.get("filters", {})
    analysis = config.get("analysis", {})
    
    table_name = data_table.get("selectedTable", "transaction_data")
    column_mappings = configuration.get("columnMappings", {})
    bucketing = analysis.get("bucketing", {})
    
    # Map column names
    txn_id = column_mappings.get("transactionId", {}).get("mappedTo", "txn_id")
    txn_amt = column_mappings.get("amount", {}).get("mappedTo", "txn_amt")
    mer_id = column_mappings.get("applicationId", {}).get("mappedTo", "mer_id")
    fraud_label = column_mappings.get("fraudLabel", {}).get("mappedTo", "fraud_label")
    txn_datetime = column_mappings.get("datetime", {}).get("mappedTo", "txn_datetime")
    
    # Generate filter conditions
    where_conditions = generate_filter_conditions(filters, column_mappings)
    
    # Generate bucket case statements
    bucket_statements = generate_bucket_case_statements(bucketing)
    
    # Base columns
    select_columns = [
        txn_id,
        txn_amt,
        mer_id,
        fraud_label,
        txn_datetime,
        "city",
        "chargeback_amt_30d",
        "txn_amt_30d"
    ]
    
    # Add bucket columns
    if bucket_statements:
        select_columns.extend(bucket_statements)
    
    # Add calculated columns
    calculated_columns = [
        "chargeback_amt_30d / NULLIF(txn_amt_30d, 0) AS chargeback_amt_ratio_30d",
        "TO_CHAR(txn_datetime, 'YYYY-MM') AS txn_mth"
    ]
    select_columns.extend(calculated_columns)
    
    # Add rule condition if it's a rule analysis
    if config.get("analysis_type") == "rule":
        rule_config = analysis.get("rule", {})
        rule_condition = generate_rule_conditions(rule_config)
        rule_column = f"CASE WHEN {rule_condition} THEN 1 ELSE 0 END AS rule_flag"
        select_columns.append(rule_column)
    
    sql = f"""
WITH prelim_data AS (
    SELECT
        {',\n        '.join(select_columns)}
    FROM {table_name}
    WHERE {where_conditions}
)
SELECT * FROM prelim_data
"""
    
    return sql.strip()

def generate_pivot_sql(config: Dict[str, Any], analysis_type: str) -> str:
    """Generate pivot table SQL query for metric or rule performance."""
    bucketing = config.get("analysis", {}).get("bucketing", {})
    
    # Base group by columns
    group_by_columns = ["txn_mth"]
    
    # Add bucket column if bucketing is configured
    if bucketing and bucketing.get("bucketingColumn"):
        bucket_column = bucketing.get("bucketingColumn")
        group_by_columns.append(f"{bucket_column}_bucket")
    
    # Aggregation columns
    agg_columns = [
        "COUNT(*) AS txn_cnt",
        "SUM(txn_amt) AS total_txn_amt",
        "SUM(fraud_label) AS fraud_cnt",
        "SUM(fraud_label) * 100.0 / COUNT(*) AS fraud_rate",
        "SUM(chargeback_amt_30d) AS total_chargeback_amt",
        "AVG(chargeback_amt_ratio_30d) AS avg_chargeback_ratio"
    ]
    
    # Add rule-specific aggregations
    if analysis_type == "rule":
        agg_columns.extend([
            "SUM(rule_flag) AS rule_triggered_cnt",
            "SUM(rule_flag) * 100.0 / COUNT(*) AS rule_trigger_rate",
            "SUM(CASE WHEN rule_flag = 1 AND fraud_label = 1 THEN 1 ELSE 0 END) AS true_positive",
            "SUM(CASE WHEN rule_flag = 1 AND fraud_label = 0 THEN 1 ELSE 0 END) AS false_positive",
            "SUM(CASE WHEN rule_flag = 0 AND fraud_label = 1 THEN 1 ELSE 0 END) AS false_negative",
            "SUM(CASE WHEN rule_flag = 0 AND fraud_label = 0 THEN 1 ELSE 0 END) AS true_negative"
        ])
    
    sql = f"""
WITH prelim_data AS (
    -- This will be replaced with the preliminary SQL
    SELECT * FROM prelim_data_placeholder
),
{analysis_type}_performance_pivot_table AS (
    SELECT
        {',\n        '.join(group_by_columns + agg_columns)}
    FROM prelim_data
    GROUP BY {', '.join(group_by_columns)}
)
SELECT * FROM {analysis_type}_performance_pivot_table
ORDER BY {group_by_columns[0] if group_by_columns else 'txn_cnt'} DESC
"""
    
    return sql.strip()

def generate_combined_sql(config: Dict[str, Any]) -> Dict[str, str]:
    """Generate all SQL queries for the analysis."""
    processed_config = process_analysis_config(config)
    analysis_type = processed_config.get("analysis_type", "rule")
    
    # Generate preliminary SQL
    prelim_sql = generate_preliminary_sql(processed_config)
    
    # Generate pivot SQL template
    pivot_sql = generate_pivot_sql(processed_config, analysis_type)
    
    # Create properly formatted combined SQL
    # Extract the prelim_data CTE content
    prelim_cte_content = prelim_sql.split("WITH prelim_data AS (")[1].split("SELECT * FROM prelim_data")[0].strip()
    
    # Extract the pivot table part
    pivot_parts = pivot_sql.split("WITH prelim_data AS (")[1].split("),")
    pivot_table_part = "),".join(pivot_parts[1:])
    
    # Combine them properly
    combined_sql = f"""
WITH prelim_data AS (
{prelim_cte_content}
{pivot_table_part}
"""
    
    return {
        "preliminary_sql": prelim_sql,
        "pivot_sql": pivot_sql,
        "combined_sql": combined_sql.strip()
    }
