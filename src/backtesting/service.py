# Backtesting service
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json
import asyncio
from uuid import UUID

from .models import BacktestingAnalysis
from .schemas import (
    BacktestingAnalysisCreate, BacktestingAnalysisUpdate, BacktestingAnalysisResponse,
    BacktestingAnalysisRequest, BacktestingExecutionRequest, BacktestingExecutionResponse,
    BacktestingStatistics, BacktestingSearchParams, BacktestingBulkOperation
)
from .exceptions import BacktestingAnalysisNotFoundError, BacktestingExecutionError
from .utils.sql_generator import generate_combined_sql
from .utils.sql_executor import execute_sql_query_async
from ..pagination import PaginationParams, PaginatedResponse

class BacktestingService:
    """Service for managing backtesting analyses and execution."""
    
    def __init__(self, db: Session):
        self.db = db

    def create_analysis(self, analysis_data: BacktestingAnalysisRequest, created_by: str) -> BacktestingAnalysisResponse:
        """Create a new backtesting analysis."""
        # Check if analysis_id already exists
        existing = self.db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_id == analysis_data.analysisId
        ).first()
        
        if existing:
            raise ValueError("Analysis ID already exists")

        # Convert request to dict for processing
        config_dict = analysis_data.model_dump()

        # Generate SQL queries
        sql_queries = generate_combined_sql(config_dict)

        # Create new analysis record
        db_analysis = BacktestingAnalysis(
            analysis_id=analysis_data.analysisId,
            analysis_type=analysis_data.analysisType,
            analysis_config=config_dict,
            prelim_sql=sql_queries["preliminary_sql"],
            pivot_sql=sql_queries["combined_sql"],
            status="created"
        )

        self.db.add(db_analysis)
        self.db.commit()
        self.db.refresh(db_analysis)

        return BacktestingAnalysisResponse(
            id=str(db_analysis.id),
            analysis_id=db_analysis.analysis_id,
            analysis_type=db_analysis.analysis_type,
            status=db_analysis.status,
            preliminary_sql=db_analysis.prelim_sql,
            pivot_sql=db_analysis.pivot_sql,
            created_at=db_analysis.created_at,
            updated_at=db_analysis.updated_at
        )

    def get_analyses(
        self, 
        pagination: PaginationParams,
        analysis_type: Optional[str] = None,
        status: Optional[str] = None
    ) -> PaginatedResponse[BacktestingAnalysisResponse]:
        """Get paginated list of analyses with filtering."""
        query = self.db.query(BacktestingAnalysis)

        if analysis_type:
            query = query.filter(BacktestingAnalysis.analysis_type == analysis_type)
        
        if status:
            query = query.filter(BacktestingAnalysis.status == status)

        total = query.count()
        
        analyses = query.offset(pagination.offset).limit(pagination.size).all()
        
        items = [
            BacktestingAnalysisResponse(
                id=str(analysis.id),
                analysis_id=analysis.analysis_id,
                analysis_type=analysis.analysis_type,
                status=analysis.status,
                preliminary_sql=analysis.prelim_sql,
                pivot_sql=analysis.pivot_sql,
                created_at=analysis.created_at,
                updated_at=analysis.updated_at
            )
            for analysis in analyses
        ]

        return PaginatedResponse(
            items=items,
            total=total,
            page=pagination.page,
            size=pagination.size,
            pages=(total + pagination.size - 1) // pagination.size
        )

    def get_analysis_by_id(self, analysis_id: str) -> BacktestingAnalysisResponse:
        """Get analysis by ID."""
        analysis = self.db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_id == analysis_id
        ).first()

        if not analysis:
            raise BacktestingAnalysisNotFoundError(f"Analysis with ID {analysis_id} not found")

        return BacktestingAnalysisResponse(
            id=str(analysis.id),
            analysis_id=analysis.analysis_id,
            analysis_type=analysis.analysis_type,
            status=analysis.status,
            preliminary_sql=analysis.prelim_sql,
            pivot_sql=analysis.pivot_sql,
            created_at=analysis.created_at,
            updated_at=analysis.updated_at
        )

    def update_analysis(
        self, 
        analysis_id: str, 
        analysis_data: BacktestingAnalysisUpdate,
        updated_by: str
    ) -> BacktestingAnalysisResponse:
        """Update an existing analysis."""
        analysis = self.db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_id == analysis_id
        ).first()

        if not analysis:
            raise BacktestingAnalysisNotFoundError(f"Analysis with ID {analysis_id} not found")

        # Update fields
        if analysis_data.analysis_config is not None:
            analysis.analysis_config = analysis_data.analysis_config
            # Regenerate SQL if config changed
            sql_queries = generate_combined_sql(analysis_data.analysis_config)
            analysis.prelim_sql = sql_queries["preliminary_sql"]
            analysis.pivot_sql = sql_queries["combined_sql"]
        
        if analysis_data.status is not None:
            analysis.status = analysis_data.status

        analysis.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(analysis)

        return BacktestingAnalysisResponse(
            id=str(analysis.id),
            analysis_id=analysis.analysis_id,
            analysis_type=analysis.analysis_type,
            status=analysis.status,
            preliminary_sql=analysis.prelim_sql,
            pivot_sql=analysis.pivot_sql,
            created_at=analysis.created_at,
            updated_at=analysis.updated_at
        )

    def delete_analysis(self, analysis_id: str, deleted_by: str) -> bool:
        """Delete an analysis."""
        analysis = self.db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_id == analysis_id
        ).first()

        if not analysis:
            raise BacktestingAnalysisNotFoundError(f"Analysis with ID {analysis_id} not found")

        self.db.delete(analysis)
        self.db.commit()
        return True

    async def execute_analysis(
        self, 
        analysis_id: str, 
        execution_request: BacktestingExecutionRequest
    ) -> BacktestingExecutionResponse:
        """Execute analysis SQL queries and return results."""
        analysis = self.db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_id == analysis_id
        ).first()

        if not analysis:
            raise BacktestingAnalysisNotFoundError(f"Analysis with ID {analysis_id} not found")

        # Update status to processing
        analysis.status = "processing"
        self.db.commit()

        try:
            start_time = datetime.now()
            
            # Determine which SQL to execute
            if execution_request.query_type == "preliminary":
                sql_to_execute = analysis.prelim_sql
            elif execution_request.query_type == "pivot":
                sql_to_execute = analysis.pivot_sql
            else:  # combined
                sql_to_execute = analysis.pivot_sql  # This contains the combined query

            # Execute SQL query
            results = await execute_sql_query_async(sql_to_execute)
            
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            # Update status to completed
            analysis.status = "completed"
            self.db.commit()

            return BacktestingExecutionResponse(
                analysis_id=analysis_id,
                query_type=execution_request.query_type,
                execution_time=execution_time,
                row_count=len(results),
                data=results,
                status="completed",
                executed_at=end_time
            )

        except Exception as e:
            # Update status to failed
            analysis.status = "failed"
            self.db.commit()
            raise BacktestingExecutionError(f"Failed to execute analysis: {str(e)}")

    def get_statistics(self) -> BacktestingStatistics:
        """Get backtesting statistics."""
        total_analyses = self.db.query(BacktestingAnalysis).count()
        rule_analyses = self.db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_type == "rule"
        ).count()
        metric_analyses = self.db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.analysis_type == "metric"
        ).count()
        completed_analyses = self.db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.status == "completed"
        ).count()
        failed_analyses = self.db.query(BacktestingAnalysis).filter(
            BacktestingAnalysis.status == "failed"
        ).count()

        # Get most recent analysis
        most_recent = self.db.query(BacktestingAnalysis).order_by(
            BacktestingAnalysis.created_at.desc()
        ).first()

        return BacktestingStatistics(
            total_analyses=total_analyses,
            rule_analyses=rule_analyses,
            metric_analyses=metric_analyses,
            completed_analyses=completed_analyses,
            failed_analyses=failed_analyses,
            most_recent_analysis=most_recent.created_at if most_recent else None
        )

    def search_analyses(
        self, 
        search_params: BacktestingSearchParams,
        pagination: PaginationParams
    ) -> PaginatedResponse[BacktestingAnalysisResponse]:
        """Search analyses with advanced criteria."""
        query = self.db.query(BacktestingAnalysis)

        if search_params.analysis_type:
            query = query.filter(BacktestingAnalysis.analysis_type == search_params.analysis_type)
        
        if search_params.status:
            query = query.filter(BacktestingAnalysis.status == search_params.status)
        
        if search_params.created_from:
            query = query.filter(BacktestingAnalysis.created_at >= search_params.created_from)
        
        if search_params.created_to:
            query = query.filter(BacktestingAnalysis.created_at <= search_params.created_to)
        
        if search_params.search_term:
            query = query.filter(
                or_(
                    BacktestingAnalysis.analysis_id.ilike(f"%{search_params.search_term}%"),
                    BacktestingAnalysis.analysis_config.astext.ilike(f"%{search_params.search_term}%")
                )
            )

        total = query.count()
        analyses = query.offset(pagination.offset).limit(pagination.size).all()
        
        items = [
            BacktestingAnalysisResponse(
                id=str(analysis.id),
                analysis_id=analysis.analysis_id,
                analysis_type=analysis.analysis_type,
                status=analysis.status,
                preliminary_sql=analysis.prelim_sql,
                pivot_sql=analysis.pivot_sql,
                created_at=analysis.created_at,
                updated_at=analysis.updated_at
            )
            for analysis in analyses
        ]

        return PaginatedResponse(
            items=items,
            total=total,
            page=pagination.page,
            size=pagination.size,
            pages=(total + pagination.size - 1) // pagination.size
        )
