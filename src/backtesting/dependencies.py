# Backtesting dependencies
from fastapi import Depends
from sqlalchemy.orm import Session
from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from .service import BacktestingService

def get_backtesting_service(db: Session = Depends(get_db)) -> BacktestingService:
    """Get backtesting service instance."""
    return BacktestingService(db)

def get_current_user_dependency() -> User:
    """Get current user dependency."""
    return Depends(get_current_user)
