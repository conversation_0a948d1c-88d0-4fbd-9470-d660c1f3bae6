# Backtesting schemas
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from uuid import UUID

# Column Configuration Models
class ColumnMapping(BaseModel):
    mappedTo: str
    description: str

class ColumnConfig(BaseModel):
    rowUniqueId: str
    datetime: str
    fraudLabel: str
    amount: str
    applicationId: Optional[str] = ""
    accountId: Optional[str] = ""
    transactionId: str

class DataTable(BaseModel):
    selectedTable: str
    columnConfig: ColumnConfig
    tableDescription: str

class Configuration(BaseModel):
    columnMappings: Dict[str, ColumnMapping]
    autoDetected: bool

# Filter Models
class TimeframeFilter(BaseModel):
    startDate: str
    endDate: str

class Filters(BaseModel):
    timeframeFilter: TimeframeFilter
    populationFilters: List[Dict[str, Any]] = []
    timeframeAutoDetected: bool

# Bucketing Models
class BucketRange(BaseModel):
    min: Optional[float] = None
    max: Optional[float] = None

class BucketConfig(BaseModel):
    bucketName: str
    bucketRange: BucketRange

class BucketingConfig(BaseModel):
    bucketingColumn: str
    buckets: List[BucketConfig]

# Rule Models
class RuleCondition(BaseModel):
    field: str
    operator: str
    value: Any
    logicalOperator: Optional[str] = None

class RuleEquation(BaseModel):
    operator: str
    conditions: List[RuleCondition]

class Rule(BaseModel):
    equation: RuleEquation
    operator: str
    conditions: List[RuleCondition]

# Analysis Models
class Analysis(BaseModel):
    type: str
    rule: Optional[Rule] = None
    bucketing: Optional[BucketingConfig] = None

# Request/Response Models
class BacktestingAnalysisRequest(BaseModel):
    analysisId: str
    analysisType: str
    timestamp: str
    dataTable: DataTable
    configuration: Configuration
    filters: Filters
    analysis: Analysis

class BacktestingAnalysisResponse(BaseModel):
    id: str
    analysis_id: str
    analysis_type: str
    status: str
    preliminary_sql: Optional[str] = None
    pivot_sql: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class BacktestingAnalysisCreate(BaseModel):
    analysis_id: str
    analysis_type: str = Field(..., regex="^(rule|metric)$")
    analysis_config: Dict[str, Any]

class BacktestingAnalysisUpdate(BaseModel):
    analysis_config: Optional[Dict[str, Any]] = None
    status: Optional[str] = Field(None, regex="^(created|processing|completed|failed)$")

class BacktestingAnalysisListResponse(BaseModel):
    data: List[BacktestingAnalysisResponse]
    total: int
    page: int
    size: int
    pages: int

class BacktestingExecutionRequest(BaseModel):
    query_type: str = Field("combined", regex="^(preliminary|pivot|combined)$")
    parameters: Optional[Dict[str, Any]] = None

class BacktestingExecutionResponse(BaseModel):
    analysis_id: str
    query_type: str
    execution_time: float
    row_count: int
    data: List[Dict[str, Any]]
    status: str
    executed_at: datetime

class BacktestingStatistics(BaseModel):
    total_analyses: int
    rule_analyses: int
    metric_analyses: int
    completed_analyses: int
    failed_analyses: int
    avg_execution_time: Optional[float] = None
    most_recent_analysis: Optional[datetime] = None

class BacktestingSearchParams(BaseModel):
    analysis_type: Optional[str] = None
    status: Optional[str] = None
    created_from: Optional[datetime] = None
    created_to: Optional[datetime] = None
    search_term: Optional[str] = None

class BacktestingBulkOperation(BaseModel):
    operation: str = Field(..., regex="^(delete|update_status|export)$")
    analysis_ids: List[str]
    parameters: Optional[Dict[str, Any]] = None

class CustomJSONResponse(BaseModel):
    data: Any
    message: str
    status: str = "success"

class LegacyBacktestingResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
