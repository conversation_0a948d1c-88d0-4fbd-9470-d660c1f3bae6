# Backtesting exceptions
from fastapi import HTTPException

class BacktestingAnalysisNotFoundError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(status_code=404, detail=detail)

class BacktestingExecutionError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(status_code=500, detail=detail)

class BacktestingValidationError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(status_code=400, detail=detail)

class BacktestingConfigurationError(HTTPException):
    def __init__(self, detail: str):
        super().__init__(status_code=422, detail=detail)
