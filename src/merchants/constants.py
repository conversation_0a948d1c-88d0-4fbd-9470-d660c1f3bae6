# merchants constants - module-specific constants for merchants

# Merchant status options
MERCHANT_STATUS_ACTIVE = "active"
MERCHANT_STATUS_INACTIVE = "inactive"
MERCHANT_STATUS_SUSPENDED = "suspended"
MERCHANT_STATUS_PENDING = "pending"

MERCHANT_STATUSES = [
    MERCHANT_STATUS_ACTIVE,
    MERCHANT_STATUS_INACTIVE,
    MERCHANT_STATUS_SUSPENDED,
    MERCHANT_STATUS_PENDING
]

# Business types
BUSINESS_TYPE_SOLE_PROPRIETORSHIP = "sole_proprietorship"
BUSINESS_TYPE_PARTNERSHIP = "partnership"
BUSINESS_TYPE_PRIVATE_LIMITED = "private_limited"
BUSINESS_TYPE_PUBLIC_LIMITED = "public_limited"
BUSINESS_TYPE_LLP = "llp"

BUSINESS_TYPES = [
    BUSINESS_TYPE_SOLE_PROPRIETORSHIP,
    BUSINESS_TYPE_PARTNERSHIP,
    BUSINESS_TYPE_PRIVATE_LIMITED,
    BUSINESS_TYPE_PUBLIC_LIMITED,
    BUSINESS_TYPE_LLP
]

# KYC verification statuses
KYC_STATUS_PENDING = "pending"
KYC_STATUS_VERIFIED = "verified"
KYC_STATUS_REJECTED = "rejected"
KYC_STATUS_EXPIRED = "expired"

KYC_STATUSES = [
    KYC_STATUS_PENDING,
    KYC_STATUS_VERIFIED,
    KYC_STATUS_REJECTED,
    KYC_STATUS_EXPIRED
]

# Contact types
CONTACT_TYPE_PRIMARY = "primary"
CONTACT_TYPE_SECONDARY = "secondary"
CONTACT_TYPE_BILLING = "billing"
CONTACT_TYPE_TECHNICAL = "technical"

CONTACT_TYPES = [
    CONTACT_TYPE_PRIMARY,
    CONTACT_TYPE_SECONDARY,
    CONTACT_TYPE_BILLING,
    CONTACT_TYPE_TECHNICAL
]

# Account types
ACCOUNT_TYPE_SAVINGS = "savings"
ACCOUNT_TYPE_CURRENT = "current"
ACCOUNT_TYPE_ESCROW = "escrow"

ACCOUNT_TYPES = [
    ACCOUNT_TYPE_SAVINGS,
    ACCOUNT_TYPE_CURRENT,
    ACCOUNT_TYPE_ESCROW
]

# Risk levels
RISK_LEVEL_LOW = "low"
RISK_LEVEL_MEDIUM = "medium"
RISK_LEVEL_HIGH = "high"
RISK_LEVEL_CRITICAL = "critical"

RISK_LEVELS = [
    RISK_LEVEL_LOW,
    RISK_LEVEL_MEDIUM,
    RISK_LEVEL_HIGH,
    RISK_LEVEL_CRITICAL
]

# Pagination defaults
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100