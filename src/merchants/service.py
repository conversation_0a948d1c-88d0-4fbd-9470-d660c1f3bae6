# merchants service - business logic for merchants module
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
from uuid import UUID
from fastapi import HTTPException, status
from .models import Merchant, MerchantContact, MerchantBanking, digital_information, network_overview, relationships, AllDFPDataTable, DigitalFootPrint
from .schemas import MerchantCreate, MerchantUpdate, MerchantResponse, MerchantLinkageUpdate, MerchantDigitalFootprintUpdate
from ..pagination import PaginationParams, PaginatedResponse, paginate_query

class MerchantService:
    def __init__(self, db: Session):
        self.db = db

    def create_merchant(self, merchant_data: MerchantCreate) -> Dict[str, Any]:
        """Create a new merchant with contacts and banking information."""
        try:
            # Create merchant
            db_merchant = Merchant(**merchant_data.dict(exclude={'contacts', 'banking'}))
            self.db.add(db_merchant)
            self.db.flush()

            # Create contacts
            for contact in merchant_data.contacts:
                db_contact = MerchantContact(**contact.dict(), merchant_id=db_merchant.id)
                self.db.add(db_contact)

            # Create banking
            db_banking = MerchantBanking(**merchant_data.banking.dict(), merchant_id=db_merchant.id)
            self.db.add(db_banking)

            self.db.commit()
            return {
                "status": "success",
                "message": "Merchant created successfully",
                "data": {"merchant_id": str(db_merchant.id)}
            }
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to create merchant: {str(e)}")

    def get_merchant_by_id(self, merchant_id: UUID) -> Merchant:
        """Get merchant by ID."""
        merchant = self.db.query(Merchant).filter(Merchant.id == merchant_id).first()
        if not merchant:
            raise HTTPException(status_code=404, detail="Merchant not found")
        return merchant

    def get_merchants(self, pagination: PaginationParams) -> PaginatedResponse[MerchantResponse]:
        """Get paginated list of merchants."""
        query = self.db.query(Merchant)
        merchants, total = paginate_query(query, pagination)

        merchant_responses = []
        for merchant in merchants:
            # Get contacts and banking for each merchant
            contacts = self.db.query(MerchantContact).filter(MerchantContact.merchant_id == merchant.id).all()
            banking = self.db.query(MerchantBanking).filter(MerchantBanking.merchant_id == merchant.id).first()

            merchant_dict = merchant.__dict__.copy()
            merchant_dict['contacts'] = [contact.__dict__ for contact in contacts]
            merchant_dict['banking'] = banking.__dict__ if banking else {}

            merchant_responses.append(MerchantResponse(**merchant_dict))

        return PaginatedResponse.create(merchant_responses, total, pagination.page, pagination.size)

    def update_merchant(self, merchant_id: UUID, merchant_data: MerchantUpdate) -> Dict[str, Any]:
        """Update merchant information."""
        merchant = self.get_merchant_by_id(merchant_id)

        update_data = merchant_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(merchant, field, value)

        self.db.commit()
        return {
            "status": "success",
            "message": "Merchant updated successfully",
            "data": {"merchant_id": str(merchant_id)}
        }

    def delete_merchant(self, merchant_id: UUID) -> Dict[str, Any]:
        """Delete merchant and related data."""
        merchant = self.get_merchant_by_id(merchant_id)

        # Delete related data
        self.db.query(MerchantContact).filter(MerchantContact.merchant_id == merchant_id).delete()
        self.db.query(MerchantBanking).filter(MerchantBanking.merchant_id == merchant_id).delete()
        self.db.query(digital_information).filter(digital_information.merchant_id == merchant_id).delete()
        self.db.query(network_overview).filter(network_overview.merchant_id == merchant_id).delete()
        self.db.query(relationships).filter(relationships.merchant_id == merchant_id).delete()

        # Delete merchant
        self.db.delete(merchant)
        self.db.commit()

        return {
            "status": "success",
            "message": "Merchant deleted successfully",
            "data": {"merchant_id": str(merchant_id)}
        }

    def get_merchant_summary(self, merchant_id: UUID) -> Dict[str, Any]:
        """Get merchant summary."""
        merchant = self.get_merchant_by_id(merchant_id)
        return {"summary": merchant.summery}

    def get_merchant_digital_footprint(self, merchant_id: UUID) -> Dict[str, Any]:
        """Get merchant digital footprint information."""
        merchant = self.get_merchant_by_id(merchant_id)

        # Get latest digital information
        digital_info = self.db.query(AllDFPDataTable).filter(
            AllDFPDataTable.merchant_id == str(merchant_id)
        ).order_by(AllDFPDataTable.timestamp.desc()).first()

        if not digital_info:
            raise HTTPException(status_code=404, detail="Digital information not found")

        return {
            "success": True,
            "message": "Digital footprint retrieved successfully",
            "data": {
                "digital_footprint": digital_info.data
            }
        }

    def get_merchant_linkages(self, merchant_id: UUID) -> Dict[str, Any]:
        """Get merchant network linkages."""
        merchant = self.get_merchant_by_id(merchant_id)

        # Get network overview
        overview = self.db.query(network_overview).filter(
            network_overview.merchant_id == merchant_id
        ).first()

        # Get related entities
        related_entities = self.db.query(relationships).filter(
            relationships.merchant_id == merchant_id
        ).all()

        return {
            "merchant_id": str(merchant_id),
            "network_overview": overview.__dict__ if overview else None,
            "related_entities": [entity.__dict__ for entity in related_entities]
        }

    def update_merchant_linkages(self, merchant_id: UUID, linkage_data: MerchantLinkageUpdate) -> Dict[str, Any]:
        """Update merchant linkage information."""
        merchant = self.get_merchant_by_id(merchant_id)

        try:
            # Delete existing records
            self.db.query(network_overview).filter(network_overview.merchant_id == merchant_id).delete()
            self.db.query(relationships).filter(relationships.merchant_id == merchant_id).delete()

            # Create new network overview
            overview_data = linkage_data.network_overview.dict()
            overview_data['merchant_id'] = merchant_id
            db_overview = network_overview(**overview_data)
            self.db.add(db_overview)

            # Create new relationships
            for entity in linkage_data.linked_entities:
                entity_data = entity.dict(exclude={'details'})
                entity_data['merchant_id'] = merchant_id
                db_entity = relationships(**entity_data)
                self.db.add(db_entity)

            self.db.commit()
            return {
                "status": "success",
                "message": "Merchant linkages updated successfully"
            }
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update linkages: {str(e)}")

    def update_merchant_digital_footprint(self, merchant_id: UUID, footprint_data: MerchantDigitalFootprintUpdate) -> Dict[str, Any]:
        """Update merchant digital footprint."""
        merchant = self.get_merchant_by_id(merchant_id)

        try:
            # Store digital footprint data
            footprint_record = DigitalFootPrint(
                merchant_id=str(merchant_id),
                data=footprint_data.dict()
            )
            self.db.add(footprint_record)
            self.db.commit()

            return {
                "status": "success",
                "message": "Digital footprint updated successfully"
            }
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Failed to update digital footprint: {str(e)}")