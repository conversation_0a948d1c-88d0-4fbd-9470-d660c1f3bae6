# merchants dependencies - dependency injection for merchants module
from fastapi import Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from .models import Merchant
from .service import MerchantService

def get_merchant_service(db: Session = Depends(get_db)) -> MerchantService:
    """Get merchant service instance."""
    return MerchantService(db)

def get_merchant_by_id(
    merchant_id: UUID,
    db: Session = Depends(get_db)
) -> Merchant:
    """Get merchant by ID or raise 404."""
    merchant = db.query(Merchant).filter(Merchant.id == merchant_id).first()
    if not merchant:
        raise HTTPException(status_code=404, detail="Merchant not found")
    return merchant

def get_merchant_with_auth(
    merchant_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> Merchant:
    """Get merchant by ID with authentication."""
    return get_merchant_by_id(merchant_id, db)