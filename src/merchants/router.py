# merchants router - merchant management endpoints
from fastapi import APIRouter, Depends, HTTPException, Path, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from ..database import get_db
from ..auth.dependencies import get_current_user
from ..auth.models import User
from ..pagination import PaginationParams
from .schemas import (
    MerchantCreate, MerchantResponse, MerchantUpdate, MerchantSummaryResponse,
    DigitalFootprintResponse, MerchantLinkageUpdate, MerchantDigitalFootprintUpdate,
    Response, MerchantListResponse
)
from .service import MerchantService

router = APIRouter()

def get_merchant_service(db: Session = Depends(get_db)) -> MerchantService:
    return MerchantService(db)

@router.post("/", response_model=Response)
async def create_merchant(
    merchant: MerchantCreate,
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Create a new merchant with contacts and banking information."""
    return merchant_service.create_merchant(merchant)

@router.get("/", response_model=MerchantListResponse)
async def get_merchants(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Get paginated list of merchants."""
    pagination = PaginationParams(page=page, size=size)
    return merchant_service.get_merchants(pagination)

@router.get("/{merchant_id}", response_model=MerchantResponse)
async def get_merchant(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Get merchant by ID."""
    merchant = merchant_service.get_merchant_by_id(merchant_id)
    # Convert to response format (simplified for now)
    return MerchantResponse(
        id=merchant.id,
        legal_name=merchant.legal_name,
        trade_name=merchant.trade_name,
        business_type=merchant.business_type,
        incorporation_date=merchant.incorporation_date,
        industry=merchant.industry,
        description=merchant.description,
        business_category=merchant.business_category,
        business_subcategory=merchant.business_subcategory,
        business_model=merchant.business_model,
        onboarding_platform=merchant.onboarding_platform,
        status=merchant.status,
        onboarding_date=merchant.onboarding_date,
        created_at=merchant.created_at,
        updated_at=merchant.updated_at,
        contacts=[],  # TODO: Load contacts
        banking={}    # TODO: Load banking
    )

@router.put("/{merchant_id}", response_model=Response)
async def update_merchant(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    merchant_data: MerchantUpdate = None,
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Update merchant information."""
    return merchant_service.update_merchant(merchant_id, merchant_data)

@router.delete("/{merchant_id}", response_model=Response)
async def delete_merchant(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Delete merchant."""
    return merchant_service.delete_merchant(merchant_id)

@router.get("/{merchant_id}/summary", response_model=MerchantSummaryResponse)
async def get_merchant_summary(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Get merchant summary."""
    return merchant_service.get_merchant_summary(merchant_id)

@router.get("/{merchant_id}/digital-footprint")
async def get_merchant_digital_footprint(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Get merchant digital footprint information."""
    return merchant_service.get_merchant_digital_footprint(merchant_id)

@router.get("/{merchant_id}/linkages")
async def get_merchant_linkages(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Get merchant network linkages."""
    return merchant_service.get_merchant_linkages(merchant_id)

@router.post("/{merchant_id}/linkages", response_model=Response)
async def update_merchant_linkages(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    linkage_data: MerchantLinkageUpdate = None,
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Update merchant linkage information."""
    return merchant_service.update_merchant_linkages(merchant_id, linkage_data)

@router.post("/{merchant_id}/digital-footprint", response_model=Response)
async def update_merchant_digital_footprint(
    merchant_id: UUID = Path(..., description="The UUID of the merchant"),
    footprint_data: MerchantDigitalFootprintUpdate = None,
    merchant_service: MerchantService = Depends(get_merchant_service),
    current_user: User = Depends(get_current_user)
):
    """Update merchant digital footprint."""
    return merchant_service.update_merchant_digital_footprint(merchant_id, footprint_data)

# Additional endpoints for merchant operations
@router.post("/add-merchant-id/{merchant_id}", response_model=Response)
async def add_merchant_id(
    merchant_id: UUID = Path(..., description="The UUID to be assigned as merchant ID"),
    merchant_service: MerchantService = Depends(get_merchant_service)
):
    """Basic endpoint to add a merchant ID to the database."""
    try:
        # Check if merchant already exists
        try:
            merchant_service.get_merchant_by_id(merchant_id)
            return {"status": "success", "message": "Merchant ID already exists"}
        except HTTPException:
            # Merchant doesn't exist, create it
            from .models import Merchant
            from datetime import datetime

            db_merchant = Merchant(
                id=merchant_id,
                created_at=datetime.now()
            )
            merchant_service.db.add(db_merchant)
            merchant_service.db.commit()

            return {"status": "success", "message": "Merchant ID added successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add merchant ID: {str(e)}")