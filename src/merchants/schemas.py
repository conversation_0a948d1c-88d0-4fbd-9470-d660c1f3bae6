# merchants schemas - Pydantic models for merchants module
from pydantic import BaseModel, ConfigDict
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime, date
from decimal import Decimal

class MerchantContactBase(BaseModel):
    contact_type: str
    name: str
    email: str
    phone: str
    address: str
    city: str
    state: str
    country: str
    postal_code: str

class MerchantBankingBase(BaseModel):
    account_number: str
    ifsc: str
    bank_name: str
    account_type: str
    verification_status: str
    settlement_cycle: str
    rolling_reserve_percentage: Optional[Decimal] = None
    current_balance: Optional[Decimal] = None

class MerchantBase(BaseModel):
    legal_name: str
    trade_name: str
    business_type: str
    incorporation_date: date
    industry: str
    description: Optional[str] = None
    business_category: str
    business_subcategory: str
    business_model: Optional[str] = None
    onboarding_platform: Optional[str] = None

class MerchantCreate(MerchantBase):
    contacts: List[MerchantContactBase]
    banking: MerchantBankingBase

class MerchantResponse(MerchantBase):
    id: UUID
    status: str
    onboarding_date: date
    created_at: datetime
    updated_at: datetime
    contacts: List[MerchantContactBase]
    banking: MerchantBankingBase
    risk_score: Optional[float] = None

    model_config = ConfigDict(from_attributes=True)

class MerchantUpdate(BaseModel):
    legal_name: Optional[str] = None
    trade_name: Optional[str] = None
    business_type: Optional[str] = None
    industry: Optional[str] = None
    description: Optional[str] = None
    business_category: Optional[str] = None
    business_subcategory: Optional[str] = None
    business_model: Optional[str] = None
    status: Optional[str] = None

class MerchantSummaryResponse(BaseModel):
    summary: Optional[str] = None

class DigitalFootprintResponse(BaseModel):
    digital_information: Optional[Dict[str, Any]] = None
    website_monitoring: Optional[List[Dict[str, Any]]] = None

class NetworkOverviewCreate(BaseModel):
    total_connections: int
    high_risk_connections: int
    network_risk_score: int

class LinkedEntityCreate(BaseModel):
    related_entity_name: str
    relationship_type: str
    registration_number: str
    location: str
    connection_count: int
    risk_level: str
    details: List[Dict[str, str]]

class MerchantLinkageUpdate(BaseModel):
    network_overview: NetworkOverviewCreate
    linked_entities: List[LinkedEntityCreate]
    common_connections: List[Dict[str, Any]]

    model_config = ConfigDict(arbitrary_types_allowed=True)

class MerchantDigitalFootprintUpdate(BaseModel):
    website_metrics: Optional[List[Dict[str, Any]]] = None
    review_sources: Optional[List[Dict[str, Any]]] = None
    social_presence: Optional[List[Dict[str, Any]]] = None
    recent_mentions: Optional[List[Dict[str, Any]]] = None

    model_config = ConfigDict(arbitrary_types_allowed=True)

class MerchantListResponse(BaseModel):
    merchants: List[MerchantResponse]
    total: int
    page: int
    size: int

class Response(BaseModel):
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None