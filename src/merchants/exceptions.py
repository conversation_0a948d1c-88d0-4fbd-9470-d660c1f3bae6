# merchants exceptions - module-specific errors for merchants
from fastapi import HTTPException, status

class MerchantNotFoundError(HTTPException):
    def __init__(self, merchant_id: str = None):
        detail = f"Merchant {merchant_id} not found" if merchant_id else "Merchant not found"
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail=detail)

class MerchantAlreadyExistsError(HTTPException):
    def __init__(self, merchant_id: str = None):
        detail = f"Merchant {merchant_id} already exists" if merchant_id else "Merchant already exists"
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class InvalidMerchantDataError(HTTPException):
    def __init__(self, detail: str = "Invalid merchant data"):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

class MerchantCreationError(HTTPException):
    def __init__(self, detail: str = "Failed to create merchant"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class MerchantUpdateError(HTTPException):
    def __init__(self, detail: str = "Failed to update merchant"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class MerchantDeletionError(HTTPException):
    def __init__(self, detail: str = "Failed to delete merchant"):
        super().__init__(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=detail)

class DigitalFootprintNotFoundError(HTTPException):
    def __init__(self):
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail="Digital footprint not found")

class LinkageDataNotFoundError(HTTPException):
    def __init__(self):
        super().__init__(status_code=status.HTTP_404_NOT_FOUND, detail="Linkage data not found")

class InvalidBusinessTypeError(HTTPException):
    def __init__(self, business_type: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid business type: {business_type}"
        )

class InvalidKYCStatusError(HTTPException):
    def __init__(self, kyc_status: str):
        super().__init__(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid KYC status: {kyc_status}"
        )

class MerchantAccessDeniedError(HTTPException):
    def __init__(self):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to merchant data"
        )