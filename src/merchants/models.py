# merchants models - SQLAlchemy models for merchants module
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, Date, DECIMAL, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import uuid

Base = declarative_base()

class Merchant(Base):
    __tablename__ = 'merchants'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    legal_name = Column(String(255))
    trade_name = Column(String(255))
    business_type = Column(String(100))
    summery = Column(Text)
    email = Column(String(255))
    num_directors = Column(Integer)
    domain = Column(String(255))
    incorporation_date = Column(Date)
    industry = Column(String(100))
    description = Column(Text)
    mca_description = Column(Text)
    business_category = Column(String(100))
    business_subcategory = Column(String(100))
    business_model = Column(String(50))
    onboarding_date = Column(Date)
    onboarding_platform = Column(String(100))
    kyc_verification_status = Column(String(50))
    kyc_verification_date = Column(Date)
    ip_geolocation = Column(String(100))
    first_txn_date = Column(Date)
    last_txn_date = Column(Date)
    status = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class MerchantContact(Base):
    __tablename__ = 'merchant_contacts'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    contact_type = Column(String(50))  # primary, secondary, billing, etc.
    name = Column(String(255))
    email = Column(String(255))
    phone = Column(String(50))
    address = Column(Text)
    city = Column(String(100))
    state = Column(String(100))
    country = Column(String(100))
    postal_code = Column(String(20))
    created_at = Column(DateTime, default=datetime.now)

class MerchantBanking(Base):
    __tablename__ = 'merchant_banking'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    account_number = Column(String(50))
    ifsc = Column(String(20))
    bank_name = Column(String(100))
    account_type = Column(String(50))
    verification_status = Column(String(50))
    verified_at = Column(DateTime)
    settlement_cycle = Column(String(10))
    rolling_reserve_percentage = Column(DECIMAL(5,2))
    current_balance = Column(DECIMAL(15,2))
    created_at = Column(DateTime, default=datetime.now)

class digital_information(Base):
    __tablename__ = 'digital_information'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    website_url = Column(String(255))
    website_status = Column(String(50))
    ssl_certificate = Column(Boolean)
    domain_age = Column(Integer)
    social_media_presence = Column(JSONB)
    online_reviews = Column(JSONB)
    digital_footprint_score = Column(Float)
    created_at = Column(DateTime, default=datetime.now)

class network_overview(Base):
    __tablename__ = 'network_overview'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    total_connections = Column(Integer)
    high_risk_connections = Column(Integer)
    network_risk_score = Column(Integer)
    directors_count = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)

class relationships(Base):
    __tablename__ = 'relationships'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(UUID(as_uuid=True))
    related_entity_id = Column(UUID(as_uuid=True))
    related_entity_name = Column(String(255))
    relationship_type = Column(String(100))
    registration_number = Column(String(100))
    location = Column(String(255))
    connection_count = Column(Integer)
    risk_level = Column(String(50))
    created_at = Column(DateTime, default=datetime.now)

class AllDFPDataTable(Base):
    __tablename__ = 'raw_dfp_data'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, default=datetime.now)
    data = Column(JSONB, nullable=False)

class DigitalFootPrint(Base):
    __tablename__ = 'digital_footprint_final'
    __table_args__ = {'schema': 'public'}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    merchant_id = Column(String, nullable=False, index=True)
    timestamp = Column(DateTime, default=datetime.now)
    data = Column(JSONB, nullable=False)