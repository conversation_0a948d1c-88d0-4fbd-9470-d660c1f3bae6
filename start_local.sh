#!/bin/bash

# Check if the virtual environment exists
if [ ! -d "venv" ]; then
    # If not, create it using Python 2
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

echo "Installing dependencies..."
pip install -r requirements.txt --timeout=0

echo "Initializing database..."
python init_db.py

echo "Adding admin user..."
python add_admin_user.py

echo "Adding LLM Red Flags Status..."
python add_llm_red_flag.py

echo "Adding Rules..."
python add_rules.py


echo "Starting the application..."
uvicorn app.main:app --host 0.0.0.0 --port 6969 --reload