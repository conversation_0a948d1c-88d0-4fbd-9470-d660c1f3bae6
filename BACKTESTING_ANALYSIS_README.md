# Backtesting Analysis API

This document describes the new backtesting analysis functionality that allows you to create, store, and execute rule-based and metric-based analyses on transaction data.

## Overview

The backtesting analysis system provides:

1. **SQL Query Generation**: Automatically generates SQL queries from JSON configuration
2. **Database Storage**: Stores analysis configurations and generated SQL in the database
3. **API Endpoints**: RESTful APIs for creating and executing analyses
4. **Flexible Configuration**: Supports complex bucketing, filtering, and rule definitions

## Architecture

### Components

1. **Database Model**: `BacktestingAnalysis` table stores analysis configurations
2. **SQL Generator**: `app/utils/backtesting_sql_generator.py` generates SQL from JSON
3. **API Endpoints**: `app/routers/strategy_backtesting.py` provides REST APIs
4. **Request/Response Models**: Pydantic models for API validation

### SQL Query Structure

The system generates three types of SQL queries:

1. **Preliminary SQL**: Processes raw data with filters, bucketing, and calculated columns
2. **Pivot SQL**: Aggregates data for performance analysis
3. **Combined SQL**: Merges preliminary and pivot queries for execution

## API Endpoints

### 1. Create Rule Analysis
```http
POST /api/strategy-backtesting/rule-analysis
```

Creates a new rule-based analysis configuration.

**Request Body**: JSON configuration with rule definitions
**Response**: Analysis details with generated SQL

### 2. Create Metric Analysis
```http
POST /api/strategy-backtesting/metric-analysis
```

Creates a new metric-based analysis configuration.

**Request Body**: JSON configuration with metric definitions
**Response**: Analysis details with generated SQL

### 3. Get Analysis
```http
GET /api/strategy-backtesting/analysis/{analysis_id}
```

Retrieves analysis configuration and SQL queries.

### 4. Execute Analysis
```http
POST /api/strategy-backtesting/analysis/{analysis_id}/execute?query_type=combined
```

Executes the generated SQL and returns results.

**Query Parameters**:
- `query_type`: `preliminary`, `pivot`, or `combined`

### 5. List Analyses
```http
GET /api/strategy-backtesting/analysis?analysis_type=rule&status=completed
```

Lists all analyses with optional filtering.

**Query Parameters**:
- `analysis_type`: `rule` or `metric`
- `status`: `created`, `processing`, `completed`, `failed`
- `limit`: Number of results (1-100)
- `offset`: Pagination offset

## Configuration Format

### Sample Rule Analysis Configuration

```json
{
  "analysisId": "analysis-*************",
  "analysisType": "rule",
  "timestamp": "2025-06-03T07:09:57.063Z",
  "dataTable": {
    "selectedTable": "transaction_data_table",
    "columnConfig": {
      "rowUniqueId": "txn_id",
      "datetime": "txn_datetime",
      "fraudLabel": "fraud_label",
      "amount": "txn_amt",
      "accountId": "mer_id",
      "transactionId": "txn_id"
    }
  },
  "configuration": {
    "columnMappings": {
      "rowUniqueId": {
        "mappedTo": "txn_id",
        "description": "Unique identifier for each row"
      }
    }
  },
  "filters": {
    "timeframeFilter": {
      "startDate": "2025-05-25T14:22",
      "endDate": "2025-07-13T14:22"
    }
  },
  "analysis": {
    "type": "rule",
    "rule": {
      "equation": {
        "operator": "AND",
        "conditions": [
          {
            "metric_name": "chargeback_amt_30d",
            "operation": ">",
            "value": 1000,
            "type": "numeric"
          }
        ]
      }
    },
    "bucketing": {
      "columns": [
        {
          "column": "txn_amt",
          "bucketing": {
            "buckets": [
              {
                "id": "auto-0",
                "label": "0 - 10000",
                "type": "auto",
                "range": {"min": 0, "max": 10000}
              }
            ],
            "bucketingType": "auto"
          }
        }
      ]
    }
  }
}
```

## Generated SQL Examples

### Preliminary SQL
```sql
WITH prelim_data AS (
    SELECT
        txn_id,
        txn_amt,
        mer_id,
        fraud_label,
        txn_datetime,
        city,
        chargeback_amt_30d,
        txn_amt_30d,
        CASE
            WHEN txn_amt >= 0 AND txn_amt <= 10000 THEN 'a.0 - 10000'
            WHEN txn_amt >= 10000 AND txn_amt <= 20000 THEN 'b.10000 - 20000'
            ELSE NULL
        END AS txn_amt_bkt,
        chargeback_amt_30d / NULLIF(txn_amt_30d, 0) AS chargeback_amt_ratio_30d,
        TO_CHAR(txn_datetime, 'YYYY-MM') AS txn_mth,
        CASE WHEN chargeback_amt_30d > 1000 THEN 1 ELSE 0 END AS rule_flag
    FROM transaction_data_table
    WHERE txn_datetime >= '2025-05-25T14:22' 
      AND txn_datetime <= '2025-07-13T14:22' 
      AND status = 'completed' 
      AND channel IN ('online', 'store') 
      AND txn_amt > 0
)
SELECT * FROM prelim_data
```

### Combined Pivot SQL
```sql
WITH prelim_data AS (
    -- Preliminary data processing
),
rule_performance_pivot_table AS (
    SELECT
        txn_mth,
        txn_amt_bkt,
        rule_flag,
        COUNT(txn_id) AS txn_cnt,
        SUM(txn_amt) AS txn_amt,
        COUNT(DISTINCT mer_id) AS mer_cnt,
        COUNT(CASE WHEN fraud_label = true THEN txn_id END) AS fraud_cnt,
        SUM(CASE WHEN fraud_label = true THEN txn_amt END) AS fraud_amt,
        COUNT(CASE WHEN fraud_label = true THEN txn_id END) * 1.0 / NULLIF(COUNT(txn_id), 0) AS fraud_cnt_rate
    FROM prelim_data
    GROUP BY txn_mth, txn_amt_bkt, rule_flag
)
SELECT * FROM rule_performance_pivot_table
ORDER BY txn_mth DESC
```

## Usage Examples

### Python Example
```python
import requests

# Create rule analysis
config = {...}  # Your analysis configuration
response = requests.post(
    "http://localhost:8000/api/strategy-backtesting/rule-analysis",
    json=config,
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

analysis_id = response.json()["analysis_id"]

# Execute analysis
result = requests.post(
    f"http://localhost:8000/api/strategy-backtesting/analysis/{analysis_id}/execute",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

data = result.json()["data"]
```

## Testing

Run the test suite:
```bash
python test_backtesting_analysis.py
```

See example usage:
```bash
python example_api_usage.py
```

## Database Schema

The `backtesting_analysis` table stores:
- `analysis_id`: Unique identifier
- `analysis_type`: 'rule' or 'metric'
- `analysis_config`: Full JSON configuration
- `prelim_sql`: Generated preliminary SQL
- `pivot_sql`: Generated pivot/combined SQL
- `status`: 'created', 'processing', 'completed', 'failed'
- Timestamps for creation and updates

## Features

### Bucketing Support
- Automatic numeric bucketing with ranges
- Custom categorical bucketing
- Multiple column bucketing

### Rule Engine
- Complex rule conditions with AND/OR operators
- Numeric, categorical, and date-based conditions
- Nested rule structures

### Filtering
- Timeframe filtering
- Population filters
- Custom WHERE conditions

### Performance Metrics
- Transaction counts and amounts
- Fraud detection rates
- Merchant-level aggregations
- Custom calculated fields

## Error Handling

The API provides comprehensive error handling:
- Validation errors for malformed configurations
- SQL execution errors with detailed messages
- Status tracking for long-running analyses
- Rollback on failures

## Security

- Authentication required for all endpoints
- Input validation and sanitization
- SQL injection prevention
- Rate limiting (if configured)
