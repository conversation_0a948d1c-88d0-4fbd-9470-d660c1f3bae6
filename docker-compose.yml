version: '3.8'

services:
  app:
    build: .
    container_name: my_app
    ports:
      - "8001:8000"
    depends_on:
      - db
      - neo4j
    environment:
      # Database Configuration
      - DATABASE_URL=*****************************************/your_database_name
      - DB_USER=chayan
      - DB_PASSWORD=your_password
      - DB_HOST=db
      - DB_PORT=5432
      - DB_NAME=your_database_name
      
      # Graph Database Configuration
      - GRAPH_DATABASE_URL=bolt://neo4j:7687
      - GRAPH_DATABASE_USER=neo4j
      - GRAPH_DATABASE_PASSWORD=password
      
      # Email Configuration
      - EMAIL_USERNAME=<EMAIL>
      - EMAIL_PASSWORD=bzmq qchb cpaq qowk
      
      # Admin Configuration
      - ADMIN_USERNAME=ADMIN
      - ADMIN_PASSWORD=ADMINPASSWORD
      - ADMIN_EMAIL=<EMAIL>
      
      # API Keys
      - GROQ_API_KEY=********************************************************
      - GROQ_API_KEY2=********************************************************
      - GROQ_API_KEY3=********************************************************
      - PERPLEXITY_API_KEY=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
      - TAVILY_API_KEY=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
      
      # Docker Configuration
      - DOCKER_VOLUME_DIRECTORY=./docker-data
      
      # Update Intervals
      - linkage_update_interval=1000000
      - percentile_update_interval=100000
      - red_flag_update_interval=1000000
      - response_time_series_update_interval=5
      - find_digital_for_all_merchants_interval=6000000
      - calculate_red_flags_interval=5000000
      - update_summery_interval=500000
      - update_key_metrics_interval=5000000
    volumes:
      - .:/app

  db:
    image: postgres:14
    container_name: postgres_db
    environment:
      POSTGRES_USER: chayan
      POSTGRES_PASSWORD: your_password
      POSTGRES_DB: your_database_name
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  neo4j:
    image: neo4j:5.26.0
    container_name: neo4j_db
    environment:
      NEO4J_AUTH: neo4j/password
    ports:
      - "7475:7474"
      - "7688:7687"
    volumes:
      - neo4j_data:/data

volumes:
  postgres_data:
  neo4j_data: