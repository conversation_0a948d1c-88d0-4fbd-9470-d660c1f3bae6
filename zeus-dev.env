GRAPH_DATABASE_URL=neo4j+s://7f027758.databases.neo4j.io
GRAPH_DATABASE_USER=neo4j
GRAPH_DATABASE_PASSWORD=3Ter6qvzjV8-mkxi35oA-imh0G7c6-MCIgg99CyBtXA

DB_USER=postgres
DB_PASSWORD=1rpraZEuRTp9aJjzFX47
DB_HOST=modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com
DB_PORT=5432
DB_NAME=postgres
GROQ_API_KEY=********************************************************
GROQ_API_KEY2=********************************************************
GROQ_API_KEY3=********************************************************
PERPLEXITY_API_KEY=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
TAVILY_API_KEY=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
ADMIN_USERNAME=ADMIN
ADMIN_PASSWORD=ADMINPASSWORD
ADMIN_EMAIL=<EMAIL>
ADMIN_EMAIL_PASSWORD=bzmq qchb cpaq qowk
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=bzmq qchb cpaq qowk
DOCKER_VOLUME_DIRECTORY=./docker-data
linkage_update_interval=1000000
percentile_update_interval=100000
red_flag_update_interval=1000000
response_time_series_update_interval=5
find_digital_for_all_merchants_interval=6000000
calculate_red_flags_interval=5000000
update_summery_interval=5000000
update_key_metrics_interval=5000000
MILVUS_URI=http://localhost:19530
MILVUS_TOKEN=
MILVUS_COLLECTION=email_docs
MILVUS_DIMENSION=768
MILVUS_INDEX_TYPE=GPU_IVF_PQ
MILVUS_METRIC_TYPE=IP
MILVUS_NPROBE=15
MILVUS_NLIST=128
MILVUS_M=64
MILVUS_NBITS=8
MINIO_ADDRESS=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
ETCD_ENDPOINTS=localhost:2379