absl-py==2.1.0
aiofiles==23.2.1
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiosignal==1.3.2
aiosqlite==0.20.0
alembic==1.13.1
annotated-types==0.7.0
anyio==4.7.0
APScheduler==3.11.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
asttokens==3.0.0
async-timeout==4.0.3
asyncpg==0.30.0
attrs==24.3.0
bcrypt==4.0.1
beautifulsoup4==4.12.3
blis==1.1.0
bs4==0.0.2
cachetools==5.5.0
catalogue==2.0.10
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.0
click==8.1.7
cloudpathlib==0.20.0
cloudpickle==3.1.0
colorama==0.4.6
coloredlogs==15.0.1
comm==0.2.2
confection
contourpy==1.3.1
cryptography==44.0.0
cycler==0.12.1
cymem==2.0.10
dataclasses-json==0.6.7
datasets
debugpy==1.8.11
decorator==5.1.1
defusedxml==0.7.1
dill==0.3.8
distro==1.9.0
dnspython==2.7.0
docker==7.1.0
ecdsa==0.19.0
email-validator==2.0.0
environs==9.5.0
et_xmlfile==2.0.0
exceptiongroup==1.2.2
executing==2.1.0
fastapi>=0.100.0
filelock==3.16.1
flatbuffers==24.12.23
fonttools==4.55.3
fpdf==1.7.2
frozenlist==1.5.0
fsspec==2024.12.0
gensim
GeoAlchemy2==0.14.0
google-auth==2.37.0
greenlet==3.1.1
groq
grpcio==1.53.0
gunicorn==21.0.0
h11==0.14.0
h5py==3.12.1
httpcore==1.0.7
httptools==0.6.4
httpx==0.26.0
httpx-sse==0.4.0
huggingface-hub==0.27.0
humanfriendly==10.0
idna==3.10
iniconfig==2.0.0
ipwhois==1.3.0
ipykernel==6.29.5
ipython==8.31.0
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.2
jiter==0.8.2
joblib==1.3.2
jsonpatch==1.33
jsonpointer==3.0.0
jupyter_client==8.6.3
jupyter_core==5.7.2
keras==3.7.0
kiwisolver==1.4.7
langchain>=0.1.0
langchain-community==0.2.19
langchain-core>=0.1.0
langchain-groq>=0.1.0
langchain-text-splitters==0.2.4
langcodes==3.5.0
langgraph==0.2.60
langgraph-checkpoint==2.0.9
langgraph-checkpoint-postgres==2.0.9
langgraph-sdk==0.1.48
langsmith==0.1.147
language_data==1.3.0
llvmlite==0.43.0
Mako==1.3.8
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.23.2
matplotlib>=3.0.0
matplotlib-inline==0.1.7
mdurl==0.1.2
milvus-lite==2.4.11
milvus-model==0.2.11
minio==7.2.13
ml_dtypes==0.5.0
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
multiprocess==0.70.16
murmurhash==1.0.11
mypy-extensions==1.0.0
namex==0.0.8
neo4j==5.14.1
nest-asyncio==1.6.0
networkx==3.1
nltk==3.9.1
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.19.3
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.1.105
onnxruntime==1.20.1
openai==1.58.1
openpyxl==3.1.2
optree==0.13.1
orjson==3.10.12
packaging==24.2
pandas>=2.0.0
parso==0.8.4
passlib==1.7.4
pdf2image==1.17.0
pexpect==4.9.0
pillow==10.2.0
platformdirs==4.3.6
pluggy==1.5.0
preshed==3.0.9
prompt_toolkit==3.0.48
propcache==0.2.1
protobuf==5.29.2
psutil==6.1.1
psycopg==3.2.3
psycopg-binary==3.2.3
psycopg-pool==3.2.4
psycopg2-binary==2.9.9
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==18.1.0
pyarrow-hotfix==0.6
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pycryptodome==3.21.0
pydantic
pydantic-extra-types==2.10.1
pydantic-settings==2.2.1
pydantic_core
Pygments==2.18.0
pymilvus==2.2.11
PyMuPDF==1.25.1
pynndescent==0.5.13
pyparsing==3.2.0
pyperclip==1.8.2
pytesseract==0.3.13
pytest==8.0.1
pytest-asyncio==0.23.5
python-dateutil==2.8.2
python-dotenv>=1.0.0
python-jose==3.3.0
python-magic==0.4.27
python-multipart>=0.0.6
python-slugify==8.0.4
python-whois==0.9.5
pytz==2024.1
PyYAML==6.0.2
pyzmq==26.2.0
redis==5.0.1
regex==2024.11.6
requests>=2.31.0
requests-toolbelt==1.0.0
rich==13.9.4
rsa==4.9
safetensors==0.4.5
scikit-learn==1.4.0
scipy
seaborn==0.13.2
sentence-transformers==3.3.1
shap==0.46.0
shellingham==1.5.4
six==1.17.0
slicer==0.0.8
smart-open==7.1.0
sniffio==1.3.1
soupsieve==2.6
spacy==3.8.3
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy>=2.0.0
srsly==2.5.0
stack-data==0.6.3
starlette==0.36.3
sympy==1.13.1
tabulate==0.9.0
tavily-python==0.5.0
tbb==2022.0.0
tcmlib==1.2.0
tenacity==8.5.0
text-unidecode==1.3
thinc==8.3.3
threadpoolctl==3.5.0
tiktoken==0.8.0
tokenizers==0.19.0
tomli==2.2.1
torch==2.2.0
torch-geometric==2.6.1
torchvision==0.17.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.41.0
triton==2.2.0
typer==0.15.1
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.2
tzlocal==5.2
ujson==5.10.0
umap-learn==0.5.4
urllib3==2.2.3
uvicorn>=0.25.0
uvloop==0.21.0
wasabi==1.1.3
watchfiles==1.0.3
wcwidth==0.2.13
weasel==0.4.1
websockets==14.1
whois==1.20240129.2
wrapt==1.17.0
xxhash==3.5.0
yarl==1.18.3
