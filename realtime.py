import imaplib
import email
from email.header import decode_header
import time
import os

# Account credentials
username = "kuma<PERSON><EMAIL>"
password = "bzmq qchb cpaq qowk"

def check_email():
    # Connect to the server
    mail = imaplib.IMAP4_SSL("imap.gmail.com")
    # Login to your account
    mail.login(username, password)

    # Select the mailbox you want to check
    mail.select("inbox")

    # Search for all emails in the inbox
    status, messages = mail.search(None, "ALL")

    # Convert messages to a list of email IDs
    email_ids = messages[0].split()

    # Fetch the latest email
    latest_email_id = email_ids[-1]

    # Fetch the email by ID
    status, msg_data = mail.fetch(latest_email_id, "(RFC822)")

    # Get the email content
    for response_part in msg_data:
        if isinstance(response_part, tuple):
            msg = email.message_from_bytes(response_part[1])
            # Decode the email subject
            subject, encoding = decode_header(msg["Subject"])[0]
            if isinstance(subject, bytes):
                # If it's a bytes type, decode to str
                subject = subject.decode(encoding if encoding else "utf-8")
            # Print the subject
            print("Subject:", subject)

    # Logout
    mail.logout()

if __name__ == "__main__":
    while True:
        check_email()
       