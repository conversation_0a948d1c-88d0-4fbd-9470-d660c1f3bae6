import requests

# List of API endpoints
endpoints = [
    "/api/v1/job-sch/update_digital_footprint",
    "/api/v1/job-sch/test-neo4j",
    "/api/v1/job-sch/update_key_metrics",
    "/api/v1/job-sch/update_risk_percentiles_and_risk_scores",
    "/api/v1/job-sch/update_linkages",
    "/api/v1/job-sch/update_summery",
    "/api/v1/job-sch/protected-route"
]
def login():
    url = "http://*************:6969/api/v1/auth/login"
    payload = {
        "email": "<EMAIL>",
        "password": "ADMINPASSWORD"
    }
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        token = response.json().get("access_token")
        return token
    except requests.exceptions.RequestException as e:
        print(f"Login failed: {e}")
        return None

# Base URL
base_url = "http://localhost:6969"  # Replace with your actual domain

# Function to make GET requests
def make_get_requests():
    token = login()
    if token is None:
        print("Login failed. Exiting.")
        return
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        try:
            response = requests.get(url, headers={"Authorization": f"Bearer {token}"})
            print(f"GET {url} - Status Code: {response.status_code}")
            # Optionally print response content
            # print(response.text)
        except requests.exceptions.RequestException as e:
            print(f"Error with {url}: {e}")

# Run the function
make_get_requests()
