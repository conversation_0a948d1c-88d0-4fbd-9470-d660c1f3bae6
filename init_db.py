from app.database import engine, Base
from app.models import models
from app.utils.seed_merchant import seed_merchant_data
import logging
from sqlalchemy import text

logging.basicConfig(level=logging.WARNING)
logger = logging.getLogger(__name__)

def init_database(reset_db: bool = False):
    try:
        if reset_db:
            logger.info("Resetting the database...")
            with engine.connect() as connection:
                connection.execute(text("COMMIT"))
                result = connection.execute(text("SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'public'"))
                if result.fetchone():
                    connection.execute(text("DROP SCHEMA public CASCADE"))
                connection.execute(text("CREATE SCHEMA public"))
                connection.execute(text("GRANT ALL ON SCHEMA public TO chayan"))
                connection.execute(text("GRANT ALL ON SCHEMA public TO public"))
                connection.commit()
        
        logger.info("Creating database tables if they don't exist...")
        Base.metadata.create_all(bind=engine)
        logger.info("Database initialization completed successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise

if __name__ == "__main__":
    init_database()