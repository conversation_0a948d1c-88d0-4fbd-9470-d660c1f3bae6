[loggers]
keys=root,uvicorn.error,uvicorn.access

[handlers]
keys=console,error_file,access_file

[formatters]
keys=generic,access

[logger_root]
level=INFO
handlers=console

[logger_uvicorn.error]
level=INFO
handlers=error_file
propagate=1
qualname=uvicorn.error

[logger_uvicorn.access]
level=INFO
handlers=access_file
propagate=0
qualname=uvicorn.access

[handler_console]
class=StreamHandler
formatter=generic
args=(sys.stdout, )

[handler_error_file]
class=FileHandler
formatter=generic
args=('logs/error.log', 'w')

[handler_access_file]
class=FileHandler
formatter=access
args=('logs/access.log', 'w')

[formatter_generic]
format=%(asctime)s [%(process)d] [%(levelname)s] %(message)s
datefmt=%Y-%m-%d %H:%M:%S
class=logging.Formatter

[formatter_access]
format=%(asctime)s [%(process)d] [%(levelname)s] %(message)s
class=logging.Formatter
